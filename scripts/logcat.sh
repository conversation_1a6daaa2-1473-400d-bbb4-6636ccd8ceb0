DEBUG_PACKAGE_NAME="com.resoluttech.barcelona.bcn.dev.debug"
RELEASE_PACKAGE_NAME="com.resoluttech.barcelona.bcn.dev.release"
NUMBER_OF_LINES_TO_LOG=5000
if [ $1 ]; then
  echo "Checking connected processes..."
  DEBUG_PID=$(adb shell pidof $DEBUG_PACKAGE_NAME)
  RELEASE_PID=$(adb shell pidof $RELEASE_PACKAGE_NAME)
  if [[ $RELEASE_PID ]]; then
    echo "You are connected to release build, logs are gettting generated for $RELEASE_PID."
    LOGS=$(adb logcat --pid=$RELEASE_PID -t $NUMBER_OF_LINES_TO_LOG)
    echo  $LOGS > $1
    echo "Logging process to file $1 is completed."
  elif [[ $DEBUG_PID ]]; then
    echo "You are connected to debug build, logs are getting generated for $DEBUG_PID"
    LOGS=$(adb logcat --pid=$DEBUG_PID -t $NUMBER_OF_LINES_TO_LOG)
    echo  $LOGS > $1
    echo "Logging process to file $1 is completed."
  else
    echo "There is no connected to process."
    echo "Writing general device logs.."
    LOGS=$(adb logcat -t $NUMBER_OF_LINES_TO_LOG)
    echo $LOGS > $1
  fi
else
    echo "Please try again with file name."
fi

