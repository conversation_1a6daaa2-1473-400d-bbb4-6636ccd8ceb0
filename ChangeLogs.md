# Change Logs

Badges: `[UPDATED]`, `[FIXED]`, `[NEW]`, `[REMOVED]`

Last Updated: `23 AUG 2023`

# [1.0(18)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(18))

Created On: `23 AUG 2023`

* `[UPDATE]` - Updated gradle to 8.0
* `[UPDATE]` - Updated deprecated APIs
* `[UPDATE]` - Handle push token expiry
* `[UPDATE]` - Update java version to 17
* `[UPDATE]` - Updated whole app's design
* `[UPDATE]` - Update app version name format
* `[UPDATE]` - Getting password policy from server
* `[UPDATE]` - Enable customer support phone number
* `[UPDATE]` - Update google map keys in github secrets
* `[UPDATE]` - Update date time and phone number format
* `[UPDATE]` - Update Font Family to support bold texts
* `[UPDATE]` - Allowing only alphanumeric in session PIN
* `[UPDATE]` - Add background shadow to material buttons
* `[UPDATE]` - Enabled buttons by default all across the app
* `[UPDATE]` - Update minimum document size for profile and KYC documents
* `[UPDATE]` - Updated strings all across the app to keep consistency between iOS and Android
* `[FIXED]` - Fixed KYC document loading icon
* `[FIXED]` - Fixed screenshot compression quality
* `[FIXED]` - Fixed issues caused by config changes
* `[FIXED]` - Fixed emoji counter issue in edit texts
* `[FIXED]` - Fixed enter session PIN status bar color
* `[FIXED]` - Fixed account to account wallet selection
* `[FIXED]` - Fixed app deeplink and profile screen crash
* `[FIXED]` - Fixed validation order for add trusted contact
* `[FIXED]` - Handle force out of flow across all the feature
* `[FIXED]` - Fixed transaction status screenshot in dark mode
* `[FIXED]` - Fixed behaviour of dialog on manage wallet screen
* `[FIXED]` - Fixed inactive sender wallet crash in payment flow
* `[FIXED]` - Clearing user data on 3 wrong session PIN attempts
* `[FIXED]` - Fixed crash happening on selecting pay bills widget
* `[FIXED]` - Fixed crash on find agent fragment for Android 8 and below
* `[FIXED]` - Fixed crash when user clicks on back button on update app screen
* `[FIXED]` - Fixed enter KYC next button is tappable before data is filled during update KYC
* `[FIXED]` - Handle leo unauthorized exception and INVALID_KYC_DATA_INPUT_FORMAT  error code
* `[FIXED]` - Fixed crash happening on changing selected wallet on home screen with no internet
* `[FIXED]` - Fixed crash happening when no web view was installed and user goes to load wallet screen
* `[FIXED]` - Fixed crash happening when user closes the internet while a KYC document was being uploaded
* `[FIXED]` - Fixed crash happening when user clicks on submit button on KYC document when a document was being uploaded
* `[NEW]` - Add a splash screen
* `[NEW]` - Trimming the password
* `[NEW]` - New third party screen
* `[NEW]` - Attach timber logs with firebase
* `[NEW]` - Show warning when only one session PIN attempt is left
* `[NEW]` - Add reset password flow for agents from agency banking app
* `[NEW]` - Add right chevron to tappable notification screen list items
* `[NEW]` - Allowing user for opting out from sending crashlytics reports

# [1.0.3(15)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(15))

Created On: `14 FEB 2023`

* `[UPDATE]` - Hide Wallet Balance and Name if getMoneyScreenDataRPC fails
* `[UPDATE]` - Not Calling getTransactionRPC automatically until for 20 seconds
* `[UPDATE]` - Keeping User on Transaction Status Screen when they add a private remark on a transaction
* `[UPDATE]` - Share App will Share a link of Yafika Mobile from Google Play
* `[FIXED]` - Fixed crash caused by contact permission getting revoked
* `[FIXED]` - Fix Navigation Issue when User Clicks on 2 Buttons Together
* `[NEW]` - Added Sign Up Requirement Screen
* `[NEW]` - Implemented Talk Back Feature(Still in Testing)
* `[NEW]` - User should complete KYC within 3 days once they sign up or else need to restart sign up process
* `[NEW]` - Support Android 12 and 13

# [1.0.2(13)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(13))

Created On: `5 DEC 2022`

* `[UPDATE]` - Dismiss Fee Dialog when no internet
* `[FIXED]` - Fixed navigating back on Enter Session PIN Screen
* `[FIXED]` - Fixed Enter Session PIN Screen being displayed twice
* `[FIXED]` - Fixed auto scroll to top in Transaction screen
* `[FIXED]` - Fixed navigation issue from Reset Password Fragment to Sign IN Trusted Contact Screen
* `[FIXED]` - Fixed resend timer in OTP Reader Dialog 

# [1.0.1(11)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(11))

Created On: `17 NOV 2022`

* `[UPDATED]` - Disabled Datadog Support
* `[UPDATED]` - Allowing user to use space in password
* `[FIXED]` - Fixed the resend timer going into negative in OTP Reader Dialog
* `[FIXED]` - Fixed crash which happened when user closes the app when alert dialog was being displayed and opens the app again after sometime
* `[FIXED]` - Fixed Orientation issue in Third Party Screen
* `[REMOVED]` - Removed delete icon in Manage Trusted Contact screen when there are only 2 Trusted Contacts

# [1.0.0(9)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(9)) 

Created On: `14 NOV 2022`

* `[FIXED]` - Fixed the name of build generated by release CI which still contained "Barcelona" in the name

# [1.0.0(8)](https://github.com/Resolut-Tech/Android/releases/tag/prod-v0.0.1(8)) 

Created On: `11 NOV 2022`

* `[NEW]` - First Release
