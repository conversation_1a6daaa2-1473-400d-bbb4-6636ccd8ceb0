# Generated Strings

## Reason
To maintain consistency between iOS and Android Applications, a script is written to convert iOS 
string file to XML file which Android can use. 

## Where to Find Generated String File
You can find generated string file here:
[Generated Android String(EN)](https://github.com/Resolut-Tech/BCN-iOS/blob/master/Barcelona/Generated/AndroidStrings/en/strings.xml)
[Generated Android String(NY)](https://github.com/Resolut-Tech/BCN-iOS/blob/master/Barcelona/Generated/AndroidStrings/ny/strings.xml)

## Steps to Use
1. Go to the above links and copy them to [Android/bcncore/src/main/res/values/strings.xml] and 
   [Android/bcncore/src/main/res/values-ny/strings.xml] respectively in the project.
2. Add Escape Apostrophe to remove errors from the string file.
3. Certain strings like plurals and string-arrays cannot be added from iOS string, you have to add 
   them manually on Android.
4. Remove the following strings as they should be plurals and release CI fails if we have them: 
   1. timeRemainingHour
   2. timeRemainingHours
   3. timeRemainingMinute
   4. timeRemainingMinutes
   5. timeRemainingSecond
   6. timeRemainingSeconds
   7. alertMessageIncorrectPin
   8. otpEntryOTPAttemptsLeft
   9. otpEntryOTPOneAttemptLeft
   10. wrongSessionPinEnteredLabel
5. Run Release CI on your PR to fix any new issues created.
