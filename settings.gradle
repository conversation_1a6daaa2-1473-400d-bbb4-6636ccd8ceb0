plugins {
    // See https://jmfayard.github.io/refreshVersions
    id 'de.fayard.refreshVersions' version '0.60.5'
}

dependencyResolutionManagement {
    repositories {
        maven {
            url "https://artifacts.surya-digital.in/repository/maven-releases/"
            def artifactoryProperties = new Properties()
            artifactoryProperties.load(new FileInputStream(new File("$rootDir/artifactory.properties")))
            credentials {
                username = artifactoryProperties["username"].toString()
                password = artifactoryProperties["password"].toString()
            }
        }
        mavenCentral()
    }
    versionCatalogs {
        lkr {
            from("com.suryadigital.leo:version-catalog:$leo_kotlin_runtime_version")
        }
    }
}

refreshVersions {
    rejectVersionIf {
        candidate.stabilityLevel.isLessStableThan(current.stabilityLevel)
    }
}

include ':bcncore'
rootProject.name='Barcelona'
include ':app'
