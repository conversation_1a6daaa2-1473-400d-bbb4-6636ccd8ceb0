# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
-keepclassmembers class com.resoluttech.core.loadwallet.WebAppInterface {
   public *;
}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# https://issuetracker.google.com/issues/142601969#comment12
-keepnames class androidx.navigation.fragment.NavHostFragment
# https://stackoverflow.com/a/50378828/5163725
-keep class * extends androidx.fragment.app.Fragment{}
-keepclassmembers class kotlinx.serialization.json.* {
    *** Companion;
}
-keepclasseswithmembers class kotlinx.serialization.json.* {
    kotlinx.serialization.KSerializer serializer(...);
}

# Navigation graphs can use Parcelable objects to pass data around and we do this in the project.
# Unfortunately these object names are referred to by their complete paths
# eg: `MyFoo` is referred to as `com.resoluttech.core.blah.MyFoo`.
# Proguard obfuscates these names so we see crashes at runtime when the
# Navgraph attempts to reference classes by names that have been obfuscated
# (even though the class exists).
# This rule makes sure that any class defined as Parcelable
# (all nav graph arguments are Parcelable) will continue to
# retain their names.
# This is a bad situation but Google recommends we add Keep rules themselves:
# https://developer.android.com/guide/navigation/navigation-pass-data#proguard_considerations
-keepnames class * extends android.os.Parcelable

# BlockHound is excluded because:
# 1. Android apps use Kotlin Coroutines + Dispatchers (Main/IO/Default) for thread management.
# 2. StrictMode and MainThread checks natively detect blocking UI thread operations.
# 3. Designed for JVM reactive stacks (e.g., Reactor), not Android's ART runtime.
# 4. Adds unnecessary overhead; Coroutines provide lightweight concurrency.
-dontwarn reactor.blockhound.integration.BlockHoundIntegration
