## Google Service Info

We are creating a new directory to store the `google-services.json` as the plugin fails to read
it when there are 2 or more upper cases in the flavor package name. For eg., the plugin is searching for
`google-service.json` in location `/app/src/barcelonaUatrelease/google-service.json` instead of
`/app/src/barcelonaUatRelease/google-service.json`.

While generating the APK, the Gradle reads the JSON file and inserts it into the string resource file. For
UAT release build, only the JSON from `uatRelease` is converted into a string and for Dev release build, only
the JSON from `devRelease` is converted into a string and the JSON file is not included in the APK.
