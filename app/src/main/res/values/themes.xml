<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--  Bridge themes are the way of using material theme with app compat please check here for more details
  https://material.io/develop/android/docs/getting-started#appcompat-themes -->
    <style name="Theme.Barcelona" parent="Theme.MaterialComponents.DayNight.NoActionBar.Bridge">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="fontFamily">@font/pt_sans</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarFullscreenTheme">
            @style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen
        </item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar
        </item>
        <item name="colorOnPrimary">@android:color/white</item>
    </style>

</resources>
