<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/startFragment">
    <include app:graph="@navigation/home_nav" />
    <include app:graph="@navigation/sign_in_nav" />
    <include app:graph="@navigation/sign_up_nav" />
    <include app:graph="@navigation/landing_screen_nav" />

    <fragment
        android:id="@+id/startFragment"
        android:name="com.resoluttech.barcelona.fragments.StartFragment"
        android:label="@string/start_fragment_label"
        tools:layout="@layout/fragment_start">
        <action
            android:id="@+id/action_startFragment_to_landingScreen"
            app:destination="@id/landing_screen_nav"
            app:popUpTo="@id/startFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_startFragment_to_home_nav"
            app:destination="@id/home_nav"
            app:popUpTo="@id/startFragment"
            app:popUpToInclusive="true" />
    </fragment>
    <include app:graph="@navigation/enter_amount_nav" />
    <include app:graph="@navigation/send_money_to_counter_party_nav" />
</navigation>
