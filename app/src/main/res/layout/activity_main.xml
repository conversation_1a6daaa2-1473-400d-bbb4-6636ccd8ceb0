<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/default_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        app:contentInsetStart="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:showIn="@layout/activity_main" />

    <TextView
        android:id="@+id/network_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/destructiveActionColor"
        android:padding="@dimen/dimen_16dp"
        android:text="@string/alertMessageNoInternet"
        android:textColor="@color/spinnerItemTextColor"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/default_toolbar"
        tools:visibility="visible" />

    <include
        android:id="@+id/mainLayout"
        layout="@layout/content_main"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/network_info" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/bottom_nav_separator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_1dp"
            android:background="@color/bottomNavSeprator" />

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_nav_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="false"
            android:elevation="@dimen/dimen_8dp"
            android:paddingBottom="@dimen/dimen_4dp"
            app:backgroundTint="@color/windowBackground"
            app:itemIconTint="@drawable/bottom_nav_bar_icon_tint_state_list"
            app:itemTextColor="@drawable/bottom_nav_bar_icon_tint_state_list"
            app:itemTextAppearanceInactive="@style/BottomNavigationText"
            app:itemTextAppearanceActive="@style/BottomNavigationText"
            app:labelVisibilityMode="labeled"
            app:menu="@menu/bottom_nav_menu" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
