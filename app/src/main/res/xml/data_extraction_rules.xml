<?xml version="1.0" encoding="utf-8"?><!--
   Sample data extraction rules file; uncomment and customize as necessary.
   See https://developer.android.com/about/versions/12/backup-restore#xml-changes
   for details.
-->
<data-extraction-rules>
    <cloud-backup>
        <!--
        User data is stored in a user’s Google Drive so that it can later be restored on that device
        or a new device.

        <include domain="file" path="file_to_include"/>
        <exclude domain="file" path="file_to_exclude"/>
        <include domain="file" path="include_folder"/>
        <exclude domain="file" path="include_folder/file_to_exclude"/>
        <exclude domain="file" path="exclude_folder"/>
        <include domain="file" path="exclude_folder/file_to_include"/>

        <include domain="sharedpref" path="include_shared_pref1.xml"/>
        <include domain="database" path="db_name/file_to_include"/>
        <exclude domain="database" path="db_name/include_folder/file_to_exclude"/>
        <include domain="external" path="file_to_include"/>
        <exclude domain="external" path="file_to_exclude"/>
        <include domain="root" path="file_to_include"/>
        <exclude domain="root" path="file_to_exclude"/>
        -->
    </cloud-backup>
</data-extraction-rules>
