<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    <uses-feature android:name="android.hardware.camera" />

    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <application
        android:name=".BarcelonaApplication"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="false"
        android:theme="@style/Theme.Barcelona"
        tools:ignore="LockedOrientationActivity,UnusedAttribute"
        tools:replace="android:name,android:supportsRtl">
        <receiver
            android:name=".widgets.ScanAndPayWidget"
            android:exported="true"
            android:label="@string/appShortCutScanAndPay">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/scan_and_pay_widget_info" />
        </receiver>

        <receiver
            android:name=".widgets.SendMoneyWidget"
            android:exported="true"
            android:label="@string/appShortCutSendMoney">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/send_money_widget_info" />
        </receiver>

        <receiver
            android:name=".widgets.LoadWalletWidget"
            android:exported="true"
            android:label="@string/appShortCutLoadWallet">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/load_wallet_widget_info" />
        </receiver>

        <receiver
            android:name=".widgets.TransactionsWidget"
            android:exported="true"
            android:label="@string/appShortCutTransactions">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/transactions_widget_info" />
        </receiver>
        <receiver
            android:name=".widgets.PayBillWidget"
            android:exported="true"
            android:label="@string/appShortCutPayBills">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/pay_bill_widget_info" />
        </receiver>

        <receiver
            android:name=".widgets.MoneyTransferWidget"
            android:exported="true"
            android:label="@string/appName">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/money_transfer_widget_info" />
        </receiver>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.App.Starting"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="DiscouragedApi">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- Added `tools:ignore` attribute here as this intent filter is added to with scheme "resoluttechbcn://” not "https" or "http" -->
            <intent-filter android:autoVerify="true"
                tools:ignore="AppLinkUrlError">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with "resoluttechbcn://” -->
                <data android:scheme="resoluttechbcn" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app-redirect.resoluttech.link"
                    android:scheme="http" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app-redirect.resoluttech.link"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Barcelona"
            tools:ignore="DiscouragedApi,LockedOrientationActivity,MissingClass" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_api_key" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <service
            android:name=".BCNFirebaseInstanceService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Set custom default icon. This is used when no icon is set for incoming notification.-->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification_logo" />

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="true" />
    </application>

</manifest>
