package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.LOAD_WALLET_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class LoadWalletWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.load_wallet_widget)
            val pendingIntent =
                getWidgetIntent(context, LOAD_WALLET_DEEPLINK, appWidgetId)

            views.setOnClickPendingIntent(R.id.load_Wallet, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
