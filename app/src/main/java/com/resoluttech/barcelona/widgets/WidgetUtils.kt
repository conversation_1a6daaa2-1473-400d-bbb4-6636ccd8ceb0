package com.resoluttech.barcelona.widgets

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import com.resoluttech.barcelona.MainActivity
import com.resoluttech.core.home.MoneyScreenFragment

internal fun getWidgetIntent(
    context: Context,
    deepLink: String,
    appWidgetId: Int,
): PendingIntent {
    val intent = Intent(context, MainActivity::class.java).apply {
        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
    }

    intent.putExtra(
        MoneyScreenFragment.WIDGET_SHORTCUT_DEEPLINK,
        deepLink,
    )

    return PendingIntent.getActivity(
        context,
        appWidgetId,
        intent,
        PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT,
    )
}
