package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.SEND_MONEY_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class SendMoneyWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.send_money_widget)
            val pendingIntent =
                getWidgetIntent(context, SEND_MONEY_DEEPLINK, appWidgetId)

            views.setOnClickPendingIntent(R.id.send_money, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
