package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.PAY_BILL_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class PayBillWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.pay_bill_widget)
            val pendingIntent = getWidgetIntent(
                context,
                PAY_BILL_DEEPLINK,
                appWidgetId,
            )

            views.setOnClickPendingIntent(R.id.pay_bill, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
