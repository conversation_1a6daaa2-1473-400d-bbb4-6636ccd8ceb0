package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.SCAN_AND_PAY_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class ScanAndPayWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.scan_and_pay_widget)
            val pendingIntent = getWidgetIntent(
                context,
                SCAN_AND_PAY_DEEPLINK,
                appWidgetId,
            )

            views.setOnClickPendingIntent(R.id.scan_and_pay, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
