package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.LOAD_WALLET_DEEPLINK
import com.resoluttech.barcelona.PAY_BILL_DEEPLINK
import com.resoluttech.barcelona.SCAN_AND_PAY_DEEPLINK
import com.resoluttech.barcelona.SEND_MONEY_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class MoneyTransferWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.money_transfer_widget)

            val scanAndPayPendingIntent =
                getWidgetIntent(context, SCAN_AND_PAY_DEEPLINK, appWidgetId)

            views.setOnClickPendingIntent(R.id.scan_and_pay, scanAndPayPendingIntent)
            val sendMoneyPendingIntent =
                getWidgetIntent(context, SEND_MONEY_DEEPLINK, appWidgetId + 101)

            views.setOnClickPendingIntent(R.id.send_money, sendMoneyPendingIntent)
            val loadWalletPendingIntent =
                getWidgetIntent(context, LOAD_WALLET_DEEPLINK, appWidgetId + 102)

            views.setOnClickPendingIntent(R.id.load_Wallet, loadWalletPendingIntent)
            val payBillPendingIntent =
                getWidgetIntent(context, PAY_BILL_DEEPLINK, appWidgetId + 103)

            views.setOnClickPendingIntent(R.id.pay_bill, payBillPendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
