package com.resoluttech.barcelona.widgets

import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.widget.RemoteViews
import com.resoluttech.barcelona.TRANSACTION_DEEPLINK
import com.resoluttech.bcncore.R

/**
 * Implementation of App Widget functionality.
 */
class TransactionsWidget : AppWidgetProvider() {
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray,
    ) {
        // There may be multiple widgets active, so update all of them
        for (appWidgetId in appWidgetIds) {
            val views = RemoteViews(context.packageName, R.layout.transactions_widget)
            val pendingIntent = getWidgetIntent(
                context,
                TRANSACTION_DEEPLINK,
                appWidgetId,
            )

            views.setOnClickPendingIntent(R.id.transactions, pendingIntent)

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
