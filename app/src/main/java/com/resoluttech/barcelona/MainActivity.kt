package com.resoluttech.barcelona

import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.content.res.Configuration
import android.graphics.Rect
import android.graphics.drawable.Icon
import android.os.Build
import android.os.Bundle
import android.util.TypedValue
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.Observer
import androidx.navigation.NavOptions
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.core.home.MoneyScreenDataPersistor
import com.resoluttech.core.home.MoneyScreenFragment
import com.resoluttech.core.home.MoneyScreenFragment.Companion.PUSH_NOTIFICATION_DEEPLINK_KEY
import com.resoluttech.core.home.MoneyScreenFragment.Companion.WIDGET_SHORTCUT_DEEPLINK
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.UnsupportedClientHandler
import com.resoluttech.core.utils.UpdateAppViewState
import com.resoluttech.core.views.InAppBrowserFragment
import timber.log.Timber
import com.resoluttech.bcncore.R as BCNCoreResource

class MainActivity :
    AppCompatActivity(),
    ToolbarSettable,
    MoneyScreenFragment.UserDataUpdatedListener {

    override lateinit var toolbar: Toolbar
    private val moneyScreenDataPersistor = MoneyScreenDataPersistor()
    private lateinit var bottomNav: BottomNavigationView
    private lateinit var bottomNavSeparator: View

    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        super.onCreate(savedInstanceState)
        UnsupportedClientHandler.currentState.observe(this, Observer(::reactToState))
        if (intent.extras?.getBoolean(UNSUPPORTED_APP_KEY) == true) {
            setContentView(R.layout.unsupported_app)
            setupUpdateButton()
        } else {
            setContentView(R.layout.activity_main)
            setupBottomNavMenu()
            setToolbar()
        }
        window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE,
        )
        setUpAppShortcuts()
        LogReportManager.init()
        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    // Required to handle the on back press event in browser fragment. It will not popup the [InAppBrowserFragment] until there is
                    // a possibility for browser to navigate back.
                    // Thanks : https://stackoverflow.com/a/10631591/5163725
                    if (supportFragmentManager.fragments.isNotEmpty()) {
                        val webViewFragment =
                            supportFragmentManager.fragments[0].childFragmentManager.fragments[0]
                        if (webViewFragment is InAppBrowserFragment && webViewFragment.canGoBack()) {
                            webViewFragment.goBack()
                        } else {
                            // Temporarily disable this callback to let the system handle back press
                            isEnabled = false
                            onBackPressedDispatcher.onBackPressed()
                        }
                    } else {
                        isEnabled = false
                        onBackPressedDispatcher.onBackPressed()
                    }
                }
            },
        )
    }

    private fun setupUpdateButton() {
        val updateButton: MaterialButton = findViewById(R.id.updated_button)
        updateButton.setOnClickListener {
            intent.removeExtra(UNSUPPORTED_APP_KEY)
            val updateAppIntent = Intent(Intent.ACTION_VIEW).apply {
                data = YAFIKA_MOBILE_PLAY_STORE_LINK.toUri()
            }
            startActivity(updateAppIntent)
        }
    }

    private fun reactToState(state: UpdateAppViewState) {
        when (state) {
            is UpdateAppViewState.UnsupportedAppVersion -> {
                handleUnsupportedClient()
            }

            is UpdateAppViewState.SupportedAppVersion -> {
                // Nothing to handle. Let user use the app.
            }

            is UpdateAppViewState.UserOnUpdateAppScreen -> {
                // Nothing to handle, user is on Update App Screen.
            }
        }
    }

    private fun setToolbar() {
        toolbar = findViewById(R.id.default_toolbar)
        setSupportActionBar(toolbar)
    }

    private fun enableBottomNavigation() {
        val cachedHomeData: GetMoneyScreenDataRPC.Response? =
            moneyScreenDataPersistor.getCachedMoneyScreenData()
        val isEnabled: Boolean = (cachedHomeData != null)
        bottomNav.menu.findItem(R.id.billersFragment).isEnabled = isEnabled
        bottomNav.menu.findItem(R.id.transactionsFragment).isEnabled = isEnabled
        bottomNav.menu.findItem(R.id.profileFragment).isEnabled = isEnabled
    }

    private fun setupBottomNavMenu() {
        // Thanks https://stackoverflow.com/a/59275182/5163725
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController
        bottomNav = findViewById(R.id.bottom_nav_bar)
        bottomNavSeparator = findViewById(R.id.bottom_nav_separator)
        bottomNav.apply {
            setupWithNavController(navController)
            setOnItemReselectedListener {
                // Doing nothing to ignore the reselection
                Timber.tag(TAG).i("Re-selecting the already selected fragment.")
            }
        }
        val mainLayout = findViewById<ConstraintLayout>(R.id.mainLayout)
        bottomNav.setOnItemSelectedListener { item ->
            navigateManuallyToDestination(item)
            true
        }
        bottomNav.setOnItemReselectedListener {
            // This will be called when we are in tab X and reselect the same,
            // for eg: let's say we select tab home and we again select the home being in home screen.
            // We are ignoring this to maintain the backstack and also to reduce the redundant network calls.
            Timber.tag(TAG).i("Ignoring the reselection of same bottom navigation item.")
        }
        navController.addOnDestinationChangedListener { _, destination, _ ->
            when (destination.id) {
                R.id.moneyScreenFragment,
                R.id.transactionsFragment, R.id.profileFragment, R.id.billersFragment,
                -> {
                    showBottomNavigationBar(mainLayout, bottomNav)
                    enableBottomNavigation()
                }

                else -> {
                    hideBottomNavigationBar(mainLayout, bottomNav)
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        /*
            New intent is delivered to app when app is already running in background, so we need to
            update the original intent with new intent.
         */
        getIntent().data = intent.data
        getIntent().putExtra(
            PUSH_NOTIFICATION_DEEPLINK_KEY,
            intent.getStringExtra(PUSH_NOTIFICATION_DEEPLINK_KEY),
        )
        getIntent().putExtra(
            WIDGET_SHORTCUT_DEEPLINK,
            intent.getStringExtra(WIDGET_SHORTCUT_DEEPLINK),
        )
    }

    private fun setUpAppShortcuts() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N) {
            val shortcutManager = getSystemService(ShortcutManager::class.java)

            val shortcutScanAndPay = getShortcutInfo(
                deepLink = SCAN_AND_PAY_DEEPLINK,
                id = SCAN_AND_PAY_ID,
                icon = BCNCoreResource.drawable.ic_shortcut_scan_and_pay,
                label = getString(BCNCoreResource.string.appShortCutScanAndPay),
            )

            val shortcutSendMoney = getShortcutInfo(
                deepLink = SEND_MONEY_DEEPLINK,
                id = SEND_MONEY_ID,
                icon = BCNCoreResource.drawable.ic_shortcut_send_money,
                label = getString(BCNCoreResource.string.appShortCutSendMoney),
            )

            val shortcutLoadMoney = getShortcutInfo(
                deepLink = LOAD_WALLET_DEEPLINK,
                id = LOAD_WALLET_ID,
                icon = BCNCoreResource.drawable.ic_shortcut_load_money,
                label = getString(BCNCoreResource.string.appShortCutLoadWallet),
            )

            val shortcutPayBill = getShortcutInfo(
                deepLink = PAY_BILL_DEEPLINK,
                id = PAY_BILL_ID,
                icon = BCNCoreResource.drawable.ic_shortcut_pay_bill,
                label = getString(BCNCoreResource.string.appShortCutPayBills),
            )

            shortcutManager.dynamicShortcuts = listOfNotNull(
                shortcutScanAndPay,
                shortcutSendMoney,
                shortcutLoadMoney,
                shortcutPayBill,
            )
        }
    }

    private fun getShortcutInfo(
        deepLink: String,
        id: String,
        icon: Int,
        label: String,
    ): ShortcutInfo? {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        intent.putExtra(
            WIDGET_SHORTCUT_DEEPLINK,
            deepLink,
        )

        intent.action = id

        return if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N) {
            ShortcutInfo.Builder(this, id)
                .setShortLabel(label)
                .setIcon(Icon.createWithResource(this, icon))
                .setIntent(intent)
                .build()
        } else {
            null
        }
    }

    private fun hideBottomNavigationBar(
        mainLayout: ConstraintLayout,
        bottomNav: BottomNavigationView,
    ) {
        mainLayout.setPadding(0, 0, 0, 0)
        bottomNav.visibility = View.GONE
        bottomNavSeparator.visibility = View.GONE
    }

    private fun navigateManuallyToDestination(item: MenuItem) {
        val navHostController = supportFragmentManager.fragments[0].findNavController()
        val navOptions = NavOptions.Builder()
            .setLaunchSingleTop(true)
            .setPopUpTo(item.itemId, true)
            .build()
        navHostController.navigate(item.itemId, null, navOptions)
    }

    private fun showBottomNavigationBar(
        mainLayout: ConstraintLayout,
        bottomNav: BottomNavigationView,
    ) {
        mainLayout.setPadding(0, 0, 0, getActionBarSize())
        bottomNav.visibility = View.VISIBLE
        bottomNavSeparator.visibility = View.VISIBLE
    }

    private fun getActionBarSize(): Int {
        val tv = TypedValue()
        return if (theme.resolveAttribute(android.R.attr.actionBarSize, tv, true)) {
            TypedValue.complexToDimensionPixelSize(tv.data, resources.displayMetrics)
        } else {
            0
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        // This is added to prevent the dynamic font.
        val newOverride = Configuration(newBase?.resources?.configuration)
        newOverride.fontScale = 1.0f
        applyOverrideConfiguration(newOverride)

        super.attachBaseContext(newBase?.let(LocaleManager.Statics::updateResources))
    }

    // Thanks: https://stackoverflow.com/questions/56218309/changing-locale-programmatically-not-working-on-android-6-0-1
    // Set the `baseContext`'s configuration onto the overridden configuration since we want to
    // preserve the `locale` present in `baseContext`.
    override fun applyOverrideConfiguration(overrideConfiguration: Configuration?) {
        try {
            if (overrideConfiguration != null) {
                overrideConfiguration.setTo(baseContext.resources.configuration)
                Timber.tag(TAG).i("Overriding configuration with base configurations.")
            }
        } catch (e: NullPointerException) {
            Timber.tag(TAG).i("Overriding configuration failed. Using the parent override.")
        }
        super.applyOverrideConfiguration(overrideConfiguration)
    }

    private fun handleUnsupportedClient() {
        val unsupportedClientIntent = Intent(this, this::class.java)
        unsupportedClientIntent.putExtra(UNSUPPORTED_APP_KEY, true)
        val options = ActivityOptions.makeCustomAnimation(this, 0, 0)
        finish()
        UnsupportedClientHandler.handleUserOnUpdateAppScreen()
        startActivity(unsupportedClientIntent, options.toBundle())
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        event?.let {
            if (it.action == MotionEvent.ACTION_DOWN) {
                val view = currentFocus
                // Checks if the view on which user tapped is an EditText or not. If the area in which
                // user tapped is an `EditText` then we won't do anything. If the area where user has
                // tapped is not an `EditText` then we hide the soft keyboard.
                if (view is EditText) {
                    val outRect = Rect()
                    view.getGlobalVisibleRect(outRect)
                    if (!outRect.contains(it.rawX.toInt(), it.rawY.toInt())) {
                        view.clearFocus()
                        val imm: InputMethodManager =
                            getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                        imm.hideSoftInputFromWindow(view.getWindowToken(), 0)
                        Timber.tag(TAG).i("User tapped outside edittext, hiding soft keyboard.")
                    }
                }
            }
        }
        return super.dispatchTouchEvent(event)
    }

    override fun onUserDataUpdated() {
        enableBottomNavigation()
    }
}

private const val YAFIKA_MOBILE_PLAY_STORE_LINK =
    "https://play.google.com/store/apps/details?id=com.resoluttech.barcelona.bcn.prod.release"
private const val UNSUPPORTED_APP_KEY = "UNSUPPORTED_APP_KEY"
private const val TAG = "MainActivity"
internal const val SCAN_AND_PAY_ID = "SCAN_AND_PAY_SHORTCUT"
internal const val SEND_MONEY_ID = "SEND_MONEY_SHORTCUT"
internal const val LOAD_WALLET_ID = "LOAD_WALLET_SHORTCUT"
internal const val PAY_BILL_ID = "PAY_BILL_SHORTCUT"
internal const val SCAN_AND_PAY_DEEPLINK = "resoluttechbcn://scan_and_pay"
internal const val SEND_MONEY_DEEPLINK = "resoluttechbcn://money-transfer"
internal const val LOAD_WALLET_DEEPLINK = "resoluttechbcn://load-wallet"
internal const val PAY_BILL_DEEPLINK = "resoluttechbcn://pay_bill"
internal const val TRANSACTION_DEEPLINK = "resoluttechbcn://transactions"
