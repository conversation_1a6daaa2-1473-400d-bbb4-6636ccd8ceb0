package com.resoluttech.barcelona

import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.File
import java.io.FileInputStream
import java.io.FileWriter
import java.io.InputStreamReader
import java.io.LineNumberReader

/**
 This class will be thrown away once we have a concrete way of sharing logs.
 */
object LogReportManager {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val FILE_PATH = "${context.applicationContext.filesDir}/logs.txt"

    fun init() {
        val file = File(FILE_PATH)
        if (!file.exists()) {
            file.createNewFile()
            CoroutineScope(Dispatchers.IO).launch {
                CaptureLogcat().run()
            }
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                withContext(Dispatchers.IO) {
                    ManageLogFile().run()
                }
                withContext(Dispatchers.IO) {
                    CaptureLogcat().run()
                }
            }
        }
    }

    private class CaptureLogcat : Runnable {
        override fun run() {
            // Clear previous logs.
            Runtime.getRuntime().exec("logcat -c")
            try {
                Runtime.getRuntime().exec("logcat -f ${File(FILE_PATH).absolutePath}")
            } catch (e: IllegalStateException) {
                Timber.tag(TAG).e(e, "Unable to capture logs")
            }
        }
    }

    private class ManageLogFile : Runnable {

        private fun manageLogFile() {
            val file = File(FILE_PATH)
            if (getNumberOfLines(file) >= MAX_LINE_ALLOWED_IN_LOG_FILE) {
                val lines = getFileContents(file)
                file.delete()
                file.createNewFile()
                val bufferedWriter = BufferedWriter(FileWriter(file))
                lines.subList(lines.size - 1001, lines.size - 1).forEach(bufferedWriter::write)
                bufferedWriter.close()
            }
        }

        private fun getNumberOfLines(file: File): Int {
            val fileInputStream = FileInputStream(file)
            val bufferedReader = BufferedReader(InputStreamReader(fileInputStream))
            val lineNumberReader = LineNumberReader(bufferedReader)
            val lines = lineNumberReader.lineNumber + 1
            lineNumberReader.close()
            bufferedReader.close()
            fileInputStream.close()
            return lines
        }

        private fun getFileContents(file: File): List<String> {
            val fileInputStream = FileInputStream(file)
            val bufferedReader = BufferedReader(InputStreamReader(fileInputStream))
            val lines = bufferedReader.readLines()
            bufferedReader.close()
            fileInputStream.close()
            return lines
        }

        override fun run() {
            manageLogFile()
        }
    }
}

private const val TAG = "LogReportManager"
private const val MAX_LINE_ALLOWED_IN_LOG_FILE = 5000
