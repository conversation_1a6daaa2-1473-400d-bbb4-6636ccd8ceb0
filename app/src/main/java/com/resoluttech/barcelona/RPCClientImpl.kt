package com.resoluttech.barcelona

import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPCClientImpl
import com.resoluttech.bcn.document.GetDocumentIdRPC
import com.resoluttech.bcn.document.GetDocumentIdRPCClientImpl
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPC
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPCClientImpl
import com.resoluttech.bcn.document.GetUrlRPC
import com.resoluttech.bcn.document.GetUrlRPCClientImpl
import com.resoluttech.bcn.homeScreen.GetAgentListRPC
import com.resoluttech.bcn.homeScreen.GetAgentListRPCClientImpl
import com.resoluttech.bcn.homeScreen.GetHomeDataRPC
import com.resoluttech.bcn.homeScreen.GetHomeDataRPCClientImpl
import com.resoluttech.bcn.homeScreen.GetInAppNotificationsRPC
import com.resoluttech.bcn.homeScreen.GetInAppNotificationsRPCClientImpl
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPCClientImpl
import com.resoluttech.bcn.homeScreen.UpdatePushTokenRPC
import com.resoluttech.bcn.homeScreen.UpdatePushTokenRPCClientImpl
import com.resoluttech.bcn.payments.ConfirmBillPaymentRequestRPC
import com.resoluttech.bcn.payments.ConfirmBillPaymentRequestRPCClientImpl
import com.resoluttech.bcn.payments.CreateBillPaymentRequestRPC
import com.resoluttech.bcn.payments.CreateBillPaymentRequestRPCClientImpl
import com.resoluttech.bcn.payments.GetBillersRPC
import com.resoluttech.bcn.payments.GetBillersRPCClientImpl
import com.resoluttech.bcn.profile.ArchiveBcnUserRPC
import com.resoluttech.bcn.profile.ArchiveBcnUserRPCClientImpl
import com.resoluttech.bcn.profile.ChangeAccountDisplayNameRPC
import com.resoluttech.bcn.profile.ChangeAccountDisplayNameRPCClientImpl
import com.resoluttech.bcn.profile.ChangeAccountStateRPC
import com.resoluttech.bcn.profile.ChangeAccountStateRPCClientImpl
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPC
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPCClientImpl
import com.resoluttech.bcn.profile.ChangeLocaleRPC
import com.resoluttech.bcn.profile.ChangeLocaleRPCClientImpl
import com.resoluttech.bcn.profile.ChangePasswordRPC
import com.resoluttech.bcn.profile.ChangePasswordRPCClientImpl
import com.resoluttech.bcn.profile.ChangeProfileImageRPC
import com.resoluttech.bcn.profile.ChangeProfileImageRPCClientImpl
import com.resoluttech.bcn.profile.CreateNewAccountRPC
import com.resoluttech.bcn.profile.CreateNewAccountRPCClientImpl
import com.resoluttech.bcn.profile.GetAllAccountsRPC
import com.resoluttech.bcn.profile.GetAllAccountsRPCClientImpl
import com.resoluttech.bcn.profile.GetKYCDataRPC
import com.resoluttech.bcn.profile.GetKYCDataRPCClientImpl
import com.resoluttech.bcn.profile.GetListOfStatementsDateRPC
import com.resoluttech.bcn.profile.GetListOfStatementsDateRPCClientImpl
import com.resoluttech.bcn.profile.GetPasswordPolicyRPC
import com.resoluttech.bcn.profile.GetPasswordPolicyRPCClientImpl
import com.resoluttech.bcn.profile.GetStatementUrlRPC
import com.resoluttech.bcn.profile.GetStatementUrlRPCClientImpl
import com.resoluttech.bcn.profile.RequestChangePasswordOTPRPC
import com.resoluttech.bcn.profile.RequestChangePasswordOTPRPCClientImpl
import com.resoluttech.bcn.profile.ResendChangePasswordOTPRPC
import com.resoluttech.bcn.profile.ResendChangePasswordOTPRPCClientImpl
import com.resoluttech.bcn.profile.ResendVerificationEmailRPC
import com.resoluttech.bcn.profile.ResendVerificationEmailRPCClientImpl
import com.resoluttech.bcn.profile.SignOutUserRPC
import com.resoluttech.bcn.profile.SignOutUserRPCClientImpl
import com.resoluttech.bcn.profile.UpdateEmailIdRPC
import com.resoluttech.bcn.profile.UpdateEmailIdRPCClientImpl
import com.resoluttech.bcn.profile.UpdateKYCDataRPC
import com.resoluttech.bcn.profile.UpdateKYCDataRPCClientImpl
import com.resoluttech.bcn.profile.VerifyKYCDataRPCClientImpl
import com.resoluttech.bcn.signUpIn.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmAddTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ConfirmForgotPasswordAnswersRPC
import com.resoluttech.bcn.signUpIn.ConfirmForgotPasswordAnswersRPCClientImpl
import com.resoluttech.bcn.signUpIn.ConfirmResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmResetPasswordByTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.GetForgotPasswordQuestionsRPC
import com.resoluttech.bcn.signUpIn.GetForgotPasswordQuestionsRPCClientImpl
import com.resoluttech.bcn.signUpIn.GetSupportedCountriesRPC
import com.resoluttech.bcn.signUpIn.GetSupportedCountriesRPCClientImpl
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPCClientImpl
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPCClientImpl
import com.resoluttech.bcn.signUpIn.RefreshSLTRPC
import com.resoluttech.bcn.signUpIn.RefreshSLTRPCClientImpl
import com.resoluttech.bcn.signUpIn.RemoveTrustedContactRPC
import com.resoluttech.bcn.signUpIn.RemoveTrustedContactRPCClientImpl
import com.resoluttech.bcn.signUpIn.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.RequestAddTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.RequestResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.RequestResetPasswordByTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPC
import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPCClientImpl
import com.resoluttech.bcn.signUpIn.RequestSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.RequestSignUpOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendAddTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ResendResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendResetPasswordByTrustedContactOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ResendSignInOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignInOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ResendSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignUpOTPRPCClientImpl
import com.resoluttech.bcn.signUpIn.ResetForgottenPasswordRPC
import com.resoluttech.bcn.signUpIn.ResetForgottenPasswordRPCClientImpl
import com.resoluttech.bcn.signUpIn.SignInUserRPC
import com.resoluttech.bcn.signUpIn.SignInUserRPCClientImpl
import com.resoluttech.bcn.signUpIn.SubmitKYCDataRPC
import com.resoluttech.bcn.signUpIn.SubmitKYCDataRPCClientImpl
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPC
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPCClientImpl
import com.resoluttech.bcn.transactions.AddPrivateRemarkRPC
import com.resoluttech.bcn.transactions.AddPrivateRemarkRPCClientImpl
import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.resoluttech.bcn.transactions.GetTransactionsRPCClientImpl
import com.resoluttech.bcn.transfers.ConfirmAccountToAccountRequestRPC
import com.resoluttech.bcn.transfers.ConfirmAccountToAccountRequestRPCClientImpl
import com.resoluttech.bcn.transfers.ConfirmLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.ConfirmLoadMoneyFromMPGSRPCClientImpl
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToBCNUserRPCClientImpl
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToExternalUserRPC
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToExternalUserRPCClientImpl
import com.resoluttech.bcn.transfers.CreateAccountToAccountRequestRPC
import com.resoluttech.bcn.transfers.CreateAccountToAccountRequestRPCClientImpl
import com.resoluttech.bcn.transfers.CreateLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.CreateLoadMoneyFromMPGSRPCClientImpl
import com.resoluttech.bcn.transfers.CreateSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToBCNUserRPCClientImpl
import com.resoluttech.bcn.transfers.CreateSendMoneyToExternalUserRequestRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToExternalUserRequestRPCClientImpl
import com.resoluttech.bcn.transfers.GetBCNRecipientFromAccountIdRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromAccountIdRPCClientImpl
import com.resoluttech.bcn.transfers.GetBCNRecipientFromPhoneNumberRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromPhoneNumberRPCClientImpl
import com.resoluttech.bcn.transfers.GetBCNRecipientFromUserIdRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromUserIdRPCClientImpl
import com.resoluttech.bcn.transfers.GetRecentlyPaidRecipientsRPC
import com.resoluttech.bcn.transfers.GetRecentlyPaidRecipientsRPCClientImpl
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPCClientImpl
import com.resoluttech.bcn.transfers.LoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.LoadMoneyFromMPGSRPCClientImpl
import com.resoluttech.bcn.transfers.LookupRecipientAtCounterpartyRPC
import com.resoluttech.bcn.transfers.LookupRecipientAtCounterpartyRPCClientImpl
import com.suryadigital.leo.kedwig.AsyncAPIClient
import com.suryadigital.leo.rpc.ClientToServerAuthenticationProvider
import kotlinx.serialization.json.Json
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.module.Module
import org.koin.dsl.module
import com.resoluttech.bcn.profile.VerifyKYCDataRPC as ProfileVerifyKYCDataRPC
import com.resoluttech.bcn.signUpIn.VerifyKYCDataRPC as SignUpInVerifyKYCDataRPC
import com.resoluttech.bcn.signUpIn.VerifyKYCDataRPCClientImpl as SignUpInVerifyKYCDataRPCClientImpl

class RPCClientImpl : KoinComponent {

    private val apiClient by inject<AsyncAPIClient>()
    private val authProvider: ClientToServerAuthenticationProvider by inject()
    private val json: Json by inject()

    val rpcClientImplModule: Module = module {

        single<GetHomeDataRPC> {
            GetHomeDataRPCClientImpl(authProvider, apiClient, json)
        }

        single<AddPrivateRemarkRPC> {
            AddPrivateRemarkRPCClientImpl(authProvider, apiClient, json)
        }

        single<ConfirmSendMoneyToBCNUserRPC> {
            ConfirmSendMoneyToBCNUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<CreateSendMoneyToExternalUserRequestRPC> {
            CreateSendMoneyToExternalUserRequestRPCClientImpl(authProvider, apiClient, json)
        }

        single<ConfirmSendMoneyToExternalUserRPC> {
            ConfirmSendMoneyToExternalUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetRecentlyPaidRecipientsRPC> {
            GetRecentlyPaidRecipientsRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetSendMoneyToExternalUserCounterpartiesRPC> {
            GetSendMoneyToExternalUserCounterpartiesRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetTransactionsRPC> {
            GetTransactionsRPCClientImpl(authProvider, apiClient, json)
        }

        single<RefreshSLTRPC> {
            RefreshSLTRPCClientImpl(apiClient, json)
        }

        single<GetBCNRecipientFromPhoneNumberRPC> {
            GetBCNRecipientFromPhoneNumberRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetBCNRecipientFromUserIdRPC> {
            GetBCNRecipientFromUserIdRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetBCNRecipientFromAccountIdRPC> {
            GetBCNRecipientFromAccountIdRPCClientImpl(authProvider, apiClient, json)
        }

        single<LookupRecipientAtCounterpartyRPC> {
            LookupRecipientAtCounterpartyRPCClientImpl(authProvider, apiClient, json)
        }

        single<CreateSendMoneyToBCNUserRPC> {
            CreateSendMoneyToBCNUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<RequestSignInOtpRPC> {
            RequestSignInOtpRPCClientImpl(apiClient, json)
        }

        single<RequestSignUpOTPRPC> {
            RequestSignUpOTPRPCClientImpl(apiClient, json)
        }

        single<ConfirmSignInOTPRPC> {
            ConfirmSignInOTPRPCClientImpl(apiClient, json)
        }

        single<ConfirmSignUpOTPRPC> {
            ConfirmSignUpOTPRPCClientImpl(apiClient, json)
        }

        single<SignInUserRPC> {
            SignInUserRPCClientImpl(apiClient, json)
        }

        single<ResendSignInOTPRPC> {
            ResendSignInOTPRPCClientImpl(apiClient, json)
        }

        single<ResendSignUpOTPRPC> {
            ResendSignUpOTPRPCClientImpl(apiClient, json)
        }

        single<CreateAccountToAccountRequestRPC> {
            CreateAccountToAccountRequestRPCClientImpl(authProvider, apiClient, json)
        }

        single<ConfirmAccountToAccountRequestRPC> {
            ConfirmAccountToAccountRequestRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetMoneyScreenDataRPC> {
            GetMoneyScreenDataRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetUrlRPC> {
            GetUrlRPCClientImpl(apiClient, json)
        }

        single<GetUrlForSignedInUserRPC> {
            GetUrlForSignedInUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetDocumentIdRPC> {
            GetDocumentIdRPCClientImpl(apiClient, json)
        }

        single<GetDocumentIdForSignedInUserRPC> {
            GetDocumentIdForSignedInUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<SubmitKYCDataRPC> {
            SubmitKYCDataRPCClientImpl(apiClient, json)
        }

        single<CreateLoadMoneyFromMPGSRPC> {
            CreateLoadMoneyFromMPGSRPCClientImpl(authProvider, apiClient, json)
        }

        single<LoadMoneyFromMPGSRPC> {
            LoadMoneyFromMPGSRPCClientImpl(authProvider, apiClient, json)
        }

        single<ConfirmLoadMoneyFromMPGSRPC> {
            ConfirmLoadMoneyFromMPGSRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetForgotPasswordQuestionsRPC> {
            GetForgotPasswordQuestionsRPCClientImpl(apiClient, json)
        }

        single<SubmitSecurityQuestionAnswersAndPasswordRPC> {
            SubmitSecurityQuestionAnswersAndPasswordRPCClientImpl(apiClient, json)
        }

        single<ResetForgottenPasswordRPC> {
            ResetForgottenPasswordRPCClientImpl(apiClient, json)
        }

        single<ConfirmForgotPasswordAnswersRPC> {
            ConfirmForgotPasswordAnswersRPCClientImpl(apiClient, json)
        }

        single<SignOutUserRPC> {
            SignOutUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<UpdatePushTokenRPC> {
            UpdatePushTokenRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetInAppNotificationsRPC> {
            GetInAppNotificationsRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetPasswordPolicyRPC> {
            GetPasswordPolicyRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetAgentListRPC> {
            GetAgentListRPCClientImpl(authProvider, apiClient, json)
        }

        single<ChangeProfileImageRPC> {
            ChangeProfileImageRPCClientImpl(authProvider, apiClient, json)
        }

        single<ArchiveBcnUserRPC> {
            ArchiveBcnUserRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetBillersRPC> {
            GetBillersRPCClientImpl(authProvider, apiClient, json)
        }

        single<CreateBillPaymentRequestRPC> {
            CreateBillPaymentRequestRPCClientImpl(authProvider, apiClient, json)
        }

        single<ConfirmBillPaymentRequestRPC> {
            ConfirmBillPaymentRequestRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetAllAccountsRPC> {
            GetAllAccountsRPCClientImpl(authProvider, apiClient, json)
        }

        single<ChangeDefaultAccountRPC> {
            ChangeDefaultAccountRPCClientImpl(authProvider, apiClient, json)
        }

        single<ChangeAccountDisplayNameRPC> {
            ChangeAccountDisplayNameRPCClientImpl(authProvider, apiClient, json)
        }

        single<CreateNewAccountRPC> {
            CreateNewAccountRPCClientImpl(authProvider, apiClient, json)
        }

        single<RequestChangePasswordOTPRPC> {
            RequestChangePasswordOTPRPCClientImpl(authProvider, apiClient, json)
        }

        single<ResendChangePasswordOTPRPC> {
            ResendChangePasswordOTPRPCClientImpl(authProvider, apiClient, json)
        }
        single<ChangePasswordRPC> {
            ChangePasswordRPCClientImpl(authProvider, apiClient, json)
        }

        single<ChangeLocaleRPC> {
            ChangeLocaleRPCClientImpl(authProvider, apiClient, json)
        }

        single<ChangeAccountStateRPC> {
            ChangeAccountStateRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetSupportedCountriesRPC> {
            GetSupportedCountriesRPCClientImpl(apiClient, json)
        }

        single<GetListOfStatementsDateRPC> {
            GetListOfStatementsDateRPCClientImpl(authProvider, apiClient, json)
        }

        single<GetStatementUrlRPC> {
            GetStatementUrlRPCClientImpl(authProvider, apiClient, json)
        }

        single<SignUpInVerifyKYCDataRPC> {
            SignUpInVerifyKYCDataRPCClientImpl(apiClient, json)
        }

        single<RequestAddTrustedContactOTPRPC> {
            RequestAddTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<ResendAddTrustedContactOTPRPC> {
            ResendAddTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<ConfirmAddTrustedContactOTPRPC> {
            ConfirmAddTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<RemoveTrustedContactRPC> {
            RemoveTrustedContactRPCClientImpl(apiClient, json)
        }

        single<GetTrustedContactsRPC> {
            GetTrustedContactsRPCClientImpl(apiClient, json)
        }

        single<com.resoluttech.bcn.profile.RequestAddTrustedContactOTPRPC> {
            com.resoluttech.bcn.profile.RequestAddTrustedContactOTPRPCClientImpl(
                authProvider,
                apiClient,
                json,
            )
        }

        single<com.resoluttech.bcn.profile.ResendAddTrustedContactOTPRPC> {
            com.resoluttech.bcn.profile.ResendAddTrustedContactOTPRPCClientImpl(
                authProvider,
                apiClient,
                json,
            )
        }

        single<com.resoluttech.bcn.profile.ConfirmAddTrustedContactOTPRPC> {
            com.resoluttech.bcn.profile.ConfirmAddTrustedContactOTPRPCClientImpl(
                authProvider,
                apiClient,
                json,
            )
        }

        single<com.resoluttech.bcn.profile.RemoveTrustedContactRPC> {
            com.resoluttech.bcn.profile.RemoveTrustedContactRPCClientImpl(
                authProvider,
                apiClient,
                json,
            )
        }

        single<com.resoluttech.bcn.profile.GetTrustedContactsRPC> {
            com.resoluttech.bcn.profile.GetTrustedContactsRPCClientImpl(
                authProvider,
                apiClient,
                json,
            )
        }

        single<RequestResetPasswordByTrustedContactOTPRPC> {
            RequestResetPasswordByTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<ResendResetPasswordByTrustedContactOTPRPC> {
            ResendResetPasswordByTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<ConfirmResetPasswordByTrustedContactOTPRPC> {
            ConfirmResetPasswordByTrustedContactOTPRPCClientImpl(apiClient, json)
        }

        single<GetTrustedContactValidatedTokenRPC> {
            GetTrustedContactValidatedTokenRPCClientImpl(apiClient, json)
        }

        single<GetKYCDataRPC> {
            GetKYCDataRPCClientImpl(authProvider, apiClient, json)
        }

        single<ProfileVerifyKYCDataRPC> {
            VerifyKYCDataRPCClientImpl(authProvider, apiClient, json)
        }

        single<UpdateKYCDataRPC> {
            UpdateKYCDataRPCClientImpl(authProvider, apiClient, json)
        }

        single<UpdateEmailIdRPC> {
            UpdateEmailIdRPCClientImpl(authProvider, apiClient, json)
        }

        single<ResendVerificationEmailRPC> {
            ResendVerificationEmailRPCClientImpl(authProvider, apiClient, json)
        }
    }
}
