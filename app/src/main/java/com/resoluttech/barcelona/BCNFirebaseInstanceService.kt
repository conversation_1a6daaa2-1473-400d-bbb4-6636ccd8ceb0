package com.resoluttech.barcelona

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.resoluttech.core.home.MoneyScreenFragment.Companion.PUSH_NOTIFICATION_DEEPLINK_KEY
import timber.log.Timber
import com.resoluttech.bcncore.R as BCNCoreResource

class BCNFirebaseInstanceService : FirebaseMessagingService(), LifecycleEventObserver {

    private var isAppInBackground = false

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        // We cannot send the token to server here because our RPC is authenticated
        // and this service runs on app startup which leads to 403 error.
        // Our [UpdatePushToken] RPC which is called on home screen overcomes this challenge and
        // update our server with latest token always.
        Timber.d("New FCM token generated")
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        // If app is in background but not closed in that case user will get the push notification.
        if (!isAppInBackground) return

        val message: String
        val title: String
        val deeplink: String

        if (remoteMessage.data.isNotEmpty()) {
            val data = remoteMessage.data
            deeplink = data[PUSH_NOTIFICATION_DEEPLINK_KEY]!!
        } else {
            throw IllegalStateException("Message doesn't contain data payload")
        }

        if (remoteMessage.notification != null) {
            val notification = remoteMessage.notification!!
            message = notification.body!!
            title = notification.title!!
        } else {
            throw IllegalStateException("Message doesn't contain notification payload")
        }

        createNotificationChannel()
        sendNotification(deeplink, message, title, remoteMessage.priority)
    }

    private fun sendNotification(
        deeplink: String,
        message: String,
        title: String,
        priority: Int,
    ) {
        // Create an explicit intent for an Activity in your app
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        intent.putExtra(PUSH_NOTIFICATION_DEEPLINK_KEY, deeplink)
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT,
        )

        val defaultSoundUri: Uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notification = NotificationCompat.Builder(this, PUSH_NOTIFICATION_CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(BCNCoreResource.drawable.ic_notification_logo)
            .setPriority(priority)
            .setContentIntent(pendingIntent)
            .setSound(defaultSoundUri)
            .setAutoCancel(true)
            .build()

        with(NotificationManagerCompat.from(this)) {
            notify(0, notification)
        }
    }

    private fun createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = getString(R.string.channel_name)
            val descriptionText = getString(R.string.channel_description)
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(
                PUSH_NOTIFICATION_CHANNEL_ID,
                name,
                importance,
            ).apply {
                description = descriptionText
            }
            val defaultSoundUri: Uri =
                RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()
            channel.setSound(defaultSoundUri, audioAttributes)
            // Register the channel with the system
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    override fun onDeletedMessages() {
        super.onDeletedMessages()
        Timber.d("Message Deleted")
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        isAppInBackground = event == Lifecycle.Event.ON_DESTROY || event == Lifecycle.Event.ON_STOP
    }

    companion object {
        const val PUSH_NOTIFICATION_CHANNEL_ID: String = "push_notification_channel"
    }
}
