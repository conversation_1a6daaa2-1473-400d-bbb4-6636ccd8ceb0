package com.resoluttech.barcelona

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber
import kotlin.math.min

class ReleaseTree : Timber.Tree() {

    private val crashlytics by lazy(FirebaseCrashlytics::getInstance)

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (priority < Log.INFO) return
        if (message.length < MAX_LOG_LENGTH) {
            Log.println(priority, tag, message)
            crashlytics.log("Priority: ${getLogLevel(priority)}; TAG: $tag; Log: $message")
        } else {
            // This is required because logcat is not able to print more than 4000 character at a time.
            // check https://github.com/Resolut-Tech/Android/pull/269#discussion_r539033299
            splitAndLog(message, tag, priority)
        }
    }

    // Thanks : https://github.com/JakeWharton/timber/blob/master/timber/src/main/java/timber/log/Timber.kt#L247
    private fun splitAndLog(
        message: String,
        tag: String?,
        priority: Int,
    ) {
        var i = 0
        val length = message.length
        while (i < length) {
            var newline = message.indexOf('\n', i)
            newline = if (newline != -1) newline else length
            do {
                val end = min(newline, i + MAX_LOG_LENGTH)
                val part = message.substring(i, end)
                Log.println(priority, tag, part)
                crashlytics.log("Priority: ${getLogLevel(priority)}; TAG: $tag; Log: $part")
                i = end
            } while (i < newline)
            i++
        }
    }

    private fun getLogLevel(priority: Int): String {
        return when (priority) {
            Log.VERBOSE -> "VERBOSE"
            Log.DEBUG -> "DEBUG"
            Log.INFO -> "INFO"
            Log.WARN -> "WARN"
            Log.ERROR -> "ERROR"
            Log.ASSERT -> "ASSERT"
            else -> "UNKNOWN"
        }
    }
}

private const val MAX_LOG_LENGTH = 4000
