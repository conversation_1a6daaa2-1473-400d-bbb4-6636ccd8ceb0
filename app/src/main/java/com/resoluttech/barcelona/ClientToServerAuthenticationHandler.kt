package com.resoluttech.barcelona

import com.resoluttech.bcn.signUpIn.RefreshSLTRPC
import com.resoluttech.core.auth.KeyStoreHelper
import com.suryadigital.leo.rpc.ClientToServerAuthenticationProvider
import com.suryadigital.leo.rpc.LeoInvalidLLTException
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.rpc.LeoUserDisabledException
import org.koin.java.KoinJavaComponent

class ClientToServerAuthenticationHandler : ClientToServerAuthenticationProvider.SLT() {

    override suspend fun getSLT(): String {
        val slt = KeyStoreHelper.getSLT()
        if (slt.isNullOrEmpty()) {
            throw IllegalStateException("Token is null or empty")
        }
        return slt
    }

    override suspend fun refreshSLT() {
        val refreshSLTRPC: RefreshSLTRPC by KoinJavaComponent.inject(RefreshSLTRPC::class.java)
        val llt = KeyStoreHelper.getLLT()
        if (!llt.isNullOrEmpty()) {
            when (val res = refreshSLTRPC.execute(RefreshSLTRPC.Request(llt))) {
                is LeoRPCResult.LeoResponse -> {
                    KeyStoreHelper.storeSLT(res.response.slt)
                }
                is LeoRPCResult.LeoError -> {
                    handleErrorState(res.error)
                }
            }
        } else {
            throw IllegalStateException("Trying to read null LLT value")
        }
    }

    private fun handleErrorState(error: RefreshSLTRPC.Error) {
        when (error) {
            is RefreshSLTRPC.Error.InvalidLlt -> {
                throw LeoInvalidLLTException()
            }
            is RefreshSLTRPC.Error.UserDisabled -> {
                throw LeoUserDisabledException()
            }
        }
    }

    override suspend fun setSLT(value: String) {
        KeyStoreHelper.storeSLT(value)
    }
}
