package com.resoluttech.barcelona

import android.app.Activity
import android.app.Application
import android.content.Context
import android.graphics.Point
import android.os.Build
import android.os.Bundle
import android.os.StrictMode
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.resoluttech.barcelona.logger.TimberLogger
import com.resoluttech.bcn.types.Gender
import com.resoluttech.core.config.AmountLimitConfig
import com.resoluttech.core.config.AppBuildConfig
import com.resoluttech.core.config.GenderConfig
import com.resoluttech.core.config.LanguageConfig
import com.resoluttech.core.config.TransactionsLimitElaboratedURL
import com.resoluttech.core.home.HomeDataPersistor
import com.resoluttech.core.home.MoneyScreenDataPersistor
import com.resoluttech.core.payments.PaymentDataPersistor
import com.resoluttech.core.profile.ApplicationBuildInfo
import com.resoluttech.core.profile.PackageInfo
import com.resoluttech.core.sendmoney.Counterparty
import com.resoluttech.core.sendmoney.CounterpartyViewType
import com.resoluttech.core.sendmoney.config.SendMoneyCounterpartyConfig
import com.resoluttech.core.sendmoney.defaultcounterparties.DefaultSendMoneyProviderView
import com.resoluttech.core.transfers.config.QRCodeConfig
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.documentupload.NetworkClientConfig
import com.suryadigital.leo.kedwig.APIClientConfiguration
import com.suryadigital.leo.kedwig.AsyncAPIClient
import com.suryadigital.leo.kedwig.LogConfiguration
import com.suryadigital.leo.kedwig.LogLevel
import com.suryadigital.leo.kedwig.OkHttpAPIClient
import com.suryadigital.leo.kedwig.headers
import com.suryadigital.leo.rpc.ClientToServerAuthenticationProvider
import kotlinx.serialization.json.Json
import org.koin.android.ext.koin.androidContext
import org.koin.core.annotation.KoinInternalApi
import org.koin.core.context.startKoin
import org.koin.dsl.module
import timber.log.Timber

class BarcelonaApplication : Application() {

    private lateinit var qrCodeConfig: Pair<String, String>
    private lateinit var packageInfo: PackageInfo
    private var activity: AppCompatActivity? = null

    private val koinModule by lazy {
        module {
            single {
                ImageRequest
                    .Builder(this@BarcelonaApplication)
                    .addHeader("user-agent", userAgent)
                    .allowHardware(false)
                    .memoryCachePolicy(
                        CachePolicy.ENABLED,
                    )
            }
            single {
                QRCodeConfig(qrCodeConfig.first, qrCodeConfig.second)
            }
            single {
                ApplicationBuildInfo(packageInfo)
            }
        }
    }

    /*
        Starting of Koin is moved to `onCreate` from `init` block as user-agent requires screen resolution
        and to fetch that, it needs application context which is not available in `init` block.
     */
    @OptIn(KoinInternalApi::class)
    override fun onCreate() {
        if (BuildConfig.DEBUG) {
            StrictMode.setThreadPolicy(
                StrictMode.ThreadPolicy.Builder()
                    .detectDiskReads()
                    .detectDiskWrites()
                    .detectNetwork() // or .detectAll() for all detectable problems
                    .penaltyLog()
                    .build(),
            )
            StrictMode.setVmPolicy(
                StrictMode.VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    .penaltyLog()
                    .build(),
            )
        }
        super.onCreate()
        userAgent = getUserAgent()
        qrCodeConfig = getQRCodeConfig()
        startKoin {
            androidContext(this@BarcelonaApplication)
            modules(appModule)
            modules(RPCClientImpl().rpcClientImplModule)
            modules(koinModule)
        }
        registerActivityLifecycleCallbacks(object : ActivityCallbacks() {
            override fun onActivityCreated(activity: Activity, p1: Bundle?) {
                <EMAIL> = activity as AppCompatActivity
            }
        })
        setupTimber()
    }

    private fun setupTimber() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            Timber.plant(ReleaseTree())
        }
    }

    @Suppress("DEPRECATION")
    private fun getDeviceScreenResolution(): String {
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            val display = windowManager.defaultDisplay
            val point = Point()
            display.getSize(point)
            "${point.x},${point.y}"
        } else {
            "${windowManager.currentWindowMetrics.bounds.width()},${windowManager.currentWindowMetrics.bounds.height()}"
        }
    }

    private fun getUserAgent(): String {
        val platform = "ANDROID"
        val osVersion = Build.VERSION.RELEASE

        val manufacturer = Build.MANUFACTURER
        val model = Build.MODEL
        val screenResolution = getDeviceScreenResolution()

        val applicationId = BuildConfig.APPLICATION_ID.split(".")

        val appName = "YAFIKA_MOBILE"
        val appVersion = BuildConfig.VERSION_NAME.split("-")[0]
        val versionCode = BuildConfig.VERSION_CODE
        val environment = applicationId[4]
        val buildType = applicationId[5]

        packageInfo = PackageInfo(
            appVersion,
            "$versionCode",
            environment,
            buildType,
        )

        return "$platform/$osVersion $manufacturer/$model/$screenResolution $appName/$appVersion($versionCode)/$environment/$buildType"
    }

    private fun getQRCodeConfig(): Pair<String, String> {
        val installationId = getString(R.string.installationId)
        return "app-redirect.resoluttech.link" to installationId
    }

    private val appModule = module {

        single<AsyncAPIClient> {
            OkHttpAPIClient(configuration = apiClientConfiguration)
        }

        single {
            NetworkClientConfig(headers, logConfiguration)
        }

        single {
            SendMoneyCounterpartyConfig(getCurrentInstallationProviderView())
        }

        single {
            Json {
                prettyPrint = true
            }
        }

        single {
            TransactionsLimitElaboratedURL("https://www.surya-soft.com/")
        }

        single<ClientToServerAuthenticationProvider> {
            ClientToServerAuthenticationHandler()
        }

        single {
            AmountLimitConfig(12, 2)
        }

        single {
            LanguageConfig(SupportedLocale.entries.toTypedArray())
        }

        single {
            GenderConfig(Gender.entries.toTypedArray())
        }

        single {
            AppBuildConfig(BuildConfig.VERSION_NAME)
        }

        single {
            HomeDataPersistor()
        }
        single {
            MoneyScreenDataPersistor()
        }
        single {
            PaymentDataPersistor()
        }
    }

    private val logConfiguration by lazy {
        LogConfiguration(
            logger = TimberLogger(),
            requestMetadata = LogLevel.INFO,
            responseMetadata = LogLevel.INFO,
            requestBody = LogLevel.INFO,
            responseBody = LogLevel.INFO,
        )
    }

    private lateinit var userAgent: String

    private val headers by lazy {
        headers {
            header("user-agent", userAgent)
            header("Content-Type", "application/json; charset=utf-8")
        }
    }

    private val apiClientConfiguration by lazy {
        APIClientConfiguration(
            baseURL = BuildConfig.BASE_URL,
            logConfiguration = logConfiguration,
            defaultHeaders = headers,
            connectionTimeoutMS = TIMEOUT_SECONDS,
            socketTimeoutMS = TIMEOUT_SECONDS,
        )
    }
}

private fun getCurrentInstallationProviderView(): CounterpartyViewType =
    CounterpartyViewType(
        DefaultSendMoneyProviderView("Yafika Mobile"),
        Counterparty("Yafika Mobile"),
    )

private const val TIMEOUT_SECONDS: Long = 60_000
