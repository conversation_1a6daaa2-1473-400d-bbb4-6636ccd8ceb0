package com.resoluttech.barcelona.mockrpcs.home

import com.resoluttech.bcn.assets.BitmapImageType
import com.resoluttech.bcn.assets.LocalizedText
import com.resoluttech.bcn.assets.MultiResolutionBitmapImage
import com.resoluttech.bcn.assets.RemoteBitmapImage
import com.resoluttech.bcn.assets.ThemedImage
import com.resoluttech.bcn.homeScreen.DefaultAccount
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.bcn.homeScreen.User
import com.resoluttech.bcn.types.Account
import com.resoluttech.bcn.types.AccountDisplayName
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcn.types.CountryCode
import com.resoluttech.bcn.types.CountryCurrency
import com.resoluttech.bcn.types.Currency
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import com.suryadigital.leo.types.LeoPhoneNumber
import java.net.URL
import java.time.LocalDate
import java.util.UUID

internal class GetMoneyScreenDataRPCClientImpl : GetMoneyScreenDataRPC {
    override suspend fun execute(request: GetMoneyScreenDataRPC.Request): LeoRPCResult<GetMoneyScreenDataRPC.Response, GetMoneyScreenDataRPC.Error> {
        return LeoRPCResult.response(
            GetMoneyScreenDataRPC.Response(
                User(
                    "Test",
                    "User",
                    null,
                    LeoPhoneNumber("+************"),
                    UUID.randomUUID(),
                    LeoEmailId("<EMAIL>"),
                    true,
                    LocalDate.of(1997, 10, 11),
                    null,
                    Currency("MWK"),
                    "Bengaluru",
                    Country(
                        LocalizedText("India", "India"),
                        CountryCode("IN"),
                        "+91",
                    ),
                    listOf(
                        CountryCurrency(
                            Currency("MWK"),
                            ThemedImage(
                                MultiResolutionBitmapImage(
                                    RemoteBitmapImage(
                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                        BitmapImageType.PNG,
                                        24,
                                        24,
                                    ),
                                    RemoteBitmapImage(
                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                        BitmapImageType.PNG,
                                        24,
                                        24,
                                    ),
                                    RemoteBitmapImage(
                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                        BitmapImageType.PNG,
                                        24,
                                        24,
                                    ),
                                    RemoteBitmapImage(
                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                        BitmapImageType.PNG,
                                        24,
                                        24,
                                    ),
                                ),
                            ),
                            LocalizedText("MWK", "MWK"),
                        ),
                    ),
                    listOf(
                        Country(
                            LocalizedText("India", "India"),
                            CountryCode("IN"),
                            "+91",
                        ),
                        Country(
                            LocalizedText("Malawi", "Malawi"),
                            CountryCode("MW"),
                            "+265",
                        ),
                    ),
                    listOf(DefaultAccount(Currency("MWK"), UUID.randomUUID())),
                    canCreateAccount = true,
                    isAgent = false,
                    isNationalIdExpired = false,
                ),
                listOf(
                    Account(
                        UUID.randomUUID(),
                        AccountDisplayName("Default"),
                        Amount(100000, Currency("MWK")),
                        true,
                        AccountState.ACTIVE,
                    ),
                    Account(
                        UUID.randomUUID(),
                        AccountDisplayName("Malawian Active"),
                        Amount(10000, Currency("MWK")),
                        false,
                        AccountState.ACTIVE,
                    ),
                    Account(
                        UUID.randomUUID(),
                        AccountDisplayName("Malawian Inactive"),
                        Amount(100400, Currency("MWK")),
                        false,
                        AccountState.INACTIVE,
                    ),
                ),
            ),
        )
    }
}
