package com.resoluttech.barcelona.mockrpcs.transaction

import com.resoluttech.bcn.assets.BitmapImageType
import com.resoluttech.bcn.assets.LocalizedImage
import com.resoluttech.bcn.assets.LocalizedText
import com.resoluttech.bcn.assets.MultiResolutionBitmapImage
import com.resoluttech.bcn.assets.RemoteBitmapImage
import com.resoluttech.bcn.assets.ThemedImage
import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.resoluttech.bcn.transactions.Transaction
import com.resoluttech.bcn.transactions.TransactionStatus
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcn.types.TransactionStatusDetail
import com.resoluttech.bcn.types.TransactionStatusItemDetail
import com.suryadigital.leo.rpc.LeoRPCResult
import java.net.URL
import java.time.Instant
import java.util.UUID

internal class GetTransactionsRPCClientImpl : GetTransactionsRPC {
    override suspend fun execute(request: GetTransactionsRPC.Request): LeoRPCResult<GetTransactionsRPC.Response, GetTransactionsRPC.Error> {
        return LeoRPCResult.response(
            GetTransactionsRPC.Response(
                listOf(
                    Transaction(
                        UUID.randomUUID(),
                        Amount(1000000, Currency("MWK")),
                        Transaction.TransactionType.TRANSACTION,
                        LocalizedText("Testing", "Testing"),
                        LocalizedText("Mocked Data", "Mocked Data"),
                        null,
                        true,
                        TransactionStatus.Success(
                            succeededAt = Instant.now(),
                        ),
                        true,
                        TransactionStatusDetail(
                            listOf(
                                TransactionStatusItemDetail(
                                    LocalizedText(
                                        "Narration to self",
                                        "Narration to self",
                                    ),
                                    TransactionStatusItemDetail.ValueType.Text(text = "Mocked Transaction"),
                                ),
                            ),
                        ),
                    ),
                    Transaction(
                        UUID.randomUUID(),
                        Amount(100000000, Currency("MWK")),
                        Transaction.TransactionType.TRANSACTION,
                        LocalizedText("Pramod", "Pramod"),
                        LocalizedText("Test Device Repair", "Test Device Repair"),
                        null,
                        false,
                        TransactionStatus.Pending(
                            createdAt = Instant.now(),
                        ),
                        true,
                        TransactionStatusDetail(
                            listOf(
                                TransactionStatusItemDetail(
                                    LocalizedText(
                                        "Payment to",
                                        "Payment to",
                                    ),
                                    TransactionStatusItemDetail.ValueType.SmallIconWithText(
                                        title = "Pramod",
                                        description = "+919999999999",
                                        image = LocalizedImage(
                                            ThemedImage(
                                                MultiResolutionBitmapImage(
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                ),
                                            ),
                                        ),
                                    ),
                                ),
                                TransactionStatusItemDetail(
                                    LocalizedText(
                                        "Debited from",
                                        "Debited from",
                                    ),
                                    TransactionStatusItemDetail.ValueType.SmallIconWithText(
                                        title = "Default",
                                        description = null,
                                        image = LocalizedImage(
                                            ThemedImage(
                                                MultiResolutionBitmapImage(
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                    RemoteBitmapImage(
                                                        URL("https://github.com/ManeShlok/Dummy-Images-/blob/main/Ellipse%2022.png"),
                                                        BitmapImageType.PNG,
                                                        24,
                                                        24,
                                                    ),
                                                ),
                                            ),
                                        ),
                                    ),
                                ),
                                TransactionStatusItemDetail(
                                    LocalizedText(
                                        "Narration to self",
                                        "Narration to self",
                                    ),
                                    TransactionStatusItemDetail.ValueType.Text(
                                        text = "Testing device repair charge",
                                    ),
                                ),

                            ),
                        ),
                    ),
                ),
            ),
        )
    }
}
