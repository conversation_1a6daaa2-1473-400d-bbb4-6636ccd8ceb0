package com.resoluttech.barcelona.mockrpcs.auth

import com.resoluttech.bcn.assets.LocalizedText
import com.resoluttech.bcn.signUpIn.GetSupportedCountriesRPC
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcn.types.CountryCode
import com.suryadigital.leo.rpc.LeoRPCResult

internal class GetSupportedCountriesRPCClientImpl : GetSupportedCountriesRPC {
    override suspend fun execute(request: GetSupportedCountriesRPC.Request): LeoRPCResult<GetSupportedCountriesRPC.Response, GetSupportedCountriesRPC.Error> {
        return LeoRPCResult.response(
            GetSupportedCountriesRPC.Response(
                listOf(
                    Country(
                        LocalizedText("India", "India"),
                        CountryCode("IN"),
                        "+91",
                    ),
                    Country(
                        LocalizedText("Malawi", "Malawi"),
                        CountryCode("MW"),
                        "+265",
                    ),
                ),
            ),
        )
    }
}
