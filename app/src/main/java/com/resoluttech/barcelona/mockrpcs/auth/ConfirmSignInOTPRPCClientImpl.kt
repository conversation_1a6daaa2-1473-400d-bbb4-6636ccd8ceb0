package com.resoluttech.barcelona.mockrpcs.auth

import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import java.util.UUID

internal class ConfirmSignInOTPRPCClientImpl : ConfirmSignInOTPRPC {
    override suspend fun execute(request: ConfirmSignInOTPRPC.Request): LeoRPCResult<ConfirmSignInOTPRPC.Response, ConfirmSignInOTPRPC.Error> {
        return LeoRPCResult.response(
            ConfirmSignInOTPRPC.Response(
                UUID.randomUUID(),
            ),
        )
    }
}
