package com.resoluttech.barcelona.mockrpcs.profile

import com.resoluttech.bcn.profile.SignOutUserRPC
import com.suryadigital.leo.rpc.LeoRPCResult

internal class SignOutUserRPCClientImpl : SignOutUserRPC {
    override suspend fun execute(request: SignOutUserRPC.Request): LeoRPCResult<SignOutUserRPC.Response, SignOutUserRPC.Error> {
        return LeoRPCResult.response(
            SignOutUserRPC.Response,
        )
    }
}
