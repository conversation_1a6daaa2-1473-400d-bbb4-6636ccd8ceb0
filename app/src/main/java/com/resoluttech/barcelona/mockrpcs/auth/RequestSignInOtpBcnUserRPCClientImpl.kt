package com.resoluttech.barcelona.mockrpcs.auth

import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPC
import com.resoluttech.bcn.types.OTPDetails
import com.resoluttech.bcn.types.OTPValidityDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import java.time.Instant
import java.util.UUID

internal class RequestSignInOtpBcnUserRPCClientImpl : RequestSignInOtpRPC {
    override suspend fun execute(request: RequestSignInOtpRPC.Request): LeoRPCResult<RequestSignInOtpRPC.Response, RequestSignInOtpRPC.Error> {
        return LeoRPCResult.response(
            RequestSignInOtpRPC.Response(
                OTPDetails(
                    UUID.randomUUID(),
                    OTPValidityDetails(
                        Instant.now().plusSeconds(50),
                        Instant.now().plusSeconds(100),
                    ),
                ),

            ),
        )
    }
}
