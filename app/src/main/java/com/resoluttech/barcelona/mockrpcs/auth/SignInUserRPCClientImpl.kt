package com.resoluttech.barcelona.mockrpcs.auth

import com.resoluttech.bcn.signUpIn.SignInUserRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import java.util.UUID

internal class SignInUserRPCClientImpl : SignInUserRPC {
    override suspend fun execute(request: SignInUserRPC.Request): LeoRPCResult<SignInUserRPC.Response, SignInUserRPC.Error> {
        return LeoRPCResult.response(
            SignInUserRPC.Response(
                SignInUserRPC.Response.Result.SignedIn(
                    slt = "${UUID.randomUUID()}",
                    llt = "${UUID.randomUUID()}",
                ),
            ),
        )
    }
}
