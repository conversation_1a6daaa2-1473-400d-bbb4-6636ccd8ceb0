package com.resoluttech.barcelona.logger

import com.suryadigital.leo.kedwig.Logger
import timber.log.Timber

internal class TimberLogger : Logger {
    override fun debug(throwable: Throwable) {
        log { Timber.d(throwable) }
    }

    override fun debug(throwable: Throwable?, message: () -> String) {
        log { Timber.d(throwable, message()) }
    }

    override fun error(throwable: Throwable) {
        log { Timber.e(throwable) }
    }

    override fun error(throwable: Throwable?, message: () -> String) {
        log { Timber.e(throwable, message()) }
    }

    override fun info(throwable: Throwable) {
        log { Timber.i(throwable) }
    }

    override fun info(throwable: Throwable?, message: () -> String) {
        log { Timber.i(throwable, message()) }
    }

    override fun warn(throwable: Throwable) {
        log { Timber.w(throwable) }
    }

    override fun warn(throwable: Throwable?, message: () -> String) {
        log { Timber.w(throwable, message()) }
    }

    private inline fun log(block: () -> Unit) {
        if (Timber.treeCount > 0) block()
    }
}
