package com.resoluttech.barcelona.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.resoluttech.barcelona.R
import com.resoluttech.core.auth.KeyStoreHelper
import kotlinx.coroutines.launch

class StartFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                if (KeyStoreHelper.isUserLoggedIn()) {
                    findNavController().navigate(R.id.action_startFragment_to_home_nav)
                } else {
                    findNavController().navigate(R.id.action_startFragment_to_landingScreen)
                }
            }
        }
        return inflater.inflate(R.layout.fragment_start, container, false)
    }
}
