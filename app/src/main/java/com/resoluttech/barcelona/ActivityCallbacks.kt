package com.resoluttech.barcelona

import android.app.Activity
import android.app.Application
import android.os.Bundle

/**
 This is created just to keep application file clean. It's empty adapter interface to avoid
 non-required callback implementation in application class.
*/
abstract class ActivityCallbacks : Application.ActivityLifecycleCallbacks {
    override fun onActivityStarted(p0: Activity) {}
    override fun onActivityResumed(p0: Activity) {}
    override fun onActivityPaused(p0: Activity) {}
    override fun onActivityStopped(p0: Activity) {}
    override fun onActivitySaveInstanceState(p0: Activity, p1: Bundle) {}
    override fun onActivityDestroyed(p0: Activity) {}
}
