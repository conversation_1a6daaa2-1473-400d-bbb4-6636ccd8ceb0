package com.resoluttech.barcelona

import kotlin.test.Test
import kotlin.test.assertEquals

class BuildConfigTests {
    @Test
    fun `test build urls`() {
        // Note: This test makes the compiler think this comparing against a constant will always succeed.
        // The trick here when we run the `test` task, we run this test for all build configurations
        // and that way each branch is tested.
        when (BuildConfig.BUILD_TYPE) {
            "devDebug" -> {
                assertEquals(BuildConfig.BASE_URL, "https://dev.resoluttech.link/")
            }
            "devRelease" -> {
                assertEquals(BuildConfig.BASE_URL, "https://dev.resoluttech.link/")
            }
            "uatDebug" -> {
                assertEquals(BuildConfig.BASE_URL, "https://uat.resoluttech.link/")
            }
            "uatRelease" -> {
                assertEquals(BuildConfig.BASE_URL, "https://uat.resoluttech.link/")
            }
            "prodRelease" -> {
                assertEquals(BuildConfig.BASE_URL, "https://resoluttech.link/")
            }
            else -> {
                throw IllegalStateException("Unknown Build Type: ${BuildConfig.BASE_URL}")
            }
        }
    }
}
