import java.nio.file.Paths

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'androidx.navigation.safeargs'
    alias(lkr.plugins.spotless)
    alias(lkr.plugins.kotlinx.serialization)
}

def googleMapApiKey = new Properties()
googleMapApiKey.load(new FileInputStream(rootProject.file("googlemaps.properties")))

android.buildFeatures.buildConfig true

android {

    namespace 'com.resoluttech.barcelona'
    /*
        ***************************************** Critical *****************************************
        NOTE: These signing configs are very critical.
        1. These signing configs provides digital fingerprint for AppLinks. Without these AppLinks
        will not work.
        2. For Play Store if you upload a version of app with these signing config then it will not
        accept any other digital fingerprint and you have to upload it as a totally different app on
        play store if in case current signing config are lost.
        3. These signing configs must be same for every developer or user.
        4. Keystore holds digital fingerprint which are used to authenticate the app. Keystore reside
        in the project directory itself so that they are not misplaced.
    */
    signingConfigs {
        debug
        release
    }
    compileSdk compile_sdk_version.toInteger()
    buildToolsVersion = build_tools_version

    // Needed to allow inline methods.
    // Taken from: https://stackoverflow.com/questions/48988778/cannot-inline-bytecode-built-with-jvm-target-1-8-into-bytecode-that-is-being-bui
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21
    }

    // Thanks: https://stackoverflow.com/a/21405744
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.canRead()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        def code = versionProps['VERSION_CODE']

        // Thanks: https://stackoverflow.com/a/17256864
        if (!code) {
            throw new GradleException("Data not set in /app/version.properties")
        }

        defaultConfig {
            applicationId "com.resoluttech.barcelona"
            minSdkVersion min_sdk_version.toInteger()
            targetSdkVersion target_sdk_version.toInteger()
            versionCode code.toString().toInteger()
            versionName "1.0"

            testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        }
    } else {
        throw new GradleException("Could not read version.properties!")
    }

    buildTypes {

        devDebug {
            signingConfig signingConfigs.debug
            applicationIdSuffix ".dev.debug"
            buildConfigField("String", "BASE_URL", "\"https://dev.resoluttech.link/\"")
            matchingFallbacks = ['default', 'debug']
            debuggable true
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
        }

        devRelease {
            signingConfig signingConfigs.release
            applicationIdSuffix ".dev.release"
            buildConfigField("String", "BASE_URL", "\"https://dev.resoluttech.link/\"")
            matchingFallbacks = ['default', 'debug']
            minifyEnabled true
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
            // Enables resource shrinking, which is performed by the Android Gradle plugin.
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        uatDebug {
            // Thanks: https://stackoverflow.com/a/47547753/4400607
            signingConfig signingConfigs.debug
            applicationIdSuffix ".uat.debug"
            buildConfigField("String", "BASE_URL", "\"https://uat.resoluttech.link/\"")
            // Thanks: https://stackoverflow.com/a/51827551/4400607
            matchingFallbacks = ['default', 'debug']
            debuggable true
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
        }

        uatRelease {
            signingConfig signingConfigs.release
            applicationIdSuffix ".uat.release"
            buildConfigField("String", "BASE_URL", "\"https://uat.resoluttech.link/\"")
            matchingFallbacks = ['uat', 'debug']
            minifyEnabled true
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
            // Enables resource shrinking, which is performed by the Android Gradle plugin.
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        prodRelease {
            signingConfig signingConfigs.release
            applicationIdSuffix ".prod.release"
            buildConfigField("String", "BASE_URL", "\"https://resoluttech.link/\"")
            matchingFallbacks = ['prod', 'default', 'debug']
            minifyEnabled true
            resValue "string", "google_maps_api_key", googleMapApiKey["api_key"]
            // Enables resource shrinking, which is performed by the Android Gradle plugin.
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions.add("applications")

    productFlavors {
        barcelona {
            dimension "applications"
            applicationIdSuffix ".bcn"
            resValue "string", "installationId", "bcn"
        }
    }

    lint {
        abortOnError true
        disable 'GradleDependency',
                'MediaCapabilities',
                'FragmentBackPressedCallback',
                'FragmentLiveDataObserve',
                'FragmentAddMenuProvider',
                'UnusedResources',
                'AppBundleLocaleChanges'
        warningsAsErrors true
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
    // Thanks : https://stackoverflow.com/a/47509465/13268813
    packagingOptions {
        resources.excludes.add("META-INF/*")
    }

}

def signingConfigPropertiesFile = rootProject.file("signingConfig.properties")
if (signingConfigPropertiesFile.exists()) {
    def signingConfigProperties = new Properties()
    signingConfigProperties.load(new FileInputStream(signingConfigPropertiesFile))
    // Setting up debug signing configs
    if (
            signingConfigProperties != null &&
            signingConfigProperties.containsKey("debugStorePassword") &&
            signingConfigProperties.containsKey("debugKeyAlias") &&
            signingConfigProperties.containsKey("debugKeyPassword")
    ) {
        android.signingConfigs.debug.storeFile file('androiddebug.keystore')
        android.signingConfigs.debug.storePassword signingConfigProperties["debugStorePassword"]
        android.signingConfigs.debug.keyAlias signingConfigProperties["debugKeyAlias"]
        android.signingConfigs.debug.keyPassword signingConfigProperties["debugKeyPassword"]
    } else {
        logger.debug('Debug signing configs are not found')
        android.buildTypes.debug.signingConfig = null
    }
    // Setting up release signing configs
    if (
            signingConfigProperties != null &&
            signingConfigProperties.containsKey("releaseStorePassword") &&
            signingConfigProperties.containsKey("releaseKeyAlias") &&
            signingConfigProperties.containsKey("releaseKeyPassword")
    ) {
        android.signingConfigs.release.storeFile file('androidrelease.keystore')
        android.signingConfigs.release.storePassword signingConfigProperties["releaseStorePassword"]
        android.signingConfigs.release.keyAlias signingConfigProperties["releaseKeyAlias"]
        android.signingConfigs.release.keyPassword signingConfigProperties["releaseKeyPassword"]
    } else {
        logger.debug('Release signing configs are not found')
        android.buildTypes.release.signingConfig = null
    }
} else {
    logger.debug('signingConfig.properties file not found')
}

//Thanks : https://stackoverflow.com/questions/38732993/how-to-eliminate-default-build-variants-in-android-gradle
android.variantFilter { variant ->
    if (variant.buildType.name == 'release' || variant.buildType.name == 'debug') {
        variant.setIgnore(true)
    }
}

// Skip tests on the UAT and Prod environment.
tasks.configureEach { task ->
    if (task.name.startsWith("test") && task.name.contains("Uat") && task.name.contains("Prod")) {
        task.enabled false
    }
    task.enabled true
}

dependencies {
    // Internal modules
    implementation project(':bcncore')
    // BCN RPCs
    implementation libs.home.screen.interface
    implementation libs.auth.interface
    implementation libs.sign.up.in.interface
    implementation libs.payments.interface
    implementation libs.profile.interface
    implementation libs.push.notification.interface
    implementation libs.transfers.interface
    implementation libs.home.screen.android.client
    implementation libs.transfers.android.client
    implementation libs.transactions.android.client
    implementation libs.payments.android.client
    implementation libs.auth.android.client
    implementation libs.sign.up.in.android.client
    implementation libs.profile.android.client
    implementation libs.push.notification.android.client
    implementation libs.transactions.interface
    implementation libs.document.interface
    implementation libs.document.android.client
    // External dependencies
    implementation lkr.koin.android
    implementation lkr.androidx.appcompat
    implementation lkr.androidx.core
    implementation lkr.androidx.constraintlayout
    implementation lkr.androidx.legacy.support
    implementation lkr.androidx.navigation.fragment
    implementation lkr.androidx.navigation.ui
    implementation lkr.coil
    implementation lkr.timber
    implementation lkr.androidx.security.crypto
    implementation lkr.pin.edit.text.field
    coreLibraryDesugaring lkr.desugar
    implementation lkr.androidx.core.splashscreen
    implementation lkr.leo.kedwig.jvm
    implementation lkr.leo.kedwig.core
    implementation lkr.ucrop

    // Firebase
    implementation platform(lkr.firebase.bom)
    implementation lkr.firebase.crashlytics
    implementation lkr.firebase.messaging

    // Test dependencies
    testImplementation lkr.junit
    testImplementation lkr.kotlin.test
    testImplementation lkr.robolectric
    androidTestImplementation lkr.androidx.test.core

}

configurations.configureEach {
    resolutionStrategy { force AndroidX.work.runtime }
}

apply from: Paths.get("${project.rootDir}", "spotless.gradle")
