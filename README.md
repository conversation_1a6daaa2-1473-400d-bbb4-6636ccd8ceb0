# Android
[![Release](https://github.com/Resolut-Tech/Android/actions/workflows/ReleaseDev.yml/badge.svg)](https://github.com/Resolut-Tech/Android/actions/workflows/ReleaseDev.yml)

## Setup

### IDE

This project uses Android Gradle Plugin (AGP) version `8.9.1`.
Please ensure you are using Android Studio Meerkat (or later), as older versions are not compatible with this version.

### Artifactory
This project uses pre-built artifacts deployed at Surya's Artifactory instance.
To see how this process works, refer to the [Artifactory document](https://github.com/surya-soft/Leo-Docs/blob/master/jvm-dependencies/Artifactory.md) in the Leo docs repository. 
To get this project to build you need to have a set of Artifactory credentials: A username and a password.
If you don't have these credentials please contact [@gps](mailto://<EMAIL>).
Once you have your credentials, create a file called `artifactory.properties` in the root of this
repository. The contents of the file should be:
```
username=[your artifactory username]
password=[your artifactory password]
```

## Java Setup
This project uses Java 21. Install it from here [Temurin Java 21](https://adoptium.net/en-GB/temurin/releases/?version=21&package=jdk)

### Github
To push or pull changes from Github repository, you need to setup your Github Credentials. These credentials
contains your github username and personal access token.
To see how to create personal access token, refer to [Github Personal Access Token](https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token).
Make sure you have given read access to repositories in the permission check boxes for your access token.
Once you have your github credentials, create a file called `github.properties` in the root of this
repository. The contents of the file should be:
```
username=[your github username]
token=[your github personal access token]
```

### AWS
This project uses pre-built packages from AWS CodeArtifact.

To get this project to build you need to have AWS credentials: `aws_access_key_id` and `aws_secret_access_key`

Create a file called `credentials` in folder `/Home/.aws/credentials` for linux/MAC & `C:\Users\<USER>\.aws\credentials` for windows with the following content:
```
[default]
aws_access_key_id = <your_access_key_id>
aws_secret_access_key = <your_secret_access_key>
region = af-south-1
```

_Note:_ If you prefer a different location for your `credentials` file, you would need to set an environment variable `AWS_SHARED_CREDENTIALS_FILE`.
For Linux:
```
export AWS_SHARED_CREDENTIALS_FILE=<path-to-credentials-file>
```
For Windows it can be done on GUI.
More details provided [here](https://docs.aws.amazon.com/sdk-for-java/latest/developer-guide/setup-additional.html#setup-additional-credentials)

### Git Large File Storage (LFS)
This project uses Git LFS to store binary files.
To install git lfs, refer to [Git LFS Installation](https://github.com/git-lfs/git-lfs/wiki/Installation).
If you are about to add a binary file, first check the contents of `.gitattributes` and see if there
already is a pattern for the type of binary file you are adding. If there isn't one, add one. For
example, to use Git LFS for all jpg files, run `git lfs track "*.jpg"`.
Once this is done, continue using git as usual even for binary files (i.e. git add, git commit,
git push, git fetch, etc.).

After cloning the repository, developers need to run the following commands (in the same order) to 
fetch existing git lfs resources:

`git lfs checkout`
`git lfs fetch`

### Android Studio Inspections

The lint tool helps find poorly structured code that can impact the reliability and efficiency of your Android apps and make your code harder to maintain.

For example, if your XML resource files contain unused namespaces, this takes up space and incurs unnecessary processing. Other structural issues, such as use of deprecated elements or API calls that are not supported by the target API versions, might lead to code failing to run correctly. Lint can help you clean up these issues.

You need to configure the Android Studio inspection settings.

Open Android studio Preferences/Settings, and find "Inspections". Click on the gear icon:

![](docs/inspection-3.png)

Click on Import Profile:

![](docs/inspection-2.png)

Select `BCNInspections.xml` from the root of the repository:

![](docs/inspection.png)

Click Apply, and then OK:

![](docs/inspection-3.png)

To test this, you can hit the shift key twice in Android Studio, type in "Inspect Code", and run the inspections. OR
You can go to analyze menu and select inspect code.

![](docs/inspection-4.png)

You will be able to see the output in inspection result section in bottom once it's completed.

Note : Please run the inspection before pushing any changes to github and make sure the inspection passes with zero error/warnings.
GitHub CI will fail the build if there is any warning or error persist during inspection.

### Use Google Maps
This project uses Google Maps to show Points of Interest.
In order to use Google Maps, you need to generate a Google Map API Key, refer here [How To Generate Key](https://github.com/surya-soft/Leo-Android-UI#location-component).
You may also want to look at Google's instructions on setting up [Google Maps](https://developers.google.com/maps/documentation/android-sdk/get-api-key).
Once key is generated, replace the text `PUT_YOUR_KEY_HERE` with your key in `googlemap.key` file.

### CI

This project uses GitHub Actions for Continuous Integration (CI) to verify the code format, build, tests and automatically deploy the application.

CI runs only when the `run-ci` label is added to the PR.
Steps to add a label to the PR:
- Click on the setting icon next to Labels.

  ![](docs/select-label-settings.png)
- Select the label `run-ci` as shown in the image below.

  ![](docs/select-label-for-pr.png)

CI will be triggered once the `run-ci` label is added to the PR, and runs some basic checks to ensure that the code in the PR meets minimum quality standards every time it is updated. This includes a passing build, passing tests, and passing lint checks.

To prevent CI from running remove the `run-ci` label from the PR before pushing any changes.

Run the CI only when the PR is ready to be merged as CI runs for a while and costs us money.
