spotless {
    kotlin {
        target "**/*.kt"
        ktlint(ktlint_version)
        trimTrailingWhitespace()
        leadingTabsToSpaces()
        endWithNewline()
    }
    format 'misc', {
        target '**/*.gradle', '**/*.md', '**/.gitignore'
        leadingTabsToSpaces()
        trimTrailingWhitespace()
        endWithNewline()
    }

    format 'xml', {
        target '**/*.xml'
        leadingTabsToSpaces()
        trimTrailingWhitespace()
        endWithNewline()
    }
}
