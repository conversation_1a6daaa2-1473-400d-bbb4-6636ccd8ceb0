## Generated by $ ./gradlew refreshVersionsCatalog

[versions]

bcn-rpcs = "0.0.780"
##    ⬆ = "0.0.781"
##    ⬆ = "0.0.782"
##    ⬆ = "0.0.783"
##    ⬆ = "0.0.784"
##    ⬆ = "0.0.785"
##    ⬆ = "0.0.786"
##    ⬆ = "0.0.787"
leo-libui = "0.0.80"

[libraries]

# BCN RPCs
auth-android-client = { module = "com.resoluttech.bcn:auth-android-client", version.ref = "bcn-rpcs" }
auth-interface = { module = "com.resoluttech.bcn:auth-interface", version.ref = "bcn-rpcs" }
document-android-client = { module = "com.resoluttech.bcn:document-android-client", version.ref = "bcn-rpcs" }
document-interface = { module = "com.resoluttech.bcn:document-interface", version.ref = "bcn-rpcs" }
home-screen-android-client = { module = "com.resoluttech.bcn:home-screen-android-client", version.ref = "bcn-rpcs" }
home-screen-interface = { module = "com.resoluttech.bcn:home-screen-interface", version.ref = "bcn-rpcs" }
payments-android-client = { module = "com.resoluttech.bcn:payments-android-client", version.ref = "bcn-rpcs" }
payments-interface = { module = "com.resoluttech.bcn:payments-interface", version.ref = "bcn-rpcs" }
profile-android-client = { module = "com.resoluttech.bcn:profile-android-client", version.ref = "bcn-rpcs" }
profile-interface = { module = "com.resoluttech.bcn:profile-interface", version.ref = "bcn-rpcs" }
push-notification-android-client = { module = "com.resoluttech.bcn:push-notification-android-client", version.ref = "bcn-rpcs" }
push-notification-interface = { module = "com.resoluttech.bcn:push-notification-interface", version.ref = "bcn-rpcs" }
sign-up-in-android-client = { module = "com.resoluttech.bcn:sign-up-in-android-client", version.ref = "bcn-rpcs" }
sign-up-in-interface = { module = "com.resoluttech.bcn:sign-up-in-interface", version.ref = "bcn-rpcs" }
transactions-android-client = { module = "com.resoluttech.bcn:transactions-android-client", version.ref = "bcn-rpcs" }
transactions-interface = { module = "com.resoluttech.bcn:transactions-interface", version.ref = "bcn-rpcs" }
transfers-android-client = { module = "com.resoluttech.bcn:transfers-android-client", version.ref = "bcn-rpcs" }
transfers-interface = { module = "com.resoluttech.bcn:transfers-interface", version.ref = "bcn-rpcs" }

# External libraries
libui = { module = "com.suryadigital.leo:libui", version.ref = "leo-libui" }
