## Checklist

Please make sure you have gone through this checklist before asking for review.

- [ ] Made UI changes in this PR (if yes)
  - [ ] Tested on Pixel 3
  - [ ] Tested on Nexus 5
  - [ ] Tested on Nexus S
  
- [ ] Added new `string` resources (if yes)
  Check if text is changing on changing locale of the app - `EN` to `NY` & vice versa
  - [ ] Tested for NY (Nyanja)
  - [ ] Tested for EN (English)
  
- [ ] Added new views (if yes)
  - [ ] Check if `TextView`s or `EditText`s you added are able to accommodate the `string` you care about, completely, and it is not truncated or invisible
  - [ ] Check if the text sizes are according to TextSize.md file
  - [ ] Check the names you give to views. They should follow the project's [Style Guide](https://github.com/Resolut-Tech/Android/blob/master/docs/StyleGuide.md). For example: `enterAmountET` instead of `enterAmountEditText`

- [ ] Test app for theme
  - [ ] Dark Mode
  - [ ] Light Mode
  
- [ ] Check all screens related to the work flow, you are working on. If screen you made edits to impact other workflows, check those workflows as well
- [ ] Correct Typos
- [ ] Run Inspection
- [ ] Apply Spotless
- [ ] Review your PR yourself after a gap of 15 minutes, allowing for a fresh perspective
