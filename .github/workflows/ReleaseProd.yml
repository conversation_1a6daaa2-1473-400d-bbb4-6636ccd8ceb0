name: Release-Prod

on:
  push:
    branches:
      - prod

jobs:
  Release:

    runs-on: ubuntu-latest

    env:
      GRADLE_OPTS: '"-Dorg.gradle.jvmargs=-Xms2048m -Xmx2048m" "-Dkotlin.daemon.jvmargs=-Xms2048m -Xmx2048m"'

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: Restore Gradle Caches
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Setup Artifactory Properties
        run: |
          echo "username=bcnbot" > artifactory.properties
          echo "password=${{secrets.BCN_BOT_SURYA_ARTIFACTS_PASSWORD}}" >> artifactory.properties

      - name: Setup GitHub properties
        run: |
          echo "username=bcn-bot" > github.properties
          echo "token=${{secrets.BCN_BOT_GH_TOKEN}}" >> github.properties

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: af-south-1
          role-to-assume: CodeArtifactPublish
          role-duration-seconds: 3600

      - name: Setup Google Maps Key
        run: echo "api_key=${{secrets.GOOGLE_MAPS_BCN_PROD_KEY}}" >> googlemaps.properties

      - name: Setup Signing Config
        run: |
          echo "debugStorePassword=${{secrets.DEBUG_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "debugKeyAlias=${{secrets.DEBUG_SIGNING_KEY_ALIAS}}" >> signingConfig.properties
          echo "debugKeyPassword=${{secrets.DEBUG_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "releaseStorePassword=${{secrets.RELEASE_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "releaseKeyAlias=${{secrets.RELEASE_SIGNING_KEY_ALIAS}}" >> signingConfig.properties
          echo "releaseKeyPassword=${{secrets.RELEASE_SIGNING_PASSWORD}}" >> signingConfig.properties

      - name: Gradle Tests & Lint
        run: ./gradlew clean check

      - name: Save Lint Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lint-results.html
          path: app/build/reports/lint-results-barcelonaDevDebug.html

      - name: Determine next version
        uses: gps/determine-next-version-mobile-app@master
        id: next_version
        with:
          GH_TOKEN: ${{ github.token }}
          TAG_PREFIX: prod-v

      - name: Set version code
        run: |
          echo -en "VERSION_CODE=$(sed 's/.*(\(.*\))/\1/' <<< "${{steps.next_version.outputs.NEXT_BUILD_VERSION}}")\n" > ./app/version.properties

      - name: Build Prod Release APK
        run: ./gradlew assembleProdRelease

      - name: Build Prod Release Signed AAB
        run: ./gradlew :app:bundleBarcelonaProdRelease

      # Rationale:
      # - Customizing output filenames directly within build.gradle can be complex,
      #   especially when dealing with multiple build variants and flavors.
      # - While it's possible to programmatically rename APKs and AABs in Gradle by
      #   configuring variant outputs or using tasks like FinalizeBundleTask, these methods
      #   can significantly increase the complexity and size of the build.gradle file.
      # - By handling renaming in the CI pipeline, we maintain a cleaner build configuration
      #   and achieve the desired naming convention in a straightforward manner.
      #
      # Note:
      # - If a more streamlined or maintainable solution becomes available in the future,
      #   consider integrating it directly into the Gradle build process.
      - name: Rename Releases
        run: |
          mv ./app/build/outputs/apk/barcelona/prodRelease/app-barcelona-prodRelease.apk ./app/build/outputs/apk/barcelona/prodRelease/Yafika-Mobile-Prod-Release.apk
          mv ./app/build/outputs/bundle/barcelonaProdRelease/app-barcelona-prodRelease.aab ./app/build/outputs/bundle/barcelonaProdRelease/Yafika-Mobile-Prod-Release.aab

      - name: Create Release and Upload Assets
        uses: softprops/action-gh-release@v2
        with:
          tag_name: prod-v${{steps.next_version.outputs.NEXT_BUILD_VERSION}}
          name: prod-v${{steps.next_version.outputs.NEXT_BUILD_VERSION}}
          draft: false
          prerelease: true
          files: |
            ./app/build/outputs/apk/barcelona/prodRelease/Yafika-Mobile-Prod-Release.apk
            ./app/build/outputs/bundle/barcelonaProdRelease/Yafika-Mobile-Prod-Release.aab
          token: ${{ github.token }}
