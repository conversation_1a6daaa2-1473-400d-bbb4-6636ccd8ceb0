name: Release-Dev-PR-Check

on:
  pull_request:
    types: [ labeled,opened,synchronize,reopened ]

jobs:
  Release:

    if: contains( github.event.pull_request.labels.*.name, 'run-release-dev-ci')
    runs-on: ubuntu-latest

    env:
      GRADLE_OPTS: "-Dorg.gradle.daemon=false"

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: Restore Gradle Caches
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Setup Artifactory Properties
        run: |
          echo "username=bcnbot" > artifactory.properties
          echo "password=${{secrets.BCN_BOT_SURYA_ARTIFACTS_PASSWORD}}" >> artifactory.properties

      - name: Setup GitHub properties
        run: |
          echo "username=bcn-bot" > github.properties
          echo "token=${{secrets.BCN_BOT_GH_TOKEN}}" >> github.properties

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: af-south-1
          role-to-assume: CodeArtifactPublish
          role-duration-seconds: 1800

      - name: Setup Google Maps Key
        run: echo "api_key=${{secrets.GOOGLE_MAPS_BCN_DEV_KEY}}" >> googlemaps.properties

      - name: Setup Signing Config
        run: |
          echo "debugStorePassword=${{secrets.DEBUG_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "debugKeyAlias=${{secrets.DEBUG_SIGNING_KEY_ALIAS}}" >> signingConfig.properties
          echo "debugKeyPassword=${{secrets.DEBUG_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "releaseStorePassword=${{secrets.RELEASE_SIGNING_PASSWORD}}" >> signingConfig.properties
          echo "releaseKeyAlias=${{secrets.RELEASE_SIGNING_KEY_ALIAS}}" >> signingConfig.properties
          echo "releaseKeyPassword=${{secrets.RELEASE_SIGNING_PASSWORD}}" >> signingConfig.properties

      - name: Gradle Tests & Lint
        run: ./gradlew clean check

      - name: Save Lint Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lint-results.html
          path: app/build/reports/lint-results-barcelonaDevDebug.html

      # We're building everything here since a merge is a good opportunity to
      # check if all flavours and build types of the project work fine.
      - name: Build Dev Debug APK
        run: ./gradlew build
