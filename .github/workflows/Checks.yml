name: CI

on:
  pull_request:
    types: [ labeled,opened,synchronize,reopened ]

jobs:
  Checks:
    if: contains( github.event.pull_request.labels.*.name, 'run-ci')
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: <PERSON><PERSON><PERSON>
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ secrets.GRADLE_CACHE_KEY }}-${{ hashFiles('**/*.gradle*') }} # Github actions does not allow to clear cache, to invalidate the cache update the GRADLE_CACHE_KEY in Github secrets. See https://github.com/actions/cache/issues/2
          restore-keys: |
               ${{ runner.os }}-gradle-${{ secrets.GRADLE_CACHE_KEY }}-

      - name: Setup Artifactory Properties
        run: |
          echo "username=bcnbot" > artifactory.properties
          echo "password=${{secrets.BCN_BOT_SURYA_ARTIFACTS_PASSWORD}}" >> artifactory.properties

      - name: Setup GitHub Properties
        run: |
          echo "username=bcn-bot" > github.properties
          echo "token=${{secrets.BCN_BOT_GH_TOKEN}}" >> github.properties

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: af-south-1
          role-to-assume: CodeArtifactPublish
          role-duration-seconds: 1800

      - name: Determine next version
        uses: gps/determine-next-version-mobile-app@master
        id: next_version
        with:
          GH_TOKEN: ${{ github.token }}
          TAG_PREFIX: dev-v

      - name: Set version code
        run: |
          echo -en "VERSION_CODE=$(sed 's/.*(\(.*\))/\1/' <<< "${{steps.next_version.outputs.NEXT_BUILD_VERSION}}")\n" > ./app/version.properties

      - name: Setup Google Maps Key
        run: echo "api_key=${{secrets.GOOGLE_MAPS_BCN_DEV_KEY}}" > googlemaps.properties

      - name: Build Dev Debug APK
        run: ./gradlew clean assembleDevDebug

      - name: Run Dev Tests
        run: ./gradlew testBarcelonaDevDebugUnitTest testDebugUnitTest

      - name: Check lint and formatting
        run: ./gradlew lintBarcelonaDevDebug spotlessCheck

      - name: Upload Dev Debug APK
        uses: actions/upload-artifact@v4
        with:
          name: Barcelona-Dev-Debug.apk
          path: app/build/outputs/apk/barcelona/devDebug/app-barcelona-devDebug.apk

      - name: Save Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test_reports
          path: app/build/reports

  Inspections:
    if: contains( github.event.pull_request.labels.*.name, 'run-ci')
    runs-on: ubuntu-latest

    env:
      GRADLE_OPTS: "-Dorg.gradle.daemon=false"

    steps:
      - uses: actions/checkout@v4
        with:
          lfs: true

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 21
          check-latest: true

      - name: Gradle Caches
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ secrets.GRADLE_CACHE_KEY }}-${{ hashFiles('**/*.gradle*') }} # Github actions does not allow to clear cache, to invalidate the cache update the GRADLE_CACHE_KEY in Github secrets. See https://github.com/actions/cache/issues/2
          restore-keys: |
               ${{ runner.os }}-gradle-${{ secrets.GRADLE_CACHE_KEY }}-

      - name: Setup Artifactory Properties
        run: |
          echo "username=bcnbot" > artifactory.properties
          echo "password=${{secrets.BCN_BOT_SURYA_ARTIFACTS_PASSWORD}}" >> artifactory.properties

      - name: Setup GitHub Properties
        run: |
          echo "username=bcn-bot" > github.properties
          echo "token=${{secrets.BCN_BOT_GH_TOKEN}}" >> github.properties

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: af-south-1
          role-to-assume: CodeArtifactPublish
          role-duration-seconds: 1800

      - name: Build Dev Debug APK
        run: ./gradlew clean assembleDevDebug

      - name: Run IntelliJ Inspections
        uses: gps/intellij-android-inspections@master
        with:
          GH_TOKEN: ${{ github.token }}
          INSPECTIONS_FILE: BCNInspections.xml

      - name: Save Inspection Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: idea_inspections
          path: target/idea_inspections
