// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // Internal library dependencies
        classpath lkr.android.gradle
        classpath lkr.kotlin.android
        classpath lkr.androidx.navigation.safe.args
        classpath lkr.awssdk.codeartifact
        classpath lkr.google.services
        classpath lkr.firebase.crashlytics.gradle

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

plugins {
    alias(lkr.plugins.kotlin.jvm)
}

import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.codeartifact.CodeartifactClient
import software.amazon.awssdk.services.codeartifact.model.GetAuthorizationTokenRequest
import software.amazon.awssdk.services.codeartifact.model.GetAuthorizationTokenResponse

def artifactoryProperties = new Properties()
artifactoryProperties.load(new FileInputStream(rootProject.file("artifactory.properties")))

def githubProperties = new Properties()
githubProperties.load(new FileInputStream(rootProject.file("github.properties")))

def getAWSArtifactsAuthToken() {
    Region region = Region.AP_SOUTH_1
    CodeartifactClient caClient = CodeartifactClient.builder()
            .region(region)
            .build()
    GetAuthorizationTokenRequest request = GetAuthorizationTokenRequest.builder()
            .domain("resolut-tech")
            .build()
    GetAuthorizationTokenResponse response = caClient.getAuthorizationToken(request)
    return response.authorizationToken()
}

/*
 * awsAuthToken : Stores the auth token returned by AWS CodeArtifact.
 */
def awsAuthToken = getAWSArtifactsAuthToken()

allprojects {
    repositories {
        google()

        maven { url 'https://jitpack.io' }
        mavenCentral()

        maven {
            url 'https://artifacts.surya-digital.in/repository/maven-releases/'
            credentials {
                username = artifactoryProperties["username"]
                password = artifactoryProperties["password"]
            }
        }

        maven {
            name = "GitHubPackages"
            url = uri('https://maven.pkg.github.com/Resolut-Tech/bcn-rpcs')
            credentials {
                username = githubProperties["username"]
                password = githubProperties["token"]
            }
        }

        maven {
            // This requires credentials file to be located in `/Home/.aws/credentials` for linux/MAC & `C:\Users\<USER>\.aws\credentials` for windows. See `README.md` for more information
            url 'https://resolut-tech-659926958023.d.codeartifact.ap-south-1.amazonaws.com/maven/bcn-rpcs/'
            credentials {
                username = 'aws'
                password = awsAuthToken
            }
        }
    }
}

tasks.named("clean") {
    doLast {
        delete rootProject.layout.buildDirectory
    }
}
