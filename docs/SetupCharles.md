# Charles

Charles Proxy is a web debugging tool that monitors the network calls and helps in troubleshooting or debugging the web traffic.
It helps in understanding the content in your network call.

## Setup

### Configuring Charles Proxy

1. Open Charles proxy application.
2. Go to Proxy > Proxy Settings.
3. Make note of the HTTP Proxy Port that is specified.
4. Go to Proxy > SSL Proxying Settings.
5. Click the SSL Proxying tab and check the Enable SSL Proxying checkbox to configure a location.
6. Add substring for the domain you are monitoring as the location and press ok. For example, if you want to monitor Timing's API calls, set this for *usetiming*. This will result in <PERSON> showing you all API calls Timing is making.
7. The port default value is 443. You can leave this field blank, as <PERSON> will set it automatically.

### Configuring Your Android Device to use the Charles Proxy

1. Make note of your computer's IPv4 Address.
2. Go to Settings > Wifi.
3. Press and hold the network you’re going to use.
4. Select Modify Network from the menu.
![](modify_wifi_network_dialog.png)
5. Select Show Advanced Options to display proxying options.
6. Under Proxy, select Manual.
7. In the Proxy Host Name box, enter the IPv4 Address that you noted above.
8. In the Proxy Port field, enter the HTTP Proxy Port that you noted from <PERSON><PERSON>'s Proxy Settings above.
![](modify_proxy_settings_dialog.png)
9. Click Save to save the settings and exit.
10. Open a browser on your device to download the certificate.  
11. Go to http://charlesproxy.com/getssl from your device and download the Charles SSL certificate.
12. Name the certificate and set it as a trusted certificate.
13. Once the certificate is installed, you are prompted to set up a PIN.
14. When prompted, add the new PIN.

Thanks: https://community.tealiumiq.com/t5/Tealium-for-Android/Setting-up-Charles-to-Proxy-your-Android-Device/ta-p/5121#check_dev_machine
