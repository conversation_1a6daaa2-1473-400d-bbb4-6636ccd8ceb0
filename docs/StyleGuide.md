# Android Programming Style Guide

This style guide incorporates practices followed on the BCN Android project.

Developers must use the [Kotlin Style Guide](https://developer.android.com/kotlin/style-guide) as much as possible in the app. Eg: Constants in the app
are `CAPITAL_SNAKE_CASED`.

## Code Inspection
ALWAYS inspect your code before making a final push to git.
You can inspect your code anytime by selecting `Analyze -> Inspect Code` from the top bar of
Android Studio. Alternatively you can bring up the actions window by pressing Shift twice and typing
"Inspect".
Resolve issues highlighted in your code when running the inspection.

## Source File Structure

### Const Val & Companion Object Declaration
Declare `companion object`s at the end of the top level class and `private const val` at the bottom of
the source file.
Example:

```
class CalenderSelector : Fragment {

    private val property = "This is a property"

    fun getProperty(): String {
        return property
    }

    ...

	companion object {
		const val DELAY_TIME_IN_SEC = 1000
	}
}

private const val MILLIS_IN_A_DAY = 86400000F

```

All `const val` must be `private`. If a `const` needs to be available to the public,
it must be namespaced and therefore used in a companion object. eg: CalendarSelector.DELAY_TIME_IN_SEC.

### View Nomenclature
While naming a view, add its type initials at the end of the variable in UPPERCASE.
Example:
```
    private lateinit var amountTV: TextView
    private lateinit var logoIV: ImageView
    private lateinit var contactsListRV: RecyclerView
```

In the above example developers can tell that `amountTV` is a TextView, `logoIV` is an ImageView and
`contactsListRV` is a RecyclerView.

### Mentioning TODOs
Always (and only) mention link of the ticket in `TODO`, which fulfills it. The ticket contains all
the details already, no need to comment it again in `TODO`.
Developers can mention `TODO`s as long as they are working on the PR however once the PR is put up for
review, there shouldn't be any `TODO`s.
Example:

```
    // This is CORRECT
    fun onNewCashOutRequest(navController: NavController) {
        // TODO: https://app.clubhouse.io/resolut-tech/story/502/create-new-cash-out-request-screen
    }

    // This is INCORRECT
    fun onNewCashOutRequest(navController: NavController) {
        // TODO: Navigate to create cash out request screen...
    }
```

## Resources

### Drawable Resource Naming
For icons to be added in `drawable`, always use prefix `ic_`. It will be helpful for developers to
automatically infer that it is an icon.
Example:

```
    ic_add.xml
```

### Layout Resource Naming
For layout resources we prefix it's usage.
Example:

```
    activity_main.xml
    fragment_calender_selector.xml
```

In example you can see that `activity_main.xml` is a layout resource for Main Activity. Similarly
`fragment_calender_selector.xml` is a layout resource for CalenderSelector Fragment.

Here are all the prefixes we use as of now:
```
    // For activity layout
    activity_main.xml

    // For fragment layout
    fragment_calender_selector.xml

    // For background of a button, layout, image view, etc.
    // It is a background for button with 4dp corner radius.
    bg_4dp_corner.xml

    // For list item layout
    item_contact.xml
```

### Naming Color Resources
You should always name a color resource in the most general way instead of anything specific.
Example:

```
    // This is CORRECT - This is in general
    <color name="recycleItemBackgroundColor">#aaa</color>

    // This is INCORRECT - This is specific
    <color name="cashOutItemBackgroundColor">#aaa</color>
```

Problem with using specific name for color resource is that it results into duplication of color
resources.
Example:

```
    // This is INCORRECT - Value of resource is same
    <color name="cashInItemBackgroundColor">#aaa</color>
    <color name="cashOutItemBackgroundColor">#aaa</color>
    and so on...
```

## Error Handling

Errors generated in the app are handled in two ways:

1. Common Error Handling
This is preferred in cases where the response to an error is going to be the same across the app.
An example of such errors are "NoInternet", or "ServerNotReachable" kinds of errors. The responses
to these kinds of error will mostly be the same across the app and therefore can be delegated to
the common error handler.

2. Custom Error Handling
When errors needs to be handled it in a custom way, we pass a method to the error handler and that
method will get executed. An example of this usecase is showing an inline error message on a Cash In
details screen.

In the app, validation errors are also be prevented in the ViewModel layer, such as errors occuring
when an amount the user entered is lower than some determined amount.

[Here's a diagram](error_handling_tree.jpg) that serves as a guideline for common vs. custom error handling.

## Logging

We are using kotlin version of [Timber(https://github.com/JakeWharton/timber) for logging purpose. The logs
must include `TAG` and proper message.

### How to write logs ?

First, Define the TAG at bottom of the file as follow
```kotlin
    private const val TAG = "ClassName"
```

and then it will be used as follow :

```kotlin
    Timber.tag(TAG).d("Provide you log message here")
```

Note : Confirm that you have imported the correct `import timber.log.Timber` Timber class.

## Naming string resources

 - Keep all letters in lowercase
 - If you are defining any label for button or text which can be reused, prefix it with `label_`.
 - If you are defining any toolbar title then add suffix `_title`.

 For example a button having title `Show`, defined in fragment having toolbar title `Cash In Locations`, can have a string resources as

 ```xml
 <string name="label_show">Show</string>

 <!-- For title -->

 <string name="cash_in_locations_title">Cash In Locations</string>
 ```

Note : Before committing please check the Naynja version of strings also.