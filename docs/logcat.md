# Extracting Logs From a Device

- Open the linux shell or bash prompt.
- Navigate to the scripts directory which resides in this project's root directory.
- Run the logcat script as follows : -
```
      ./logcat.sh <output_filename> // you can specify any filename here.

      Example :
      ./logcat.sh output.txt
      This command will write the logs in output.txt file which you can find in current working directory.
```

Note: - You need adb installed in system in order to run the logcat script. If you don't have please follow this
link to install it [Click Here](https://www.xda-developers.com/install-adb-windows-macos-linux/).

