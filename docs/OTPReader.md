# OTP Reader

The Android app reads the OTP automatically after user's consent to read the SMS.

A message triggers the broadcast only if it meets these criteria: 

- The message contains a 4-10 character alphanumeric string with at least one number.
- The message was sent by a phone number that's not in the user's contacts.

Example message: 

```
    Your OTP is 123456.
```

### How to test?

Go to change password screen and open the OTP reader dialog then copy the above message in another mobile and send it.

Note: The sender's phone number should not be saved in receiver's device.

It will read the OTP automatically once user grants the consent to read the SMS.
