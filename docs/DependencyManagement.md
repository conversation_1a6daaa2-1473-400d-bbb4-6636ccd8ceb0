# RefreshVersions

We use a gradle plugin named [refresh<PERSON><PERSON><PERSON>]() which handles the dependency management for us. 

There are two files that have been created after migrating to use `refreshVersions`,

- `version.properties`
- `libs.versions.toml`

## versions.properties

This file is not used for tracking dependency versions.
All dependencies are managed centrally via a single source: `libs.versions.toml`.

## libs.versions.toml

This file holds the list of the dependencies with their versions.

Refer [Creating a catalog](https://docs.gradle.org/current/userguide/version_catalogs.html#sec:version-catalog-declaration) to add a dependency.

## lint.xml

This is a file generated by the plugin to avoid errors when using `_` as a version placeholder.

## Usage

To know if there are any updates to the dependencies, run the following in the terminal. 

```
$ ./gradlew refreshVersions
```

This will fetch the information about the updates available for the dependencies and updates the `libs.versions.toml` file accordingly. All the updates available would be commented out in the following way:

```
version.firebase-bom=31.5.0
##       # available=32.0.0
##       # available=32.1.0
##       # available=32.1.1
##       # available=32.2.0
##       # available=32.2.1
##       # available=32.2.2
##       # available=32.2.3
##       # available=32.3.0
##       # available=32.3.1
##       # available=32.4.0
##       # available=32.4.1
##       # available=32.5.0
##       # available=32.6.0
##       # available=32.7.0
```

Update to the required version and run gradle sync for the changes to be reflected.
