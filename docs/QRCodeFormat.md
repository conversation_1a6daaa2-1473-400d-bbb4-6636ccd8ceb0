# QR Codes

## Format
This application reads QR codes of a certain format.
The format is: `com.resoluttech.qr://<installationId>?username=<username>&userId=<userId>&shouldNavigate=<shouldNavigate>`
or
`com.resoluttech.qr://<installationId>?username=<username>&accountId=<accountId>&shouldNavigate=<shouldNavigate>`

`com.resoluttech.qr` is an identifier used to distinguish BCN QR code URLs from all other URLs available globally. This identifier will remain the same for all QR codes across all BCN installations.

`installationId` (type: `String`) is the current installation of application. Examples can be `bcn` or `mar`.

`username` (type: `String`) represents recipient's name (user's name).

`userId` (type: `UUID`) represents a recipient via their BCN User ID. If a recipient is identified by their `userId`, the money is received in their default account. Users can change their default account in the BCN mobile app.

`accountId` (type: `UUID`) represents a recipient's particular account.

`shouldNavigate` (type: `Boolean`) represent the navigation type in app links and deep links.

The format for Cash In and Cash Out Request is:
`com.resoluttech.qr://<installationId>?phoneNumber=<phoneNumber>`

`com.resoluttech.qr` is an identifier used to distinguish BCN QR code URLs from all other URLs available globally. This identifier will remain the same for all QR codes across all BCN installations.

`installationId` (type: `String`) is the current installation of application. Examples can be `bcn` or `mar`.

`phoneNumber` (type: `LeoPhoneNumber`) represents a recipient's phone number.

## Invalid QR Codes Cases

* When identifier doesn't match.

* When `installationId` doesn't match.

* When neither `userId` nor `accountId` is set, or aren't formatted correctly.

* When `username` is not set.

*Note: A QR code should not have both the `userId` & `accountId`, but it can have both (as that can't be restricted). In case both are available, only `accountId` will be considered as it is more particular than `userId`.*

## Test QR Codes
To test QR code scanning and payment flow, you can use the codes given below.

## Examples

### Valid QR Code

#### QR Code With Valid Username & UserId
![](qrcode_userid_correct.png)

#### QR Code With Valid Username & AccountId
![](qrcode_account_id_correct.png)

### Invalid QR Code

![](qr_code_incorrect.png)
