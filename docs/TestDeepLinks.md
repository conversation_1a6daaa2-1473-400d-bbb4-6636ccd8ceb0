# Test Your Deep Links

This application uses deeplinks to navigate between app contents.
Once you have added a deeplink you can test it by running the following command in terminal.

``
    $ adb shell am start
            -W -a android.intent.action.VIEW
            -d "<YOUR_DEEP_LINK>" <APPLICATION_PACKAGE_NAME>
``

After running the command you will see on your device/emulator that the app is launched and it will
directly navigate to the screen, your deeplink is pointing to.
Note: Please make sure that, when you navigate back from the screen to which deeplink is pointing at,
that screen should also get initialized properly.
For Example: On testing `resoluttechbcn://enter-amount` deeplink, it will take us to `LoadMoney`
screen and on tapping on back button, it should go to `HomeScreen` and then `HomeScreen` should get
initialized properly (load all data as expected).

### Application Package Name
To test your deeplinks you need package name in `adb` command. In our case the package name is not
just the application package but it contains some other information as well.
The package name which is required contains the following information:

``
    <PACKAGE_NAME>.<FLAVOUR>.<BUILD_VARIANT>
``

So the package name will look like:

```
    com.resoluttech.barcelona.bcn.dev.debug
```

Where `com.resoluttech.barcelona` is the default package name, `bcn` is flavour and `dev.debug` is
build variant.

So, now the complete command to test your deeplink will look like:

```
    $ adb shell am start
            -W -a android.intent.action.VIEW
            -d "resoluttechbcn://cash-in-request" com.resoluttech.barcelona.bcn.dev.debug
```

For more information, refer to [Test Your Deeplinks](https://developer.android.com/training/app-links/deep-linking#testing-filters)
