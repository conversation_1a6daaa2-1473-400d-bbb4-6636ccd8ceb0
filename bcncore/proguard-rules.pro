# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# R8 shrinker fix for missing `java.lang.invoke.StringConcatFactory`.
# Modern Java/Kotlin compilers use `StringConcatFactory` internally for optimized string concatenation (like using "+" or string templates).
# This class is not present in older Android runtimes, so R8 fails during shrinking/optimization.
# To fix this, we enable core library desugaring to provide runtime support for Java 8+ APIs on older Android versions.
# Use `-dontwarn` to suppress missing class warning since desugaring handles the functionality.
# Now suppressing warning from R8 about `StringConcatFactory` missing is safe with desugaring enabled.
-dontwarn java.lang.invoke.StringConcatFactory
