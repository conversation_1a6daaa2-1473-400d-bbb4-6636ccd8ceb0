package com.resoluttech.core

import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.config.AmountLimitConfig
import com.resoluttech.core.utils.formatted
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.core.module.Module
import org.koin.dsl.module
import java.util.Locale

class AmountFormatterTests {

    private val testModule: Module = module {
        single {
            AmountLimitConfig(12, 2)
        }
    }

    @Before
    fun setup() {
        startKoin {
            modules(testModule)
        }
    }

    @Test
    fun testAmountFormatting() {
        val balance = Amount(balanceAmount, Currency("ZMW"))
        testFormatting(balance, null, defaultBalanceFormatted)
        testFormatting(balance, Locale.ITALY, italyBalanceFormatted)
        testFormatting(balance, Locale(NY_LITERAL), nyanjaBalanceFormatted)
        testFormatting(balance, Locale(EN_US_LITERAL), usENBalanceFormatted)
    }

    private fun testFormatting(balance: Amount, locale: Locale?, expectedValue: String) {
        val defaultFormattedValue = balance.formatted(locale)
        assertEquals(expectedValue, defaultFormattedValue)
    }

    @After
    fun destroy() {
        stopKoin()
    }

    private val balanceAmount = 12345678912345
    private val defaultBalanceFormatted = "1,234,567,891.23"
    private val nyanjaBalanceFormatted = "1,234,567,891.23"
    private val usENBalanceFormatted = "1,234,567,891.23"
    private val italyBalanceFormatted = "1.234.567.891,23"
}

private const val EN_US_LITERAL: String = "en_US"
private const val NY_LITERAL: String = "ny"
