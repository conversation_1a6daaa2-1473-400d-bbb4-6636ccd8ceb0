package com.resoluttech.core

import com.resoluttech.core.utils.getTimeDifference
import org.junit.Test
import java.time.Instant

class DateTimeUtilsTest {
    @Test
    fun testTimeDifference() {
        val currentTime = Instant.now().toEpochMilli()
        val addOneDay = currentTime + 86400000
        val timeDiffInDays = addOneDay.getTimeDifference(currentTime)
        assert(timeDiffInDays.diffDays == 1)

        val addOneHour = currentTime + 60 * 60 * 1000
        val timeDiffInHours = addOneHour.getTimeDifference(currentTime)
        assert(timeDiffInHours.diffHours == 1)

        val addOneMinute = currentTime + 60 * 1000
        val timeDiffInMinute = addOneMinute.getTimeDifference(currentTime)
        assert(timeDiffInMinute.diffMinutes == 1)
    }
}
