package com.resoluttech.core

import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.isDecimalRequired
import org.junit.Assert
import org.junit.Test

class UserFacingAmountTest {

    @Test
    fun isDecimalRequiredTest() {
        val serverAmount = 12345600L //  equal to 1234.56
        val isDecimalIncluded = isDecimalRequired(serverAmount)
        Assert.assertEquals(isDecimalIncluded, true)

        val serverAmount2 = 12340000L //  equal to 1234.00
        val isDecimalIncluded2 = isDecimalRequired(serverAmount2)
        Assert.assertEquals(isDecimalIncluded2, false)
    }

    @Test
    fun amountFormattingTest() {
        val formatterAmountStr: String
        val serverAmount = 45679800L
        formatterAmountStr = if (isDecimalRequired(serverAmount)) {
            String.format("%.2f", serverAmount.getUserFacingValue())
        } else {
            String.format("%.0f", serverAmount.getUserFacingValue())
        }
        Assert.assertEquals("4567.98", formatterAmountStr)
    }
}
