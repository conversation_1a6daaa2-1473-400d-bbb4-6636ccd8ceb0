package com.resoluttech.core.sendmoney

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import coil.load
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.transfers.SendMoneyExternalRecipient
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.CounterpartyId
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config.Companion.MAX_REMARKS_LENGTH
import com.resoluttech.core.listeners.DialogListener
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.sendmoney.RecipientIdentifierVM.RecipientDetails
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.uicomponents.OTPChargesAlertDialog
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.utils.CounterpartyRemarkSupportVM
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.SEND_MONEY_DIALOG_CODE
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.BCNAppAppAuthenticationProvider
import com.resoluttech.core.utils.centerCrop
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getImmediateBackstackDestinationId
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.setSystemBackPress
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.utils.toUri
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.LimitedCharacterEditText
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_NAME
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import timber.log.Timber
import java.time.Instant
import java.util.UUID

class SendMoneyToExternalUserConfirmationFragment :
    BaseFragment(),
    DialogListener,
    AlertDialog.ActionListener,
    AmountEditText.ErrorListener,
    AppAuthenticationStatus,
    BaseFragment.NetworkListener {

    private val sendMoneyToExternalUserConfirmationVM by lazy {
        ViewModelProvider(this)[SendMoneyToExternalUserConfirmationVM::class.java]
    }
    private val counterpartyRemarkSupportVM: CounterpartyRemarkSupportVM by navGraphViewModels(R.id.send_money_nav)
    private lateinit var dataBlockView: View
    private lateinit var userInfoView: View
    private lateinit var errorBlockView: View
    private lateinit var titleTV: TextView
    private lateinit var contactIV: ImageView
    private lateinit var contactCV: CardView
    private lateinit var subtitleTV: TextView
    private lateinit var amountET: AmountEditText
    private lateinit var privateRemarkET: LimitedCharacterEditText
    private lateinit var paymentPB: MaterialButton
    private var inlineErrorSnackbar: Snackbar? = null
    private lateinit var publicRemarkET: LimitedCharacterEditText
    private lateinit var publicRemarkTIL: TextInputLayout
    private lateinit var privateRemarkTIL: TextInputLayout
    private lateinit var recipientDetails: RecipientDetails
    private lateinit var currencySuffixTV: TextView
    private lateinit var itemWalletSelector: ConstraintLayout
    private lateinit var accountName: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_send_money, container, false)
        initViews(rootView)
        setRecipientDetail()
        setCurrencySuffix(rootView)
        setRemarksSupport()
        setupWalletSelector()
        showToolbar()
        return rootView
    }

    private fun initViews(rootView: View) {
        dataBlockView = rootView.findViewById(R.id.send_money_layout_enter_amount)
        userInfoView = rootView.findViewById(R.id.user_info)
        errorBlockView = rootView.findViewById(R.id.send_money_layout_error)
        titleTV = rootView.findViewById(R.id.person_name_tv)
        subtitleTV = rootView.findViewById(R.id.person_phone_number_tv)
        contactIV = rootView.findViewById(R.id.profile_image_view)
        contactCV = rootView.findViewById(R.id.cardView)
        amountET = rootView.findViewById(R.id.amount_edit_text)
        paymentPB = rootView.findViewById(R.id.pay_now_button)
        privateRemarkET = rootView.findViewById(R.id.private_remark_et)
        publicRemarkET = rootView.findViewById(R.id.public_remark_et)
        publicRemarkTIL = rootView.findViewById(R.id.public_remark_til)
        privateRemarkTIL = rootView.findViewById(R.id.private_remark_til)
        paymentPB.enable(false)
        amountET.setErrorListener(this)
        itemWalletSelector = rootView.findViewById(R.id.item_select_wallet)
        accountName = rootView.findViewById(R.id.account_name_tv)
        paymentPB.setOnClickListener {
            sendMoneyToExternalUserConfirmationVM.onAttemptPaymentButtonTapped(
                amountET.getFormattedAmount(),
                findNavController(),
                requireContext(),
            )
            hideKeyboard()
        }
    }

    private fun setRemarksSupport() {
        privateRemarkET.setTextInputLayout(privateRemarkTIL, MAX_REMARKS_LENGTH)
        counterpartyRemarkSupportVM.apply {
            if (maxPublicRemarkLength != null) {
                publicRemarkTIL.visibility = View.VISIBLE
                publicRemarkET.setTextInputLayout(publicRemarkTIL, maxPublicRemarkLength!!)
            } else {
                publicRemarkTIL.visibility = View.GONE
            }
        }
    }

    private fun setRecipientDetail() {
        recipientDetails =
            BundleUtils.getBundlesParcelable(
                requireArguments(),
                RecipientIdentifierFragment.RECIPIENT_IDENTIFIER_DATA,
                RecipientDetails::class.java,
            ) ?: throw IllegalStateException("Recipient details cannot be null.")
        if (recipientDetails.displayName.isNullOrEmpty()) {
            titleTV.text = recipientDetails.identifier
            subtitleTV.visibility = View.GONE
        } else {
            titleTV.text = recipientDetails.displayName
            subtitleTV.visibility = View.VISIBLE
            subtitleTV.text = recipientDetails.identifier
        }
        recipientDetails.image?.apply {
            contactIV.loadImage(toUri())
            contactIV.centerCrop()
        } ?: contactIV.load(R.drawable.ic_default_profile)
    }

    private fun setCurrencySuffix(rootView: View) {
        currencySuffixTV = rootView.findViewById(R.id.currency_suffix)
        currencySuffixTV.text = UserSharedPreference.getUserPrimaryCurrency()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.sendMoneyViewTitle))
        setSystemBackPress(getImmediateBackstackDestinationId())
        setupAuthenticationStatusListener()
        networkListenerCallback = this
        sendMoneyToExternalUserConfirmationVM.currentState.observe(
            viewLifecycleOwner,
            ::reactToState,
        )
        setupWalletSelectorListeners()
    }

    private fun setupWalletSelector() {
        val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            ?: throw IllegalStateException("Selected account cannot be null")
        accountName.text = selectedAccount.name
        sendMoneyToExternalUserConfirmationVM.selectedAccountId =
            UUID.fromString(selectedAccount.id)
        itemWalletSelector.setOnClickListener {
            findNavController().navigate(Uri.parse("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}&selectedAccountId=${sendMoneyToExternalUserConfirmationVM.selectedAccountId}"))
        }
    }

    private fun setupWalletSelectorListeners() {
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            sendMoneyToExternalUserConfirmationVM.selectedAccountId = it
        }
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>(
            KEY_SELECTED_ACCOUNT_NAME,
        )?.observe(
            viewLifecycleOwner,
        ) {
            accountName.text = it
        }
    }

    private fun setupAuthenticationStatusListener() {
        BCNAppAppAuthenticationProvider.setListener(this)
    }

    private fun reactToState(state: SendMoneyConfirmationScreenState) {
        when (state) {
            is SendMoneyConfirmationScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is SendMoneyConfirmationScreenState.Loading -> {
                handleLoadingState()
            }
            is SendMoneyConfirmationScreenState.InlineError -> {
                handleInlineError(state.uiError)
            }
            is SendMoneyConfirmationScreenState.FullscreenError -> {
                handleFullscreenError(
                    state.errorMessage,
                )
            }
            is SendMoneyConfirmationScreenState.SuccessfulTransaction -> {
                handleSuccessfulTransaction(
                    state.transactionId,
                    state.amount,
                    state.transactionSucceededAt,
                    state.transactionDescription,
                    state.transactionStatusItemDetail,
                )
            }
            is SendMoneyConfirmationScreenState.PendingTransaction -> {
                handlePendingTransaction(
                    state.transactionId,
                    state.amount,
                    state.transactionCreatedAt,
                    state.transactionDescription,
                    state.transactionStatusItemDetail,
                )
            }
            is SendMoneyConfirmationScreenState.TransactionFeesConfirmation -> {
                handleTransactionFeesConfirmationState(
                    state.amount,
                    state.transactionFees,
                    state.accountName,
                )
            }
            is SendMoneyConfirmationScreenState.UserAuthenticationFailed -> {
                handleInlineError(state.inlineError)
            }
            is SendMoneyConfirmationScreenState.UserAuthenticationSuccess -> {
                handleUserAuthenticationSuccess(state)
            }
            is SendMoneyConfirmationScreenState.RequestAuthentication -> {
                handleLoadingState()
            }
        }
    }

    private fun handleUserAuthenticationSuccess(state: SendMoneyConfirmationScreenState.UserAuthenticationSuccess) {
        sendMoneyToExternalUserConfirmationVM.onUserAuthenticated(
            amountET.getFormattedAmount(),
            state.recipient,
            requireContext(),
        )
    }

    private fun handlePendingTransaction(
        transactionId: String,
        amount: Amount,
        transactionCreatedAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        dismissProgressDialog()
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        sendMoneyToExternalUserConfirmationVM.handlePendingTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionCreatedAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
    }

    private fun handleSuccessfulTransaction(
        transactionId: String,
        amount: Amount,
        transactionSucceededAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        dismissProgressDialog()
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        sendMoneyToExternalUserConfirmationVM.handleSuccessfulTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionSucceededAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
    }

    private fun handleAcceptInputState() {
        dismissDialog()
        dismissProgressDialog()
        showStateView(dataBlockView)
    }

    private fun handleLoadingState() {
        dismissDialog()
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleTransactionFeesConfirmationState(
        amount: Amount,
        transactionFees: Amount,
        accountName: String,
    ) {
        dismissProgressDialog()
        val transactionFeeAlertDialog =
            OTPChargesAlertDialog.newInstance(transactionFees, amount, accountName)
        transactionFeeAlertDialog.setArguments(false)
        transactionFeeAlertDialog.show(childFragmentManager, OTPChargesAlertDialog.TAG)
    }

    private fun handleInlineError(uiError: UIError) {
        dismissDialog()
        dismissProgressDialog()
        hideKeyboard()
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(uiError.errorMessage)
            }
            ErrorType.DIALOG -> {
                showInlineErrorDialog(uiError.errorTitle, uiError.errorMessage, uiError.errorCode)
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineErrorSnackBar(errorMessage: String) {
        inlineErrorSnackbar = Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackbar?.let {
            it.setAction(R.string.alertActionDismiss) {
                sendMoneyToExternalUserConfirmationVM.inlineErrorDismissed()
            }
            it.show()
        }
    }

    private fun showInlineErrorDialog(errorTitle: String, errorMessage: String, errorCode: Int?) {
        dismissProgressDialog()
        showErrorDialog(
            errorTitle,
            errorMessage,
            errorCode ?: SEND_MONEY_DIALOG_CODE,
            forceOutUserDestination = ForceOutUserDestination.HOME,
        )
    }

    private fun handleFullscreenError(
        errorMessage: String,
    ) {
        dismissProgressDialog()
        showStateView(errorBlockView)
        dismissDialog()

        val retryTV = requireActivity().findViewById<TextView>(R.id.retry_message)
        retryTV.text = errorMessage

        val retryButton = requireActivity().findViewById<MaterialButton>(R.id.retry_button)
        retryButton.setOnClickListener {
            showStateView(dataBlockView)
        }
    }

    private fun dismissDialog() {
        val frag = childFragmentManager.findFragmentByTag(OTPChargesAlertDialog.TAG)
        if (frag is DialogFragment && isVisible) {
            frag.dismiss()
        } else {
            Timber.tag(TAG).i("Trying to dismiss dialog when it is not showing.")
        }
    }

    private fun showStateView(view: View) {
        when (view) {
            paymentPB -> {
                errorBlockView.visibility = View.GONE
                dataBlockView.visibility = View.VISIBLE
                userInfoView.visibility = View.VISIBLE
                dismissProgressDialog()
                showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
            }
            errorBlockView -> {
                errorBlockView.visibility = View.VISIBLE
                dataBlockView.visibility = View.GONE
                userInfoView.visibility = View.GONE
                dismissProgressDialog()
            }
            dataBlockView -> {
                errorBlockView.visibility = View.GONE
                dataBlockView.visibility = View.VISIBLE
                userInfoView.visibility = View.VISIBLE
                dismissProgressDialog()
            }
        }
    }

    override fun onProceed() {
        sendMoneyToExternalUserConfirmationVM.onTransactionFeesDialogProceedClicked(
            requireContext(),
            privateRemarkET.getInputText(),
            publicRemarkET.getInputText(),
        )
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    override fun onDeclined() {
        sendMoneyToExternalUserConfirmationVM.onTransactionFeesDialogDismissed()
    }

    override fun onClickTermsAndCondition() {
        throw IllegalStateException("onClickTermsAndCondition action is not applicable")
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                leoServerExceptionHandler(findNavController())
            }

            SEND_MONEY_TO_COUNTERPARTY_FAILED -> {
                sendMoneyToExternalUserConfirmationVM.onMoneyTransferDeclinedByCounterparty(
                    findNavController(),
                )
            }
            else -> {
                sendMoneyToExternalUserConfirmationVM.inlineErrorDismissed()
                Timber.tag(TAG)
                    .i("Positive action occurred on alert dialog having id $dialogId.")
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onAmountInput(isValid: Boolean) {
        paymentPB.enable(isValid)
    }

    override fun onAppAuthenticationSuccessful(navController: NavController) {
        if (sendMoneyToExternalUserConfirmationVM.currentState.value is SendMoneyConfirmationScreenState.RequestAuthentication) {
            sendMoneyToExternalUserConfirmationVM.onUserAuthenticationSuccessful(
                SendMoneyExternalRecipient(
                    recipientDetails.identifier,
                    CounterpartyId(recipientDetails.couterpartyId),
                ),
            )
        } else {
            sendMoneyToExternalUserConfirmationVM.onAuthenticationSuccess()
        }
    }

    override fun onAppAuthenticationFailed(navController: NavController) {
        sendMoneyToExternalUserConfirmationVM.onUserAuthenticationFailed(requireContext())
    }

    override fun onAppAuthenticationCancelled(navController: NavController) {
        sendMoneyToExternalUserConfirmationVM.onUserAuthenticationCancelled()
    }

    companion object {
        const val SEND_MONEY_TO_COUNTERPARTY_FAILED: Int = 100
    }

    override fun onNetworkAvailable() {
        sendMoneyToExternalUserConfirmationVM.inlineErrorDismissed()
    }
}

private const val TAG = "SendMoneyToExternalUserConfirmationFragment"
