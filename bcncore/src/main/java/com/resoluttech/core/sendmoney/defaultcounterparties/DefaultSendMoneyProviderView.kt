package com.resoluttech.core.sendmoney.defaultcounterparties

import android.content.Context
import android.view.View
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.resoluttech.core.sendmoney.SendMoneyProviderView

class DefaultSendMoneyProviderView(
    private val providerName: String,
) : SendMoneyProviderView {
    override fun getProviderView(context: Context): View {
        val defaultView = View.inflate(context, R.layout.item_providers, null)
        val providerNameTV: TextView = defaultView.findViewById(R.id.provider_name)

        providerNameTV.text = providerName
        return defaultView
    }
}
