package com.resoluttech.core.sendmoney.config

import com.resoluttech.core.sendmoney.CounterpartyViewType

/**
 * This class provides capability to app module to configure money transfer counterparties.
 *
 * @param currentInstallationProvider, counterparty that represents the bank/account for which this
 * variant of the app is built.
 */
class SendMoneyCounterpartyConfig(
    val currentInstallationProvider: CounterpartyViewType,
)
