package com.resoluttech.core.sendmoney

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.core.rpcexceptionhandlers.SendMoneyRPCExceptionHandler
import com.resoluttech.core.transfers.peertopeer.SendMoneyRepository
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber

class SendMoneyVM : ViewModel() {

    val counterpartyViewHandler: CounterpartyViewHandler = CounterpartyViewHandler()
    private var _currentState: MutableLiveData<SendMoneyState> =
        MutableLiveData(SendMoneyState.Loading)
    val currentState: LiveData<SendMoneyState> = _currentState
    private val repository = SendMoneyRepository()

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private var fetchedCounterpartiesJob: Job? = null

    fun getCounterparties(context: Context) {
        if (fetchedCounterpartiesJob != null) {
            Timber.w("Already running a get counterparty job. Ignoring duplicate request.")
            return
        }
        fetchedCounterpartiesJob = vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = repository.getSendMoneyToExternalUserCounterparties()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleRPCResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getSendMoneyToExternalUserDestinationErrorMessage(
                                response.error,
                            )
                                .apply {
                                    handleRPCException(
                                        errorTitle,
                                        errorMessage,
                                        errorCode,
                                    )
                                }
                        }
                    }
                },
                handleException = {
                    handleRPCException(
                        it.errorTitle,
                        it.errorMessage,
                        it.errorCode,
                    )
                },
            )
        }
    }

    fun onRetryClicked(context: Context) {
        _currentState.postValue(SendMoneyState.Loading)
        fetchedCounterpartiesJob = null
        getCounterparties(context)
    }

    private fun handleRPCResponse(response: GetSendMoneyToExternalUserCounterpartiesRPC.Response) {
        _currentState.postValue(SendMoneyState.Data(response))
    }

    private fun handleRPCException(errorTitle: String, errorMessage: String, errorCode: Int?) {
        if (errorCode != null && errorCode != DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
            _currentState.postValue(SendMoneyState.Error(errorTitle, errorMessage, errorCode))
        } else {
            _currentState.postValue(SendMoneyState.FullScreenError(errorMessage))
        }
    }
}

sealed class SendMoneyState {
    data class Data(val response: GetSendMoneyToExternalUserCounterpartiesRPC.Response) :
        SendMoneyState()

    object Loading : SendMoneyState()
    data class FullScreenError(val errorMessage: String) : SendMoneyState()
    data class Error(val errorTitle: String, val errorMessage: String, val errorCode: Int) : SendMoneyState()
}
