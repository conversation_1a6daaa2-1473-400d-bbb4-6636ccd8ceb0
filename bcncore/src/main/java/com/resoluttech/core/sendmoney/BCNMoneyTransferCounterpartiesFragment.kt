package com.resoluttech.core.sendmoney

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.home.HomeDataPersistor
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.views.BaseFragment
import org.koin.android.ext.android.inject

class BCNMoneyTransferCounterpartiesFragment : BaseFragment() {

    private lateinit var moneyTransferToOtherAccountTV: TextView
    private lateinit var moneyTransferToSelfAccountTV: TextView
    private lateinit var noAccessSendMoneyWithinBCNTV: TextView
    private val homeDataPersistor: HomeDataPersistor by inject()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.bcn_money_transfer_counterparties_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        setDefaultToolbar(getString(R.string.moneyTransferViewTitle))
        setAnotherAccountTransfer()
        setSelfAccountTransfer()
    }

    private fun setAnotherAccountTransfer() {
        val isAgent = homeDataPersistor.checkIfUserIsAgent()
        if (isAgent != null) {
            if (isAgent) {
                moneyTransferToOtherAccountTV.disable()
                noAccessSendMoneyWithinBCNTV.visibility = View.VISIBLE
            } else {
                moneyTransferToOtherAccountTV.enable()
                noAccessSendMoneyWithinBCNTV.visibility = View.GONE
                moneyTransferToOtherAccountTV.setOnClickListener {
                    findNavController().navigateSafe(R.id.action_bcn_counterparty_to_qr_code)
                }
            }
        } else {
            throw IllegalStateException("isAgent value is not set in the shared preference.")
        }
    }

    private fun setSelfAccountTransfer() {
        if (SelectedAccountHelper.getPersistedAccounts().size > 1) {
            moneyTransferToSelfAccountTV.visibility = View.VISIBLE
            moneyTransferToSelfAccountTV.setOnClickListener {
                findNavController().navigateSafe(R.id.action_bcn_counterparty_to_self_transfer)
            }
        } else {
            moneyTransferToSelfAccountTV.visibility = View.GONE
        }
    }

    private fun initView(view: View) {
        view.apply {
            moneyTransferToOtherAccountTV = findViewById(R.id.to_another_person_tv)
            moneyTransferToSelfAccountTV = findViewById(R.id.to_own_account_tv)
            noAccessSendMoneyWithinBCNTV = findViewById(R.id.no_access_tv)
        }
    }
}
