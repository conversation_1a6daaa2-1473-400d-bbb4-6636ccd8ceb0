package com.resoluttech.core.sendmoney

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.Gravity
import android.widget.LinearLayout
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.bcn.transfers.SendMoneyCounterparty
import com.resoluttech.bcncore.R
import com.resoluttech.core.sendmoney.SendMoneyCounterpartyFragment.Companion.KEY_COUNTERPARTY
import com.resoluttech.core.sendmoney.config.SendMoneyCounterpartyConfig
import com.resoluttech.core.sendmoney.defaultcounterparties.DefaultSendMoneyProviderView
import com.resoluttech.core.utils.CounterpartyRemarkSupportVM
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.utils.navigateSafe
import kotlinx.parcelize.Parcelize
import org.koin.java.KoinJavaComponent
import timber.log.Timber

class CounterpartyViewHandler {

    private val sendMoneyCounterpartyConfig: SendMoneyCounterpartyConfig by KoinJavaComponent.inject(
        SendMoneyCounterpartyConfig::class.java,
    )

    /**
     * This method would add providers to main provider section and continue with the next actions.
     * @param context: Context that would be used in inflating the layouts.
     * @param parentContainer: parent container for adding main provider's view.
     * @param navController: Navigation controller to navigate to details screen.
     */
    fun addCurrentInstallationProviderView(
        context: Context,
        parentContainer: LinearLayout,
        navController: NavController,
    ) {
        val containerLayout = getWrapperLayout(context)

        containerLayout.addView(
            sendMoneyCounterpartyConfig.currentInstallationProvider.sendMoneyProviderView.getProviderView(
                context,
            ),
        )
        containerLayout.setOnClickListener {
            navController.navigateSafe(R.id.action_sendMoneyFragment_to_BCNMoneyTransferCounterpartiesFragment2)
        }

        parentContainer.addView(containerLayout)
    }

    /**
     * This method would add providers to other provider section and continue with the next actions.
     * @param context: Context that would be used in inflating the layouts.
     * @param parentContainer: parent container for adding other provider's view.
     * @param navController: Navigation controller to navigate to details screen.
     */
    fun addNonInstallationProviderView(
        context: Context,
        parentContainer: LinearLayout,
        navController: NavController,
        response: GetSendMoneyToExternalUserCounterpartiesRPC.Response,
        counterpartyRemarkSupportVM: CounterpartyRemarkSupportVM,
    ) {
        parentContainer.removeAllViews()
        response.counterparties.forEach { counterparty ->
            val containerLayout = getWrapperLayout(context)

            containerLayout.addView(
                DefaultSendMoneyProviderView(
                    counterparty.displayName.localisedText(LocaleManager.getCurrentLocale(context)),
                ).getProviderView(context),
            )
            containerLayout.setOnClickListener {
                val args = Bundle()
                args.putParcelable(
                    KEY_COUNTERPARTY,
                    SendMoneyConterparty(
                        counterparty.displayName.localisedText(LocaleManager.getCurrentLocale(context)),
                        counterparty.id.id,
                        getExternalRecipientType(counterparty.recipientIdentifierType),
                        counterparty.supportsLookup,
                        counterparty.currency.currencyCode,
                    ),
                )
                setCounterpartyRemarksSupport(counterparty, counterpartyRemarkSupportVM)
                navController.navigateSafe(
                    R.id.action_sendMoneyFragment_to_recipientIdentifierFragment,
                    args,
                )
            }

            parentContainer.addView(containerLayout)
        }
    }

    private fun setCounterpartyRemarksSupport(
        counterparty: SendMoneyCounterparty,
        counterpartyRemarkSupportVM: CounterpartyRemarkSupportVM,
    ) {
        if (counterparty.maxPublicRemarkLength != null) {
            counterpartyRemarkSupportVM.maxPublicRemarkLength = counterparty.maxPublicRemarkLength!!
        } else {
            Timber.tag(TAG).i("Counterparty doesn't support public remark.")
        }
    }

    private fun getExternalRecipientType(recipientIdentifierType: SendMoneyCounterparty.RecipientIdentifierType): RecipientIdentifierType {
        return when (recipientIdentifierType) {
            SendMoneyCounterparty.RecipientIdentifierType.ACCOUNT_ID -> RecipientIdentifierType.AccountId
            SendMoneyCounterparty.RecipientIdentifierType.PHONE_NUMBER -> RecipientIdentifierType.PhoneNumber
        }
    }

    private fun getWrapperLayout(
        context: Context,
        width: Int = LinearLayout.LayoutParams.MATCH_PARENT,
        height: Int = LinearLayout.LayoutParams.WRAP_CONTENT,
        orientation: Int = LinearLayout.VERTICAL,
    ): LinearLayout {
        val wrapperLayout = LinearLayout(context)
        val layoutParams = LinearLayout.LayoutParams(width, height)
        layoutParams.gravity = Gravity.CENTER_VERTICAL
        wrapperLayout.layoutParams = layoutParams
        wrapperLayout.orientation = orientation

        return wrapperLayout
    }
}

sealed class RecipientIdentifierType : Parcelable {
    @Parcelize
    object PhoneNumber : RecipientIdentifierType()

    @Parcelize
    object AccountId : RecipientIdentifierType()
}

@Parcelize
data class SendMoneyConterparty(
    val displayName: String,
    val id: String,
    val recipientIdentifierType: RecipientIdentifierType,
    val supportsLookup: Boolean,
    val currency: String,
) : Parcelable

private const val BOTTOM_BORDER_HEIGHT: Int = 1
private const val BOTTOM_BORDER_START_MARGIN: Int = 16
private const val TAG = "CounterpartyViewHandler"
