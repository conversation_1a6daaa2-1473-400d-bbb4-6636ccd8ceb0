package com.resoluttech.core.sendmoney

import android.os.Build
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.sendmoney.SendMoneyCounterpartyFragment.Companion.KEY_COUNTERPARTY
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DialogCodes.Companion.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID
import com.resoluttech.core.utils.DialogCodes.Companion.RECIPIENT_IDENTIFIER_DIALOG_CODE
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseFragment

class RecipientIdentifierFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val recipientIdentifierVM: RecipientIdentifierVM by lazy {
        ViewModelProvider(this)[RecipientIdentifierVM::class.java]
    }

    private lateinit var identifierET: EditText
    private lateinit var confirmPayeeET: EditText
    private lateinit var payeeNameET: EditText
    private lateinit var nextButton: MaterialButton
    private var inlineErrorSnackbar: Snackbar? = null
    private lateinit var enterRecipientIdentifierTIL: TextInputLayout
    private lateinit var confirmPayeeIdentifierTIL: TextInputLayout
    private lateinit var payeeNameIdentifierTIL: TextInputLayout
    private lateinit var sendMoneyCounterparty: SendMoneyConterparty

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView =
            inflater.inflate(R.layout.fragment_recipient_identifier_screen, container, false)
        setExternalRecipientDestination()
        initView(rootView)
        setNextButton()
        setupTextWatcher()
        setRecipientIdentifierTILHint()
        setConfirmPayeeIdentifierTILHint()
        return rootView
    }

    private fun setupTextWatcher() {
        identifierET.addTextChangedListener {
            if (sendMoneyCounterparty.supportsLookup) {
                validInput()
            }
        }
    }

    private fun validInput() {
        if (identifierET.text.isNotBlank()) {
            nextButton.enable()
            nextButton.addContentDescriptionString(
                R.string.axCounterPartyLookupNextHint,
                requireContext(),
            )
        } else {
            nextButton.disable()
            nextButton.addContentDescriptionString(
                R.string.axCounterPartyLookupNextDisabled,
                requireContext(),
            )
        }
    }

    private fun initView(rootView: View) {
        identifierET = rootView.findViewById(R.id.identifier_edit_text)
        confirmPayeeET = rootView.findViewById(R.id.confirm_payee_identifier_edit_text)
        payeeNameET = rootView.findViewById(R.id.payee_name_edit_text)
        nextButton = rootView.findViewById(R.id.identifier_next_button)
        enterRecipientIdentifierTIL = rootView.findViewById(R.id.enter_payee_identifier_text)
        confirmPayeeIdentifierTIL = rootView.findViewById(R.id.confirm_payee_identifier_til)
        payeeNameIdentifierTIL = rootView.findViewById(R.id.payee_name_til)
    }

    private fun setExternalRecipientDestination() {
        requireArguments().apply {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                sendMoneyCounterparty =
                    getParcelable(KEY_COUNTERPARTY, SendMoneyConterparty::class.java)
                        ?: throw IllegalArgumentException("External recipient destination cannot be null")
            } else {
                @Suppress("DEPRECATION")
                sendMoneyCounterparty = getParcelable(KEY_COUNTERPARTY)
                    ?: throw IllegalArgumentException("External recipient destination cannot be null")
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.selectCounterpartyUserViewTitle))
        networkListenerCallback = this
        recipientIdentifierVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun setNextButton() {
        nextButton.setOnClickListener {
            hideKeyboard()
            recipientIdentifierVM.onRecipientIdentifierEntered(
                requireContext(),
                identifierET.text.toString(),
                confirmPayeeET.text.toString(),
                payeeNameET.text.toString(),
                sendMoneyCounterparty,
                findNavController(),
            )
        }
    }

    private fun setRecipientIdentifierTILHint() {
        enterRecipientIdentifierTIL.hint = when (sendMoneyCounterparty.recipientIdentifierType) {
            RecipientIdentifierType.PhoneNumber -> {
                if (sendMoneyCounterparty.supportsLookup) {
                    identifierET.inputType = InputType.TYPE_CLASS_PHONE
                } else {
                    identifierET.inputType =
                        InputType.TYPE_CLASS_NUMBER or
                        InputType.TYPE_NUMBER_VARIATION_PASSWORD
                }
                getString(R.string.selectCounterpartyUserPhoneNumber)
            }
            RecipientIdentifierType.AccountId -> {
                if (sendMoneyCounterparty.supportsLookup) {
                    identifierET.inputType =
                        InputType.TYPE_CLASS_TEXT
                } else {
                    identifierET.inputType =
                        InputType.TYPE_CLASS_TEXT or
                        InputType.TYPE_TEXT_VARIATION_PASSWORD
                }
                getString(R.string.selectCounterpartyUserAccountId)
            }
        }
    }

    private fun setConfirmPayeeIdentifierTILHint() {
        if (!sendMoneyCounterparty.supportsLookup) {
            confirmPayeeIdentifierTIL.visibility = View.VISIBLE
            payeeNameIdentifierTIL.visibility = View.VISIBLE
            confirmPayeeIdentifierTIL.hint = when (sendMoneyCounterparty.recipientIdentifierType) {
                RecipientIdentifierType.PhoneNumber -> {
                    confirmPayeeET.inputType = InputType.TYPE_CLASS_PHONE
                    getString(R.string.confirmCounterpartyUserPhoneNumber)
                }
                RecipientIdentifierType.AccountId -> {
                    confirmPayeeET.inputType = InputType.TYPE_CLASS_TEXT
                    getString(R.string.confirmCounterpartyUserAccountId)
                }
            }
        } else {
            confirmPayeeIdentifierTIL.visibility = View.GONE
            payeeNameIdentifierTIL.visibility = View.INVISIBLE
            nextButton.disable()
        }
    }

    private fun reactToState(state: RecipientIdentifierScreenState) {
        when (state) {
            is RecipientIdentifierScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is RecipientIdentifierScreenState.InlineError -> {
                handleInlineError(state.error)
            }
            is RecipientIdentifierScreenState.Loading -> {
                handleLoadingState()
            }
            is RecipientIdentifierScreenState.Data -> {
                handleDataState(state)
            }
        }
    }

    private fun handleDataState(state: RecipientIdentifierScreenState.Data) {
        dismissProgressDialog()
        recipientIdentifierVM.onConfirmedRecipient(state.recipientDetail, findNavController())
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleInlineError(error: UIError) {
        dismissProgressDialog()
        when (error.type) {
            ErrorType.SNACKBAR -> showInlineSnackbar(error.errorMessage)
            ErrorType.DIALOG -> {
                showInlineDialog(
                    error.errorTitle,
                    error.errorMessage,
                    error.errorCode ?: RECIPIENT_IDENTIFIER_DIALOG_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineSnackbar(errorMessage: String) {
        inlineErrorSnackbar = Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackbar?.let {
            it.setAction(R.string.alertActionDismiss) {
                recipientIdentifierVM.inlineErrorDismissed()
            }
            it.show()
        }
    }

    private fun showInlineDialog(title: String, errorMessage: String, errorCode: Int) {
        showErrorDialog(
            title,
            errorMessage,
            errorCode,
            forceOutUserDestination = ForceOutUserDestination.HOME,
        )
    }

    companion object {
        const val RECIPIENT_IDENTIFIER_DATA: String = "recipient_identifier_data"
    }

    override fun onPositiveAction(dialogId: Int) {
        if (dialogId == LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
            leoServerExceptionHandler(findNavController())
        } else {
            recipientIdentifierVM.onErrorDialogDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        recipientIdentifierVM.onErrorDialogDismissed()
    }
}
