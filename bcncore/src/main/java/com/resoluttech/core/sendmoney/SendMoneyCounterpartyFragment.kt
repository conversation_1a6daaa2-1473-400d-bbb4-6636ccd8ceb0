package com.resoluttech.core.sendmoney

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.home.HomeDataPersistor
import com.resoluttech.core.sendmoney.config.SendMoneyCounterpartyConfig
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.CounterpartyRemarkSupportVM
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseFragment
import org.koin.android.ext.android.inject

class SendMoneyCounterpartyFragment :
    BaseFragment(),
    BaseFragment.NetworkListener,
    AlertDialog.ActionListener {

    private val sendMoneyVM: SendMoneyVM by navGraphViewModels(R.id.send_money_nav)
    private val counterpartyRemarkSupportVM: CounterpartyRemarkSupportVM by navGraphViewModels(R.id.send_money_nav)
    private lateinit var currentInstallationProvider: TextView
    private lateinit var toAnotherPersonTV: TextView
    private lateinit var noAccessSendMoneyWithinBCNTV: TextView
    private lateinit var toOwnWalletTV: TextView
    private lateinit var nonInstallationProvidersContainer: LinearLayout
    private lateinit var layoutErrorStateView: View
    private lateinit var errorMessageTV: TextView
    private lateinit var retryButton: MaterialButton
    private lateinit var noProviderFoundContainer: LinearLayout
    private lateinit var counterpartyPB: ProgressBar
    private val homeDataPersistor: HomeDataPersistor by inject()
    private val sendMoneyCounterpartyConfig: SendMoneyCounterpartyConfig by inject()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_send_money_counterparty, container, false)
        setViews(rootView)
        addMainProviders()
        setRetryButton()
        return rootView
    }

    private fun setViews(rootView: View) {
        nonInstallationProvidersContainer =
            rootView.findViewById(R.id.counterparty_section)
        layoutErrorStateView = rootView.findViewById(R.id.select_counterparty_error)
        errorMessageTV = rootView.findViewById(R.id.error_message)
        retryButton = rootView.findViewById(R.id.retry_button)
        noProviderFoundContainer = rootView.findViewById(R.id.no_provider_found_section)
        counterpartyPB = rootView.findViewById(R.id.progress_bar)
        currentInstallationProvider = rootView.findViewById(R.id.current_installation_provider_tv)
        toAnotherPersonTV = rootView.findViewById(R.id.to_another_person_tv)
        noAccessSendMoneyWithinBCNTV = rootView.findViewById(R.id.error_agent_tv)
        toOwnWalletTV = rootView.findViewById(R.id.to_own_wallet_tv)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.selectDestinationViewTitle))
        networkListenerCallback = this
        sendMoneyVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        sendMoneyVM.getCounterparties(requireContext())
    }

    private fun setRetryButton() {
        retryButton.setOnClickListener {
            sendMoneyVM.onRetryClicked(requireContext())
        }
    }

    private fun reactToState(state: SendMoneyState) {
        when (state) {
            is SendMoneyState.Loading -> handleLoadingState()
            is SendMoneyState.Data -> handleDataState(state.response)
            is SendMoneyState.FullScreenError -> handleFullScreenErrorState(state.errorMessage)
            is SendMoneyState.Error -> handleErrorState(state.errorTitle, state.errorMessage, state.errorCode)
        }
    }

    private fun handleLoadingState() {
        counterpartyPB.visibility = View.VISIBLE
        layoutErrorStateView.visibility = View.GONE
        noProviderFoundContainer.visibility = View.GONE
    }

    private fun handleDataState(response: GetSendMoneyToExternalUserCounterpartiesRPC.Response) {
        counterpartyPB.visibility = View.GONE
        layoutErrorStateView.visibility = View.GONE
        nonInstallationProvidersContainer.visibility = View.VISIBLE
        noProviderFoundContainer.visibility = View.GONE
        addOtherProviders(response)
    }

    private fun handleFullScreenErrorState(errorMessage: String) {
        nonInstallationProvidersContainer.visibility = View.GONE
        counterpartyPB.visibility = View.GONE
        errorMessageTV.text = errorMessage
        layoutErrorStateView.visibility = View.VISIBLE
        noProviderFoundContainer.visibility = View.GONE
    }

    private fun handleErrorState(errorTitle: String, errorMessage: String, errorCode: Int) {
        showErrorDialog(
            errorTitle,
            message = errorMessage,
            dialogId = errorCode,
        )
    }

    private fun addMainProviders() {
        currentInstallationProvider.text =
            sendMoneyCounterpartyConfig.currentInstallationProvider.counterparty.name
        val isAgent = homeDataPersistor.checkIfUserIsAgent()
        if (isAgent != null) {
            if (isAgent) {
                toAnotherPersonTV.disable()
                noAccessSendMoneyWithinBCNTV.visibility = View.VISIBLE
            } else {
                toAnotherPersonTV.enable()
                noAccessSendMoneyWithinBCNTV.visibility = View.GONE
                toAnotherPersonTV.setOnClickListener {
                    findNavController().navigate(R.id.peer_to_peer_transfer_nav)
                }
            }
        }
        if (SelectedAccountHelper.getPersistedAccounts().size > 1) {
            toOwnWalletTV.visibility = View.VISIBLE
            toOwnWalletTV.setOnClickListener {
                findNavController().navigate(R.id.account_to_account_nav)
            }
        } else {
            toOwnWalletTV.visibility = View.GONE
        }
    }

    private fun addOtherProviders(response: GetSendMoneyToExternalUserCounterpartiesRPC.Response) {
        if (response.counterparties.isEmpty()) {
            nonInstallationProvidersContainer.visibility = View.GONE
            counterpartyPB.visibility = View.GONE
            noProviderFoundContainer.visibility = View.VISIBLE
        } else {
            sendMoneyVM.counterpartyViewHandler.addNonInstallationProviderView(
                requireContext(),
                nonInstallationProvidersContainer,
                findNavController(),
                response,
                counterpartyRemarkSupportVM,
            )
        }
    }

    override fun onResume() {
        super.onResume()
        counterpartyRemarkSupportVM.onReset()
    }

    companion object {
        const val KEY_COUNTERPARTY: String = "counterparty"
    }

    override fun onPositiveAction(dialogId: Int) {
        // Default action is already handled
    }

    override fun onNegativeAction(dialogId: Int) {
        // Default action is already handled
    }

    override fun onNetworkAvailable() {
        sendMoneyVM.onRetryClicked(requireContext())
    }
}
