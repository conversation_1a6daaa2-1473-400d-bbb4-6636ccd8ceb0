package com.resoluttech.core.sendmoney

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.LookupRecipientAtCounterpartyRPC
import com.resoluttech.bcn.transfers.SendMoneyExternalRecipient
import com.resoluttech.bcn.types.CounterpartyId
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.SendMoneyRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.peertopeer.SendMoneyRepository
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.parcelize.Parcelize
import java.net.URL

class RecipientIdentifierVM : ViewModel() {

    private var _currentState: MutableLiveData<RecipientIdentifierScreenState> =
        MutableLiveData(RecipientIdentifierScreenState.AcceptInput)
    val currentState: LiveData<RecipientIdentifierScreenState> = _currentState
    private val vmIOScope = viewModelScope + Dispatchers.IO

    fun onRecipientIdentifierEntered(
        context: Context,
        identifier: String,
        confirmIdentifier: String,
        payeeName: String,
        sendMoneyCounterparty: SendMoneyConterparty,
        navController: NavController,
    ) {
        if (identifier.isBlank()) {
            when (sendMoneyCounterparty.recipientIdentifierType) {
                is RecipientIdentifierType.PhoneNumber -> {
                    _currentState.postValue(
                        RecipientIdentifierScreenState.InlineError(
                            UIError(
                                ErrorType.SNACKBAR,
                                context.getString(R.string.alertTitleCounterpartyMissingDetails),
                                context.getString(R.string.alertMessageMissingRecipientNumber),
                            ),
                        ),
                    )
                }
                is RecipientIdentifierType.AccountId -> {
                    _currentState.postValue(
                        RecipientIdentifierScreenState.InlineError(
                            UIError(
                                ErrorType.SNACKBAR,
                                context.getString(R.string.alertTitleCounterpartyMissingDetails),
                                context.getString(R.string.alertMessageMissingRecipientId),
                            ),
                        ),
                    )
                }
            }
        } else {
            _currentState.postValue(RecipientIdentifierScreenState.Loading)
            if (sendMoneyCounterparty.supportsLookup) {
                lookupRecipientDetails(identifier, sendMoneyCounterparty, context)
            } else {
                if (identifier != confirmIdentifier) {
                    val errorMessage = when (sendMoneyCounterparty.recipientIdentifierType) {
                        RecipientIdentifierType.PhoneNumber -> context.getString(R.string.alertMessageCounterpartyNumbersDoNotMatch)
                        RecipientIdentifierType.AccountId -> context.getString(R.string.alertMessageCounterpartyIdsDoNotMatch)
                    }
                    val errorTitle = when (sendMoneyCounterparty.recipientIdentifierType) {
                        RecipientIdentifierType.PhoneNumber -> context.getString(R.string.alertTitleCounterpartyNumbersDoNotMatch)
                        RecipientIdentifierType.AccountId -> context.getString(R.string.alertTitleCounterpartyIdsDoNotMatch)
                    }
                    _currentState.postValue(
                        RecipientIdentifierScreenState.InlineError(
                            UIError(
                                ErrorType.DIALOG,
                                errorTitle,
                                errorMessage,
                            ),
                        ),
                    )
                } else {
                    onConfirmedRecipient(
                        RecipientDetails(
                            payeeName.ifBlank { null },
                            null,
                            sendMoneyCounterparty.id,
                            identifier,
                            sendMoneyCounterparty.currency,
                        ),
                        navController,
                    )
                }
            }
        }
    }

    private fun lookupRecipientDetails(
        identifier: String,
        sendMoneyConterparty: SendMoneyConterparty,
        context: Context,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val result = SendMoneyRepository().lookupRecipientAtCounterparty(
                        SendMoneyExternalRecipient(identifier, CounterpartyId(sendMoneyConterparty.id)),
                    )
                    when (result) {
                        is LeoRPCResult.LeoResponse -> {
                            handleLookupRecipientAtCounterpartyRPCResponse(
                                result.response,
                                identifier,
                                sendMoneyConterparty.id,
                                sendMoneyConterparty.currency,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.lookupRecipientAtCounterpartyRPC(
                                result.error,
                                context,
                                sendMoneyConterparty,
                                identifier,
                            ).apply {
                                showError(this)
                            }
                        }
                    }
                },
                handleException = ::showError,
            )
        }
    }

    fun onConfirmedRecipient(recipientDetail: RecipientDetails, navController: NavController) {
        val args = Bundle()
        args.putParcelable(RecipientIdentifierFragment.RECIPIENT_IDENTIFIER_DATA, recipientDetail)
        navController.navigate(
            R.id.action_recipientIdentifierFragment_to_confirmationFragment,
            args,
        )
        _currentState.postValue(RecipientIdentifierScreenState.AcceptInput)
    }

    private fun handleLookupRecipientAtCounterpartyRPCResponse(
        response: LookupRecipientAtCounterpartyRPC.Response,
        identifier: String,
        counterpartyId: String,
        currency: String,
    ) {
        _currentState.postValue(
            RecipientIdentifierScreenState.Data(
                RecipientDetails(
                    response.recipientName,
                    response.recipientProfileImage?.getSpecificResolutionImageURL(),
                    counterpartyId,
                    identifier,
                    currency,
                ),
            ),
        )
    }

    fun inlineErrorDismissed() {
        _currentState.postValue(RecipientIdentifierScreenState.AcceptInput)
    }

    private fun showError(uiError: UIError) {
        _currentState.postValue(RecipientIdentifierScreenState.InlineError(uiError))
    }

    fun onErrorDialogDismissed() {
        _currentState.postValue(RecipientIdentifierScreenState.AcceptInput)
    }

    @Parcelize
    data class RecipientDetails(
        val displayName: String?,
        val image: URL?,
        val couterpartyId: String,
        val identifier: String,
        val currency: String,
    ) : Parcelable
}

sealed class RecipientIdentifierScreenState {
    object AcceptInput : RecipientIdentifierScreenState()
    data class InlineError(val error: UIError) : RecipientIdentifierScreenState()
    object Loading : RecipientIdentifierScreenState()
    data class Data(val recipientDetail: RecipientIdentifierVM.RecipientDetails) :
        RecipientIdentifierScreenState()
}
