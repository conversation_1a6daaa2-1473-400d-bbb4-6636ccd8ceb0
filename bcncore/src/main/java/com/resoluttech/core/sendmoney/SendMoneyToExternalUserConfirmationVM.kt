package com.resoluttech.core.sendmoney

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToExternalUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToExternalUserRequestRPC
import com.resoluttech.bcn.transfers.SendMoneyExternalRecipient
import com.resoluttech.bcn.transfers.TransferStatus
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.SendMoneyRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.sendmoney.SendMoneyToExternalUserConfirmationFragment.Companion.SEND_MONEY_TO_COUNTERPARTY_FAILED
import com.resoluttech.core.transactions.areRemarksValid
import com.resoluttech.core.transfers.peertopeer.SendMoneyRepository
import com.resoluttech.core.transfers.peertopeer.SendMoneyVM
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.views.FormattedAmount
import com.resoluttech.core.views.PendingTransactionDetails
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatus
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.time.Instant
import java.util.UUID

class SendMoneyToExternalUserConfirmationVM : ViewModel() {

    private val repository = SendMoneyRepository()
    private var _currentState: MutableLiveData<SendMoneyConfirmationScreenState> =
        MutableLiveData(SendMoneyConfirmationScreenState.AcceptInput)
    val currentState: LiveData<SendMoneyConfirmationScreenState> = _currentState
    private val vmIOScope = viewModelScope + Dispatchers.IO
    lateinit var selectedAccountId: UUID
    private lateinit var amount: Amount

    fun onUserAuthenticated(
        formattedAmount: FormattedAmount?,
        externalRecipient: SendMoneyExternalRecipient,
        context: Context,
    ) {
        if (formattedAmount == null) {
            _currentState.postValue(
                SendMoneyConfirmationScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return
        }

        if (!::selectedAccountId.isInitialized) {
            _currentState.postValue(
                SendMoneyConfirmationScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderAccountNotSelected),
                        context.getString(R.string.alertMessageSenderAccountNotSelected),
                    ),
                ),
            )
            return
        }

        _currentState.postValue(SendMoneyConfirmationScreenState.Loading)
        amount = Amount(
            formattedAmount.processedValue,
            Currency(UserSharedPreference.getUserPrimaryCurrency()),
        )
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val response = repository.sendMoneyToExternalUser(
                        amount,
                        selectedAccountId,
                        externalRecipient,
                    )
                    when (response) {
                        is LeoRPCResult.LeoResponse -> {
                            handleTransactionFees(response.response)
                        }

                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getSendMoneyToExternalUserRPCErrorMessage(
                                context,
                                response.error,
                                SelectedAccountHelper.getAccountFromId(selectedAccountId).name,
                            ).apply {
                                _currentState.postValue(
                                    SendMoneyConfirmationScreenState.InlineError(
                                        this,
                                    ),
                                )
                            }
                        }
                    }
                },
                handleException = {
                    if (it.errorCode == DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
                        handleRPCException(it)
                    } else {
                        handleRPCExceptionCase(
                            SendMoneyConfirmationScreenState.FullscreenError(
                                formattedAmount,
                                externalRecipient,
                                it.errorMessage,
                            ),
                        )
                    }
                },
            )
        }
    }

    fun onTransactionFeesDialogProceedClicked(
        context: Context,
        privateRemark: String?,
        publicRemark: String?,
    ) {
        if (!areRemarksValid(publicRemark, privateRemark)) {
            _currentState.postValue(
                SendMoneyConfirmationScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInvalidNarration),
                        context.getString(R.string.alertMessageTransactionInvalidNarration),
                    ),
                ),
            )
            return
        }
        _currentState.postValue(SendMoneyConfirmationScreenState.Loading)
        if (currentState.value is SendMoneyConfirmationScreenState.TransactionFeesConfirmation) {
            val transactionId =
                (currentState.value as SendMoneyConfirmationScreenState.TransactionFeesConfirmation).recordId
            vmIOScope.launch {
                executeRPC(
                    context,
                    rpcBlock = {
                        when (
                            val response = repository.confirmSendMoneyToExternalUser(
                                transactionId,
                                privateRemark,
                                publicRemark,
                            )
                        ) {
                            is LeoRPCResult.LeoResponse -> {
                                handleConfirmSendMoneyToUserRPCResponse(
                                    context,
                                    response.response,
                                    transactionId,
                                )
                            }

                            is LeoRPCResult.LeoError -> {
                                handleConfirmSendMoneyRPCError(context, response.error)
                            }
                        }
                    },
                    handleException = ::showError,
                )
            }
        } else {
            throw IllegalStateException("Send money to user request confirmation can only happen in TransactionFeesConfirmation state")
        }
    }

    private fun handleConfirmSendMoneyRPCError(
        context: Context,
        error: ConfirmSendMoneyToExternalUserRPC.Error,
    ) {
        when (error) {
            is ConfirmSendMoneyToExternalUserRPC.Error.SendMoneyToCounterpartyFailed -> {
                val uiError = UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleSendMoneyToCounterpartyFailed),
                    context.getString(R.string.alertMessageSendMoneyToCounterpartyFailed),
                    SEND_MONEY_TO_COUNTERPARTY_FAILED,
                )
                handleRPCException(uiError)
            }

            is ConfirmSendMoneyToExternalUserRPC.Error.UnableToReachCounterparty -> {
                val uiError = UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleUnableToPerformTransaction),
                    context.getString(R.string.alertMessageUnableToPerformTransaction),
                    SEND_MONEY_TO_COUNTERPARTY_FAILED,
                )
                handleRPCException(uiError)
            }

            else -> {
                SendMoneyRPCExceptionHandler.getConfirmSendMoneyToUserRPCErrorMessage(
                    context,
                    error,
                    SelectedAccountHelper.getAccountFromId(selectedAccountId).name,
                ).apply {
                    handleRPCException(this)
                }
            }
        }
    }

    private fun handleTransactionFees(
        response: CreateSendMoneyToExternalUserRequestRPC.Response,
    ) {
        _currentState.postValue(
            SendMoneyConfirmationScreenState.TransactionFeesConfirmation(
                response.recordId,
                amount,
                response.claimedTransactionFee,
                SelectedAccountHelper.getAccountFromId(selectedAccountId).name,
            ),
        )
    }

    private fun showError(uiError: UIError) {
        _currentState.postValue(
            SendMoneyConfirmationScreenState.InlineError(
                uiError,
            ),
        )
    }

    private fun handleRPCException(
        uiError: UIError,
    ) {
        _currentState.postValue(SendMoneyConfirmationScreenState.InlineError(uiError))
    }

    private fun handleConfirmSendMoneyToUserRPCResponse(
        context: Context,
        result: ConfirmSendMoneyToExternalUserRPC.Response,
        recordId: UUID,
    ) {
        when (val status = result.status) {
            is TransferStatus.Success -> {
                _currentState.postValue(
                    SendMoneyConfirmationScreenState.SuccessfulTransaction(
                        "$recordId",
                        result.amount,
                        status.succeededAt,
                        result.transactionDetail.description?.let {
                            context.getLocalizedString(
                                it.en,
                                it.ny,
                            )
                        },
                        result.transactionDetail.itemDetail,
                    ),
                )
            }

            is TransferStatus.Failed -> {
                _currentState.postValue(
                    SendMoneyConfirmationScreenState.InlineError(
                        UIError(
                            ErrorType.DIALOG,
                            context.getString(R.string.alertTitleUnableToPerformTransaction),
                            context.getString(R.string.alertMessageUnableToPerformTransaction),
                        ),
                    ),
                )
            }

            is TransferStatus.Pending -> {
                _currentState.postValue(
                    SendMoneyConfirmationScreenState.PendingTransaction(
                        "$recordId",
                        result.amount,
                        status.createdAt,
                        result.transactionDetail.description?.let {
                            context.getLocalizedString(
                                it.en,
                                it.ny,
                            )
                        },
                        result.transactionDetail.itemDetail,
                    ),
                )
            }
        }
    }

    private fun handleRPCExceptionCase(fullscreenError: SendMoneyConfirmationScreenState.FullscreenError) {
        _currentState.postValue(fullscreenError)
    }

    fun inlineErrorDismissed() {
        _currentState.postValue(SendMoneyConfirmationScreenState.AcceptInput)
    }

    fun onAuthenticationSuccess() {
        _currentState.postValue(SendMoneyConfirmationScreenState.AcceptInput)
    }

    fun handleSuccessfulTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        succeededAtTimestamp: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.SuccessfulTransaction(
            SuccessfulTransactionDetails(
                context.getString(R.string.transactionStatusSuccessLabel),
                getFormattedDate(context, succeededAtTimestamp),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            data,
        )
        navController.navigate(R.id.transaction_status_nav, args)
    }

    fun handlePendingTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        createdAtTimestamp: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.PendingTransaction(
            PendingTransactionDetails(
                context.getString(R.string.transactionStatusPendingLabel),
                getFormattedDate(context, createdAtTimestamp),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )
        val args = Bundle()
        args.putParcelable(TransactionStatusFragment.TRANSACTION_DATA_KEY, data)
        navController.navigate(R.id.transaction_status_nav, args)
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onTransactionFeesDialogDismissed() {
        _currentState.postValue(SendMoneyConfirmationScreenState.AcceptInput)
    }

    fun onMoneyTransferDeclinedByCounterparty(controller: NavController) {
        controller.popBackStack(R.id.moneyScreenFragment, false)
    }

    fun onUserAuthenticationSuccessful(recipient: SendMoneyExternalRecipient) {
        _currentState.postValue(SendMoneyConfirmationScreenState.UserAuthenticationSuccess(recipient))
    }

    fun onUserAuthenticationFailed(context: Context) {
        _currentState.postValue(
            SendMoneyConfirmationScreenState.UserAuthenticationFailed(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleBiometryAuthenticationFailure),
                    context.getString(R.string.alertMessageAuthenticationFailed),
                    SendMoneyVM.ERROR_CODE_APP_AUTHENTICATION_FAILED,
                ),
            ),
        )
    }

    fun onUserAuthenticationCancelled() {
        _currentState.postValue(SendMoneyConfirmationScreenState.AcceptInput)
    }

    fun onAttemptPaymentButtonTapped(
        formattedAmount: FormattedAmount?,
        navController: NavController,
        context: Context,
    ) {
        if (formattedAmount == null) {
            _currentState.postValue(
                SendMoneyConfirmationScreenState.InlineError(
                    UIError(
                        ErrorType.SNACKBAR,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return
        }

        if (!::selectedAccountId.isInitialized) {
            _currentState.postValue(
                SendMoneyConfirmationScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderAccountNotSelected),
                        context.getString(R.string.alertMessageSenderAccountNotSelected),
                    ),
                ),
            )
            return
        }

        _currentState.postValue(SendMoneyConfirmationScreenState.RequestAuthentication)
        navController.navigate(R.id.action_sendMoneyConfirmationFragment_To_AuthenticationProvider)
    }
}

sealed class SendMoneyConfirmationScreenState {

    object Loading : SendMoneyConfirmationScreenState()

    object AcceptInput : SendMoneyConfirmationScreenState()
    object RequestAuthentication : SendMoneyConfirmationScreenState()

    data class InlineError(val uiError: UIError) :
        SendMoneyConfirmationScreenState()

    data class FullscreenError(
        val formattedAmount: FormattedAmount?,
        val externalRecipient: SendMoneyExternalRecipient,
        val errorMessage: String,
    ) : SendMoneyConfirmationScreenState()

    data class TransactionFeesConfirmation(
        val recordId: UUID,
        val amount: Amount,
        val transactionFees: Amount,
        val accountName: String,
    ) : SendMoneyConfirmationScreenState()

    data class SuccessfulTransaction(
        val transactionId: String,
        val amount: Amount,
        val transactionSucceededAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) : SendMoneyConfirmationScreenState()

    data class PendingTransaction(
        val transactionId: String,
        val amount: Amount,
        val transactionCreatedAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) : SendMoneyConfirmationScreenState()

    data class UserAuthenticationSuccess(val recipient: SendMoneyExternalRecipient) :
        SendMoneyConfirmationScreenState()

    data class UserAuthenticationFailed(val inlineError: UIError) :
        SendMoneyConfirmationScreenState()
}
