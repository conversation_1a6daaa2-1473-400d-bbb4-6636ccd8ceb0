package com.resoluttech.core.views

import android.graphics.Bitmap
import android.os.Build
import android.os.Bundle
import android.view.InflateException
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showToolbar
import timber.log.Timber

class InAppBrowserFragment :
    BaseFragment(),
    BaseFragment.NetworkListener,
    AlertDialog.ActionListener {

    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private lateinit var url: String
    private var shouldShowBackButton: Boolean = false
    private lateinit var title: String
    private var isWebViewInstalled = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        url = InAppBrowserFragmentArgs.fromBundle(
            requireArguments(),
        ).destinationUrl
        shouldShowBackButton =
            InAppBrowserFragmentArgs.fromBundle(requireArguments()).shouldShowBackButton
        title = InAppBrowserFragmentArgs.fromBundle(requireArguments()).toolbarTitle
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        try {
            val view = inflater.inflate(R.layout.fragment_in_app_browser, container, false)
            view.initViews()
            setWebView()
            isWebViewInstalled = true
            return view
        } catch (e: InflateException) {
            Timber.tag(TAG).e(e)
            isWebViewInstalled = false
            showAlertDialog(
                requireContext().getString(R.string.alertTitleLoadWalletNoBrowser),
                requireContext().getString(R.string.alertMessageLoadWalletNoBrowser),
                DialogCodes.WEB_VIEW_NOT_INSTALLED,
            )
            showToolbar()
            setDefaultToolbar(title, shouldShowBackButton)
            return null
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(title, shouldShowBackButton)
        networkListenerCallback = this
    }

    private fun View.initViews() {
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)
    }

    private fun setWebView() {
        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                progressBar.visibility = View.INVISIBLE
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                progressBar.visibility = View.VISIBLE
            }

            override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
                view.loadUrl(url)
                return true
            }
        }

        with(webView.settings) {
            setSupportZoom(true)
            builtInZoomControls = true
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                safeBrowsingEnabled = true
            }
            displayZoomControls = false
        }
        webView.loadUrl(url)
    }

    fun canGoBack(): Boolean {
        return webView.canGoBack()
    }

    fun goBack() {
        webView.goBack()
    }

    override fun onNetworkAvailable() {
        if (isWebViewInstalled) {
            webView.loadUrl(url)
        } else {
            Timber.tag(TAG).i("No web view found on device to open URLs")
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        if (dialogId == DialogCodes.WEB_VIEW_NOT_INSTALLED) {
            findNavController().navigateUp()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        // Nothing to handle
    }
}

private const val TAG = "InAppBrowserFragment"
