package com.resoluttech.core.views

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.kyc.DocumentUploadTarget

class DocumentPickerDialog : BaseDialogFragment() {

    interface DocumentPickerListener {
        fun captureImageFromCamera(documentUploadTarget: DocumentUploadTarget)
        fun pickImageFromGallery(documentUploadTarget: DocumentUploadTarget)
        fun pickDocumentFromStorage(documentUploadTarget: DocumentUploadTarget)
        fun removeDocument(documentUploadTarget: DocumentUploadTarget)
        fun dismissed()
    }

    private var showDocumentOption: Boolean = false
    private var showRemoveOption: Boolean = false
    private lateinit var documentUploadTarget: DocumentUploadTarget
    private lateinit var titleTV: TextView
    private lateinit var openCameraTV: TextView
    private lateinit var openGalleryTV: TextView
    private lateinit var pickDocumentTV: TextView
    private lateinit var removeDocumentTV: TextView
    private lateinit var cancelButton: MaterialButton
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_document_picker_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
            isCancelable = false
        }
        initViews(rootView)
        return rootView
    }

    fun setArguments(
        showDocumentOption: Boolean,
        showRemoveOption: Boolean,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        this.showDocumentOption = showDocumentOption
        this.documentUploadTarget = documentUploadTarget
        this.showRemoveOption = showRemoveOption
    }

    override fun onDestroy() {
        dismissAllowingStateLoss()
        super.onDestroy()
    }

    private fun initViews(rootView: View) {
        if (!::documentUploadTarget.isInitialized) {
            dismissAllowingStateLoss()
        }
        titleTV = rootView.findViewById(R.id.title_tv)
        openCameraTV = rootView.findViewById(R.id.take_a_picture)
        openGalleryTV = rootView.findViewById(R.id.select_from_gallery)
        pickDocumentTV = rootView.findViewById(R.id.select_document)
        removeDocumentTV = rootView.findViewById(R.id.remove_document)
        cancelButton = rootView.findViewById(R.id.cancel_button)

        if (!showDocumentOption) {
            pickDocumentTV.visibility = View.GONE
            titleTV.text = requireContext().getString(R.string.profileAlertAddProfilePhoto)
        } else {
            pickDocumentTV.visibility = View.VISIBLE
            titleTV.text = requireContext().getString(R.string.kycAlertAddDocument)
        }

        if (!showRemoveOption) {
            removeDocumentTV.visibility = View.GONE
        } else {
            removeDocumentTV.visibility = View.VISIBLE
        }

        openCameraTV.setOnClickListener {
            (parentFragment as DocumentPickerListener).captureImageFromCamera(documentUploadTarget)
            dismiss()
        }
        openGalleryTV.setOnClickListener {
            (parentFragment as DocumentPickerListener).pickImageFromGallery(documentUploadTarget)
            dismiss()
        }
        pickDocumentTV.setOnClickListener {
            (parentFragment as DocumentPickerListener).pickDocumentFromStorage(documentUploadTarget)
            dismiss()
        }
        removeDocumentTV.setOnClickListener {
            (parentFragment as DocumentPickerListener).removeDocument(documentUploadTarget)
            dismiss()
        }
        cancelButton.setOnClickListener {
            dismiss()
            (parentFragment as DocumentPickerListener).dismissed()
        }
    }

    companion object {
        const val TAG: String = "DocumentPickerDialog"
    }
}
