package com.resoluttech.core.views

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signin.TYPE_ITEM
import com.suryadigital.leo.libui.listview.ListAdapter

class PasswordPolicyAdapter(
    private val passwordPolicyList: List<String>,
) : ListAdapter<String, RecyclerView.ViewHolder>(passwordPolicyList) {
    override fun filter(query: String) {
        // Nothing: We are not performing any filters here.
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val item = passwordPolicyList[position]
        val viewHolder = holder as ItemViewHolder
        viewHolder.apply {
            passwordPolicyTV.text = item
        }
    }

    override fun getItemCount(): Int {
        return passwordPolicyList.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_password_policy, parent, false)
                return ItemViewHolder(itemView)
            }
            else -> {
                throw IllegalStateException("Unknown View Type")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return TYPE_ITEM
    }

    private class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val passwordPolicyTV: TextView = itemView.findViewById(R.id.password_policy_tv)
    }
}

const val KEY_PRIVACY_POLICY_TITLE: String = "privacyPolicyTitle"
const val KEY_PRIVACY_POLICY_DESCRIPTION: String = "privacyPolicyDescription"
