package com.resoluttech.core.views

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.uicomponents.ProgressDialog
import com.resoluttech.core.uicomponents.ResetPasswordAlertDialog
import com.resoluttech.core.utils.SignOutRepository
import com.resoluttech.core.utils.checkForAppLinkArguments
import com.resoluttech.core.utils.clearAllUserData
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.showToolbar
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import timber.log.Timber

open class BaseFragment : Fragment(), ResetPasswordAlertDialog.ActionListener {

    protected lateinit var networkInfo: TextView
    protected var networkListenerCallback: NetworkListener? = null
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private lateinit var progressDialog: ProgressDialog
    private var deviceLockedBroadcastReceiver: BroadcastReceiver? = null

    interface NetworkListener {
        fun onNetworkAvailable()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        registerScreenLockBroadcastReceiver()
    }

    private fun dismissDialogs() {
        childFragmentManager.fragments.takeIf<MutableList<Fragment>>(MutableList<Fragment>::isNotEmpty)
            ?.map {
                (it as? BaseDialogFragment)?.dismiss()
            }
    }

    override fun onResume() {
        super.onResume()
        checkForAppLinkArguments(
            requireActivity(),
            requireContext(),
            findNavController(),
            childFragmentManager,
            isUserSignedIn = true,
        )
        inactiveAppHandler.removeCallbacks(inactiveAppRunnable)
        if (isAppInactive && shouldAuthenticate && !isAuthenticationInProgress) {
            try {
                isAuthenticationInProgress = true
                isAppInactive = false
                val args = Bundle()
                args.putString(KEY_PREVIOUS_PATH, PREVIOUS_PATH_APP_INACTIVE)
                dismissDialogs()
                findNavController().navigate(R.id.auth_provider_nav, args)
            } catch (e: IllegalArgumentException) {
                Timber.tag(TAG)
                    .e("App cannot navigate to auth provider in sign up/sign in flow - Ignoring the exception")
            }
        } else {
            isAuthenticationInProgress = false
            isAppInactive = false
        }
        try {
            networkInfo = requireActivity().findViewById(R.id.network_info)

            if (!requireContext().isNetworkConnected()) {
                handleNetworkLostState()
            }

            networkCallback = requireContext().registerAndGetNetworkCallback(
                onNetworkAvailableBlock = {
                    activity?.apply {
                        if (isAdded && ::networkInfo.isInitialized) {
                            runOnUiThread {
                                networkInfo.setBackgroundColor(requireContext().getColor(R.color.successful_transaction_theme_color))
                                networkInfo.text =
                                    requireActivity().getString(R.string.backOnlineBannerText)
                                if (networkInfo.isVisible) {
                                    networkListenerCallback?.onNetworkAvailable()
                                    networkInfo.postDelayed(
                                        {
                                            networkInfo.collapse(500)
                                        },
                                        1000,
                                    )
                                }
                            }
                        }
                    }
                },
                onNetworkLostBlock = ::handleNetworkLostState,
            )
        } catch (e: NullPointerException) {
            Timber.tag(TAG).e(e, "Ignoring the exception")
        }
    }

    protected fun handleNetworkLostState() {
        activity?.apply {
            if (isAdded && ::networkInfo.isInitialized) {
                runOnUiThread {
                    networkInfo.visibility = View.VISIBLE
                    networkInfo.text =
                        requireActivity().getString(R.string.noInternetBannerText)
                    networkInfo.setBackgroundColor(requireContext().getColor(R.color.destructiveActionColor))
                    networkInfo.expand(500)
                }
            }
        }
    }

    fun showProgressDialog(childFragmentManager: FragmentManager, message: String) {
        if (::progressDialog.isInitialized) {
            progressDialog.dismiss()
        }
        progressDialog = ProgressDialog.newInstance(
            message,
        )
        progressDialog.setArguments(false)
        progressDialog.show(childFragmentManager, ProgressDialog.TAG)
    }

    fun dismissProgressDialog() {
        if (::progressDialog.isInitialized) {
            progressDialog.dismiss()
        }
    }

    fun leoServerExceptionHandler(navController: NavController) {
        navController.popBackStack(R.id.moneyScreenFragment, false)
    }

    private fun registerScreenLockBroadcastReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF)

        deviceLockedBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(p0: Context?, p1: Intent?) {
                isAppInactive = true
                shouldAuthenticate = true
                onDeviceLocked()
            }
        }
        deviceLockedBroadcastReceiver?.let {
            requireContext().registerReceiver(it, intentFilter)
        }
    }

    open fun onDeviceLocked() {
        // Can be overridden to have custom implementation
    }

    override fun onStop() {
        super.onStop()
        inactiveAppHandler.postDelayed(inactiveAppRunnable, 20000)
        requireContext().unRegisterNetwork(networkCallback)
    }

    override fun onDestroy() {
        super.onDestroy()
        hideKeyboard()
        inactiveAppHandler.removeCallbacks(inactiveAppRunnable)
        deviceLockedBroadcastReceiver?.let(requireContext()::unregisterReceiver)
    }

    companion object {
        var shouldAuthenticate: Boolean = true
        var isAuthenticationInProgress: Boolean = false
        private var isAppInactive = false
        private val inactiveAppHandler by lazy {
            Handler(Looper.getMainLooper())
        }
        private val inactiveAppRunnable by lazy {
            Runnable {
                isAppInactive = true
            }
        }
    }

    override fun onPositiveAction() {
        CoroutineScope(Dispatchers.IO).launch {
            val llt: String? = KeyStoreHelper.getLLT()
            val pushToken = PushTokenHelper.getPushToken(requireContext())
            clearData()
            Timber.tag(TAG).i("Clearing all the tokens and cashed data.")
            when (
                val response =
                    SignOutRepository.signOut(pushToken, llt)
            ) {
                is LeoRPCResult.LeoResponse -> {
                    Timber.tag(TAG)
                        .i("signOutUserRPC Called Successfully ${Json.encodeToString(response.response)}")
                }
                is LeoRPCResult.LeoError -> {
                    Timber.tag(TAG)
                        .w("signOutUserRPC thrown and exception ${Json.encodeToString(response.error)}")
                }
            }
            restartActivity()
        }
    }

    private suspend fun clearData() {
        clearAllUserData()
    }
}

private const val TAG = "BaseFragment"
private const val KEY_PREVIOUS_PATH: String = "previousPath"
const val PREVIOUS_PATH_APP_INACTIVE: String = "previousPath.appInactive"

fun isLaunchedFromHistory(activity: FragmentActivity): Boolean {
    /*
        The check `Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY` is added so that once user is entered
        to the app using App Link then, minimizes the app and relaunch from recent apps tabs then
        we don't want to show the same content to user.
        Thanks: https://stackoverflow.com/a/49400366/13268813
     */
    return (activity.intent.flags and Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY) != Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY
}
