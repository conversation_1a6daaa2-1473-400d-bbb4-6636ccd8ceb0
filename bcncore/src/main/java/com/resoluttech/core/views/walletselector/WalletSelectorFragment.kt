package com.resoluttech.core.views.walletselector

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.manageaccount.KEY_PREVIOUS_PATH
import com.resoluttech.core.profile.manageaccount.ManageAccountPreviousPath
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.views.BaseFragment
import java.util.UUID

class WalletSelectorFragment :
    BaseFragment(),
    WalletSelectorAdapter.WalletSelectorListener {

    private val walletSelectorVM: WalletSelectorVM by lazy {
        ViewModelProvider(this)[WalletSelectorVM::class.java]
    }

    private lateinit var progressBar: ProgressBar
    private lateinit var walletsRV: RecyclerView
    private lateinit var addWalletFAB: ExtendedFloatingActionButton
    private var shouldUpdateSelectedWallet = false
    private var isWalletToWalletDebitWalletSelection = false
    private var isWalletToWalletCreditWalletSelection = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_wallet_selector, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(title = getString(R.string.selectAccountViewTitle), isBackArrowShown = true)
        when (
            WalletSelectorPreviousPath.valueOf(
                requireArguments().getString(
                    KEY_PREVIOUS_PATH,
                    null,
                ),
            )
        ) {
            WalletSelectorPreviousPath.HOME_SCREEN,
            WalletSelectorPreviousPath.TRANSACTIONS_SCREEN,
            -> {
                shouldUpdateSelectedWallet = true
            }

            WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS -> {
                shouldUpdateSelectedWallet = false
            }

            WalletSelectorPreviousPath.WALLET_TO_WALLET_DEBIT_WALLET -> {
                shouldUpdateSelectedWallet = false
                isWalletToWalletDebitWalletSelection = true
                isWalletToWalletCreditWalletSelection = false
            }
            WalletSelectorPreviousPath.WALLET_TO_WALLET_CREDIT_WALLET -> {
                shouldUpdateSelectedWallet = false
                isWalletToWalletDebitWalletSelection = false
                isWalletToWalletCreditWalletSelection = true
            }
        }
        initViews()
        walletSelectorVM.onViewCreated()
        walletSelectorVM.state.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun initViews() {
        requireView().apply {
            progressBar = findViewById(R.id.progress_bar)
            walletsRV = findViewById(R.id.wallets_rv)
            addWalletFAB = findViewById(R.id.add_wallet_fab)
        }
        addWalletFAB.setOnClickListener {
            findNavController().navigate("resoluttech://manage_accounts/?previousPath=${ManageAccountPreviousPath.WALLET_SELECTOR.name}".toUri())
        }
        if (shouldUpdateSelectedWallet) {
            addWalletFAB.visibility = View.VISIBLE
        } else {
            addWalletFAB.visibility = View.GONE
        }
    }

    private fun reactToState(state: WalletSelectorScreenState) {
        when (state) {
            is WalletSelectorScreenState.Data -> {
                handleDataState(state)
            }

            is WalletSelectorScreenState.Loading -> {
                showViewForState(progressBar)
            }
        }
    }

    private fun handleDataState(state: WalletSelectorScreenState.Data) {
        showViewForState(walletsRV)
        walletsRV.adapter = WalletSelectorAdapter(
            listener = this,
            listOfAccounts = state.walletsList,
            requireArguments().getString(KEY_SELECTED_ACCOUNT_ID, null),
        )
    }

    private fun showViewForState(view: View) {
        when (view) {
            progressBar -> {
                progressBar.visibility = View.VISIBLE
                walletsRV.visibility = View.GONE
            }

            walletsRV -> {
                progressBar.visibility = View.GONE
                walletsRV.visibility = View.VISIBLE
            }
        }
    }

    override fun onWalletSelected(accountName: String, accountId: UUID, accountIndex: Int) {
        if (shouldUpdateSelectedWallet) {
            walletSelectorVM.onWalletSelected(
                context = requireContext(),
                accountId = accountId,
            )
        }
        if (isWalletToWalletDebitWalletSelection) {
            walletSelectorVM.storeAccountToAccountDebitWalletData(accountId, accountName, accountIndex)
        } else if (isWalletToWalletCreditWalletSelection) {
            walletSelectorVM.storeAccountToAccountCreditWalletData(accountId, accountName, accountIndex)
        } else {
            findNavController().previousBackStackEntry?.savedStateHandle?.apply {
                set(KEY_SELECTED_ACCOUNT_ID, accountId)
                set(KEY_SELECTED_ACCOUNT_NAME, accountName)
                set(KEY_SELECTED_ACCOUNT_INDEX, accountIndex)
            }
        }
        findNavController().navigateUp()
    }
}

enum class WalletSelectorPreviousPath {
    HOME_SCREEN,
    TRANSACTIONS_SCREEN,
    MONEY_TRANSFER_SCREENS,
    WALLET_TO_WALLET_DEBIT_WALLET,
    WALLET_TO_WALLET_CREDIT_WALLET,
}

const val KEY_SELECTED_ACCOUNT_ID: String = "selectedAccountId"
const val KEY_SELECTED_ACCOUNT_NAME: String = "selectedAccountName"
const val KEY_SELECTED_ACCOUNT_INDEX: String = "selectedAccountIndex"

const val KEY_SELECTED_DEBIT_WALLET_ID: String = "selectedDebitWalletId"
const val KEY_SELECTED_DEBIT_WALLET_NAME: String = "selectedDebitWalletName"
const val KEY_SELECTED_DEBIT_WALLET_INDEX: String = "selectedDebitWalletIndex"

const val KEY_SELECTED_CREDIT_WALLET_ID: String = "selectedCreditWalletId"
const val KEY_SELECTED_CREDIT_WALLET_NAME: String = "selectedCreditWalletName"
const val KEY_SELECTED_CREDIT_WALLET_INDEX: String = "selectedCreditWalletIndex"
