package com.resoluttech.core.views

import android.content.Context
import android.os.Parcelable
import android.text.InputType
import android.util.AttributeSet
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.widget.AppCompatEditText
import com.resoluttech.core.utils.AmountTextWatcher
import com.resoluttech.core.utils.getEmptyString
import kotlinx.parcelize.Parcelize
import java.lang.NullPointerException
import java.text.DecimalFormatSymbols
import kotlin.jvm.Throws
import kotlin.math.pow
import kotlin.math.roundToLong

/**
 * A special edit text which formats the entered input to amount format in realtime.
 * */
class AmountEditText : AppCompatEditText {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    )

    interface ErrorListener {
        fun onAmountInput(isValid: Boolean)
    }

    private val amountTextWatcher: AmountTextWatcher = AmountTextWatcher(this, context)

    init {
        inputType =
            InputType.TYPE_CLASS_NUMBER or
            InputType.TYPE_NUMBER_FLAG_DECIMAL or
            InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS
        // This is to disable paste, copy, select all, autofill.
        customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                menu?.clear()
                return false
            }

            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                menu?.clear()
                return false
            }

            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false
            }

            override fun onDestroyActionMode(mode: ActionMode?) {}
        }
        customInsertionActionModeCallback = object : ActionMode.Callback {
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                menu?.clear()
                return false
            }

            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                menu?.clear()
                return false
            }

            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false
            }

            override fun onDestroyActionMode(mode: ActionMode?) {}
        }
        addTextChangedListener(amountTextWatcher)
    }

    /**
     * This function provides realtime-check on input, if input is invalid it will return onAmountInvalid(false),
     * else it will return onAmountInput(true). It is must to set the listener else it will throw [NullPointerException].
     * This listener can be used to block the user actions such as button click, if entered input is not valid.
     * */
    @Throws(NullPointerException::class)
    fun setErrorListener(errorListener: ErrorListener) {
        amountTextWatcher.setErrorListener(errorListener)
    }

    /**
     * It removes the grouping separator(,) from the entered input and
     * returns cleaned string that can be converted to long or double.
     * */
    fun getInputText(): String {
        val groupingSeparator = DecimalFormatSymbols.getInstance().groupingSeparator
        return text.toString().replace("$groupingSeparator", context.getEmptyString()).trim()
    }

    /**
     * This method will return the amount multiplied with [10 * multiplication factor].
     * */
    fun getFormattedAmount(): FormattedAmount? {
        val inputText = getInputText()
        return if (inputText.isEmpty()) {
            null
        } else {
            val formattedAmount = text.toString()
            val actualValue = getInputText().toDouble()
            val processedValue = (actualValue * 10.0.pow(amountMultiplicationFactor)).roundToLong()
            FormattedAmount(
                userFacingAmountValue = actualValue,
                enteredAmountText = getInputText(),
                userFacingAmountText = formattedAmount,
                processedValue = processedValue,
            )
        }
    }

    companion object {
        var amountMultiplicationFactor: Int = 4
    }
}

@Parcelize
data class FormattedAmount(
    val userFacingAmountValue: Double, // 12345.34
    val enteredAmountText: String, // "12345.34"
    val userFacingAmountText: String, // "12,345.34"
    val processedValue: Long, // 123453400
) : Parcelable
