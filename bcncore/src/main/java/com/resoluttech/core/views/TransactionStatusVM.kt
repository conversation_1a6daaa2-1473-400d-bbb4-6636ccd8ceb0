package com.resoluttech.core.views

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Parcelable
import android.widget.ScrollView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcn.transactions.AddPrivateRemarkRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.RemarkRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.TransactionsRepository
import com.resoluttech.core.utils.ScreenshotHandler
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.TransactionRemarkSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.parcelize.Parcelize
import java.util.UUID

class TransactionStatusVM : ViewModel() {

    private val _currentStatus: MutableLiveData<TransactionStatus> = MutableLiveData()
    val currentStatus: LiveData<TransactionStatus> get() = _currentStatus
    private val _doneBTVisibility: MutableLiveData<Boolean> = MutableLiveData()
    val doneBTVisibility: LiveData<Boolean> = _doneBTVisibility

    private val _addRemarkStatus: MutableLiveData<RemarkState> = MutableLiveData()
    val addRemarkState: LiveData<RemarkState> get() = _addRemarkStatus
    private val remarkRepository = RemarkRepository()
    private val repository = TransactionsRepository()
    private var transactionJob: Job? = null

    private val vmIoScope = viewModelScope + Dispatchers.IO

    fun onSuccessFullTransactionData(transactionStatus: TransactionStatus) {
        _currentStatus.value = transactionStatus
    }

    fun shareButtonTapped(activity: Activity, view: ScrollView, transactionId: String) {
        val screenshotHandler = ScreenshotHandler()
        val fileName = String.format(
            activity.getString(R.string.transactionStatusScreenshotFileName),
            transactionId,
        )
        screenshotHandler.shareScreenshot(activity, view, fileName)
    }

    fun onResume(isDoneButtonVisible: Boolean) {
        _doneBTVisibility.postValue(isDoneButtonVisible)
    }

    fun onBackPressed(activity: FragmentActivity) {
        activity.onBackPressedDispatcher.onBackPressed()
    }

    fun onAddRemark(context: Context, transactionId: UUID, remark: String) {
        _addRemarkStatus.postValue(RemarkState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = remarkRepository.addPrivateRemark(transactionId, remark)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleResponseState()
                            getTransactions(
                                context,
                                UUID.fromString(SelectedAccountHelper.getSelectedAccount()?.id),
                            )
                        }
                        is LeoRPCResult.LeoError -> handleErrorState(response.error)
                    }
                },
                handleException = {
                    _addRemarkStatus.postValue(RemarkState.Error(it))
                },
            )
        }
    }

    private fun handleResponseState() {
        _addRemarkStatus.postValue(RemarkState.Success)
    }

    private fun getTransactions(context: Context, accountId: UUID) {
        if (transactionJob != null) {
            transactionJob!!.cancel()
        }
        transactionJob = vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (repository.refreshTransactionScreenData(accountId)) {
                        is LeoRPCResult.LeoResponse -> {
                            TransactionRemarkSharedPreference.setTransactionRPCSuccess()
                            TransactionRemarkSharedPreference.setTransactionAccountID(accountId)
                        }
                        is LeoRPCResult.LeoError -> {
                            // Default action is already handled
                        }
                    }
                },
                handleException = {
                },
            )
        }
    }

    private fun handleErrorState(error: AddPrivateRemarkRPC.Error) {
        error.apply {
            _addRemarkStatus.postValue(
                RemarkState.Error(
                    RemarkRPCExceptionHandler.getRemarkRPCErrorMessage(this),
                ),
            )
        }
    }

    fun onInlineErrorDismissed(sb: Snackbar) {
        sb.dismiss()
    }

    fun onOptionAddRemarkSelected() {
        _addRemarkStatus.postValue(RemarkState.OnOptionAddRemarkSelected)
    }

    fun onRemarkDialogDismissed() {
        _addRemarkStatus.postValue(RemarkState.Waiting)
    }
}

sealed class RemarkState {
    object Loading : RemarkState()
    data class Error(val uiError: UIError) : RemarkState()
    object Success : RemarkState()
    object OnOptionAddRemarkSelected : RemarkState()
    object Waiting : RemarkState()
}

sealed class TransactionStatus : Parcelable {

    @Parcelize
    data class SuccessfulTransaction(val successfulTransactionDetails: SuccessfulTransactionDetails) :
        TransactionStatus()

    @Parcelize
    data class FailedTransaction(val failedTransactionDetails: FailedTransactionDetails) :
        TransactionStatus()

    @Parcelize
    data class ExpiredTransaction(val expiredTransactionDetails: ExpiredTransactionDetails) :
        TransactionStatus()

    @Parcelize
    data class CancelledTransaction(val cancelledTransactionDetails: CancelledTransactionDetails) :
        TransactionStatus()

    @Parcelize
    data class PendingTransaction(val pendingTransactionDetails: PendingTransactionDetails) :
        TransactionStatus()

    @Parcelize
    data class ReverseTransaction(val reverseTransactionDetails: ReverseTransactionDetails) :
        TransactionStatus()
}

sealed class TransactionStatusItemDetail : Parcelable {
    @Parcelize
    data class LargeIconWithText(
        val label: LocalizedString,
        val title: String,
        val description: String?,
        val icon: Uri,
    ) : TransactionStatusItemDetail()

    @Parcelize
    data class SmallIconWithText(
        val label: LocalizedString,
        val title: String,
        val description: String?,
        val icon: Uri,
    ) : TransactionStatusItemDetail()

    @Parcelize
    data class LabelWithText(val label: LocalizedString, val text: String) :
        TransactionStatusItemDetail()

    @Parcelize
    data class CopyableText(val label: LocalizedString, val text: String) :
        TransactionStatusItemDetail()
}

@Parcelize
data class LocalizedString(
    val en: String,
    val ny: String? = null,
) : Parcelable

@Parcelize
data class SuccessfulTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val itemDetails: List<TransactionStatusItemDetail>,
    val allowAddingRemarks: Boolean? = false,
) : Parcelable

@Parcelize
data class PendingTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val itemDetails: List<TransactionStatusItemDetail>,
    val allowAddingRemarks: Boolean? = false,
) : Parcelable

@Parcelize
data class ReverseTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val itemDetails: List<TransactionStatusItemDetail>,
) : Parcelable

@Parcelize
data class FailedTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val failureDescription: String?,
    val itemDetails: List<TransactionStatusItemDetail>,
) : Parcelable

@Parcelize
data class ExpiredTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val itemDetails: List<TransactionStatusItemDetail>,
) : Parcelable

@Parcelize
data class CancelledTransactionDetails(
    val title: String,
    val dateTime: String,
    val transactionId: String,
    val transactionAmount: Long,
    val transactionCurrency: String,
    val transactionDescriptions: String? = null,
    val itemDetails: List<TransactionStatusItemDetail>,
) : Parcelable
