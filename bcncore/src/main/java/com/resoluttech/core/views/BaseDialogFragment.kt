package com.resoluttech.core.views

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.widget.LinearLayout
import androidx.fragment.app.DialogFragment
import com.hbb20.CountryCodePicker

/**
 * This is a base class which sets the width of the dialog to 85% the device's width
 */
open class BaseDialogFragment : DialogFragment() {

    override fun onStart() {
        super.onStart()
        requireDialog().setDialogSize(requireContext())
    }
}

fun CountryCodePicker.setDialogSize(context: Context) {
    setDialogEventsListener(object : CountryCodePicker.DialogEventsListener {
        override fun onCcpDialogOpen(dialog: Dialog) {
            dialog.setDialogSize(context)
        }

        override fun onCcpDialogDismiss(dialogInterface: DialogInterface) {
            // Nothing to handle
        }

        override fun onCcpDialogCancel(dialogInterface: DialogInterface) {
            // Nothing to handle
        }
    })
}

private fun Dialog.setDialogSize(context: Context) {
    /*
     Maintaining the width of this dialog to 85% of device's width.
     */
    val calculatedWidth = (context.resources.displayMetrics.widthPixels * 85) / 100
    val dialogWidth =
        if (calculatedWidth > FIXED_DIALOG_WIDTH) FIXED_DIALOG_WIDTH else calculatedWidth
    val dialogHeight = LinearLayout.LayoutParams.WRAP_CONTENT

    this.window?.setLayout(dialogWidth, dialogHeight)
}

private const val FIXED_DIALOG_WIDTH = 1224
