package com.resoluttech.core.views

import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue

class DividerItemDecorator(private val divider: Drawable?, private val dividerLeft: Int, private val viewTypes: List<Int>) : RecyclerView.ItemDecoration() {

    override fun onDraw(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(canvas, parent, state)

        divider?.let {
            val dividerRight = parent.width

            val childCount = parent.childCount
            for (i in 0..childCount - 2) {
                val child: View = parent.getChildAt(i)
                val position = parent.getChildAdapterPosition(child)
                if (position >= 0) {
                    val viewType: Int = parent.adapter!!.getItemViewType(position)
                    if (getViewType(viewTypes, viewType)) {
                        val params = child.layoutParams as RecyclerView.LayoutParams
                        val dividerTop: Int = child.bottom + params.bottomMargin
                        val dividerBottom: Int = dividerTop + it.intrinsicHeight
                        it.setBounds(dividerLeft.getDPPixelValue(), dividerTop, dividerRight, dividerBottom)
                        it.draw(canvas)
                    }
                }
            }
        }
    }

    private fun getViewType(types: List<Int>, type: Int): Boolean {
        if (types.isEmpty()) {
            // Empty list will denote that divider is required for each view type. Can be used in list such as
            // Recurring transaction list.
            return true
        } else {
            types.forEach {
                if (it == type) {
                    return true
                }
            }
            return false
        }
    }

    companion object {
        const val LIST_MARGIN_0: Int = 0
        const val LIST_MARGIN_16: Int = 16
        const val LIST_MARGIN_72: Int = 72
        const val LIST_MARGIN_74: Int = 74
    }
}
