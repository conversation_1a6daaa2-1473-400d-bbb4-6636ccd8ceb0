package com.resoluttech.core.views.walletselector

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.imageview.ShapeableImageView
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.manageaccount.ActiveAccountsItem
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.getFlagFromCurrency
import com.resoluttech.core.utils.loadImage
import com.suryadigital.leo.libui.listview.ListAdapter
import java.util.UUID

class WalletSelectorAdapter(
    private val listener: WalletSelectorListener,
    listOfAccounts: List<ActiveAccountsItem>,
    private val selectedAccountId: String?,
) : ListAdapter<ActiveAccountsItem, RecyclerView.ViewHolder>(listOfAccounts) {

    private var currentlySelectedItem: ImageView? = null

    interface WalletSelectorListener {
        fun onWalletSelected(accountName: String, accountId: UUID, accountIndex: Int)
    }

    override fun filter(query: String) {
        // Nothing: We are not performing any filters here.
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_HEADER -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_manage_account_header, parent, false)
                HeaderViewHolder(itemView)
            }

            TYPE_ITEM -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_wallet_selector, parent, false)
                ItemViewHolder(itemView)
            }

            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (filterItemList[position]) {
            is ActiveAccountsItem.AccountHeader -> TYPE_HEADER
            is ActiveAccountsItem.Account -> TYPE_ITEM
        }
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            TYPE_HEADER -> bindHeaderViewHolder(
                holder as HeaderViewHolder,
                filterItemList[position] as ActiveAccountsItem.AccountHeader,
            )

            TYPE_ITEM -> bindItemViewHolder(
                holder as ItemViewHolder,
                filterItemList[position] as ActiveAccountsItem.Account,
                position,
            )
        }
    }

    private fun bindHeaderViewHolder(
        holder: HeaderViewHolder,
        accountHeader: ActiveAccountsItem.AccountHeader,
    ) {
        val country = UserSharedPreference.getUserCountryCurrency()
            .find { it.currencyCode == accountHeader.currency.currencyCode }
        val displayLabel = when (LocaleManager.getCurrentLocale(holder.itemView.context)) {
            SupportedLocale.EN_US -> country!!.displayNameEn
            SupportedLocale.NYANJA ->
                country!!.displayNameNy
                    ?: country.displayNameEn
        }
        holder.headerTV.text = displayLabel
        holder.headerTV.addContentDescriptionString(
            R.string.axSendMoneyAccountPickerCurrencyLabel,
            holder.itemView.context,
            displayLabel,
        )
        holder.currencyCountryFlagIV.loadImage(getFlagFromCurrency(accountHeader.currency.currencyCode))
    }

    private fun bindItemViewHolder(
        holder: ItemViewHolder,
        accountItem: ActiveAccountsItem.Account,
        position: Int,
    ) {
        holder.accountNameTV.text = accountItem.accountName
        holder.balanceTV.text = holder.itemView.context.getString(
            R.string.moneyScreenAccountPickerBalance,
            accountItem.balance,
        )
        if (accountItem.isSelected && selectedAccountId == null) {
            holder.tickIV.visibility = View.VISIBLE
            currentlySelectedItem = holder.tickIV
        } else if (selectedAccountId == accountItem.accountId) {
            holder.tickIV.visibility = View.VISIBLE
            currentlySelectedItem = holder.tickIV
        } else {
            holder.tickIV.visibility = View.INVISIBLE
        }
        holder.itemWalletInfo.setOnClickListener {
            currentlySelectedItem?.visibility = View.INVISIBLE
            holder.tickIV.visibility = View.VISIBLE
            currentlySelectedItem = holder.tickIV
            listener.onWalletSelected(
                accountName = accountItem.accountName,
                accountId = UUID.fromString(accountItem.accountId),
                accountIndex = position,
            )
        }
    }

    private class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val itemWalletInfo: ConstraintLayout = itemView.findViewById(R.id.item_wallet_info)
        val accountNameTV: TextView = itemView.findViewById(R.id.account_name_tv)
        val balanceTV: TextView = itemView.findViewById(R.id.balance_tv)
        val tickIV: ImageView = itemView.findViewById(R.id.is_selected_iv)
    }

    private class HeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headerTV: TextView = itemView.findViewById(R.id.header_text_tv)
        val currencyCountryFlagIV: ShapeableImageView = itemView.findViewById(R.id.flag_iv)
    }
}

private const val TYPE_HEADER = 1
const val TYPE_ITEM: Int = 2
