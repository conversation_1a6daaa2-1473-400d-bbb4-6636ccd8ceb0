package com.resoluttech.core.views

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupMenu
import android.widget.ProgressBar
import android.widget.ScrollView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.size
import androidx.lifecycle.Observer
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.transition.MaterialContainerTransform
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.TransactionVM
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.TextInputDialog
import com.resoluttech.core.uicomponents.TextInputDialogUseCase
import com.resoluttech.core.uicomponents.transactionStatus.CopyableText
import com.resoluttech.core.uicomponents.transactionStatus.LabelWithText
import com.resoluttech.core.uicomponents.transactionStatus.LargeIconWithText
import com.resoluttech.core.uicomponents.transactionStatus.SmallIconWithText
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.TRANSACTION_STATUS_FRAGMENT_DIALOG_CODE
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getDrawable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getImmediateBackstackDestinationId
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.resetToolbarColor
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setSystemBackPress
import com.resoluttech.core.utils.setToolbarBackgroundColor
import com.resoluttech.core.utils.showErrorDialog
import timber.log.Timber
import java.util.UUID

class TransactionStatusFragment :
    BaseFragment(),
    TextInputDialog.TextInputListener,
    AlertDialog.ActionListener,
    ContactSupport.ActionListener {

    private var allowAddingRemarks: Boolean? = null
    private lateinit var transactionDescriptionTV: TextView
    private lateinit var transactionDetailsContainer: LinearLayout
    private lateinit var transactionIdTV: TextView
    private lateinit var amountTV: TextView
    private lateinit var dateTimeTV: TextView
    private lateinit var doneButton: MaterialButton
    private lateinit var supportTV: TextView
    private lateinit var supportHeaderTV: TextView
    private lateinit var supportDescTV: TextView
    private lateinit var topContainer: LinearLayout
    private lateinit var root: ConstraintLayout
    private lateinit var scrollView: ScrollView
    private lateinit var subtitleTV: TextView
    private lateinit var titleTV: TextView
    private lateinit var transactionIV: ImageView
    private lateinit var initiatedDate: String
    private var popupMenu: PopupMenu? = null
    private var initiatedAmount: Long? = null

    private lateinit var remarkPB: ProgressBar
    private lateinit var inlineErrorSnackBar: Snackbar

    private var shouldTakeScreenShot = false

    private val transactionVM: TransactionVM by navGraphViewModels(R.id.home_nav)
    private val transactionStatusVM: TransactionStatusVM by navGraphViewModels(R.id.transaction_status_nav)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_transaction_status, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setMaterialContainerTransform()
        initViews(view)
        setDataToVM(requireArguments())
        setToolbar()
        handleDoneAction()
        handleSupportAction()
        transactionStatusVM.currentStatus.observe(viewLifecycleOwner, Observer(::reactToState))
        transactionStatusVM.addRemarkState.observe(
            viewLifecycleOwner,
            Observer(::reactToRemarkState),
        )
        transactionStatusVM.doneBTVisibility.observe(
            viewLifecycleOwner,
            Observer(::reactToVisibility),
        )
        setSystemBackPress(getImmediateBackstackDestinationId())
    }

    private fun setMaterialContainerTransform() {
        sharedElementEnterTransition = MaterialContainerTransform().apply {
            drawingViewId = android.R.id.content
            duration = resources.getInteger(android.R.integer.config_mediumAnimTime).toLong()
            scrimColor = Color.TRANSPARENT
            setAllContainerColors(requireContext().getColor(R.color.surfaceColor))
        }
    }

    private fun initViews(view: View) {
        root = view.findViewById(R.id.root_view)
        topContainer = view.findViewById(R.id.top_container)
        scrollView = view.findViewById(R.id.scroll_view)
        remarkPB = view.findViewById(R.id.progress_bar)
        when (
            BundleUtils.getBundlesParcelable(
                requireArguments(),
                TRANSACTION_DATA_KEY,
                TransactionStatus::class.java,
            ) ?: throw IllegalStateException("Transaction Status details cannot be null.")
        ) {
            is TransactionStatus.SuccessfulTransaction ->
                topContainer.background =
                    getDrawable(R.color.successful_transaction_theme_color)

            is TransactionStatus.FailedTransaction ->
                topContainer.background =
                    getDrawable(R.color.failed_transaction_theme_color)

            is TransactionStatus.ExpiredTransaction ->
                topContainer.background =
                    getDrawable(R.color.expire_transaction_theme_color)

            is TransactionStatus.CancelledTransaction ->
                topContainer.background =
                    getDrawable(R.color.failed_transaction_theme_color)

            is TransactionStatus.PendingTransaction ->
                topContainer.background =
                    getDrawable(R.color.expire_transaction_theme_color)

            is TransactionStatus.ReverseTransaction ->
                topContainer.background =
                    getDrawable(R.color.failed_transaction_theme_color)
        }

        transactionDescriptionTV = view.findViewById(R.id.transaction_description_tv)
        transactionIdTV = view.findViewById(R.id.transaction_id_tv)
        amountTV = view.findViewById(R.id.amount_tv)
        dateTimeTV = view.findViewById(R.id.date_time_tv)
        doneButton = view.findViewById(R.id.done_button)
        supportTV = view.findViewById(R.id.support_button)
        supportHeaderTV = view.findViewById(R.id.label_need_help)
        supportDescTV = view.findViewById(R.id.desc_support)
        subtitleTV = view.findViewById(R.id.subtitle_tv)
        titleTV = view.findViewById(R.id.title_tv)
        transactionIV = view.findViewById(R.id.transaction_state_iv)
        transactionDetailsContainer = view.findViewById(R.id.details_container)
        doneButton.viewTreeObserver.addOnGlobalLayoutListener {
            /*
                This callback will be hit whenever there is a change in layout. But we are only
                interest in visibility of `doneButton`.
             */
            if (doneButton.isGone && shouldTakeScreenShot) {
                transactionStatusVM.shareButtonTapped(
                    requireActivity(),
                    scrollView,
                    transactionIdTV.text.toString(),
                )
            }
            shouldTakeScreenShot = false
        }
    }

    private fun setupContentDescription() {
        transactionIdTV.addContentDescriptionString(
            R.string.axTransactionStatusIDHint,
            requireContext(),
            transactionIdTV.text.toString(),
        )
        amountTV.addContentDescriptionString(
            R.string.axTransactiontatusAmountLabel,
            requireContext(),
            amountTV.text.toString(),
        )
        dateTimeTV.addContentDescriptionString(
            R.string.axTransactionStatusTime,
            requireContext(),
            dateTimeTV.text.toString(),
        )
    }

    private fun handleDoneAction() {
        doneButton.setOnClickListener {
            transactionStatusVM.onBackPressed(requireActivity())
        }
    }

    private fun setDataToVM(requireArguments: Bundle) {
        transactionStatusVM.onSuccessFullTransactionData(
            (
                BundleUtils.getBundlesParcelable(
                    requireArguments,
                    TRANSACTION_DATA_KEY,
                    TransactionStatus::class.java,
                ) ?: throw IllegalStateException("Transaction Status details cannot be null.")
                ),
        )
    }

    fun reactToState(transactionStatus: TransactionStatus) {
        when (transactionStatus) {
            is TransactionStatus.SuccessfulTransaction -> {
                handleSuccessfulTransactionData(transactionStatus.successfulTransactionDetails)
            }

            is TransactionStatus.FailedTransaction -> {
                handleFailedTransactionData(transactionStatus.failedTransactionDetails)
            }

            is TransactionStatus.ExpiredTransaction -> {
                handleExpiredTransactionData(transactionStatus.expiredTransactionDetails)
            }

            is TransactionStatus.CancelledTransaction -> {
                handleCancelledTransactionData(transactionStatus.cancelledTransactionDetails)
            }

            is TransactionStatus.PendingTransaction -> {
                handlePendingTransactionData(transactionStatus.pendingTransactionDetails)
            }

            is TransactionStatus.ReverseTransaction -> {
                handleReverseTransactionData(transactionStatus.reverseTransactionDetails)
            }
        }
    }

    private fun reactToRemarkState(remarkState: RemarkState) {
        when (remarkState) {
            is RemarkState.Loading -> {
                showLoadingStateView()
            }

            is RemarkState.Error -> {
                handleErrorState(remarkState.uiError)
            }

            is RemarkState.Success -> {
                handleSuccessState()
            }

            RemarkState.OnOptionAddRemarkSelected -> {
                handleOnOptionAddRemark()
            }

            RemarkState.Waiting -> {
                // Nothing to handle
            }
        }
    }

    private fun handleErrorState(uiError: UIError) {
        showDataStateView()
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(uiError.errorMessage)
            }

            ErrorType.DIALOG -> {
                showInlineErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: TRANSACTION_STATUS_FRAGMENT_DIALOG_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineErrorSnackBar(errorMessage: String) {
        inlineErrorSnackBar =
            Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackBar.let { sb ->
            sb.setAction(R.string.alertActionDismiss) {
                transactionStatusVM.onInlineErrorDismissed(sb)
            }
            sb.show()
        }
    }

    private fun showInlineErrorDialog(
        errorTitle: String,
        errorMessage: String,
        errorCodes: Int,
    ) {
        showErrorDialog(
            title = errorTitle,
            message = errorMessage,
            dialogId = errorCodes,
        )
    }

    private fun showDataStateView() {
        remarkPB.visibility = View.GONE
        root.visibility = View.VISIBLE
    }

    private fun handleSuccessState() {
        showDataStateView()
        transactionVM.transactionID = transactionIdTV.text.toString()
        addRemarkView()
    }

    private fun addRemarkView() {
        val narrationToSelf: String =
            transactionVM.privateRemark ?: throw IllegalArgumentException("This cannot be null")
        val lastIndex = transactionDetailsContainer.size - 1
        transactionDetailsContainer.addView(
            getAddLabelWithText(
                TransactionStatusItemDetail.LabelWithText(
                    LocalizedString(
                        "Narration to self",
                        "Kufotokozera Ndekha",
                    ),
                    narrationToSelf,
                ),
            ),
            lastIndex - 1,
        )
        transactionDetailsContainer.addView(getDivider(), lastIndex - 1)
        allowAddingRemarks = false
    }

    private fun showLoadingStateView() {
        remarkPB.visibility = View.VISIBLE
        root.visibility = View.GONE
    }

    private fun handleExpiredTransactionData(expiredTransactionDetails: ExpiredTransactionDetails) {
        titleTV.text = expiredTransactionDetails.title
        dateTimeTV.text = expiredTransactionDetails.dateTime
        transactionIdTV.text = expiredTransactionDetails.transactionId
        handleTopSectionColor(R.color.expire_transaction_theme_color)
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        transactionIV.background = getDrawable(R.drawable.ic_expire)
        amountTV.text = displayAmountWithCurrency(
            expiredTransactionDetails.transactionCurrency,
            expiredTransactionDetails.transactionAmount.getUserFacingValue(),
        )
        if (!expiredTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text = expiredTransactionDetails.transactionDescriptions
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        expiredTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun handleCancelledTransactionData(cancelledTransactionDetails: CancelledTransactionDetails) {
        titleTV.text = cancelledTransactionDetails.title
        dateTimeTV.text = cancelledTransactionDetails.dateTime
        transactionIdTV.text = cancelledTransactionDetails.transactionId
        handleTopSectionColor(R.color.failed_transaction_theme_color)
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        transactionIV.background = getDrawable(R.drawable.ic_cancelled_screen)
        amountTV.text = displayAmountWithCurrency(
            cancelledTransactionDetails.transactionCurrency,
            cancelledTransactionDetails.transactionAmount.getUserFacingValue(),
        )
        if (!cancelledTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text = cancelledTransactionDetails.transactionDescriptions
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        cancelledTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun handleFailedTransactionData(failedTransactionDetails: FailedTransactionDetails) {
        titleTV.text = failedTransactionDetails.title
        dateTimeTV.text = failedTransactionDetails.dateTime
        transactionIdTV.text = failedTransactionDetails.transactionId
        subtitleTV.visibility = View.VISIBLE
        if (failedTransactionDetails.failureDescription != null) {
            subtitleTV.text = failedTransactionDetails.failureDescription
        } else {
            subtitleTV.visibility = View.GONE
        }
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        handleTopSectionColor(R.color.failed_transaction_theme_color)
        transactionIV.background = getDrawable(R.drawable.ic_failed)
        amountTV.text = displayAmountWithCurrency(
            failedTransactionDetails.transactionCurrency,
            failedTransactionDetails.transactionAmount.getUserFacingValue(),
        )
        if (!failedTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text = failedTransactionDetails.transactionDescriptions
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        failedTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun handleSuccessfulTransactionData(successfulTransactionDetails: SuccessfulTransactionDetails) {
        titleTV.text = successfulTransactionDetails.title
        dateTimeTV.text = successfulTransactionDetails.dateTime
        transactionIdTV.text = successfulTransactionDetails.transactionId
        transactionIV.background = getDrawable(R.drawable.ic_success_tick)
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        handleTopSectionColor(R.color.successful_transaction_theme_color)
        amountTV.text = displayAmountWithCurrency(
            successfulTransactionDetails.transactionCurrency,
            successfulTransactionDetails.transactionAmount.getUserFacingValue(),
        )

        allowAddingRemarks = successfulTransactionDetails.allowAddingRemarks
        initiatedAmount = successfulTransactionDetails.transactionAmount
        initiatedDate = successfulTransactionDetails.dateTime

        if (!successfulTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text =
                successfulTransactionDetails.transactionDescriptions.replace("\\n", "\n")
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        successfulTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun handlePendingTransactionData(pendingTransactionDetails: PendingTransactionDetails) {
        titleTV.text = pendingTransactionDetails.title
        dateTimeTV.text = pendingTransactionDetails.dateTime
        transactionIdTV.text = pendingTransactionDetails.transactionId
        transactionIV.background = getDrawable(R.drawable.ic_pending)
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        handleTopSectionColor(R.color.expire_transaction_theme_color)
        amountTV.text = displayAmountWithCurrency(
            pendingTransactionDetails.transactionCurrency,
            pendingTransactionDetails.transactionAmount.getUserFacingValue(),
        )

        allowAddingRemarks = pendingTransactionDetails.allowAddingRemarks
        initiatedAmount = pendingTransactionDetails.transactionAmount
        initiatedDate = pendingTransactionDetails.dateTime

        if (!pendingTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text = pendingTransactionDetails.transactionDescriptions
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        pendingTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun handleReverseTransactionData(reverseTransactionDetails: ReverseTransactionDetails) {
        titleTV.text = reverseTransactionDetails.title
        dateTimeTV.text = reverseTransactionDetails.dateTime
        transactionIdTV.text = reverseTransactionDetails.transactionId
        transactionIV.background = getDrawable(R.drawable.ic_reverse)
        supportTV.visibility = View.VISIBLE
        supportHeaderTV.visibility = View.VISIBLE
        supportDescTV.visibility = View.VISIBLE
        handleTopSectionColor(R.color.failed_transaction_theme_color)
        amountTV.text = displayAmountWithCurrency(
            reverseTransactionDetails.transactionCurrency,
            reverseTransactionDetails.transactionAmount.getUserFacingValue(),
        )

        allowAddingRemarks = false
        initiatedAmount = reverseTransactionDetails.transactionAmount
        initiatedDate = reverseTransactionDetails.dateTime

        if (!reverseTransactionDetails.transactionDescriptions.isNullOrBlank()) {
            transactionDescriptionTV.text = reverseTransactionDetails.transactionDescriptions
            transactionDescriptionTV.visibility = View.VISIBLE
        } else {
            transactionDescriptionTV.visibility = View.GONE
        }
        reverseTransactionDetails.itemDetails.addItemDetailsToScreen()
        setupContentDescription()
    }

    private fun List<TransactionStatusItemDetail>.addItemDetailsToScreen() {
        this.forEach { itemDetail ->
            when (itemDetail) {
                is TransactionStatusItemDetail.LargeIconWithText -> {
                    addLargeIconWithText(itemDetail)
                }

                is TransactionStatusItemDetail.SmallIconWithText -> {
                    addSmallIconWithText(itemDetail)
                }

                is TransactionStatusItemDetail.LabelWithText -> {
                    addLabelWithText(itemDetail)
                }

                is TransactionStatusItemDetail.CopyableText -> {
                    addCopyableText(itemDetail)
                }
            }
            if (itemDetail == this[this.size - 1]) {
                val transactionId: String? = transactionVM.transactionID
                if (transactionId != null) {
                    if (transactionId == transactionIdTV.text.toString() && allowAddingRemarks!!) {
                        addRemarkView()
                    }
                }
            }
        }
    }

    private fun addLargeIconWithText(itemDetail: TransactionStatusItemDetail.LargeIconWithText) {
        val largeIconWithText = LargeIconWithText(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        largeIconWithText.setLayoutData(
            itemDetail.icon,
            requireContext().getLocalizedString(itemDetail.label.en, itemDetail.label.ny),
            itemDetail.title,
            itemDetail.description,
        )
        largeIconWithText.layoutParams = layoutParams
        addDivider()
        transactionDetailsContainer.addView(largeIconWithText)
    }

    private fun addSmallIconWithText(itemDetail: TransactionStatusItemDetail.SmallIconWithText) {
        val smallIconWithText = SmallIconWithText(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        smallIconWithText.setLayoutData(
            itemDetail.icon,
            requireContext().getLocalizedString(itemDetail.label.en, itemDetail.label.ny),
            itemDetail.title,
            itemDetail.description,
        )
        smallIconWithText.layoutParams = layoutParams
        addDivider()
        transactionDetailsContainer.addView(smallIconWithText)
    }

    private fun addDivider() {
        val divider = View(requireContext())
        val dividerLayoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            1.getDPPixelValue(),
        )
        divider.background = ContextCompat.getDrawable(requireContext(), R.color.listDividerColor)
        divider.layoutParams = dividerLayoutParams
        transactionDetailsContainer.addView(divider)
    }

    private fun getDivider(): View {
        val divider = View(requireContext())
        val dividerLayoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            1.getDPPixelValue(),
        )
        divider.background = ContextCompat.getDrawable(requireContext(), R.color.listDividerColor)
        divider.layoutParams = dividerLayoutParams
        return divider
    }

    private fun addLabelWithText(itemDetail: TransactionStatusItemDetail.LabelWithText) {
        val labelWithText = LabelWithText(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        labelWithText.setLayoutData(
            requireContext().getLocalizedString(itemDetail.label.en, itemDetail.label.ny),
            itemDetail.text,
        )
        labelWithText.layoutParams = layoutParams
        addDivider()
        transactionDetailsContainer.addView(labelWithText)
    }

    private fun getAddLabelWithText(itemDetail: TransactionStatusItemDetail.LabelWithText): View {
        val labelWithText = LabelWithText(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        labelWithText.setLayoutData(
            requireContext().getLocalizedString(itemDetail.label.en, itemDetail.label.ny),
            itemDetail.text,
        )
        labelWithText.layoutParams = layoutParams
        return labelWithText
    }

    private fun addCopyableText(itemDetail: TransactionStatusItemDetail.CopyableText) {
        val copyableText = CopyableText(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        copyableText.setLayoutData(
            requireContext().getLocalizedString(itemDetail.label.en, itemDetail.label.ny),
            itemDetail.text,
        )
        copyableText.layoutParams = layoutParams
        addDivider()
        transactionDetailsContainer.addView(copyableText)
    }

    private fun handleTopSectionColor(color: Int) {
        setStatusBarColor(color)
        setToolbarBackgroundColor(color)
    }

    private fun setToolbar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.successful_transaction_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            setToolbarBackgroundColor(R.color.successful_transaction_theme_color)
            val backArrowIcon = toolbarView.findViewById<ImageView>(R.id.ic_back_arrow)
            backArrowIcon.setOnClickListener {
                transactionStatusVM.onBackPressed(requireActivity())
            }

            toolbarView.findViewById<ImageView>(R.id.overflow_icon).apply {
                setOnClickListener {
                    onOverflowMenuClick()
                }
            }
        }
    }

    private fun ImageView?.onOverflowMenuClick() {
        val popupMenu = PopupMenu(requireContext(), this)
        popupMenu.menuInflater.inflate(R.menu.trasanction_status_menu, popupMenu.menu)
        val remarkMenu = popupMenu.menu.findItem(R.id.option_remark)
        if (allowAddingRemarks != null) {
            remarkMenu.isVisible = allowAddingRemarks!!
        } else {
            remarkMenu.isVisible = false
        }
        popupMenu.setOnMenuItemClickListener {
            when (it.itemId) {
                R.id.option_share -> onOptionShare()
                R.id.option_remark -> {
                    onOptionRemark()
                }

                else -> false
            }
        }
        <EMAIL> = popupMenu
        popupMenu.show()
    }

    override fun onDeviceLocked() {
        super.onDeviceLocked()
        popupMenu?.dismiss()
    }

    private fun onOptionRemark(): Boolean {
        transactionStatusVM.onOptionAddRemarkSelected()
        return true
    }

    private fun handleOnOptionAddRemark() {
        val remarkDialog = TextInputDialog()
        remarkDialog.setArguments(TextInputDialogUseCase.REMARKS)
        remarkDialog.show(childFragmentManager, TextInputDialog.TAG)
    }

    private fun onOptionShare(): Boolean {
        doneButton.visibility = View.GONE
        supportTV.visibility = View.GONE
        supportHeaderTV.visibility = View.GONE
        supportDescTV.visibility = View.GONE
        networkInfo.visibility = View.GONE
        shouldTakeScreenShot = true
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        resetToolbarColor()
        setStatusBarColor()
    }

    override fun onResume() {
        super.onResume()
        transactionStatusVM.onResume(doneButton.isVisible)
    }

    private fun reactToVisibility(isDoneButtonVisible: Boolean) {
        if (!isDoneButtonVisible) {
            doneButton.visibility = View.VISIBLE
            supportTV.visibility = View.VISIBLE
            supportHeaderTV.visibility = View.VISIBLE
            supportDescTV.visibility = View.VISIBLE
        } else {
            Timber.tag(TAG).i("Buttons are visible")
        }
    }

    private fun handleSupportAction() {
        supportTV.setOnClickListener {
            val contactSupport = ContactSupport()
            contactSupport.show(childFragmentManager, ContactSupport.TAG)
        }
    }

    companion object {
        const val TRANSACTION_DATA_KEY: String = "transaction_status_data_key"
    }

    override fun onTextInputSave(text: String) {
        transactionVM.privateRemark = text
        transactionStatusVM.onAddRemark(
            requireContext(),
            UUID.fromString(transactionIdTV.text.toString()),
            text,
        )
    }

    override fun dismissed() {
        transactionStatusVM.onRemarkDialogDismissed()
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("Positive action occurred on alert dialog having id $dialogId.")
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun handleNoEmailAppInstalled() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.transactionStatusContactSupportButtonTitle),
            getString(
                R.string.contactSupportNoEmailApp,
            ),
            DialogCodes.CONTACT_SUPPORT_ERROR_DIALOG_ID,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }
}

private const val TAG = "TransactionStatusFragment"
