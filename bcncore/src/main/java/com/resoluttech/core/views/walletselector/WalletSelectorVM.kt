package com.resoluttech.core.views.walletselector

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.accounttoaccount.AccountToAccountSharedPrefHelper
import com.resoluttech.core.home.MoneyScreenDataPersistor
import com.resoluttech.core.profile.manageaccount.ActiveAccountsItem
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getUserFacingValue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class WalletSelectorVM : ViewModel() {

    private val _state: MutableLiveData<WalletSelectorScreenState> =
        MutableLiveData(WalletSelectorScreenState.Loading)
    val state: LiveData<WalletSelectorScreenState> = _state

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val moneyScreenDataPersistor = MoneyScreenDataPersistor()
    private val listOfActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()

    fun onViewCreated() {
        vmIOScope.launch {
            listOfActiveAccounts.clear()
            val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            SelectedAccountHelper.getPersistedAccounts().map { account ->
                if (listOfActiveAccounts.find { it is ActiveAccountsItem.AccountHeader && it.currency.currencyCode == account.currencyCode } == null) {
                    listOfActiveAccounts.add(
                        ActiveAccountsItem.AccountHeader(
                            currency = Currency(account.currencyCode),
                        ),
                    )
                }
                listOfActiveAccounts.add(
                    ActiveAccountsItem.Account(
                        accountName = account.name,
                        balance = displayAmountWithCurrency(
                            currency = account.currencyCode,
                            userFacingAmount = account.balance.getUserFacingValue(),
                        ),
                        accountId = account.id,
                        currency = Currency(account.currencyCode),
                        isPrimary = account.isDefault,
                        isActive = true,
                        isSelected = selectedAccount?.id == account.id,
                    ),
                )
            }
            _state.postValue(
                WalletSelectorScreenState.Data(
                    walletsList = listOfActiveAccounts,
                ),
            )
        }
    }

    fun onWalletSelected(context: Context, accountId: UUID) {
        vmIOScope.launch {
            SelectedAccountHelper.setSelectedAccountId(
                context = context,
                accountId = accountId,
            )
        }
    }

    fun storeAccountToAccountDebitWalletData(
        accountId: UUID,
        accountName: String,
        accountIndex: Int,
    ) {
        vmIOScope.launch {
            AccountToAccountSharedPrefHelper.setDebitAccountName(accountName)
            AccountToAccountSharedPrefHelper.setDebitAccountId("$accountId")
            AccountToAccountSharedPrefHelper.setDebitAccountIndex(accountIndex)
        }
    }

    fun storeAccountToAccountCreditWalletData(
        accountId: UUID,
        accountName: String,
        accountIndex: Int,
    ) {
        vmIOScope.launch {
            AccountToAccountSharedPrefHelper.setCreditAccountName(accountName)
            AccountToAccountSharedPrefHelper.setCreditAccountId("$accountId")
            AccountToAccountSharedPrefHelper.setCreditAccountIndex(accountIndex)
        }
    }
}

sealed class WalletSelectorScreenState {
    object Loading : WalletSelectorScreenState()
    data class Data(
        val walletsList: List<ActiveAccountsItem>,
    ) : WalletSelectorScreenState()
}
