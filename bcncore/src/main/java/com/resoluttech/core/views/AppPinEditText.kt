package com.resoluttech.core.views

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatEditText
import com.resoluttech.bcncore.R

/**
 * A special edit text which takes care of the App Pin entered in realtime.
 */
class AppPinEditText(context: Context, attributeSet: AttributeSet? = null) : AppCompatEditText(context, attributeSet) {

    /**
     * Gives callbacks to implementer when app pin is entered
     */
    interface AppPinFieldListener {
        fun onAppPinEntered(appPin: String)
        fun onTextCleared()
    }

    private var isEditing = true
    private var isDeletingCharacter = false
    private var enteredAppPin = ""
    private var listener: AppPinFieldListener? = null

    private val attributes: AppPinEditTextAttributes = AppPinEditTextAttributes(context, attributeSet)

    init {

        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                // Nothing
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (isEditing) {
                    isEditing = false
                    isDeletingCharacter = before > 0
                    if (s != null && "$s".isNotEmpty() && !isDeletingCharacter) {
                        if (attributes.enableMask) {
                            enteredAppPin = getEnteredPin(enteredAppPin, s[start], start)
                            <EMAIL>(getMask("$s"))
                            <EMAIL>("$s".length)
                        } else {
                            enteredAppPin = "$s"
                        }
                        if (enteredAppPin.length == 4) {
                            listener?.onAppPinEntered(enteredAppPin)
                        }
                    } else if (isDeletingCharacter) {
                        enteredAppPin = enteredAppPin.slice(0 until start) + enteredAppPin.slice((start + 1) until enteredAppPin.length)
                        listener?.onTextCleared()
                    }
                    isEditing = true
                }
            }

            override fun afterTextChanged(s: Editable?) {
                if (s.isNullOrEmpty()) {
                    listener?.onTextCleared()
                    enteredAppPin = ""
                }
            }
        })
    }

    private fun getMask(text: String): String {
        var mask = ""
        for (i in 1..text.length) {
            mask = "$mask*"
        }
        return mask
    }

    private fun getEnteredPin(str: String, char: Char, pos: Int): String {
        return str.take(pos) + "$char" + str.substring(pos)
    }

    fun setListener(listener: AppPinFieldListener) {
        this.listener = listener
    }
}

private class AppPinEditTextAttributes(context: Context, attributeSet: AttributeSet?) {

    var enableMask: Boolean internal set

    init {
        context.obtainStyledAttributes(attributeSet, R.styleable.AppPinEditText).run {
            enableMask = getBoolean(R.styleable.AppPinEditText_enableMask, true)
            recycle()
        }
    }
}
