package com.resoluttech.core.views

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View.OnTouchListener
import android.widget.EditText
import androidx.appcompat.widget.AppCompatEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.core.config.Config
import com.resoluttech.core.config.Config.Companion.FIRST_INDEX
import com.resoluttech.core.config.Config.Companion.TEXT_COUNTER_THRESHOLD
import com.resoluttech.core.utils.getUTF8Size

/**
 * A special edit text designed for limited character input, can be used to remarks or nickname etc, the counter will be enabled by default.
 * */
class LimitedCharacterEditText : AppCompatEditText {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr,
    )

    init {
        // Thanks : https://stackoverflow.com/a/24428854/5163725
        // Solves the problem of nested scrolling of multiline edittext inside scrollview.
        scrollBarStyle = SCROLLBARS_INSIDE_INSET
        overScrollMode = OVER_SCROLL_ALWAYS
        isVerticalScrollBarEnabled = true
        setOnTouchListener(
            OnTouchListener { v, event ->
                if (hasFocus()) {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                    when (event.action and MotionEvent.ACTION_MASK) {
                        MotionEvent.ACTION_SCROLL -> {
                            v.parent.requestDisallowInterceptTouchEvent(false)
                            return@OnTouchListener true
                        }

                        else -> performClick()
                    }
                }
                false
            },
        )
    }

    private var textInputLayout: TextInputLayout? = null
    private val limitedCharacterTextWatcher: LimitedCharacterTextWatcher by lazy {
        LimitedCharacterTextWatcher(
            textInputLayout!!,
        )
    }

    /**
     * This method sets the text input layout which wraps the [LimitedCharacterEditText].
     *
     * @param textInputLayout: [TextInputLayout] which wrap the current [LimitedCharacterEditText].
     * @param inputMaxLength: Maximum character allowed for input.
     *
     * The visibility of counter label is enabled and disabled using [LimitedCharacterTextWatcher] & [textInputLayout].
     * */
    fun setTextInputLayout(textInputLayout: TextInputLayout, inputMaxLength: Int) {
        textInputLayout.apply {
            isHelperTextEnabled = true
            counterMaxLength = inputMaxLength
            <EMAIL> = this
            filters = arrayOf(InputFilter.LengthFilter(counterMaxLength))
            addTextChangedListener(limitedCharacterTextWatcher)
        }
    }

    /**
     * This method returns null when nothing is entered as input else it returns the entered string
     * */
    fun getInputText(): String? {
        return if (text != null && "$text".isNotEmpty()) {
            text.toString().trim()
        } else {
            null
        }
    }
}

private class LimitedCharacterTextWatcher(private val textInputLayout: TextInputLayout) :
    TextWatcher {

    private var lastValue = ""

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        lastValue = s.toString().trimStart()
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    override fun afterTextChanged(s: Editable?) {
        val cleanedStringLength: Int
        (
            textInputLayout.editText
                ?: throw IllegalStateException("TextInputLayout does not have an ET.")
            ).apply {
            removeTextChangedListener(this@LimitedCharacterTextWatcher)
            if ("$text".getUTF8Size() > textInputLayout.counterMaxLength) {
                resetSelection(lastValue)
                cleanedStringLength = lastValue.getUTF8Size()
            } else {
                if (lineCount > Config.MAX_REMARKS_LINES) {
                    resetSelection(lastValue)
                    cleanedStringLength = lastValue.getUTF8Size()
                } else {
                    if (s != null && (s.isBlank() || s[FIRST_INDEX] == ' ')) {
                        val cleanedString = "$s".trimStart()
                        setText(cleanedString)
                        setSelection(FIRST_INDEX)
                        cleanedStringLength = FIRST_INDEX
                    } else {
                        cleanedStringLength = s.toString().getUTF8Size()
                    }
                }
            }
            addTextChangedListener(this@LimitedCharacterTextWatcher)
        }

        handleHelperCounterText(currentTextLength = cleanedStringLength)
        if (textInputLayout.isErrorEnabled) {
            textInputLayout.isErrorEnabled = false
        }
    }

    private fun handleHelperCounterText(currentTextLength: Int) {
        // Enables the counter label only when user fills >=80% of the remarks length.
        if (currentTextLength >= textInputLayout.counterMaxLength * TEXT_COUNTER_THRESHOLD) {
            textInputLayout.isHelperTextEnabled = true
            textInputLayout.helperText = "$currentTextLength/${textInputLayout.counterMaxLength}"
        } else {
            textInputLayout.isHelperTextEnabled = false
        }
    }
}

class SingleLineCharacterLimitTextWatcher(
    private val textInputLayout: TextInputLayout,
    private val maxLength: Int,
    private val beforeTextChanged: () -> Unit = {},
    private val onTextChanged: (s: CharSequence?) -> Unit = {},
    private val afterTextChange: (s: Editable?) -> Unit = {},
) : TextWatcher {

    private var lastValue = ""

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        lastValue = s.toString().trimStart()
        beforeTextChanged()
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        onTextChanged(s)
    }

    override fun afterTextChanged(s: Editable?) {
        (
            textInputLayout.editText
                ?: throw IllegalStateException("TextInputLayout does not have an ET.")
            ).apply {
            removeTextChangedListener(this@SingleLineCharacterLimitTextWatcher)
            if ("$text".getUTF8Size() > maxLength) {
                resetSelection(lastValue)
            } else {
                if (s != null && (s.isBlank() || s[FIRST_INDEX] == ' ')) {
                    val cleanedString = "$s".trimStart()
                    setText(cleanedString)
                    setSelection(FIRST_INDEX)
                }
            }
            addTextChangedListener(this@SingleLineCharacterLimitTextWatcher)
        }

        if (textInputLayout.isErrorEnabled) {
            textInputLayout.isErrorEnabled = false
        }
        afterTextChange(s)
    }
}

private fun EditText.resetSelection(lastValue: String) {
    var selectionStart = selectionStart - Config.ET_RESET_INDEX
    setText(lastValue)
    if (selectionStart >= length()) {
        selectionStart = length()
    }
    setSelection(selectionStart)
}
