package com.resoluttech.core.views

import com.resoluttech.bcn.transactions.AddPrivateRemarkRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

class RemarkRepository {

    private val addPrivateRemarkRPC: AddPrivateRemarkRPC by KoinJavaComponent.inject(
        AddPrivateRemarkRPC::class.java,
    )

    suspend fun addPrivateRemark(transactionId: UUID, remark: String): LeoRPCResult<AddPrivateRemarkRPC.Response, AddPrivateRemarkRPC.Error> {
        return addPrivateRemarkRPC.execute(AddPrivateRemarkRPC.Request(transactionId, remark))
    }
}
