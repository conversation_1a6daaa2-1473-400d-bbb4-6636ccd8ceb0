package com.resoluttech.core.views

import android.content.Context
import android.net.ConnectivityManager
import android.net.ConnectivityManager.NetworkCallback
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import timber.log.Timber

fun Context.isNetworkConnected(): Boolean {
    val connectivityManager =
        getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    val networkInfo = connectivityManager.activeNetwork ?: return false
    val activeNetworkInfo = connectivityManager.getNetworkCapabilities(networkInfo) ?: return false
    return when {
        activeNetworkInfo.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) || activeNetworkInfo.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) || activeNetworkInfo.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> true
        else -> false
    }
}

fun Context.registerAndGetNetworkCallback(onNetworkAvailableBlock: () -> Unit, onNetworkLostBlock: () -> Unit): NetworkCallback {
    val connectivityManager =
        getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    return if (Build.VERSION.SDK_INT > 24) {
        val networkCallback: NetworkCallback = object : NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                onNetworkAvailableBlock()
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                onNetworkLostBlock()
            }
        }
        connectivityManager.registerDefaultNetworkCallback(
            networkCallback,
        )
        networkCallback
    } else {
        val networkCallback: NetworkCallback = object : NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                onNetworkAvailableBlock()
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                onNetworkLostBlock()
            }
        }
        val networkChangeFilter = NetworkRequest.Builder().build()
        connectivityManager.registerNetworkCallback(
            networkChangeFilter,
            networkCallback,
        )
        networkCallback
    }
}

fun Context.unRegisterNetwork(networkCallback: NetworkCallback?) {
    val connectivityManager =
        getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    if (networkCallback != null) {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
            Timber.i("Network unregistered failed")
        }
    }
}
