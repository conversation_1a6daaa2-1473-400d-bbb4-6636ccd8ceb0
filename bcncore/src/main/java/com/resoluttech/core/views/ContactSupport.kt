package com.resoluttech.core.views

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.net.toUri
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.IntentUtils

class ContactSupport : BottomSheetDialogFragment() {

    interface ActionListener {
        fun handleNoEmailAppInstalled()
    }

    private lateinit var contactSupportPhoneView: TextView
    private lateinit var contactSupportEmailView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.dialog_contact_support_bottom_sheet, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupContactSupportPhone()
        setupContactSupportEmail()
    }

    private fun initViews(view: View) {
        contactSupportPhoneView = view.findViewById(R.id.call_us)
        contactSupportEmailView = view.findViewById(R.id.send_email)
    }

    private fun setupContactSupportPhone() {
        contactSupportPhoneView.setOnClickListener {
            IntentUtils.callIntent(requireContext(), PHONE_NUMBER)
        }
    }

    private fun setupContactSupportEmail() {
        contactSupportEmailView.setOnClickListener {
            val intent = Intent(Intent.ACTION_SENDTO)
            intent.data = "mailto:$EMAIL".toUri()
            if (intent.resolveActivity(requireActivity().packageManager) != null) {
                startActivity(intent)
            } else {
                dismiss()
                if (parentFragment != null && parentFragment is ActionListener) {
                    dismiss()
                    (parentFragment as ActionListener).handleNoEmailAppInstalled()
                } else {
                    throw IllegalStateException("$parentFragment must implement ContactSupportBottomSheet.ActionListener.")
                }
            }
        }
    }

    companion object {
        const val TAG: String = "ContactSupport"
    }
}

private const val EMAIL: String = "<EMAIL>"
private const val PHONE_NUMBER: String = "+265888693224"
