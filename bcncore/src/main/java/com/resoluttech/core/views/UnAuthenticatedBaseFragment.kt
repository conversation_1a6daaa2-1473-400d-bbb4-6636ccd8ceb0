package com.resoluttech.core.views

import android.net.ConnectivityManager
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.ProgressDialog
import com.resoluttech.core.uicomponents.ResetPasswordAlertDialog
import com.resoluttech.core.utils.checkForAppLinkArguments
import timber.log.Timber

open class UnAuthenticatedBaseFragment : Fragment(), ResetPasswordAlertDialog.ActionListener {

    private lateinit var networkInfo: TextView
    protected var networkListenerCallback: NetworkListener? = null
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private lateinit var progressDialog: ProgressDialog

    interface NetworkListener {
        fun onNetworkAvailable()
    }

    override fun onResume() {
        super.onResume()
        try {
            networkInfo = requireActivity().findViewById(R.id.network_info)

            if (!requireContext().isNetworkConnected()) {
                handleNetworkLostState()
            }

            networkCallback = requireContext().registerAndGetNetworkCallback(
                onNetworkAvailableBlock = {
                    activity?.apply {
                        if (isAdded && ::networkInfo.isInitialized) {
                            runOnUiThread {
                                networkInfo.setBackgroundColor(requireContext().getColor(R.color.successful_transaction_theme_color))
                                networkInfo.text =
                                    requireActivity().getString(R.string.backOnlineBannerText)
                                if (networkInfo.isVisible) {
                                    networkInfo.postDelayed(
                                        {
                                            networkInfo.collapse(500)
                                            networkListenerCallback?.onNetworkAvailable()
                                        },
                                        1000,
                                    )
                                }
                            }
                        }
                    }
                },
                onNetworkLostBlock = ::handleNetworkLostState,
            )
        } catch (e: NullPointerException) {
            Timber.tag(TAG).e(e)
        }
        checkForAppLinkArguments(
            requireActivity(),
            requireContext(),
            findNavController(),
            childFragmentManager,
            isUserSignedIn = false,
        )
    }

    protected fun handleNetworkLostState() {
        activity?.apply {
            if (isAdded && ::networkInfo.isInitialized) {
                runOnUiThread {
                    networkInfo.visibility = View.VISIBLE
                    networkInfo.text =
                        requireActivity().getString(R.string.noInternetBannerText)
                    networkInfo.setBackgroundColor(requireContext().getColor(R.color.destructiveActionColor))
                    networkInfo.expand(500)
                }
            }
        }
    }

    fun showProgressDialog(childFragmentManager: FragmentManager, message: String) {
        progressDialog = ProgressDialog.newInstance(
            message,
        )
        progressDialog.setArguments(false)
        progressDialog.show(childFragmentManager, ProgressDialog.TAG)
    }

    fun dismissProgressDialog() {
        if (::progressDialog.isInitialized) {
            progressDialog.dismiss()
        }
    }

    override fun onStop() {
        super.onStop()
        activity?.apply {
            if (isAdded && ::networkInfo.isInitialized) {
                runOnUiThread {
                    networkInfo.visibility = View.GONE
                }
            }
        }
        requireContext().unRegisterNetwork(networkCallback)
    }

    override fun onPositiveAction() {
        // Nothing to handle.
    }
}

private const val TAG = "UnAuthenticatedBaseFragment"
