package com.resoluttech.core.views

import android.content.Context
import com.resoluttech.bcn.types.TransactionStatusItemDetail
import com.resoluttech.core.utils.getUri

fun List<TransactionStatusItemDetail>.getItemDetails(context: Context): List<com.resoluttech.core.views.TransactionStatusItemDetail> {
    return this.map {
        when (val valueType = it.valueType) {
            is TransactionStatusItemDetail.ValueType.LargeIconWithText -> {
                com.resoluttech.core.views.TransactionStatusItemDetail.LargeIconWithText(
                    LocalizedString(it.label.en, it.label.ny),
                    valueType.title,
                    valueType.description,
                    valueType.image.getUri(context),
                )
            }
            is TransactionStatusItemDetail.ValueType.SmallIconWithText -> {
                com.resoluttech.core.views.TransactionStatusItemDetail.SmallIconWithText(
                    LocalizedString(it.label.en, it.label.ny),
                    valueType.title,
                    valueType.description,
                    valueType.image.getUri(context),
                )
            }
            is TransactionStatusItemDetail.ValueType.Text -> {
                com.resoluttech.core.views.TransactionStatusItemDetail.LabelWithText(
                    LocalizedString(it.label.en, it.label.ny),
                    valueType.text,
                )
            }
            is TransactionStatusItemDetail.ValueType.CopyableText -> {
                com.resoluttech.core.views.TransactionStatusItemDetail.CopyableText(
                    LocalizedString(it.label.en, it.label.ny),
                    valueType.text,
                )
            }
        }
    }
}
