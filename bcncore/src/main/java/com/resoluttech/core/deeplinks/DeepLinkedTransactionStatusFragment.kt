package com.resoluttech.core.deeplinks

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.home.newLineDecoder
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getDrawable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.resetToolbarColor
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setToolbarBackgroundColor
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.ContactSupport
import timber.log.Timber
import java.time.Instant
import java.util.Locale

class DeepLinkedTransactionStatusFragment :
    BaseFragment(),
    ContactSupport.ActionListener,
    AlertDialog.ActionListener {

    private lateinit var dateTimeTV: TextView
    private lateinit var transactionIdTV: TextView
    private lateinit var amountTV: TextView
    private lateinit var titleTV: TextView
    private lateinit var privateRemarkTV: TextView
    private lateinit var privateRemarkLabelTV: TextView
    private lateinit var transactionIdLabelTV: TextView
    private lateinit var dividerOne: View
    private lateinit var dividerTwo: View
    private lateinit var privateLayoutSection: LinearLayout
    private lateinit var publicLayoutSection: LinearLayout
    private lateinit var publicRemarkTV: TextView
    private lateinit var publicRemarkLabelTV: TextView
    private lateinit var failureReasonTV: TextView
    private lateinit var doneButton: MaterialButton
    private lateinit var supportButton: TextView
    private lateinit var statusIconIV: ImageView
    private lateinit var root: View

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_deeplinked_transaction_status, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setToolbar()
        updateViews(requireArguments())
    }

    private fun updateViews(args: Bundle) {
        args.run {
            val resource = getLocalizedResources()
            updateBackground(resource)
            updateDateTime()
            updateAmount()
            updatePrivateRemark(resource)
            updatePublicRemark(resource)
            updateTransactionId(resource)
            updateFailureReason()
            updateContactSupport(resource)
            setupContentDescription()
            doneButton.setOnClickListener {
                findNavController().navigateUp()
            }
        }
    }

    private fun setToolbar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.successful_transaction_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            setToolbarBackgroundColor(R.color.successful_transaction_theme_color)
            toolbarView.findViewById<ImageView>(R.id.overflow_icon).apply {
                visibility = View.GONE
            }
        }
    }

    private fun Bundle.updateFailureReason() {
        getString(FAILURE_REASON_ID_KEY).let {
            if (it.isValid()) {
                failureReasonTV.visibility = View.VISIBLE
                failureReasonTV.text = it
            } else {
                failureReasonTV.visibility = View.GONE
            }
        }
    }

    private fun setupContentDescription() {
        transactionIdTV.addContentDescriptionString(
            R.string.axTransactionStatusIDHint,
            requireContext(),
            transactionIdTV.text.toString(),
        )
        amountTV.addContentDescriptionString(
            R.string.axTransactiontatusAmountLabel,
            requireContext(),
            amountTV.text.toString(),
        )
    }

    private fun Bundle.getLocalizedResources(): Resources {
        getString(LOCALE_KEY).let {
            val context = requireContext()
            var conf: Configuration = context.resources.configuration
            conf = Configuration(conf)
            conf.setLocale(Locale(it ?: SupportedLocale.EN_US.identifier))
            val localizedContext: Context = context.createConfigurationContext(conf)
            return localizedContext.resources
        }
    }

    private fun String?.isValid(): Boolean {
        return if (this.isNullOrEmpty()) {
            false
        } else {
            this != "null"
        }
    }

    private fun Bundle.updateTransactionId(resource: Resources) {
        transactionIdLabelTV.text = resource.getString(R.string.transactionStatusScreenIdLabel)
        transactionIdTV.text = getString(TRANSACTION_ID_KEY)
    }

    private fun updateContactSupport(resource: Resources) {
        supportButton.text = resource.getString(R.string.transactionStatusContactSupportButtonTitle)
        supportButton.setOnClickListener {
            val contactSupport = ContactSupport()
            contactSupport.show(childFragmentManager, ContactSupport.TAG)
        }
    }

    private fun Bundle.updatePublicRemark(resource: Resources) {
        getString(PUBLIC_REMARK_ID_KEY).let {
            if (it.isValid()) {
                publicRemarkLabelTV.text = resource.getString(R.string.transactionStatusNarrationForReceiverLabel)
                publicRemarkTV.text = it?.newLineDecoder()
            } else {
                publicLayoutSection.visibility = View.GONE
                dividerTwo.visibility = View.GONE
            }
        }
    }

    private fun Bundle.updatePrivateRemark(resource: Resources) {
        getString(PRIVATE_REMARK_ID_KEY).let {
            if (it.isValid()) {
                privateRemarkLabelTV.text = resource.getString(R.string.transactionStatusNarrationToSelfLabel)
                privateRemarkTV.text = it?.newLineDecoder()
            } else {
                privateLayoutSection.visibility = View.GONE
                dividerOne.visibility = View.GONE
            }
        }
    }

    private fun Bundle.updateAmount() {
        amountTV.text = displayAmountWithCurrency(
            getString(CURRENCY_ID_KEY)!!,
            getString(AMOUNT_ID_KEY)!!.toLong().getUserFacingValue(),
        )
    }

    private fun Bundle.updateDateTime() {
        (
            Instant.parse(getString(DATE_TIME_ID_KEY)).getFormattedDateTime(
                context = requireContext(),
                dateTimeType = DateTimeType.TIME,
            ) + requireContext().getString(R.string.dateTimeSeparator) + Instant.parse(
                getString(
                    DATE_TIME_ID_KEY,
                ),
            ).getFormattedDateTime(
                context = requireContext(),
                dateTimeType = DateTimeType.DATE,
            )
            ).also {
            dateTimeTV.text = it
        }
    }

    private fun Bundle.updateBackground(resource: Resources) {
        when (getString(STATUS_ID_KEY)) {
            FAILURE_STATUS_VALUE -> {
                root.background = getDrawable(R.color.failed_transaction_theme_color)
                statusIconIV.background = getDrawable(R.drawable.ic_failed)
                titleTV.text = resource.getString(R.string.transactionStatusFailedLabel)
                handleTopSectionColor(R.color.failed_transaction_theme_color)
                setToolbarBackgroundColor(R.color.failed_transaction_theme_color)
            }

            SUCCESS_STATUS_VALUE -> {
                root.background = getDrawable(R.color.successful_transaction_theme_color)
                statusIconIV.background = getDrawable(R.drawable.ic_success_tick)
                titleTV.text = resource.getString(R.string.transactionStatusSuccessLabel)
                handleTopSectionColor(R.color.successful_transaction_theme_color)
                setToolbarBackgroundColor(R.color.successful_transaction_theme_color)
            }
        }
    }

    private fun handleTopSectionColor(color: Int) {
        setStatusBarColor(color)
        setToolbarBackgroundColor(color)
    }

    private fun initViews(view: View) {
        view.run {
            dateTimeTV = findViewById(R.id.date_time_tv)
            transactionIdTV = findViewById(R.id.transaction_id_tv)
            amountTV = findViewById(R.id.amount_tv)
            privateRemarkTV = findViewById(R.id.private_remark_tv)
            privateRemarkLabelTV = findViewById(R.id.private_remark_label)
            transactionIdLabelTV = findViewById(R.id.tranaaction_id_label)
            publicRemarkTV = findViewById(R.id.public_remark_tv)
            publicRemarkLabelTV = findViewById(R.id.public_remark_label)
            doneButton = findViewById(R.id.done_button)
            supportButton = findViewById(R.id.support_button)
            root = findViewById(R.id.top_container)
            privateLayoutSection = findViewById(R.id.private_layout_section)
            dividerOne = findViewById(R.id.divider_1)
            dividerTwo = findViewById(R.id.divider_2)
            publicLayoutSection = findViewById(R.id.public_layout_section)
            failureReasonTV = findViewById(R.id.subtitle_tv)
            statusIconIV = findViewById(R.id.transaction_state_iv)
            titleTV = findViewById(R.id.title_tv)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("Positive action occurred on alert dialog having id $dialogId.")
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun handleNoEmailAppInstalled() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.transactionStatusContactSupportButtonTitle),
            getString(
                R.string.contactSupportNoEmailApp,
            ),
            DialogCodes.CONTACT_SUPPORT_ERROR_DIALOG_ID,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    override fun onDestroy() {
        super.onDestroy()
        resetToolbarColor()
        setStatusBarColor()
    }

    companion object {
        const val TRANSACTION_ID_KEY: String = "transaction_id"
        const val DATE_TIME_ID_KEY: String = "date_time"
        const val PRIVATE_REMARK_ID_KEY: String = "private_remark"
        const val PUBLIC_REMARK_ID_KEY: String = "public_remark"
        const val FAILURE_REASON_ID_KEY: String = "failure_reason"
        const val LOCALE_KEY: String = "locale"
        const val STATUS_ID_KEY: String = "status"
        const val AMOUNT_ID_KEY: String = "amount"
        const val CURRENCY_ID_KEY: String = "currency"
        const val SUCCESS_STATUS_VALUE: String = "SUCCESS"
        const val FAILURE_STATUS_VALUE: String = "FAILURE"
    }
}

private const val TAG = "DeepLinkedTransactionStatusFragment"
