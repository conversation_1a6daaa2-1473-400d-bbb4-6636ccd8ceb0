package com.resoluttech.core.transfers.peertopeer

import com.resoluttech.bcn.transfers.GetRecentlyPaidRecipientsRPC
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.rpc.LeoServerException
import com.suryadigital.leo.rpc.LeoUnauthenticatedException
import com.suryadigital.leo.rpc.LeoUnauthorizedException
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import org.koin.java.KoinJavaComponent

class TransfersRepository {
    private val getRecentlyPaidRecipientsRPC: GetRecentlyPaidRecipientsRPC by KoinJavaComponent.inject(
        GetRecentlyPaidRecipientsRPC::class.java,
    )

    @Throws(
        LeoUnauthenticatedException::class,
        LeoUnauthorizedException::class,
        LeoUnsupportedClientException::class,
        LeoServerException::class,
        LeoInvalidResponseException::class,
    )
    suspend fun getRecentlyPaidUsers(): LeoRPCResult<GetRecentlyPaidRecipientsRPC.Response, GetRecentlyPaidRecipientsRPC.Error> {
        return getRecentlyPaidRecipientsRPC.execute(GetRecentlyPaidRecipientsRPC.Request)
    }
}
