package com.resoluttech.core.transfers.peertopeer

import com.resoluttech.bcn.transfers.BCNRecipientIdentifierType
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToExternalUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToExternalUserRequestRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromAccountIdRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromPhoneNumberRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromUserIdRPC
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.bcn.transfers.LookupRecipientAtCounterpartyRPC
import com.resoluttech.bcn.transfers.SendMoneyExternalRecipient
import com.resoluttech.bcn.types.Amount
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import java.util.UUID

class SendMoneyRepository {

    private val sendMoneyToBCNUserRPC: ConfirmSendMoneyToBCNUserRPC by KoinJavaComponent.inject(
        ConfirmSendMoneyToBCNUserRPC::class.java,
    )

    private val sendMoneyToExternalUserRPC: CreateSendMoneyToExternalUserRequestRPC by KoinJavaComponent.inject(
        CreateSendMoneyToExternalUserRequestRPC::class.java,
    )

    private val confirmSendMoneyToExternalUserRPC: ConfirmSendMoneyToExternalUserRPC by KoinJavaComponent.inject(
        ConfirmSendMoneyToExternalUserRPC::class.java,
    )

    private val getSendMoneyToExternalUserCounterpartiesRPC: GetSendMoneyToExternalUserCounterpartiesRPC by KoinJavaComponent.inject(
        GetSendMoneyToExternalUserCounterpartiesRPC::class.java,
    )

    private val getUserDetailFromPhoneNumberRPC: GetBCNRecipientFromPhoneNumberRPC by KoinJavaComponent.inject(
        GetBCNRecipientFromPhoneNumberRPC::class.java,
    )

    private val getBCNRecipientFromUserIdRPC: GetBCNRecipientFromUserIdRPC by KoinJavaComponent.inject(
        GetBCNRecipientFromUserIdRPC::class.java,
    )

    private val getBCNRecipientFromAccountIdRPC: GetBCNRecipientFromAccountIdRPC by KoinJavaComponent.inject(
        GetBCNRecipientFromAccountIdRPC::class.java,
    )

    private val lookupRecipientAtCounterpartyRPC: LookupRecipientAtCounterpartyRPC by KoinJavaComponent.inject(
        LookupRecipientAtCounterpartyRPC::class.java,
    )

    private val createSendMoneyToBcnUserRPC: CreateSendMoneyToBCNUserRPC by KoinJavaComponent.inject(
        CreateSendMoneyToBCNUserRPC::class.java,
    )

    suspend fun sendMoneyToExternalUser(
        amount: Amount,
        senderAccountId: UUID,
        externalRecipient: SendMoneyExternalRecipient,
    ): LeoRPCResult<CreateSendMoneyToExternalUserRequestRPC.Response, CreateSendMoneyToExternalUserRequestRPC.Error> {
        val sendMoneyRequest = CreateSendMoneyToExternalUserRequestRPC.Request(
            amount,
            senderAccountId,
            externalRecipient,
        )
        return sendMoneyToExternalUserRPC.execute(sendMoneyRequest)
    }

    suspend fun confirmSendMoneyToExternalUser(
        transactionID: UUID,
        privateRemark: String?,
        publicRemark: String?,
    ): LeoRPCResult<ConfirmSendMoneyToExternalUserRPC.Response, ConfirmSendMoneyToExternalUserRPC.Error> {
        val confirmSendMoneyToExternalUserRequest = ConfirmSendMoneyToExternalUserRPC.Request(
            transactionID,
            privateRemark,
            publicRemark,
        )
        return confirmSendMoneyToExternalUserRPC.execute(confirmSendMoneyToExternalUserRequest)
    }

    suspend fun getSendMoneyToExternalUserCounterparties(): LeoRPCResult<GetSendMoneyToExternalUserCounterpartiesRPC.Response, GetSendMoneyToExternalUserCounterpartiesRPC.Error> {
        val getSendMoneyToExternalUserDestinationsRequest =
            GetSendMoneyToExternalUserCounterpartiesRPC.Request
        return getSendMoneyToExternalUserCounterpartiesRPC.execute(
            getSendMoneyToExternalUserDestinationsRequest,
        )
    }

    suspend fun getUserDetailFromPhoneNumber(phoneNumber: LeoPhoneNumber): LeoRPCResult<GetBCNRecipientFromPhoneNumberRPC.Response, GetBCNRecipientFromPhoneNumberRPC.Error> {
        return getUserDetailFromPhoneNumberRPC.execute(GetBCNRecipientFromPhoneNumberRPC.Request(phoneNumber))
    }

    suspend fun getUserDetailFromUserId(userId: UUID): LeoRPCResult<GetBCNRecipientFromUserIdRPC.Response, GetBCNRecipientFromUserIdRPC.Error> {
        return getBCNRecipientFromUserIdRPC.execute(GetBCNRecipientFromUserIdRPC.Request(userId))
    }

    suspend fun getUserDetailFromAccountId(accountId: UUID): LeoRPCResult<GetBCNRecipientFromAccountIdRPC.Response, GetBCNRecipientFromAccountIdRPC.Error> {
        return getBCNRecipientFromAccountIdRPC.execute(GetBCNRecipientFromAccountIdRPC.Request(accountId))
    }

    suspend fun lookupRecipientAtCounterparty(sendMoneyExternalRecipient: SendMoneyExternalRecipient): LeoRPCResult<LookupRecipientAtCounterpartyRPC.Response, LookupRecipientAtCounterpartyRPC.Error> {
        val lookupRecipientAtCounterpartyRequest = LookupRecipientAtCounterpartyRPC.Request(
            sendMoneyExternalRecipient,
        )
        return lookupRecipientAtCounterpartyRPC.execute(lookupRecipientAtCounterpartyRequest)
    }

    suspend fun createSendMoneyToBCNUser(
        amount: Amount,
        senderAccountId: UUID,
        recipient: BCNRecipientIdentifierType,
    ): LeoRPCResult<CreateSendMoneyToBCNUserRPC.Response, CreateSendMoneyToBCNUserRPC.Error> {
        val createSendMoneyToBcnUserRequest = CreateSendMoneyToBCNUserRPC.Request(
            amount,
            senderAccountId,
            recipient,
        )
        return createSendMoneyToBcnUserRPC.execute(createSendMoneyToBcnUserRequest)
    }

    suspend fun confirmSendMoneyToBCNUser(
        recordId: UUID,
        privateRemark: String?,
        publicRemark: String?,
    ): LeoRPCResult<ConfirmSendMoneyToBCNUserRPC.Response, ConfirmSendMoneyToBCNUserRPC.Error> {
        val sendMoneyRequest = ConfirmSendMoneyToBCNUserRPC.Request(
            recordId,
            privateRemark,
            publicRemark,
        )
        return sendMoneyToBCNUserRPC.execute(sendMoneyRequest)
    }
}
