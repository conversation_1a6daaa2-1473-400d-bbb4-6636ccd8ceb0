package com.resoluttech.core.transfers.peertopeer

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import coil.load
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcn.transfers.RecentlyPaidRecipient
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.QR_CODE_SCANNER_NO_INTERNET
import com.resoluttech.core.utils.DialogCodes.Companion.QR_FRAGMENT_DIALOG_CODE
import com.resoluttech.core.utils.HapticConstant
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.centerCrop
import com.resoluttech.core.utils.chooseQrCodeFromGallery
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.resoluttech.core.utils.handleQRCodeImagePickedFromGallery
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.performHapticFeedback
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.toPx
import com.resoluttech.core.utils.toUri
import com.resoluttech.core.views.BaseFragment
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.qrcode.BarcodeException
import com.suryadigital.leo.libui.qrcode.BarcodeFragment
import com.suryadigital.leo.libui.qrcode.BarcodeListener
import com.suryadigital.leo.libui.qrcode.ScanFormat
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.libui.qrcode.setScanFormat
import timber.log.Timber
import kotlin.Int
import kotlin.String
import kotlin.apply
import kotlin.getValue
import kotlin.let

class TransfersQRFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val peerTransfersVM: PeerTransfersVM by navGraphViewModels(R.id.peer_to_peer_transfer_nav)
    private val transfersQRScreenVM: TransfersQRScreenVM by navGraphViewModels(R.id.peer_to_peer_transfer_nav)
    private var contactPermissionErrorSnackBar: Snackbar? = null
    private var readyToScanQR = true
    private lateinit var inlineErrorSnackbar: Snackbar
    private lateinit var recentProgressBar: ProgressBar
    private lateinit var recentlyTV: TextView
    private lateinit var recentlyPaidRecipientsBlock: View
    private lateinit var barcodeCameraMask: ImageView
    private lateinit var barcodeErrorTV: TextView
    private lateinit var galleryBarcodeErrorTV: TextView
    private lateinit var barcodeFragment: BarcodeFragment
    private lateinit var flashIconButton: ImageButton
    private lateinit var galleryIconButton: ImageButton
    private lateinit var sheetBehavior: BottomSheetBehavior<LinearLayout>
    private var isFlashOn = false

    private var imagePickerResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            handleQRCodeImagePickedFromGallery(
                requireActivity(),
                result,
                requireContext(),
                ::onBarcodeValueAvailable,
            ) { peerTransfersVM.onInvalidImageSelectedFromGallery(requireContext()) }
        }

    private val startForCameraPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                transfersQRScreenVM.onCameraPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                transfersQRScreenVM.onCameraPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Camera permission denied, unable to proceed ith transfer.")
                transfersQRScreenVM.onCameraPermissionDenied()
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_transfers_qr, container, false)
        handleBarcodeFragmentSetup()
        initViews(rootView)
        setupManualEntry(rootView)
        setupBottomSheet(rootView)
        return rootView
    }

    private fun setupBottomSheet(rootView: View) {
        val recentlyPaidBottomSheetLayout =
            rootView.findViewById<LinearLayout>(R.id.recently_paid_bottom_sheet)
        sheetBehavior = BottomSheetBehavior.from(recentlyPaidBottomSheetLayout)
        sheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    private fun initViews(view: View) {
        recentProgressBar = view.findViewById(R.id.recentUserProgressBar)
        recentlyTV = view.findViewById(R.id.recent_tv)
        barcodeCameraMask = view.findViewById(R.id.barcode_camera_mask)
        barcodeErrorTV = view.findViewById(R.id.barcode_error_tv)
        galleryBarcodeErrorTV = view.findViewById(R.id.gallery_barcode_error_tv)
        recentlyPaidRecipientsBlock = view.findViewById(R.id.recent_user_grid_view)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setToolBar()
        refreshRecentlyPaidRecipient()
        networkListenerCallback = this
        transfersQRScreenVM.currentCameraState.observe(
            viewLifecycleOwner,
            Observer(::reactToCameraState),
        )
        transfersQRScreenVM.recentlyPaidRecipientsState.observe(
            viewLifecycleOwner,
            Observer(::setupRecentlyPaidRecipients),
        )
        peerTransfersVM.qrCodeScannerState.observe(
            viewLifecycleOwner,
            Observer(::reactToScannerState),
        )
        setImmediateBackstackDestinationId(findNavController().previousBackStackEntry!!.destination.id)
    }

    private fun setToolBar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.qr_scanner_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            setArrowBackIcon(toolbarView)
            setFlashIcon(toolbarView)
            setGalleryIcon(toolbarView)
        }
    }

    private fun setArrowBackIcon(toolbar: View) {
        val arrowBackButton = toolbar.findViewById<ImageButton>(R.id.arrow_back_icon)
        arrowBackButton.setOnClickListener {
            transfersQRScreenVM.onBackArrowTapped(findNavController())
        }
    }

    private fun setFlashIcon(toolbar: View) {
        flashIconButton = toolbar.findViewById(R.id.flash_icon)
        flashIconButton.setOnClickListener {
            isFlashOn = !isFlashOn
            barcodeFragment.toggleFlashLight(isFlashOn)
            if (isFlashOn) {
                flashIconButton.load(R.drawable.ic_flashlight_on)
            } else {
                flashIconButton.load(R.drawable.ic_flashlight_off)
            }
        }
    }

    private fun setGalleryIcon(toolbar: View) {
        galleryIconButton = toolbar.findViewById(R.id.pick_qr_code_from_gallery)
        galleryIconButton.setOnClickListener {
            chooseQrCodeFromGallery(imagePickerResultLauncher)
        }
    }

    private fun disableFlashIcon() {
        isFlashOn = false
        flashIconButton.visibility = View.GONE
    }

    private fun enableFlashIcon() {
        isFlashOn = false
        flashIconButton.visibility = View.VISIBLE
    }

    private fun reactToScannerState(state: QRCodeScannerState) {
        when (state) {
            is QRCodeScannerState.AcceptInput -> {
                handleQRCodeAcceptInputState()
            }
            is QRCodeScannerState.InlineError -> {
                handleQRCodeInlineError(state.errorMessage)
            }
            QRCodeScannerState.NoInternet -> {
                handleNoInternetState()
            }
        }
    }

    private fun handleNoInternetState() {
        stopBarcodeDetection()
        if (isFlashOn && flashIconButton.isVisible) {
            isFlashOn = false
            flashIconButton.load(R.drawable.ic_flashlight_off)
        }
        showInlineErrorDialog(
            getString(R.string.alertTitleNoInternet),
            getString(R.string.alertMessageNoInternet),
            QR_CODE_SCANNER_NO_INTERNET,
        )
    }

    private fun handleQRCodeAcceptInputState() {
        readyToScanQR = true
        barcodeCameraMask.load(R.drawable.bg_scanner_background)
    }

    private fun handleQRCodeInlineError(errorMessage: String) {
        barcodeCameraMask.performHapticFeedback(
            HapticConstant.HAPTIC_CODE_REJECT,
            requireContext(),
            TAG,
        )
        barcodeCameraMask.load(R.drawable.bg_scanner_background_error)
        AnimationUtils.loadAnimation(requireContext(), R.anim.bounce).apply {
            reset()
            barcodeErrorTV.clearAnimation()
            barcodeErrorTV.startAnimation(this)
            barcodeErrorTV.text = errorMessage
            galleryBarcodeErrorTV.clearAnimation()
            galleryBarcodeErrorTV.startAnimation(this)
            galleryBarcodeErrorTV.text = errorMessage
            duration = 1000
            barcodeErrorTV.setTextColor(requireContext().getColor(R.color.failed_transaction_theme_color))
            galleryBarcodeErrorTV.setTextColor(requireContext().getColor(R.color.failed_transaction_theme_color))
            barcodeErrorTV.animation.setAnimationListener(
                object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {
                        // Do nothing
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        peerTransfersVM.onInlineErrorDismiss {
                            barcodeErrorTV.setTextColor(requireContext().getColor(R.color.spinnerItemTextColor))
                            barcodeErrorTV.text = getString(R.string.qrCodeScannerOverlayTitle)
                        }
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                        // Do nothing
                    }
                },
            )
            galleryBarcodeErrorTV.animation.setAnimationListener(
                object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {
                        galleryBarcodeErrorTV.visibility = View.VISIBLE
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        peerTransfersVM.onInlineErrorDismiss {
                            galleryBarcodeErrorTV.visibility = View.GONE
                            if (context != null) {
                                barcodeErrorTV.setTextColor(requireContext().getColor(R.color.spinnerItemTextColor))
                                barcodeErrorTV.text = getString(R.string.qrCodeScannerOverlayTitle)
                            }
                        }
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                        // Do nothing
                    }
                },
            )
        }
    }

    override fun onPause() {
        super.onPause()
        stopBarcodeDetection()
        if (isFlashOn && flashIconButton.isVisible) {
            isFlashOn = false
            flashIconButton.load(R.drawable.ic_flashlight_off)
        }
        hideContactPermissionError()
    }

    override fun onResume() {
        super.onResume()
        if (checkCameraPermissionGranted(requireContext())) {
            startBarcodeDetection()
            transfersQRScreenVM.onCameraPermissionGranted()
        }
    }

    private fun showCameraPermissionAlert(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun setupManualEntry(rootView: View) {
        rootView.findViewById<View>(R.id.enter_manually).setOnClickListener {
            transfersQRScreenVM.onContactNumberManuallyEntered(findNavController())
        }

        rootView.findViewById<View>(R.id.request_camera_permission).setOnClickListener {
            transfersQRScreenVM.onCameraPermissionRequested()
        }
    }

    private fun reactToCameraState(cameraState: PeerTransfersCameraState) {
        when (cameraState) {
            is PeerTransfersCameraState.CameraPermissionRequested -> {
                showCameraPermissionDeniedError()
                disableFlashIcon()
                startForCameraPermissionResult.launch(Manifest.permission.CAMERA)
            }
            is PeerTransfersCameraState.CameraPermissionGranted -> {
                enableFlashIcon()
                dismissCameraPermissionDeniedError()
            }
            is PeerTransfersCameraState.CameraPermissionDenied -> {
                disableFlashIcon()
                showCameraPermissionDeniedError()
            }
            is PeerTransfersCameraState.CameraPermissionAlert -> {
                disableFlashIcon()
                handleCameraPermissionAlertState()
            }
            is PeerTransfersCameraState.Waiting -> {
                disableFlashIcon()
                handleWaitingOnCameraState()
            }
        }
    }

    private fun handleWaitingOnCameraState() {
        dismissCameraPermissionDeniedError()
        if (!checkCameraPermissionGranted(requireContext())) {
            transfersQRScreenVM.onCameraPermissionRequested()
        }
    }

    private fun handleCameraPermissionAlertState() {
        showCameraPermissionAlert(
            getString(R.string.alertTitleCameraAccessNotPermitted),
            getString(R.string.alertMessageCameraAccessNotPermitted),
            CAMERA_PERMISSION_DIALOG_CODE,
            getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun hideContactPermissionError() {
        contactPermissionErrorSnackBar?.dismiss()
        contactPermissionErrorSnackBar = null
    }

    private fun showCameraPermissionDeniedError() {
        val errorBlock: View = requireView().findViewById(R.id.camera_permission_error)
        val cameraMask: View = requireView().findViewById(R.id.barcode_camera_mask)
        val footerLayout: View = requireView().findViewById(R.id.footer_layout)

        errorBlock.visibility = View.VISIBLE
        cameraMask.visibility = View.INVISIBLE
        footerLayout.visibility = View.GONE
    }

    private fun dismissCameraPermissionDeniedError() {
        val errorBlock: View = requireView().findViewById(R.id.camera_permission_error)
        val cameraMask: View = requireView().findViewById(R.id.barcode_camera_mask)
        val footerLayout: View = requireView().findViewById(R.id.footer_layout)

        errorBlock.visibility = View.GONE
        cameraMask.visibility = View.VISIBLE
        footerLayout.visibility = View.VISIBLE
    }

    private fun setupRecentlyPaidRecipients(recentlyPaidRecipients: RecentlyPaidRecipientsState) {
        when (recentlyPaidRecipients) {
            is RecentlyPaidRecipientsState.Loading -> handleRecentUsersLoading()
            is RecentlyPaidRecipientsState.Data -> handleRecentUsers(recentlyPaidRecipients.recentUserList)
            is RecentlyPaidRecipientsState.InlineError -> {
                handleInlineError(
                    recentlyPaidRecipients.uiError,
                    recentlyPaidRecipients.recentUserList,
                )
            }
        }
    }

    private fun handleRecentUsersLoading() {
        recentProgressBar.visibility = View.VISIBLE
        recentlyPaidRecipientsBlock.visibility = View.GONE
    }

    private fun handleInlineError(uiError: UIError, recentUserList: List<RecentlyPaidRecipient>?) {
        recentProgressBar.visibility = View.GONE
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(uiError.errorMessage, recentUserList)
            }
            ErrorType.DIALOG -> {
                showInlineErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: QR_FRAGMENT_DIALOG_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineErrorSnackBar(
        errorMessage: String,
        recentUserList: List<RecentlyPaidRecipient>?,
    ) {
        inlineErrorSnackbar = Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
            .setAction(requireContext().getString(R.string.alertActionDismiss)) {
                transfersQRScreenVM.onInlineErrorDismissed(recentUserList)
            }
        inlineErrorSnackbar.show()
    }

    private fun showInlineErrorDialog(errorTitle: String, errorMessage: String, errorCodes: Int) {
        showErrorDialog(
            title = errorTitle,
            message = errorMessage,
            dialogId = errorCodes,
        )
    }

    private fun handleRecentUsers(recentUserList: List<RecentlyPaidRecipient>) {
        recentProgressBar.visibility = View.GONE
        for ((i, recentlyPaidRecipient) in recentUserList.withIndex()) {
            recentlyPaidRecipientsBlock.visibility = View.VISIBLE
            recentlyTV.visibility = View.VISIBLE
            setupRecentlyPaidRecipient(i, recentlyPaidRecipient)
            if (i >= MAX_RECENT_PAYMENT_OPTIONS) break
        }
        if (recentUserList.isNotEmpty()) {
            sheetBehavior.setPeekHeight(
                HALF_COLLAPSED_BOTTOM_SHEET_OFFSET.toPx(requireContext().resources),
                true,
            )
        } else {
            sheetBehavior.setPeekHeight(
                COLLAPSED_BOTTOM_SHEET_OFFSET.toPx(requireContext().resources),
                true,
            )
        }
    }

    private fun setupRecentlyPaidRecipient(i: Int, recentlyPaidRecipient: RecentlyPaidRecipient) {
        if (i >= MAX_RECENT_PAYMENT_OPTIONS) {
            return
        }

        when (i) {
            RECENT_PAYMENT_OPTION_1 -> {
                val userCIV: ContactIconView =
                    requireView().findViewById(R.id.recent_transfer1_contact_icon)
                val usernameTV: TextView =
                    requireView().findViewById(R.id.recent_transfer1_username)
                userCIV.visibility = View.VISIBLE
                usernameTV.visibility = View.VISIBLE
                userCIV.imageView.centerCrop()
                setupRecentlyPaidRecipientView(
                    userCIV,
                    usernameTV,
                    recentlyPaidRecipient,
                )
            }
            RECENT_PAYMENT_OPTION_2 -> {
                val userCIV: ContactIconView =
                    requireView().findViewById(R.id.recent_transfer2_contact_icon)
                val usernameTV: TextView =
                    requireView().findViewById(R.id.recent_transfer2_username)
                userCIV.visibility = View.VISIBLE
                usernameTV.visibility = View.VISIBLE
                userCIV.imageView.centerCrop()
                setupRecentlyPaidRecipientView(
                    userCIV,
                    usernameTV,
                    recentlyPaidRecipient,
                )
            }
            RECENT_PAYMENT_OPTION_3 -> {
                val userCIV: ContactIconView =
                    requireView().findViewById(R.id.recent_transfer3_contact_icon)
                val usernameTV: TextView =
                    requireView().findViewById(R.id.recent_transfer3_username)
                userCIV.visibility = View.VISIBLE
                usernameTV.visibility = View.VISIBLE
                userCIV.imageView.centerCrop()
                setupRecentlyPaidRecipientView(
                    userCIV,
                    usernameTV,
                    recentlyPaidRecipient,
                )
            }
            RECENT_PAYMENT_OPTION_4 -> {
                val userCIV: ContactIconView =
                    requireView().findViewById(R.id.recent_transfer4_contact_icon)
                val usernameTV: TextView =
                    requireView().findViewById(R.id.recent_transfer4_username)
                userCIV.visibility = View.VISIBLE
                usernameTV.visibility = View.VISIBLE
                userCIV.imageView.centerCrop()
                setupRecentlyPaidRecipientView(
                    userCIV,
                    usernameTV,
                    recentlyPaidRecipient,
                )
            }
        }
    }

    private fun setupRecentlyPaidRecipientView(
        userCIV: ContactIconView,
        displayNameTV: TextView,
        recentlyPaidRecipient: RecentlyPaidRecipient,
    ) {
        if (recentlyPaidRecipient.bcnRecipient.profileImage != null) {
            recentlyPaidRecipient.bcnRecipient.profileImage?.getSpecificResolutionImageURL()?.let {
                userCIV.imageView.loadImage(it.toUri())
            }
        } else {
            userCIV.setContactInitials(recentlyPaidRecipient.bcnRecipient.displayName)
        }
        displayNameTV.text = recentlyPaidRecipient.bcnRecipient.displayName
        setImmediateBackstackDestinationId(R.id.qrCode)
        userCIV.setOnClickListener {
            peerTransfersVM.onContactSelectedFromRecent(recentlyPaidRecipient, findNavController())
        }
        displayNameTV.setOnClickListener {
            peerTransfersVM.onContactSelectedFromRecent(recentlyPaidRecipient, findNavController())
        }
    }

    private fun handleBarcodeFragmentSetup() {
        barcodeFragment = BarcodeFragment()
        val metrics = requireContext().resources.displayMetrics
        barcodeFragment.setPreviewSize(metrics.heightPixels, metrics.widthPixels)
        barcodeFragment.setScanFormat(ScanFormat.QR_CODE)
        barcodeFragment.setBarcodeListener(object : BarcodeListener {
            override fun onBarcodeValueReceived(rawValue: String) {
                onBarcodeValueAvailable(rawValue)
            }

            override fun onFailure(e: BarcodeException) {
                Timber.tag(TAG).d(e.message!!)
            }
        })
        presentBarcodeFragment()
    }

    private fun onBarcodeValueAvailable(rawValue: String) {
        if (readyToScanQR) {
            peerTransfersVM.onReceiveQRValue(
                requireContext(),
                findNavController(),
                rawValue,
            ) {
                barcodeCameraMask.performHapticFeedback(
                    HapticConstant.HAPTIC_CODE_CONFIRM,
                    requireContext(),
                    TAG,
                )
            }
            readyToScanQR = false
        }
    }

    private fun presentBarcodeFragment() {
        childFragmentManager.beginTransaction()
            .add(R.id.barcode_fragment_container, barcodeFragment)
            .commit()
    }

    private fun startBarcodeDetection() {
        if (!checkCameraPermissionGranted(requireContext())) {
            transfersQRScreenVM.onCameraPermissionRequested()
        } else {
            val barcodeView: View = requireView().findViewById(R.id.barcode_fragment_container)
            barcodeView.visibility = View.VISIBLE
            getBarcodeFragment().startBarcodeDetection()
        }
    }

    private fun stopBarcodeDetection() {
        getBarcodeFragment().stopBarcodeDetection()
    }

    private fun getBarcodeFragment(): BarcodeFragment {
        val metrics = requireContext().resources.displayMetrics
        barcodeFragment.setPreviewSize(metrics.heightPixels, metrics.widthPixels)
        return barcodeFragment
    }

    private fun refreshRecentlyPaidRecipient() {
        transfersQRScreenVM.refreshRecentlyPaidRecipients(requireContext())
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> transfersQRScreenVM.onOpenSettingsClicked(
                requireContext(),
            )
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> leoServerExceptionHandler(
                findNavController(),
            )
            QR_CODE_SCANNER_NO_INTERNET -> transfersQRScreenVM.onBackArrowTapped(findNavController())
            else -> Timber.tag(TAG)
                .i("Positive action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> transfersQRScreenVM.onCameraPermissionAlertDismissed()
            QR_FRAGMENT_DIALOG_CODE -> Timber.tag(TAG)
                .i("Negative action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onNetworkAvailable() {
        refreshRecentlyPaidRecipient()
    }
}

private const val TAG = "TransfersQRFragment"
private const val MAX_RECENT_PAYMENT_OPTIONS = 4
private const val RECENT_PAYMENT_OPTION_1 = 0
private const val RECENT_PAYMENT_OPTION_2 = 1
private const val RECENT_PAYMENT_OPTION_3 = 2
private const val RECENT_PAYMENT_OPTION_4 = 3
private const val CAMERA_PERMISSION_DIALOG_CODE = 115
private const val HALF_COLLAPSED_BOTTOM_SHEET_OFFSET = 72
private const val COLLAPSED_BOTTOM_SHEET_OFFSET = 82
