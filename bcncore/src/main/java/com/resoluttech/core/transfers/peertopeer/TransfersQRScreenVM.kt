package com.resoluttech.core.transfers.peertopeer

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.RecentlyPaidRecipient
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.ContactPickerFragment
import com.resoluttech.core.utils.ContactPickerFragment.Companion.ContactPickerUseCase
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.utils.openSystemSettings
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class TransfersQRScreenVM : ViewModel() {

    private val repository = TransfersRepository()
    private val _currentCameraState: MutableLiveData<PeerTransfersCameraState> =
        MutableLiveData(PeerTransfersCameraState.Waiting)
    private val _recentlyPaidUsersState: MutableLiveData<RecentlyPaidRecipientsState> =
        MutableLiveData(RecentlyPaidRecipientsState.Loading)
    val currentCameraState: LiveData<PeerTransfersCameraState> = _currentCameraState
    val recentlyPaidRecipientsState: MutableLiveData<RecentlyPaidRecipientsState> =
        _recentlyPaidUsersState
    private var vmIoScope = viewModelScope + Dispatchers.IO
    private var currentData: List<RecentlyPaidRecipient>? = null

    /**
     * This method would fetch the information about recently paid users. The [_recentlyPaidUsersState]
     * will be updated with the refreshed recently paid users.
     */
    fun refreshRecentlyPaidRecipients(context: Context) {
        _recentlyPaidUsersState.postValue(RecentlyPaidRecipientsState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = repository.getRecentlyPaidUsers()) {
                        is LeoRPCResult.LeoResponse -> {
                            currentData = result.response.recentlyPaidRecipients
                            _recentlyPaidUsersState.postValue(
                                RecentlyPaidRecipientsState.Data(
                                    currentData!!,
                                ),
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            // No error to handle
                        }
                    }
                },
                handleException = {
                    handleRPCException(it, currentData)
                },
            )
        }
    }

    private fun handleRPCException(uiError: UIError, currentData: List<RecentlyPaidRecipient>?) {
        _recentlyPaidUsersState.postValue(
            RecentlyPaidRecipientsState.InlineError(uiError, currentData),
        )
    }

    fun onCameraPermissionRequested() {
        _currentCameraState.postValue(PeerTransfersCameraState.CameraPermissionRequested)
    }

    fun onCameraPermissionNeverAskAgainSelected() {
        _currentCameraState.postValue(PeerTransfersCameraState.CameraPermissionAlert)
    }

    fun onCameraPermissionGranted() {
        _currentCameraState.postValue(PeerTransfersCameraState.CameraPermissionGranted)
    }

    fun onCameraPermissionDenied() {
        _currentCameraState.postValue(PeerTransfersCameraState.CameraPermissionDenied)
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
    }

    fun onCameraPermissionAlertDismissed() {
        _currentCameraState.postValue(PeerTransfersCameraState.CameraPermissionDenied)
    }

    fun onContactNumberManuallyEntered(navController: NavController) {
        val args = Bundle()
        args.putSerializable(
            ContactPickerFragment.USE_CASE,
            ContactPickerUseCase.PEER_TO_PEER,
        )
        navController.navigateSafe(R.id.action_start_contact_picker, args)
    }

    fun onInlineErrorDismissed(refreshRecentlyPaidUser: List<RecentlyPaidRecipient>?) {
        refreshRecentlyPaidUser?.apply {
            _recentlyPaidUsersState.postValue(RecentlyPaidRecipientsState.Data(this))
        }
    }

    fun onBackArrowTapped(navController: NavController) {
        navController.navigateUp()
    }
}

sealed class PeerTransfersCameraState {
    object Waiting : PeerTransfersCameraState()
    object CameraPermissionRequested : PeerTransfersCameraState()
    object CameraPermissionDenied : PeerTransfersCameraState()
    object CameraPermissionGranted : PeerTransfersCameraState()
    object CameraPermissionAlert : PeerTransfersCameraState()
}

sealed class RecentlyPaidRecipientsState {
    object Loading : RecentlyPaidRecipientsState()
    data class Data(val recentUserList: List<RecentlyPaidRecipient>) : RecentlyPaidRecipientsState()
    data class InlineError(val uiError: UIError, val recentUserList: List<RecentlyPaidRecipient>?) :
        RecentlyPaidRecipientsState()
}
