package com.resoluttech.core.transfers.peertopeer

import android.content.Context
import android.os.Bundle
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.BCNRecipientIdentifierType
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromAccountIdRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromUserIdRPC
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.SendMoneyRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.Remarks
import com.resoluttech.core.transactions.areRemarksValid
import com.resoluttech.core.transfers.model.RecipientType
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DownloadManager
import com.resoluttech.core.utils.PDFRendererPreviousPath
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.resoluttech.core.utils.logout
import com.resoluttech.core.views.FormattedAmount
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatus
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import java.io.File
import java.time.Instant
import java.util.UUID

class SendMoneyVM : ViewModel() {

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val repository = SendMoneyRepository()
    private val _currentState: MutableLiveData<SendMoneyState> =
        MutableLiveData(SendMoneyState.AcceptInput)
    val currentState: LiveData<SendMoneyState> = _currentState
    var selectedAccountId: UUID? = null
    private var lookedUpID: UUID? = null
    private lateinit var remarks: Remarks
    private var isOtpChargesDialogShown: Boolean = false

    fun onRetryTapped(context: Context) {
        when (
            val recipient =
                (currentState.value as SendMoneyState.FullScreenError).recipientType
        ) {
            is RecipientType.AccountId -> {
                lookupRecipientFromAccountId(recipient.accountId, recipient.username, context)
            }
            is RecipientType.UserId -> {
                lookupRecipientFromUserId(recipient.userId, recipient.username, context)
            }
        }
    }

    fun onQRCodeScannedExternally(
        context: Context,
        username: String?,
        userId: String?,
        accountId: String?,
    ) {
        val accountIdUUID = getUUIDFromString(accountId)
        val userIdUUID = getUUIDFromString(userId)
        when {
            !username.isNullOrEmpty() && accountIdUUID != null -> {
                lookupRecipientFromAccountId(accountIdUUID, username, context)
            }
            !username.isNullOrEmpty() && userIdUUID != null -> {
                lookupRecipientFromUserId(userIdUUID, username, context)
            }
            else -> {
                _currentState.postValue(SendMoneyState.InvalidAppLinkArguments(context.getString(R.string.qrCodeInvalidRecipient)))
            }
        }
    }

    fun lookupRecipientFromUserId(userId: UUID, username: String, context: Context) {
        // This check eliminate the multiple lookups for same user id.
        if (selectedAccountId != null && lookedUpID == userId) return
        _currentState.postValue(SendMoneyState.Loading)
        viewModelScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = repository.getUserDetailFromUserId(userId)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetRecipientFromUserIdResponse(result.response, userId)
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getUserFromUserIdErrorMessage(
                                result.error,
                                context,
                            ).apply { handleLookupRPCError(errorMessage) }
                        }
                    }
                },
                handleException = {
                    handleLookupRecipientFromUserIdRPCException(
                        userId,
                        username,
                        context,
                        it.errorMessage,
                    )
                },
            )
        }
    }

    private fun handleLookupRPCError(message: String) {
        _currentState.postValue(SendMoneyState.InvalidAppLinkArguments(message))
    }

    private fun handleGetRecipientFromUserIdResponse(
        response: GetBCNRecipientFromUserIdRPC.Response,
        userId: UUID,
    ) {
        lookedUpID = userId
        _currentState.postValue(
            SendMoneyState.RecipientScannedFromQRCode(
                RecipientType.UserId(
                    response.bcnRecipient.profileImage?.getSpecificResolutionImageURL(),
                    response.bcnRecipient.displayName,
                    userId,
                    UserSharedPreference.getUserCountryCurrency().map { Currency(it.currencyCode) },
                ),
            ),
        )
    }

    private fun handleLookupRecipientFromUserIdRPCException(
        userId: UUID,
        username: String,
        context: Context,
        errorMessage: String,
    ) {
        val message = context.getString(R.string.alertMessageUnableToLookUpRPC, username, errorMessage)
        _currentState.postValue(
            SendMoneyState.FullScreenError(
                RecipientType.UserId(
                    null,
                    username = username,
                    userId = userId,
                ),
                message,
            ),
        )
    }

    fun lookupRecipientFromAccountId(accountId: UUID, username: String, context: Context) {
        // This check eliminate the multiple lookups for same account id.
        if (selectedAccountId != null && lookedUpID == accountId) return
        _currentState.postValue(SendMoneyState.Loading)
        viewModelScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = repository.getUserDetailFromAccountId(accountId)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetRecipientFromAccountIdResponse(result.response, accountId)
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getUserFromAccountIdErrorMessage(
                                result.error,
                                context,
                            ).apply { handleLookupRPCError(errorMessage) }
                        }
                    }
                },
                handleException = {
                    if (it.errorCode == DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
                        showError(it)
                    } else {
                        handleLookupRecipientFromAccountIdRPCException(
                            accountId,
                            username,
                            context,
                            it.errorMessage,
                        )
                    }
                },
            )
        }
    }

    private fun handleGetRecipientFromAccountIdResponse(
        response: GetBCNRecipientFromAccountIdRPC.Response,
        accountId: UUID,
    ) {
        lookedUpID = accountId
        _currentState.postValue(
            SendMoneyState.RecipientScannedFromQRCode(
                RecipientType.AccountId(
                    response.bcnRecipient.profileImage?.getSpecificResolutionImageURL(),
                    response.bcnRecipient.displayName,
                    accountId,
                    UserSharedPreference.getUserCountryCurrency().map { Currency(it.currencyCode) },
                ),
            ),
        )
    }

    private fun handleLookupRecipientFromAccountIdRPCException(
        accountId: UUID,
        username: String,
        context: Context,
        errorMessage: String,
    ) {
        val message = context.getString(R.string.alertMessageUnableToLookUpRPC, username, errorMessage)
        _currentState.postValue(
            SendMoneyState.FullScreenError(
                RecipientType.AccountId(
                    null,
                    username = username,
                    accountId = accountId,
                ),
                message,
            ),
        )
    }

    private fun getUUIDFromString(string: String?): UUID? {
        return if (string != null) {
            try {
                UUID.fromString(string)
            } catch (e: IllegalArgumentException) {
                null
            }
        } else {
            null
        }
    }

    fun onErrorDialogDismiss(navController: NavController, shouldNavigate: Boolean) {
        /*
           If user scans QR code and none of the arguments are available in it, then we
           redirect user to money transfer screen so that user can enter the recipient manually.

           We need to pop current screen from navigation backstack, otherwise on pressing back on
           `resoluttechbcn://money-transfer`, will navigate to this screen, with retained state.
         */
        navController.popBackStack()
        if (shouldNavigate) {
            navController.navigate("resoluttechbcn://money-transfer".toUri())
        } else {
            // When user land on enter amount screen from QR Code screen then we just need to
            // pop the backstack.
        }
    }

    fun onUserAuthenticated(
        formattedAmount: FormattedAmount?,
        currency: Currency,
        recipient: BCNRecipientIdentifierType,
        context: Context,
        recipientDisplayName: String,
    ) {
        if (selectedAccountId == null) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderAccountNotSelected),
                        context.getString(R.string.alertMessageSenderAccountNotSelected),
                    ),
                ),
            )
            return
        }

        if (formattedAmount != null) {
            _currentState.postValue(SendMoneyState.InlineLoading)
            vmIOScope.launch {
                executeRPC(
                    context,
                    rpcBlock = {
                        val result = repository.createSendMoneyToBCNUser(
                            Amount(formattedAmount.processedValue, currency),
                            selectedAccountId!!,
                            recipient,
                        )
                        when (result) {
                            is LeoRPCResult.LeoResponse -> {
                                handleCreateSendMoneyToBCNUserResponse(
                                    result.response,
                                    formattedAmount.processedValue,
                                    currency,
                                    recipient,
                                )
                            }
                            is LeoRPCResult.LeoError -> {
                                SendMoneyRPCExceptionHandler.getCreateSendMoneyToBCNUserErrorMessage(
                                    result.error,
                                    context,
                                    SelectedAccountHelper.getAccountFromId(selectedAccountId!!).name,
                                    recipientDisplayName,
                                ).apply {
                                    showError(this)
                                }
                            }
                        }
                    },
                    handleException = ::showError,
                )
            }
        } else {
            Timber.e("Formatted amount is null")
        }
    }

    private fun showError(uiError: UIError) {
        _currentState.postValue(SendMoneyState.InlineError(uiError))
    }

    private fun handleCreateSendMoneyToBCNUserResponse(
        response: CreateSendMoneyToBCNUserRPC.Response,
        processedValue: Long,
        currency: Currency,
        recipient: BCNRecipientIdentifierType,
    ) {
        if (!isOtpChargesDialogShown) {
            isOtpChargesDialogShown = true
            _currentState.postValue(
                SendMoneyState.Confirmation(
                    response.recordId,
                    response.claimedTransactionFee,
                    Amount(
                        processedValue,
                        currency,
                    ),
                    recipient,
                    SelectedAccountHelper.getAccountFromId(selectedAccountId!!).name,
                    response.exchangeRateAmount,
                ),
            )
        }
    }

    fun onConfirmPayment(
        formattedAmount: FormattedAmount?,
        currency: Currency,
        privateRemark: String?,
        publicRemark: String?,
        displayName: String,
        context: Context,
    ) {
        if (!areRemarksValid(publicRemark, privateRemark)) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInvalidNarration),
                        context.getString(R.string.alertMessageTransactionInvalidNarration),
                    ),
                ),
            )
            return
        }
        if (formattedAmount == null) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.SNACKBAR,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return
        }
        val recordId = (currentState.value as SendMoneyState.Confirmation).recordId
        _currentState.postValue(SendMoneyState.InlineLoading)
        val amount = Amount(formattedAmount.processedValue, currency)
        when (val state = currentState.value) {
            is SendMoneyState.Confirmation -> {
                confirmPayment(
                    recordId,
                    formattedAmount,
                    state.recipient,
                    privateRemark,
                    publicRemark,
                    context,
                    amount,
                    displayName,
                )
            }
            is SendMoneyState.InlineErrorWithData -> {
                confirmPayment(
                    recordId,
                    formattedAmount,
                    state.recipient,
                    privateRemark,
                    publicRemark,
                    context,
                    amount,
                    displayName,
                )
            }
            else -> {
                throw IllegalStateException("Payment can happen only in confirmation and InlineErrorWithData state")
            }
        }
    }

    private fun confirmPayment(
        recordId: UUID,
        formattedAmount: FormattedAmount,
        recipient: BCNRecipientIdentifierType,
        privateRemark: String?,
        publicRemark: String?,
        context: Context,
        amount: Amount,
        displayName: String,
    ) {
        if (!areRemarksValid(publicRemark, privateRemark)) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInvalidNarration),
                        context.getString(R.string.alertMessageTransactionInvalidNarration),
                    ),
                ),
            )
            return
        }
        remarks = Remarks(publicRemark, privateRemark)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val response = repository.confirmSendMoneyToBCNUser(
                        recordId,
                        privateRemark,
                        publicRemark,
                    )
                    when (response) {
                        is LeoRPCResult.LeoResponse -> {
                            handleConfirmSendMoneyToUserRPCResponse(
                                response.response,
                                recordId,
                                amount,
                                context,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getSendMoneyToUserRPCErrorMessage(
                                context,
                                response.error,
                                displayName,
                                SelectedAccountHelper.getAccountFromId(selectedAccountId!!).name,
                            ).apply {
                                handleConfirmSendMoneyToBCNUserException(
                                    SendMoneyState.InlineErrorWithData(
                                        formattedAmount,
                                        privateRemark,
                                        publicRemark,
                                        recipient,
                                        this,
                                    ),
                                )
                            }
                        }
                    }
                },
                handleException = {
                    if (it.errorCode == DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
                        showError(it)
                    } else {
                        handleConfirmSendMoneyToBCNUserException(
                            SendMoneyState.InlineErrorWithData(
                                formattedAmount,
                                privateRemark,
                                publicRemark,
                                recipient,
                                it,
                            ),
                        )
                    }
                },
            )
        }
    }

    private fun handleConfirmSendMoneyToUserRPCResponse(
        result: ConfirmSendMoneyToBCNUserRPC.Response,
        recordId: UUID,
        amount: Amount,
        context: Context,
    ) {
        _currentState.postValue(
            SendMoneyState.SuccessfulTransaction(
                "$recordId",
                amount,
                result.completedAt,
                result.transactionDetail.description?.let {
                    context.getLocalizedString(
                        it.en,
                        it.ny,
                    )
                },
                result.transactionDetail.itemDetail,
            ),
        )
    }

    private fun handleConfirmSendMoneyToBCNUserException(inlineErrorWithData: SendMoneyState.InlineErrorWithData) {
        _currentState.postValue(inlineErrorWithData)
    }

    fun inlineErrorDismissed() {
        _currentState.postValue(SendMoneyState.AcceptInput)
    }

    fun onAuthenticationSuccess() {
        _currentState.postValue(SendMoneyState.AcceptInput)
    }

    fun handleSuccessfulTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        succeededAtTimestamp: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.SuccessfulTransaction(
            SuccessfulTransactionDetails(
                context.getString(R.string.transactionStatusSuccessLabel),
                getFormattedDate(context, succeededAtTimestamp),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )

        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            data,
        )
        navController.navigate(R.id.transaction_status_nav, args)
    }

    fun onTermsAndConditionTapped(context: Context, url: String) {
        _currentState.postValue(SendMoneyState.Loading)
        vmIOScope.launch {
            val file = File(
                context.applicationContext.filesDir.path + File.separator + "${
                    context.getString(R.string.alertTransactionTermsAndConditionsTitle).replace(
                        " ",
                        "_",
                    )
                }.pdf",
            )
            DownloadManager.download(
                url,
                file.path,
                context,
                {
                    _currentState.postValue(
                        SendMoneyState.TermsAndConditionsDocumentDownloaded,
                    )
                },
                SendMoneyState::InlineError,
            )
        }
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onErrorDismissed() {
        isOtpChargesDialogShown = false
        _currentState.postValue(SendMoneyState.AcceptInput)
    }

    fun onTermsAndConditionsDownloaded(
        navController: NavController,
        context: Context,
        recipient: BCNRecipientIdentifierType,
    ) {
        val action = SendMoneyFragmentDirections.actionSendMoneyFragmentToPDFRendererFragment(
            context.getString(R.string.alertTransactionTermsAndConditionsTitle),
            PDFRendererPreviousPath.TERMS_AND_CONDITIONS.name,
        )
        isOtpChargesDialogShown = false
        onUserAuthenticationSuccessful(recipient)
        navController.navigate(action)
    }

    fun onAttemptPaymentButtonTapped(
        navController: NavController,
        formattedAmount: FormattedAmount?,
        context: Context,
    ) {
        if (selectedAccountId == null) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderAccountNotSelected),
                        context.getString(R.string.alertMessageSenderAccountNotSelected),
                    ),
                ),
            )
            return
        }

        if (formattedAmount == null) {
            _currentState.postValue(
                SendMoneyState.InlineError(
                    UIError(
                        ErrorType.SNACKBAR,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return
        }

        _currentState.postValue(SendMoneyState.RequestAuthentication)
        navController.navigate(R.id.action_sendMoneyToUser_To_AuthenticationProvider)
    }

    fun onUserAuthenticationSuccessful(recipient: BCNRecipientIdentifierType) {
        _currentState.postValue(SendMoneyState.UserAuthenticationSuccess(recipient))
    }

    fun onUserAuthenticationFailed(context: Context) {
        _currentState.postValue(
            SendMoneyState.UserAuthenticationFailed(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleBiometryAuthenticationFailure),
                    context.getString(R.string.alertMessageAuthenticationFailed),
                    ERROR_CODE_APP_AUTHENTICATION_FAILED,
                ),
            ),
        )
    }

    fun onUserAuthenticationCancelled() {
        _currentState.postValue(SendMoneyState.AcceptInput)
    }

    fun onAuthenticationFailed(activity: FragmentActivity?) {
        vmIOScope.launch {
            logout(activity)
        }
    }

    fun onReceiverIsAgent(navController: NavController) {
        navController.popBackStack(R.id.moneyScreenFragment, false)
    }

    companion object {
        const val ERROR_CODE_APP_AUTHENTICATION_FAILED: Int = 101
    }
}

sealed class SendMoneyState {
    object AcceptInput : SendMoneyState()
    object InlineLoading : SendMoneyState()
    object Loading : SendMoneyState()
    object RequestAuthentication : SendMoneyState()
    object InlineLoadingWithData : SendMoneyState()
    data class InlineError(val inlineError: UIError) : SendMoneyState()
    data class InlineErrorWithData(
        val formattedAmount: FormattedAmount?,
        val privateRemark: String?,
        val publicRemark: String?,
        val recipient: BCNRecipientIdentifierType,
        val error: UIError,
    ) : SendMoneyState()

    data class FullScreenError(val recipientType: RecipientType, val errorMessage: String) :
        SendMoneyState()

    data class InvalidAppLinkArguments(val errorMessage: String) : SendMoneyState()
    data class RecipientScannedFromQRCode(val recipientType: RecipientType) : SendMoneyState()

    data class SuccessfulTransaction(
        val transactionId: String,
        val amount: Amount,
        val transactionSucceededAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) : SendMoneyState()

    data class Confirmation(
        val recordId: UUID,
        val transactionFee: Amount,
        val amount: Amount,
        val recipient: BCNRecipientIdentifierType,
        val accountName: String,
        val exchangeRateAmount: Amount?,
    ) : SendMoneyState()

    object TermsAndConditionsDocumentDownloaded : SendMoneyState()

    data class UserAuthenticationSuccess(val recipient: BCNRecipientIdentifierType) :
        SendMoneyState()

    data class UserAuthenticationFailed(val inlineError: UIError) : SendMoneyState()
}
