package com.resoluttech.core.transfers.model

import com.resoluttech.bcn.types.Currency
import java.net.URL
import java.util.UUID

sealed class RecipientType {
    data class UserId(val imageUri: URL?, val username: String, val userId: UUID, val acceptsCurrencies: List<Currency> = listOf()) : RecipientType()
    data class AccountId(val imageUri: URL?, val username: String, val accountId: UUID, val acceptsCurrencies: List<Currency> = listOf()) : RecipientType()
}
