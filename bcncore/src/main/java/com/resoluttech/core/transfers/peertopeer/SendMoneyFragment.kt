package com.resoluttech.core.transfers.peertopeer

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.transfers.BCNRecipientIdentifierType
import com.resoluttech.bcn.transfers.RecentlyPaidRecipient
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.BCNUserDisplayInfo
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config.Companion.MAX_REMARKS_LENGTH
import com.resoluttech.core.listeners.DialogListener
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.model.RecipientType
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_ACCOUNT_ID
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_SHOULD_NAVIGATE
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_USER_ID
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_USER_NAME
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ErrorMessageDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.uicomponents.OTPChargesAlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.SEND_MONEY_DIALOG_CODE
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.BCNAppAppAuthenticationProvider
import com.resoluttech.core.utils.centerCrop
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getImmediateBackstackDestinationId
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.setSystemBackPress
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.utils.toUri
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.LimitedCharacterEditText
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_NAME
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber
import java.net.URL
import java.time.Instant
import java.util.UUID

class SendMoneyFragment :
    BaseFragment(),
    ErrorMessageDialog.ErrorMessageDialogListener,
    DialogListener,
    AmountEditText.ErrorListener,
    AlertDialog.ActionListener,
    AppAuthenticationStatus,
    BaseFragment.NetworkListener {

    private lateinit var sendMoneyVM: SendMoneyVM
    private val peerTransferVM: PeerTransfersVM by navGraphViewModels(R.id.peer_to_peer_transfer_nav)
    private val shouldNavigate: Boolean by lazy {
        arguments?.getBoolean(KEY_SHOULD_NAVIGATE) ?: false
    }

    private lateinit var titleTV: TextView
    private lateinit var contactIV: ImageView
    private lateinit var subtitleTV: TextView
    private lateinit var amountET: AmountEditText
    private lateinit var privateNoteET: LimitedCharacterEditText
    private lateinit var paymentMB: MaterialButton
    private var inlineErrorSnackbar: Snackbar? = null
    private lateinit var publicNoteET: LimitedCharacterEditText
    private lateinit var publicNoteTIL: TextInputLayout
    private lateinit var privateNoteTIL: TextInputLayout
    private var recipientImageURL: URL? = null
    private lateinit var currencySuffixTV: TextView
    private var transactionFee: Amount? = null
    private var recipient: BCNRecipientIdentifierType? = null

    private lateinit var enterAmountView: View
    private lateinit var errorView: View
    private lateinit var errorMessage: TextView
    private lateinit var retryButton: MaterialButton
    private var rootView: View? = null

    private lateinit var itemWalletSelector: ConstraintLayout
    private lateinit var accountName: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        if (rootView == null) {
            rootView = inflater.inflate(R.layout.fragment_send_money, container, false)?.apply {
                initViews(this)
                initVMs()
                setCurrencySuffix(this)
                setupErrorView(this)
                setupWalletSelector()
                setRemarksSupport()
                setupAuthenticationStatusListener()
                setSystemBackPress(getImmediateBackstackDestinationId())
            }
        }
        return rootView
    }

    private fun setupWalletSelector() {
        val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            ?: throw IllegalStateException("Selected account cannot be null")
        accountName.text = selectedAccount.name
        sendMoneyVM.selectedAccountId = UUID.fromString(selectedAccount.id)
        itemWalletSelector.setOnClickListener {
            if (sendMoneyVM.selectedAccountId != null) {
                findNavController().navigate(Uri.parse("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}&selectedAccountId=${sendMoneyVM.selectedAccountId}"))
            } else {
                findNavController().navigate(Uri.parse("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}"))
            }
        }
    }

    private fun setupAuthenticationStatusListener() {
        BCNAppAppAuthenticationProvider.setListener(this)
    }

    private fun setupErrorView(rootView: View) {
        errorView = rootView.findViewById(R.id.send_money_layout_error)
        errorMessage = rootView.findViewById(R.id.retry_message)
        retryButton = rootView.findViewById(R.id.retry_button)

        retryButton.setOnClickListener {
            sendMoneyVM.onRetryTapped(requireContext())
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.sendMoneyViewTitle))
        showToolbar()
        networkListenerCallback = this
        sendMoneyVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        setupWalletSelectorListeners()
        reactToTransferPath(peerTransferVM.peerTransferDestination)
    }

    private fun setupWalletSelectorListeners() {
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            sendMoneyVM.selectedAccountId = it
        }
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>(
            KEY_SELECTED_ACCOUNT_NAME,
        )?.observe(
            viewLifecycleOwner,
        ) {
            accountName.text = it
        }
    }

    private fun setRemarksSupport() {
        privateNoteET.setTextInputLayout(privateNoteTIL, MAX_REMARKS_LENGTH)
        publicNoteET.setTextInputLayout(publicNoteTIL, MAX_REMARKS_LENGTH)
    }

    private fun initVMs() {
        sendMoneyVM = ViewModelProvider(this)[SendMoneyVM::class.java]
    }

    private fun initViews(rootView: View) {
        enterAmountView = rootView.findViewById(R.id.send_money_layout_enter_amount)
        titleTV = rootView.findViewById(R.id.person_name_tv)
        subtitleTV = rootView.findViewById(R.id.person_phone_number_tv)
        contactIV = rootView.findViewById(R.id.profile_image_view)
        amountET = rootView.findViewById(R.id.amount_edit_text)
        paymentMB = rootView.findViewById(R.id.pay_now_button)
        privateNoteET = rootView.findViewById(R.id.private_remark_et)
        publicNoteET = rootView.findViewById(R.id.public_remark_et)
        publicNoteTIL = rootView.findViewById(R.id.public_remark_til)
        privateNoteTIL = rootView.findViewById(R.id.private_remark_til)
        itemWalletSelector = rootView.findViewById(R.id.item_select_wallet)
        accountName = rootView.findViewById(R.id.account_name_tv)
        amountET.setErrorListener(this)
    }

    private fun reactToTransferPath(transferDestination: PeerTransferDestination) {
        when (transferDestination) {
            is PeerTransferDestination.RecipientSelectedFromAddressBook -> handleContactSelectedFromAddressBook(
                transferDestination.bcnRecipient,
                transferDestination.phoneNumber,
            )

            is PeerTransferDestination.RecipientSelectedRecent -> handleRecentUserSelected(
                transferDestination.recentlyPaidRecipient,
            )

            is PeerTransferDestination.RecipientScannedFromQRCode -> handleQRCodeScan(
                transferDestination.recipientType,
            )

            is PeerTransferDestination.RecipientScannedFromQRCodeExternally -> handleContactScannedFromQRCodeExternally()
            is PeerTransferDestination.InlineError -> handleInlineErrorState(transferDestination.errorMessage)
        }
    }

    private fun handleInlineErrorState(errorMessage: String) {
        showInlineErrorSnackbar(errorMessage)
    }

    private fun showStateView(view: View) {
        when (view) {
            paymentMB -> {
                enterAmountView.visibility = View.VISIBLE
                errorView.visibility = View.GONE
                dismissProgressDialog()
                showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
            }

            errorView -> {
                errorView.visibility = View.VISIBLE
                enterAmountView.visibility = View.GONE
            }

            enterAmountView -> {
                enterAmountView.visibility = View.VISIBLE
                errorView.visibility = View.GONE
            }
        }
    }

    private fun reactToState(sendMoneyState: SendMoneyState) {
        when (sendMoneyState) {
            is SendMoneyState.AcceptInput -> {
                handleAcceptInputState()
            }

            is SendMoneyState.InlineLoading -> {
                handleInlineLoadingState()
            }

            is SendMoneyState.InlineError -> {
                handleDataWithInlineError(sendMoneyState.inlineError)
            }

            is SendMoneyState.InlineErrorWithData -> {
                handleInlineErrorWithDataState(
                    sendMoneyState,
                )
            }

            is SendMoneyState.InlineLoadingWithData -> {
                handleInlineLoadingWithData()
            }

            is SendMoneyState.SuccessfulTransaction -> {
                handleSuccessfulTransaction(
                    sendMoneyState.transactionId,
                    sendMoneyState.amount,
                    sendMoneyState.transactionSucceededAt,
                    sendMoneyState.transactionDescription,
                    sendMoneyState.transactionStatusItemDetail,
                )
            }

            is SendMoneyState.RecipientScannedFromQRCode -> {
                handleContactScannedFromQRCode(sendMoneyState.recipientType)
            }

            is SendMoneyState.InvalidAppLinkArguments -> {
                handleInvalidAppLinkArgument(sendMoneyState.errorMessage)
            }

            is SendMoneyState.Confirmation -> {
                handleConfirmationState(
                    sendMoneyState.transactionFee,
                    sendMoneyState.amount,
                    sendMoneyState.accountName,
                    sendMoneyState.exchangeRateAmount,
                )
            }

            is SendMoneyState.TermsAndConditionsDocumentDownloaded -> {
                handleTermsAndConditionsDocumentDownloaded()
            }

            is SendMoneyState.Loading -> {
                handleLoadingState()
            }

            is SendMoneyState.FullScreenError -> {
                handleFullScreenErrorState(sendMoneyState)
            }

            is SendMoneyState.UserAuthenticationFailed -> {
                handleDataWithInlineError(sendMoneyState.inlineError)
            }

            is SendMoneyState.UserAuthenticationSuccess -> {
                handleUserAuthenticationSuccess(sendMoneyState)
            }

            is SendMoneyState.RequestAuthentication -> {
                handleInlineLoadingWithData()
            }
        }
    }

    private fun handleUserAuthenticationSuccess(state: SendMoneyState.UserAuthenticationSuccess) {
        sendMoneyVM.onUserAuthenticated(
            amountET.getFormattedAmount(),
            getCurrentCurrency(),
            state.recipient,
            requireContext(),
            titleTV.text.toString(),
        )
    }

    private fun handleFullScreenErrorState(sendMoneyState: SendMoneyState.FullScreenError) {
        dismissProgressDialog()
        showStateView(errorView)
        errorMessage.text = sendMoneyState.errorMessage
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleConfirmationState(
        transactionFee: Amount,
        amount: Amount,
        accountName: String,
        exchangeRateAmount: Amount?,
    ) {
        dismissProgressDialog()
        dismissConfirmationDialog()
        this.transactionFee = transactionFee
        val transactionFeeAlertDialog =
            OTPChargesAlertDialog.newInstance(
                transactionFee,
                amount,
                accountName,
                termsAndConditionLink = "https://www.surya-soft.com/",
                exchangeRateAmount = exchangeRateAmount,
            )
        transactionFeeAlertDialog.setArguments(false)
        transactionFeeAlertDialog.show(childFragmentManager, OTPChargesAlertDialog.TAG)
    }

    private fun handleTermsAndConditionsDocumentDownloaded() {
        sendMoneyVM.onTermsAndConditionsDownloaded(
            findNavController(),
            requireContext(),
            recipient ?: throw IllegalArgumentException("Recipient Identifier type cannot be null"),
        )
    }

    private fun handleInvalidAppLinkArgument(errorMessage: String) {
        dismissProgressDialog()
        val errorMessageDialog = ErrorMessageDialog.newInstance(errorMessage)
        errorMessageDialog.show(childFragmentManager, ErrorMessageDialog.TAG)
    }

    override fun onErrorDialogDismiss() {
        dismissErrorDialog()
        sendMoneyVM.onErrorDialogDismiss(findNavController(), shouldNavigate)
    }

    private fun dismissErrorDialog() {
        val frag = childFragmentManager.findFragmentByTag(ErrorMessageDialog.TAG)
        if (frag is DialogFragment && isVisible) {
            frag.dismiss()
        } else {
            throw IllegalStateException("Trying to dismiss dialog when it is not showing.")
        }
    }

    private fun handleQRCodeScan(recipientType: RecipientType) {
        subtitleTV.visibility = View.GONE
        when (recipientType) {
            is RecipientType.AccountId -> {
                titleTV.text = recipientType.username
                titleTV.addContentDescriptionString(
                    R.string.axSendMoneyNameLabel,
                    requireContext(),
                    recipientType.username,
                )
                sendMoneyVM.lookupRecipientFromAccountId(
                    recipientType.accountId,
                    recipientType.username,
                    requireContext(),
                )
            }

            is RecipientType.UserId -> {
                titleTV.text = recipientType.username
                titleTV.addContentDescriptionString(
                    R.string.axSendMoneyNameLabel,
                    requireContext(),
                    recipientType.username,
                )
                sendMoneyVM.lookupRecipientFromUserId(
                    recipientType.userId,
                    recipientType.username,
                    requireContext(),
                )
            }
        }
    }

    private fun handleContactScannedFromQRCode(recipientType: RecipientType) {
        dismissProgressDialog()
        subtitleTV.visibility = View.GONE
        showStateView(enterAmountView)
        paymentMB.enable()
        when (recipientType) {
            is RecipientType.AccountId -> {
                if (recipientType.acceptsCurrencies.size == 1) {
                    currencySuffixTV.text = recipientType.acceptsCurrencies.last().currencyCode
                }
                setPaymentButtonAction(
                    BCNRecipientIdentifierType.AccountId(
                        accountId = recipientType.accountId,
                    ),
                )
                titleTV.text = recipientType.username
                titleTV.addContentDescriptionString(
                    R.string.axSendMoneyNameLabel,
                    requireContext(),
                    recipientType.username,
                )
                recipientImageURL = recipientType.imageUri
                if (recipientType.imageUri != null) {
                    contactIV.loadImage(recipientType.imageUri.toUri())
                    contactIV.centerCrop()
                }
            }

            is RecipientType.UserId -> {
                if (recipientType.acceptsCurrencies.size == 1) {
                    currencySuffixTV.text = recipientType.acceptsCurrencies.last().currencyCode
                }
                setPaymentButtonAction(
                    BCNRecipientIdentifierType.UserId(
                        userId = recipientType.userId,
                    ),
                )
                titleTV.text = recipientType.username
                recipientImageURL = recipientType.imageUri
                titleTV.addContentDescriptionString(
                    R.string.axSendMoneyNameLabel,
                    requireContext(),
                    recipientType.username,
                )
                if (recipientType.imageUri != null) {
                    contactIV.loadImage(recipientType.imageUri.toUri())
                    contactIV.centerCrop()
                }
            }
        }
    }

    private fun handleContactScannedFromQRCodeExternally() {
        val username = arguments?.getString(KEY_USER_NAME)
        val userId = arguments?.getString(KEY_USER_ID)
        val accountId = arguments?.getString(KEY_ACCOUNT_ID)
        titleTV.text = username
        titleTV.addContentDescriptionString(
            R.string.axSendMoneyNameLabel,
            requireContext(),
            username,
        )
        subtitleTV.visibility = View.GONE
        sendMoneyVM.onQRCodeScannedExternally(requireContext(), username, userId, accountId)
    }

    private fun handleContactSelectedFromAddressBook(
        bcnRecipient: BCNUserDisplayInfo,
        phoneNumber: LeoPhoneNumber,
    ) {
        recipientImageURL = bcnRecipient.profileImage?.getSpecificResolutionImageURL()
        subtitleTV.text = phoneNumber.getFormattedPhoneNumber()
        titleTV.text = bcnRecipient.displayName
        titleTV.addContentDescriptionString(
            R.string.axSendMoneyNameAndNumberLabel,
            requireContext(),
            bcnRecipient.displayName,
            phoneNumber.value,
        )
        bcnRecipient.profileImage?.getSpecificResolutionImageURL()?.let {
            contactIV.loadImage(it.toUri())
            contactIV.centerCrop()
        }
        setPaymentButtonAction(
            BCNRecipientIdentifierType.PhoneNumber(
                phoneNumber = phoneNumber,
            ),
        )
    }

    private fun handleRecentUserSelected(recentlyPaidBCNUser: RecentlyPaidRecipient) {
        recipientImageURL =
            recentlyPaidBCNUser.bcnRecipient.profileImage?.getSpecificResolutionImageURL()
        titleTV.text = recentlyPaidBCNUser.bcnRecipient.displayName
        titleTV.addContentDescriptionString(
            R.string.axSendMoneyNameLabel,
            requireContext(),
            recentlyPaidBCNUser.bcnRecipient.displayName,
        )
        subtitleTV.visibility = View.GONE
        recentlyPaidBCNUser.bcnRecipient.profileImage?.getSpecificResolutionImageURL()?.let {
            contactIV.loadImage(it.toUri())
            contactIV.centerCrop()
        }
        setPaymentButtonAction(
            BCNRecipientIdentifierType.UserId(
                userId = recentlyPaidBCNUser.userId,
            ),
        )
    }

    private fun handleSuccessfulTransaction(
        transactionId: String,
        amount: Amount,
        transactionSucceededAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        dismissProgressDialog()
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        sendMoneyVM.handleSuccessfulTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionSucceededAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
    }

    private fun handleAcceptInputState() {
        dismissConfirmationDialog()
        dismissProgressDialog()
        showStateView(enterAmountView)
    }

    private fun handleInlineLoadingState() {
        dismissConfirmationDialog()
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleDataWithInlineError(uiError: UIError) {
        dismissConfirmationDialog()
        dismissProgressDialog()
        showStateView(enterAmountView)
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackbar(uiError.errorMessage)
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: SEND_MONEY_DIALOG_CODE,
                    forceOutUserDestination = ForceOutUserDestination.HOME,
                )
            }

            ErrorType.BANNER -> {
                dismissConfirmationDialog()
                handleNetworkLostState()
            }
        }
    }

    private fun showInlineErrorSnackbar(
        inLineError: String,
    ) {
        dismissConfirmationDialog()
        dismissProgressDialog()
        hideKeyboard()
        inlineErrorSnackbar = Snackbar.make(requireView(), inLineError, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackbar?.let {
            it.setAction(R.string.alertActionDismiss) {
                sendMoneyVM.inlineErrorDismissed()
            }
            it.show()
        }
    }

    private fun handleInlineErrorWithDataState(
        state: SendMoneyState.InlineErrorWithData,
    ) {
        dismissProgressDialog()
        when (state.error.type) {
            ErrorType.DIALOG -> {
                handleInlineErrorWithDataDialog(state)
            }

            ErrorType.SNACKBAR -> {
                showInlineErrorSnackbar(state.error.errorMessage)
            }

            ErrorType.BANNER -> {
                dismissConfirmationDialog()
                handleNetworkLostState()
            }
        }
    }

    private fun handleInlineErrorWithDataDialog(
        state: SendMoneyState.InlineErrorWithData,
    ) {
        dismissConfirmationDialog()
        dismissProgressDialog()
        showErrorDialog(
            state.error.errorTitle,
            state.error.errorMessage,
            state.error.errorCode ?: SEND_MONEY_DIALOG_CODE,
            getString(R.string.alertActionDismiss),
            forceOutUserDestination = ForceOutUserDestination.HOME,
        )
    }

    private fun handleInlineLoadingWithData() {
        dismissConfirmationDialog()
        dismissProgressDialog()
        showStateView(enterAmountView)
    }

    private fun setPaymentButtonAction(recipient: BCNRecipientIdentifierType) {
        this.recipient = recipient
        paymentMB.enable(amountET.text?.isNotBlank() ?: false)
        paymentMB.setOnClickListener {
            sendMoneyVM.onAttemptPaymentButtonTapped(
                findNavController(),
                amountET.getFormattedAmount(),
                requireContext(),
            )
        }
    }

    private fun setCurrencySuffix(rootView: View) {
        currencySuffixTV = rootView.findViewById(R.id.currency_suffix)
        currencySuffixTV.text = UserSharedPreference.getUserPrimaryCurrency()
    }

    private fun getCurrentCurrency(): Currency {
        return Currency(currencySuffixTV.text.toString())
    }

    private fun dismissConfirmationDialog() {
        val frag = childFragmentManager.findFragmentByTag(OTPChargesAlertDialog.TAG)
        if (frag is DialogFragment && isVisible) {
            frag.dismiss()
        } else {
            Timber.tag(TAG).i("Trying to dismiss dialog when it is not showing.")
        }
    }

    override fun onProceed() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
        sendMoneyVM.onConfirmPayment(
            amountET.getFormattedAmount(),
            getCurrentCurrency(),
            privateNoteET.getInputText(),
            publicNoteET.getInputText(),
            titleTV.text.toString(),
            requireContext(),
        )
    }

    override fun onDeclined() {
        sendMoneyVM.onErrorDismissed()
    }

    override fun onClickTermsAndCondition() {
        dismissProgressDialog()
        dismissConfirmationDialog()
        sendMoneyVM.onTermsAndConditionTapped(requireContext(), TERMS_AND_CONDITION_URL)
    }

    override fun onAmountInput(isValid: Boolean) {
        paymentMB.enable(isValid)
        if (isValid) {
            paymentMB.addContentDescriptionString(
                R.string.axSendMoneyAttemptPaymentHint,
                requireContext(),
            )
        } else {
            paymentMB.addContentDescriptionString(
                R.string.axSendMoneyAttemptPaymentHint,
                requireContext(),
            )
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        sendMoneyVM.onErrorDismissed()
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                sendMoneyVM.onErrorDismissed()
                leoServerExceptionHandler(findNavController())
            }

            SendMoneyVM.ERROR_CODE_APP_AUTHENTICATION_FAILED -> {
                sendMoneyVM.onAuthenticationFailed(activity)
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onAppAuthenticationSuccessful(navController: NavController) {
        if (sendMoneyVM.currentState.value is SendMoneyState.RequestAuthentication) {
            sendMoneyVM.onUserAuthenticationSuccessful(recipient!!)
        } else {
            sendMoneyVM.onAuthenticationSuccess()
        }
    }

    override fun onAppAuthenticationFailed(navController: NavController) {
        sendMoneyVM.onUserAuthenticationFailed(requireContext())
    }

    override fun onAppAuthenticationCancelled(navController: NavController) {
        sendMoneyVM.onUserAuthenticationCancelled()
    }

    override fun onNetworkAvailable() {
        sendMoneyVM.onErrorDismissed()
    }
}

private const val TAG = "SendMoneyFragment"
private const val TERMS_AND_CONDITION_URL =
    "https://s3.af-south-1.amazonaws.com/images.resoluttech.link/Terms-and-Conditions.pdf"
