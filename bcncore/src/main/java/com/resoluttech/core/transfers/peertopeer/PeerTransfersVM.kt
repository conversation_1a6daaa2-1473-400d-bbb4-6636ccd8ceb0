package com.resoluttech.core.transfers.peertopeer

import android.content.Context
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.RecentlyPaidRecipient
import com.resoluttech.bcn.types.BCNUserDisplayInfo
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.transfers.config.QRCodeConfig
import com.resoluttech.core.transfers.model.RecipientType
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.views.isNetworkConnected
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.util.UUID

class PeerTransfersVM : ViewModel() {
    private val qrCodeConfig: QRCodeConfig by KoinJavaComponent.inject(
        QRCodeConfig::class.java,
    )
    var peerTransferDestination: PeerTransferDestination =
        PeerTransferDestination.RecipientScannedFromQRCodeExternally

    private val _qrCodeScannerState: MutableLiveData<QRCodeScannerState> =
        MutableLiveData(QRCodeScannerState.AcceptInput)
    val qrCodeScannerState: LiveData<QRCodeScannerState> = _qrCodeScannerState
    private var dismissJob: Job? = null

    fun onReceiveQRValue(
        context: Context,
        navController: NavController,
        scannedValue: String,
        performHapticFeedback: () -> Unit,
    ) {
        if (context.isNetworkConnected()) {
            if (validateQRCodeInput(scannedValue, context)) {
                val username = getUsernameFromScannedValue(scannedValue, context)
                val userId = getUserIdFromScannedValue(scannedValue, context)
                val accountId = getAccountIdFromScannedValue(scannedValue, context)
                when {
                    accountId != null -> {
                        performHapticFeedback()
                        peerTransferDestination =
                            PeerTransferDestination.RecipientScannedFromQRCode(
                                RecipientType.AccountId(
                                    imageUri = null,
                                    username = username,
                                    accountId = accountId,
                                ),
                            )
                        navigateToSendMoney(navController)
                    }
                    userId != null -> {
                        performHapticFeedback()
                        peerTransferDestination =
                            PeerTransferDestination.RecipientScannedFromQRCode(
                                RecipientType.UserId(
                                    imageUri = null,
                                    username = username,
                                    userId = userId,
                                ),
                            )
                        navigateToSendMoney(navController)
                    }
                    else -> {
                        _qrCodeScannerState.postValue(
                            QRCodeScannerState.InlineError(
                                context.getString(
                                    R.string.qrCodeInvalidRecipient,
                                ),
                            ),
                        )
                    }
                }
            } else {
                _qrCodeScannerState.postValue(QRCodeScannerState.InlineError(context.getString(R.string.qrCodeInvalidRecipient)))
            }
        } else {
            _qrCodeScannerState.postValue(QRCodeScannerState.NoInternet)
        }
    }

    fun onInvalidImageSelectedFromGallery(context: Context) {
        _qrCodeScannerState.postValue(QRCodeScannerState.InlineError(context.getString(R.string.qrCodeInvalidRecipient)))
    }

    private fun navigateToSendMoney(navController: NavController) {
        if (navController.currentDestination?.id == R.id.qrCode) {
            navController.navigate(R.id.action_qr_code_to_sendMoneyToUser)
        } else {
            Timber.tag(TAG)
                .e("Trying to navigate from invalid current destination: ${navController.currentDestination?.id}")
        }
    }

    fun onInlineErrorDismiss(changeErrorText: () -> Unit) {
        if (dismissJob != null) {
            dismissJob?.cancel()
        }
        dismissJob = viewModelScope.launch {
            // Delay is added here so that if user scan a wrong QR code, it provides a gap of 1 second between next scan and
            // displays the error for 1 second duration.
            delay(SCAN_DELAY)
            changeErrorText()
            _qrCodeScannerState.postValue(QRCodeScannerState.AcceptInput)
        }
    }

    @Throws(IllegalStateException::class)
    fun onContactSelectedFromAddressBook(
        bcnRecipient: BCNUserDisplayInfo,
        phoneNumber: LeoPhoneNumber,
        acceptsCurrencies: List<Currency>,
        navController: NavController,
    ) {
        handleSelectedContactValue(bcnRecipient, phoneNumber, acceptsCurrencies)
        navController.navigate(R.id.action_contact_picker_to_sendMoneyToUser)
    }

    fun onContactSelectedFromRecent(
        recentlyPaidUser: RecentlyPaidRecipient,
        navController: NavController,
    ) {
        navController.navigateSafe(R.id.action_qr_code_to_sendMoneyToUser)
        peerTransferDestination = PeerTransferDestination.RecipientSelectedRecent(recentlyPaidUser)
    }

    @Throws(IllegalStateException::class)
    private fun handleSelectedContactValue(
        user: BCNUserDisplayInfo,
        phoneNumber: LeoPhoneNumber,
        acceptsCurrencies: List<Currency>,
    ) {
        peerTransferDestination =
            PeerTransferDestination.RecipientSelectedFromAddressBook(
                user,
                phoneNumber,
                acceptsCurrencies,
            )
    }

    private fun validateQRCodeInput(value: String, context: Context): Boolean {
        if (!value.startsWith("${qrCodeConfig.scheme}://${qrCodeConfig.identifier}", false)) {
            return false
        }

        val uri = value.toUri()
        // When no query parameters are set
        if (uri.queryParameterNames == null) {
            return false
        }
        // When query parameters are not assigned any value
        if (uri.queryParameterNames!!.size <= 0) {
            return false
        }
        // When username is not a query parameter or holds no value
        if ((uri.getQueryParameter(KEY_USER_NAME) ?: context.getEmptyString()).isEmpty()) {
            return false
        }

        return true
    }

    private fun getUsernameFromScannedValue(value: String, context: Context): String {
        return value.toUri().getQueryParameter(KEY_USER_NAME) ?: context.getEmptyString()
    }

    private fun getUserIdFromScannedValue(value: String, context: Context): UUID? {
        val userId = value.toUri().getQueryParameter(KEY_USER_ID) ?: context.getEmptyString()
        return try {
            UUID.fromString(userId)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    private fun getAccountIdFromScannedValue(value: String, context: Context): UUID? {
        val accountId =
            value.toUri().getQueryParameter(KEY_ACCOUNT_ID) ?: context.getEmptyString()
        return try {
            UUID.fromString(accountId)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    companion object {
        const val KEY_USER_NAME: String = "username"
        const val KEY_USER_ID: String = "userId"
        const val KEY_ACCOUNT_ID: String = "accountId"
        const val KEY_SHOULD_NAVIGATE: String = "shouldNavigate"
    }
}

sealed class PeerTransferDestination {
    data class RecipientSelectedFromAddressBook(
        val bcnRecipient: BCNUserDisplayInfo,
        val phoneNumber: LeoPhoneNumber,
        val availableCurrencies: List<Currency>,
    ) : PeerTransferDestination()

    data class RecipientSelectedRecent(val recentlyPaidRecipient: RecentlyPaidRecipient) :
        PeerTransferDestination()

    data class RecipientScannedFromQRCode(val recipientType: RecipientType) :
        PeerTransferDestination()

    object RecipientScannedFromQRCodeExternally : PeerTransferDestination()
    data class InlineError(val errorMessage: String) : PeerTransferDestination()
}

sealed class QRCodeScannerState {
    object AcceptInput : QRCodeScannerState()
    data class InlineError(val errorMessage: String) : QRCodeScannerState()
    object NoInternet : QRCodeScannerState()
}

private const val TAG = "PeerTransfersVM"
private const val SCAN_DELAY = 1000L
