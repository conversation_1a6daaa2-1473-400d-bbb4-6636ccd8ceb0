package com.resoluttech.core.home

import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.bcn.homeScreen.UpdatePushTokenRPC
import com.resoluttech.bcn.types.PushToken
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent

class MoneyScreenRepository {

    private val moneyRPC: GetMoneyScreenDataRPC by KoinJavaComponent.inject(GetMoneyScreenDataRPC::class.java)

    private val updatePushToken: UpdatePushTokenRPC by KoinJavaComponent.inject(UpdatePushTokenRPC::class.java)

    suspend fun getMoneyScreenData(request: GetMoneyScreenDataRPC.Request): LeoRPCResult<GetMoneyScreenDataRPC.Response, GetMoneyScreenDataRPC.Error> {
        return moneyRPC.execute(request)
    }

    suspend fun updatePushToken(token: PushToken): LeoRPCResult<UpdatePushTokenRPC.Response, UpdatePushTokenRPC.Error> {
        return updatePushToken.execute(UpdatePushTokenRPC.Request(token))
    }
}
