package com.resoluttech.core.home

import android.net.Uri
import com.resoluttech.core.deeplinks.DeepLinkedTransactionStatusFragment
import com.resoluttech.core.utils.SupportedLocale
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

internal fun getAppNotificationDestination(payload: String): String? {
    return try {
        val json = Json.parseToJsonElement(payload)
        val payloadObject = json.jsonObject
        when (payloadObject["type"]?.jsonPrimitive?.content) {
            "Transaction" -> {
                getTransactionsDeeplink(payloadObject)
            }
            else -> null
        }
    } catch (e: SerializationException) {
        e.printStackTrace()
        null
    }
}

private fun getTransactionsDeeplink(payloadObject: JsonObject): String {
    val attributes = payloadObject["attributes"]?.jsonObject ?: throw SerializationException()
    val status = attributes.getValueFrom("status")
        ?: throw SerializationException("No value found for key: status")
    val id =
        attributes.getValueFrom("id") ?: throw SerializationException("No value found for key: id")
    val amount = attributes.getValueFrom("amount")
        ?: throw SerializationException("No value found for key: amount")
    val currency = attributes.getValueFrom("currency")
        ?: throw SerializationException("No value found for key: currency")
    val locale = attributes.getValueFrom("locale") ?: SupportedLocale.EN_US.name
    val publicRemark = attributes.getValueFrom("publicRemark")
    val privateRemark =
        attributes.getValueFrom("privateRemark")
    val timestamp = attributes.getValueFrom("timestamp")
        ?: throw SerializationException("No value found for key: timestamp")
    val failureReason =
        attributes.getValueFrom("failureReason")
    val deeplink: String
    DeepLinkedTransactionStatusFragment.apply {
        deeplink =
            Uri.encode(
                "resoluttech://transaction_status/?$STATUS_ID_KEY=$status&$TRANSACTION_ID_KEY=$id&$AMOUNT_ID_KEY=$amount&$CURRENCY_ID_KEY=$currency&$PUBLIC_REMARK_ID_KEY=${publicRemark?.newLineEncoder()}&$PRIVATE_REMARK_ID_KEY=${privateRemark?.newLineEncoder()}&$DATE_TIME_ID_KEY=$timestamp&$FAILURE_REASON_ID_KEY=$failureReason&$LOCALE_KEY=$locale",
                "://?=&",
            )
    }
    return deeplink
}

fun String.newLineEncoder(): String {
    return replace("\n", "_0A")
}

fun String.newLineDecoder(): String {
    return replace("_0A", "\n")
}

private fun JsonObject.getValueFrom(key: String): String? {
    return get(key)?.jsonPrimitive?.content
}
