package com.resoluttech.core.home

import com.resoluttech.core.utils.SelectedAccountHelper
import java.util.UUID

data class MoneyScreenWallet(
    val displayName: String,
    val balance: String?,
    val accountId: UUID,
)

fun getPreviouslySelectedAccount(accountList: List<MoneyScreenWallet>): MoneyScreenWallet {
    return accountList.find { it.accountId.toString() == SelectedAccountHelper.getSelectedAccount()?.id } ?: accountList[0]
}
