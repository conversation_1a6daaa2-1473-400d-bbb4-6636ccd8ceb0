package com.resoluttech.core.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.bcn.types.Account
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.isLaunchedFromHistory
import org.koin.android.ext.android.inject
import timber.log.Timber

class MoneyScreenFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    interface UserDataUpdatedListener {
        fun onUserDataUpdated()
    }

    private val moneyScreenVM: MoneyScreenVM =
        ViewModelProvider.NewInstanceFactory().create(MoneyScreenVM::class.java)
    private lateinit var selectedWalletNameTV: TextView
    private lateinit var sendMoneyCard: TextView
    private lateinit var loadWalletCard: TextView
    private lateinit var loadingView: ProgressBar
    private lateinit var dataView: View
    private lateinit var errorLayout: LinearLayout
    private lateinit var errorTitleTV: TextView
    private lateinit var errorMessageTV: TextView
    private lateinit var retryButton: MaterialButton
    private lateinit var balanceTV: TextView
    private lateinit var barcodeIconIV: ImageView
    private lateinit var notificationIconIV: ImageView
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout
    private lateinit var nationalIdExpiredLayout: ConstraintLayout
    private val homeDataPersistor: HomeDataPersistor by inject()
    private val moneyScreenDataPersistor = MoneyScreenDataPersistor()
    private var isGetMoneyScreenDataRPCSuccessful: Boolean = true

    private val accountList = mutableListOf<MoneyScreenWallet>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.layout_money_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        networkListenerCallback = this
        initViews(view)
        setToolbar()
        setupActions()
        setPTR()
        setupBackPressed(requireActivity()::finish)
        setupRetryButton()
        moneyScreenVM.state.observe(viewLifecycleOwner, ::reactToState)
        moneyScreenVM.getMoneyScreenData(requireContext())
        moneyScreenVM.updatePushToken(requireContext())
    }

    private fun setPTR() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            moneyScreenVM.onPullToRefreshAction(requireContext())
        }
    }

    private fun reactToState(state: MoneyScreenState) {
        when (state) {
            is MoneyScreenState.Data -> {
                isGetMoneyScreenDataRPCSuccessful = true
                handleDataState(state.response, isErrorState = false)
            }

            is MoneyScreenState.Error -> {
                handleErrorState(state)
            }

            is MoneyScreenState.FullScreenError -> {
                handleFullScreenErrorState(state)
            }

            is MoneyScreenState.Loading -> {
                handleLoadingState()
            }

            is MoneyScreenState.InlineLoading -> {
                handleInlineLoadingState()
            }

            is MoneyScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            MoneyScreenState.Reset -> {
                handleResetState()
            }
        }
    }

    private fun setupRetryButton() {
        retryButton.setOnClickListener {
            moneyScreenVM.getMoneyScreenData(requireContext())
        }
    }

    private fun handleAcceptInputState() {
        swipeToRefreshLayout.isRefreshing = false
        loadingView.visibility = View.GONE
        barcodeIconIV.enable()
        notificationIconIV.enable()
        selectedWalletNameTV.enable()
        setToolbarIconState()
    }

    private fun handleResetState() {
        loadingView.visibility = View.GONE
        dataView.visibility = View.GONE
        errorLayout.visibility = View.GONE
        barcodeIconIV.disable()
        notificationIconIV.disable()
        selectedWalletNameTV.disable()
    }

    private fun handleInlineLoadingState() {
        setToolbarIconState()
        swipeToRefreshLayout.isRefreshing = true
    }

    private fun handleFullScreenErrorState(state: MoneyScreenState.FullScreenError) {
        loadingView.visibility = View.GONE
        setToolbarIconState()
        swipeToRefreshLayout.isRefreshing = false
        errorLayout.visibility = View.VISIBLE
        dataView.visibility = View.GONE
        if (state.uiError.errorTitle.isBlank()) {
            errorTitleTV.visibility = View.GONE
        } else {
            errorTitleTV.visibility = View.VISIBLE
            errorTitleTV.text = state.uiError.errorTitle
        }
        errorMessageTV.text = state.uiError.errorMessage
    }

    private fun handleErrorState(state: MoneyScreenState.Error) {
        setToolbarIconState()
        isGetMoneyScreenDataRPCSuccessful = false
        loadingView.visibility = View.GONE
        barcodeIconIV.enable()
        notificationIconIV.enable()
        selectedWalletNameTV.enable()
        swipeToRefreshLayout.isRefreshing = false
        errorLayout.visibility = View.GONE
        dataView.visibility = View.VISIBLE
        handleDataState(state.data, isErrorState = true)
        when (state.uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                state.uiError.errorMessage,
                requireView(),
            ) { moneyScreenVM.onErrorDismissed(state.data, requireContext()) }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    state.uiError.errorTitle,
                    state.uiError.errorMessage,
                    DialogCodes.MONEY_SCREEN_ERROR_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleLoadingState() {
        loadingView.visibility = View.VISIBLE
        dataView.visibility = View.GONE
        errorLayout.visibility = View.GONE
        setToolbarIconState()
        swipeToRefreshLayout.isRefreshing = false
        barcodeIconIV.disable()
        notificationIconIV.disable()
        selectedWalletNameTV.disable()
    }

    private fun handleDataState(response: GetMoneyScreenDataRPC.Response, isErrorState: Boolean) {
        setToolbarIconState()
        loadingView.visibility = View.GONE
        barcodeIconIV.enable()
        notificationIconIV.enable()
        selectedWalletNameTV.enable()
        dataView.visibility = View.VISIBLE
        errorLayout.visibility = View.GONE
        swipeToRefreshLayout.isRefreshing = false
        updateAccountsList(response)
        if (isErrorState) {
            selectedWalletNameTV.disable()
            selectedWalletNameTV.text =
                requireContext().getString(R.string.moneyScreenAvailableBalance)
            balanceTV.text = requireContext().getString(R.string.moneyScreenAvailableBalancePlaceholder)
        } else {
            if (SelectedAccountHelper.getSelectedAccount() == null) {
                setSelectedAccount(response.accounts.sortedByDescending(Account::isDefault)[0])
                val selectedAccount = getSelectedAccount(0)
                balanceTV.text = selectedAccount.balance
                selectedWalletNameTV.text = selectedAccount.displayName
                selectedWalletNameTV.enable()
            } else {
                if (isGetMoneyScreenDataRPCSuccessful) {
                    val selectedAccount = getPreviouslySelectedAccount(accountList.toList())
                    balanceTV.text = selectedAccount.balance
                    selectedWalletNameTV.text = selectedAccount.displayName
                    selectedWalletNameTV.enable()
                } else {
                    val selectedAccount = getSelectedAccount(0)
                    balanceTV.text = selectedAccount.balance
                    selectedWalletNameTV.text = selectedAccount.displayName
                    selectedWalletNameTV.enable()
                }
            }
        }
        if (response.user.isNationalIdExpired) {
            nationalIdExpiredLayout.visibility = View.VISIBLE
        } else {
            nationalIdExpiredLayout.visibility = View.GONE
        }
        homeDataPersistor.setUserIsAgent(response.user.isAgent)
        setupToolBarBarCodeIVVisibility()
    }

    private fun setSelectedAccount(moneyScreenAccount: Account) {
        moneyScreenVM.onAccountDropdownUpdate(
            requireContext(),
            moneyScreenAccount.accountId,
            isGetMoneyScreenDataRPCSuccessful,
        )
    }

    private fun updateAccountsList(response: GetMoneyScreenDataRPC.Response) {
        accountList.clear()
        response.accounts.sortedByDescending(Account::isDefault)
            .filter { it.accountState == AccountState.ACTIVE }.forEach {
                accountList.add(
                    MoneyScreenWallet(
                        it.displayName.name,
                        displayAmountWithCurrency(
                            it.balance.currency.currencyCode,
                            it.balance.amount.getUserFacingValue(),
                        ),
                        it.accountId,
                    ),
                )
            }
    }

    private fun setupActions() {
        sendMoneyCard.setOnClickListener {
            moneyScreenVM.onSendMoneyTapped(findNavController())
        }

        loadWalletCard.setOnClickListener {
            moneyScreenVM.onLoadWalletTapped(findNavController())
        }
    }

    private fun initViews(view: View) {
        view.apply {
            sendMoneyCard = findViewById(R.id.money_transfer_card)
            loadWalletCard = findViewById(R.id.load_wallet_card)
            loadingView = findViewById(R.id.progress_bar)
            dataView = findViewById(R.id.data_layout)
            balanceTV = findViewById(R.id.balance_tv)
            swipeToRefreshLayout = findViewById(R.id.swipe_to_refresh)
            nationalIdExpiredLayout = findViewById(R.id.national_id_expired_layout)
            errorLayout = findViewById(R.id.error_view)
            errorTitleTV = findViewById(R.id.fullScreenErrorTitle)
            errorMessageTV = findViewById(R.id.fullScreenErrorMessage)
            retryButton = findViewById(R.id.retry_button)
        }
    }

    private fun setToolbar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView = layoutInflater.inflate(R.layout.home_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            selectedWalletNameTV = toolbarView.findViewById(R.id.selected_wallet_name_tv)
            val account = SelectedAccountHelper.getSelectedAccount()
            if (account != null) {
                selectedWalletNameTV.enable()
                selectedWalletNameTV.text = account.name
            } else {
                selectedWalletNameTV.disable()
                selectedWalletNameTV.text =
                    requireContext().getString(R.string.moneyScreenAvailableBalancePlaceholder)
                balanceTV.text = requireContext().getString(R.string.moneyScreenAvailableBalancePlaceholder)
            }
            selectedWalletNameTV.setOnClickListener {
                moneyScreenVM.onToolbarSelectWalletTapped(findNavController())
            }
            barcodeIconIV = toolbarView.findViewById<ImageButton>(R.id.barcode_icon)
            setupToolBarBarCodeIVVisibility()
            notificationIconIV = toolbarView.findViewById<ImageButton>(R.id.notifications_icon)
            barcodeIconIV.setOnClickListener {
                moneyScreenVM.onToolbarBarcodeTapped(findNavController())
            }
            notificationIconIV.setOnClickListener {
                moneyScreenVM.onToolbarNotificationTapped(findNavController())
            }
        }
    }

    private fun setToolbarIconState() {
        (activity as UserDataUpdatedListener).onUserDataUpdated()
        val cachedHomeData: GetMoneyScreenDataRPC.Response? =
            moneyScreenDataPersistor.getCachedMoneyScreenData()
        if (cachedHomeData != null) {
            barcodeIconIV.enable()
            notificationIconIV.enable()
        } else {
            barcodeIconIV.disable()
            notificationIconIV.disable()
        }
    }

    private fun setupToolBarBarCodeIVVisibility() {
        if (homeDataPersistor.checkIfUserIsAgent() == true) {
            barcodeIconIV.visibility = View.GONE
        } else {
            barcodeIconIV.visibility = View.VISIBLE
        }
    }

    private fun getSelectedAccount(position: Int): MoneyScreenWallet {
        return accountList[position]
    }

    override fun onResume() {
        super.onResume()
        checkForDeepLinkArguments()
    }

    private fun checkForDeepLinkArguments() {
        val deeplink = requireActivity().intent.getStringExtra(PUSH_NOTIFICATION_DEEPLINK_KEY)
        Timber.d(message = deeplink)
        if (deeplink != null && isLaunchedFromHistory(requireActivity())) {
            requireActivity().intent.removeExtra(PUSH_NOTIFICATION_DEEPLINK_KEY)
            getAppNotificationDestination(deeplink)?.let {
                findNavController().navigate(it.toUri())
            }
        }
        val widgetDeeplink = requireActivity().intent.getStringExtra(WIDGET_SHORTCUT_DEEPLINK)
        if (widgetDeeplink != null && UserSharedPreference.getUser() != null) {
            requireActivity().intent.removeExtra(WIDGET_SHORTCUT_DEEPLINK)
            findNavController().navigate(widgetDeeplink.toUri())
        }
    }

    companion object {
        const val PUSH_NOTIFICATION_DEEPLINK_KEY: String = "deeplink"
        const val WIDGET_SHORTCUT_DEEPLINK: String = "WIDGET_SHORTCUT_DEEPLINK"
    }

    override fun onNetworkAvailable() {
        moneyScreenVM.getMoneyScreenData(requireContext())
    }

    override fun onPositiveAction(dialogId: Int) {
        moneyScreenVM.onDialogDismissed()
    }

    override fun onNegativeAction(dialogId: Int) {
        moneyScreenVM.onDialogDismissed()
    }
}
