package com.resoluttech.core.home

import com.resoluttech.bcn.homeScreen.GetInAppNotificationsRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent

class NotificationsRepository {
    private val notificationRPC: GetInAppNotificationsRPC by KoinJavaComponent.inject(GetInAppNotificationsRPC::class.java)

    suspend fun getNotifications(): LeoRPCResult<GetInAppNotificationsRPC.Response, GetInAppNotificationsRPC.Error> {
        return notificationRPC.execute(GetInAppNotificationsRPC.Request)
    }
}
