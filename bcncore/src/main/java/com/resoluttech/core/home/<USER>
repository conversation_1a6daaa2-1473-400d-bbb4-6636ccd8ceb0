package com.resoluttech.core.home

import android.content.Context
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcn.types.App
import com.resoluttech.bcn.types.FrontEndPlatform
import com.resoluttech.bcn.types.PushToken
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config.Companion.PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.Account
import com.resoluttech.core.utils.Country
import com.resoluttech.core.utils.CountryCurrency
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.User
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getImage
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import java.time.Duration
import java.time.Instant
import java.util.UUID

class MoneyScreenVM : ViewModel() {

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val moneyRepository = MoneyScreenRepository()
    private val homeRepository = HomeRepository()
    private val moneyScreenDataPersistor = MoneyScreenDataPersistor()
    private val _state: MutableLiveData<MoneyScreenState> =
        MutableLiveData(MoneyScreenState.Loading)
    val state: LiveData<MoneyScreenState> = _state

    fun onSendMoneyTapped(controller: NavController) {
        _state.postValue(MoneyScreenState.Reset)
        controller.navigateSafe(R.id.action_moneyScreenFragment_to_send_money_nav)
    }

    fun onLoadWalletTapped(controller: NavController) {
        _state.postValue(MoneyScreenState.Reset)
        controller.navigateSafe(R.id.action_moneyScreenFragment_to_load_wallet_nav)
    }

    fun onToolbarNotificationTapped(controller: NavController) {
        _state.postValue(MoneyScreenState.Reset)
        controller.navigate(R.id.action_moneyScreenFragment_to_notificationsFragment)
    }

    fun onToolbarBarcodeTapped(controller: NavController) {
        _state.postValue(MoneyScreenState.Reset)
        controller.navigate(R.id.peer_to_peer_transfer_nav)
    }

    fun onToolbarSelectWalletTapped(controller: NavController) {
        _state.postValue(MoneyScreenState.Reset)
        controller.navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.HOME_SCREEN.name}".toUri())
    }

    fun getMoneyScreenData(context: Context) {
        _state.postValue(MoneyScreenState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response =
                            moneyRepository.getMoneyScreenData(GetMoneyScreenDataRPC.Request)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleMoneyScreenRPCSuccessResponse(
                                context,
                                response.response,
                            )
                        }

                        is LeoRPCResult.LeoError -> handleMoneyScreenRPCErrorResponse()
                    }
                },
                handleException = {
                    val cachedHomeData: GetMoneyScreenDataRPC.Response? =
                        moneyScreenDataPersistor.getCachedMoneyScreenData()
                    if (cachedHomeData == null) {
                        _state.postValue(MoneyScreenState.FullScreenError(it))
                    } else {
                        _state.postValue(MoneyScreenState.Error(it, cachedHomeData))
                    }
                },
            )
        }
    }

    private fun handleMoneyScreenRPCErrorResponse() {
        // There is no known error codes in `GetMoneyScreenDataRPC`
        throw IllegalStateException("Some unexpected error occurred in GetMoneyScreenDataRPC.")
    }

    private fun handleMoneyScreenRPCSuccessResponse(
        context: Context,
        response: GetMoneyScreenDataRPC.Response,
    ) {
        moneyScreenDataPersistor.writeMoneyScreenLayoutFile(response)
        val accountList = mutableListOf<Account>()
        response.accounts.filter { it.accountState == AccountState.ACTIVE }
            .forEach { account ->
                accountList.add(
                    Account(
                        account.displayName.name,
                        account.accountId.toString(),
                        account.balance.currency.currencyCode,
                        account.isDefault,
                        account.balance.amount,
                    ),
                )
            }
        homeRepository.refreshSavedAccountList(context, accountList)
        val user = User(
            userId = response.user.userId.toString(),
            name = "${response.user.firstName} ${response.user.lastName ?: ""}",
            emailId = response.user.emailId?.value,
            isEmailVerified = response.user.isEmailVerified,
            imageUrl = response.user.profileImage?.getSpecificResolutionImageURL().toString(),
            phoneNumber = response.user.phoneNumber.value,
            primaryCurrency = response.user.primaryCurrency.currencyCode,
            country = Country(
                response.user.country.displayName.en,
                response.user.country.displayName.ny,
                response.user.country.code.code,
                response.user.country.phoneCode,
            ),
            countryCurrencies = response.user.supportedCurrencies.map {
                CountryCurrency(
                    it.currencyCode.currencyCode,
                    it.flag.getImage(context.resources.configuration.uiMode).toString(),
                    it.displayName.en,
                    it.displayName.ny,
                )
            },
            supportedCountries = response.user.supportedCountries.map {
                Country(
                    it.displayName.en,
                    it.displayName.ny,
                    it.code.code,
                    it.phoneCode,
                )
            },
            district = response.user.district,
        )
        UserSharedPreference.saveUser(user)
        _state.postValue(MoneyScreenState.Data(response))
    }

    fun onInlineErrorDismissed() {
        val cachedHomeData: GetMoneyScreenDataRPC.Response? =
            moneyScreenDataPersistor.getCachedMoneyScreenData()
        if (cachedHomeData != null) {
            _state.postValue(MoneyScreenState.Data(cachedHomeData))
        }
    }

    fun onErrorDismissed(data: GetMoneyScreenDataRPC.Response?, context: Context) {
        if (data != null) {
            handleMoneyScreenRPCSuccessResponse(context, data)
        } else {
            _state.postValue(MoneyScreenState.AcceptInput)
        }
    }

    fun onDialogDismissed() {
        _state.postValue(MoneyScreenState.AcceptInput)
    }

    fun onAccountDropdownUpdate(
        context: Context,
        accountId: UUID,
        isGetMoneyScreenDataRPCSuccessful: Boolean,
    ) {
        if (isGetMoneyScreenDataRPCSuccessful) {
            SelectedAccountHelper.setSelectedAccountId(
                context,
                accountId,
            )
        }
    }

    fun onPullToRefreshAction(context: Context) {
        _state.postValue(MoneyScreenState.InlineLoading)
        getMoneyScreenData(context)
    }

    fun updatePushToken(context: Context) {
        PushTokenHelper.init(context) {
            /** If the current token and the stored token are different, then we should update the
             * token on server, else if the current token and stored token are the same, we should
             * check if last time we updated the token on the server, and the current time is more
             * than [PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS] If it is more, then we should
             * Update the token on the server; otherwise, there is no need to update it on the
             * server.
             */
            if (PushTokenHelper.getPushToken(context) != it || Duration.between(
                    Instant.ofEpochMilli(PushTokenHelper.getPushTokenUpdateTime(context)),
                    Instant.now(),
                ).toDays() >= PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS
            ) {
                sendTokenToServer(context, it)
            } else {
                return@init
            }
        }
    }

    private fun sendTokenToServer(context: Context, token: String) {
        PushTokenHelper.updateToken(context, token)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        moneyRepository.updatePushToken(
                            PushToken(
                                token,
                                App.BCN,
                                FrontEndPlatform.ANDROID,
                            ),
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            PushTokenHelper.setPushTokenUpdateTime(context)
                            Timber.tag(TAG).i("Push token registration successful")
                        }

                        is LeoRPCResult.LeoError -> {
                            Timber.tag(TAG).e("Push token update failed")
                        }
                    }
                },
                handleException = {
                    Timber.tag(TAG).e("Push token update failed")
                },
            )
        }
    }
}

private const val TAG = "MoneyScreenVM"

sealed class MoneyScreenState {
    object Loading : MoneyScreenState()
    object AcceptInput : MoneyScreenState()
    object Reset : MoneyScreenState()
    data class Data(val response: GetMoneyScreenDataRPC.Response) : MoneyScreenState()
    data class Error(val uiError: UIError, val data: GetMoneyScreenDataRPC.Response) :
        MoneyScreenState()

    data class FullScreenError(val uiError: UIError) : MoneyScreenState()

    object InlineLoading : MoneyScreenState()
}
