package com.resoluttech.core.home

import android.content.Context
import com.resoluttech.bcn.homeScreen.GetHomeDataRPC
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.core.utils.Account
import com.resoluttech.core.utils.AccountList
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.SelectedAccountHelper.Companion.ACCOUNT_LIST_KEY
import com.resoluttech.core.utils.SelectedAccountHelper.Companion.ACCOUNT_SHARED_PREFERENCES_FILE
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber

class HomeRepository {

    private val getHomeDataRPC: GetHomeDataRPC by KoinJavaComponent.inject(GetHomeDataRPC::class.java)
    private val homeDataPersistor = HomeDataPersistor()

    @Throws(LeoInvalidResponseException::class)
    suspend fun refreshGetHomeData(context: Context): LeoRPCResult<GetHomeDataRPC.Response, GetHomeDataRPC.Error> {
        val homeDataRpcResult = getHomeDataRPC.execute(GetHomeDataRPC.Request)
        if (homeDataRpcResult is LeoRPCResult.LeoResponse) {
            /*  We are comparing latest home data and cached home data. If both are different then
                we are writing the home data in file and shared preferences to make it in sync and
                avoid race conditions. If the response is null we are clearing the cache and saved
                preferences to avoid redundant data. check this link for possible race conditions
                - https://app.clubhouse.io/resolut-tech/story/853/improve-selected-account-helper-class#activity-1788
             */
            if (homeDataRpcResult.response != getCachedHomeData()) {
                cacheHomeLayout(homeDataRpcResult.response)
                val accountList = mutableListOf<Account>()
                homeDataRpcResult.response.accounts.filter { it.account.accountState == AccountState.ACTIVE }
                    .forEach { accountLayout ->
                        accountList.add(
                            Account(
                                accountLayout.account.displayName.name,
                                accountLayout.account.accountId.toString(),
                                accountLayout.account.balance.currency.currencyCode,
                                accountLayout.account.isDefault,
                                accountLayout.account.balance.amount,
                            ),
                        )
                    }
                refreshSavedAccountList(context, accountList)
                Timber.tag(TAG).i("Home data written to cache.")
            } else {
                Timber.tag(TAG).i("Cached home data is same from network data.")
            }
        } else {
            clearHomeDataCache()
            clearSavedAccountList()
        }
        return homeDataRpcResult
    }

    fun getCachedHomeData(): GetHomeDataRPC.Response? {
        return homeDataPersistor.getCachedHomeData()
    }

    fun clearAllCachedHomeData() {
        clearHomeDataCache()
        clearSavedAccountList()
    }

    private fun clearHomeDataCache() {
        homeDataPersistor.clearHomeDataCache()
    }

    private fun clearSavedAccountList() {
        SelectedAccountHelper.clearSavedAccountList()
    }

    private fun cacheHomeLayout(homeData: GetHomeDataRPC.Response) {
        homeDataPersistor.writeHomeLayoutFile(homeData)
    }

    /**
     * This method refreshes the account list in shared preferences when home screen is loading.
     *
     * @param context : Context is required to access the shared preferences.
     * @param accounts : List of accounts that needs to be stored.
     * */
    fun refreshSavedAccountList(context: Context, accounts: List<Account>) {
        val accountString =
            Json.encodeToString(
                AccountList.serializer(),
                AccountList(accounts.sortedByDescending(Account::isDefault)),
            )

        val sharedPref =
            context.getSharedPreferences(ACCOUNT_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            putString(ACCOUNT_LIST_KEY, accountString)
            apply()
        }
    }
}

private const val TAG = "HomeRepository"
