package com.resoluttech.core.home

import android.content.Context
import android.os.Build
import android.os.FileObserver
import com.resoluttech.bcn.homeScreen.GetHomeDataRPC
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.io.File
import java.time.Instant

class HomeDataPersistor {

    private val context: Context by <PERSON>inJavaComponent.inject(Context::class.java)
    private var homeDataFile = getHomeDataFile()
    private val json: Json by KoinJavaComponent.inject(Json::class.java)
    private var fileObserver: FileObserver = getFileObserver(homeDataFile)

    fun writeHomeLayoutFile(homeData: GetHomeDataRPC.Response) {
        validateHomeDataObserver()
        val jsonString = Json.encodeToString(homeData)
        homeDataFile.writeBytes(jsonString.toByteArray())
    }

    fun clearHomeDataCache() {
        context.applicationContext.deleteFile(HOME_DATA_FILE_NAME)
    }

    fun getCachedHomeData(): GetHomeDataRPC.Response? {
        validateHomeDataFile()
        return if (homeDataFile.exists()) {
            val homeLayoutString = homeDataFile.readText()
            deserializeHomeLayout(homeLayoutString)
        } else {
            Timber.tag(TAG).d("No cached HomeLayout file found")
            null
        }
    }

    /**
     * This method allow us to store whether user is an agent or not.
     *
     * @param isAgent : Indicates whether user is an agent or not.
     * */
    fun setUserIsAgent(isAgent: Boolean) {
        val sharedPref =
            context.getSharedPreferences(USER_DETAILS_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            putString(USER_ISAGENT_KEY, "$isAgent")
            apply()
        }
    }

    /**
     * This method returns true if the user is an agent, returns false if the user is not an agent,
     * and returns null if the value is never set.
     * */
    fun checkIfUserIsAgent(): Boolean? {
        return context
            .getSharedPreferences(USER_DETAILS_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
            .getString(
                USER_ISAGENT_KEY,
                "false",
            )?.let(String::toBoolean)
    }

    /**
     * This method will clear the user isAgent cache.
     * */
    fun clearUserIsAgentCache() {
        val sharedPref =
            context.getSharedPreferences(USER_DETAILS_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            remove(USER_ISAGENT_KEY)
            apply()
        }
    }

    @Suppress("DEPRECATION")
    private fun getFileObserver(file: File): FileObserver {
        val observer = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            object : FileObserver(file, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedHomeData()
                }
            }
        } else {
            object : FileObserver(file.path, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedHomeData()
                }
            }
        }
        observer.startWatching()
        return observer
    }

    private fun validateHomeDataObserver() {
        if (!homeDataFile.exists()) {
            homeDataFile = getHomeDataFile()
            fileObserver = getFileObserver(homeDataFile)
        }
    }

    private fun getHomeDataFile(): File {
        val file = File(context.applicationContext.filesDir, HOME_DATA_FILE_NAME)
        file.createNewFile()
        return file
    }

    private fun validateHomeDataFile() {
        if (getHomeDataAgeMillis() > HOME_DATA_CACHE_VALIDITY_MILLIS) {
            Timber.tag(TAG).w(
                message = """Cached home data is older than
                | $HOME_DATA_CACHE_VALIDITY_MILLIS milli seconds, it will be cleared.
                """.trimMargin(),
            )
            clearHomeDataCache()
        } else {
            Timber.tag(TAG).d("Cached home data is valid.")
        }
    }

    private fun getHomeDataAgeMillis(): Long {
        return Instant.now().toEpochMilli() - homeDataFile.lastModified()
    }

    private fun deserializeHomeLayout(homeDataString: String): GetHomeDataRPC.Response? {
        return try {
            val retrievedValue: GetHomeDataRPC.Response = json.decodeFromString<GetHomeDataRPC.Response>(homeDataString)
            Timber.tag(TAG).d("Home layout Value updated")
            retrievedValue
        } catch (serializationException: SerializationException) {
            Timber.tag(TAG).d("Unable to serialize the object to HomeLayout")
            null
        }
    }
}

private const val TAG = "HomeDataPersistor"
private const val HOME_DATA_FILE_NAME = "homeData.json"
private const val USER_DETAILS_SHARED_PREFERENCES_FILE = "USER_DETAILS_SHARED_PREFERENCES_FILE"
private const val USER_ISAGENT_KEY = "USER_ISAGENT_KEY"
private const val HOME_DATA_CACHE_VALIDITY_MILLIS = 2592000000 * 3 // 3 months.
