package com.resoluttech.core.home

import android.content.Context
import android.os.Build
import android.os.FileObserver
import com.resoluttech.bcn.homeScreen.GetMoneyScreenDataRPC
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.io.File
import java.time.Instant

class MoneyScreenDataPersistor {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private var moneyScreenDataFile = getMoneyScreenDataFile()
    private val json: Json by KoinJavaComponent.inject(Json::class.java)
    private var fileObserver: FileObserver = getFileObserver(moneyScreenDataFile)

    fun writeMoneyScreenLayoutFile(moneyScreenData: GetMoneyScreenDataRPC.Response) {
        validateMoneyScreenDataObserver()
        val jsonString = Json.encodeToString(moneyScreenData)
        moneyScreenDataFile.writeBytes(jsonString.toByteArray())
    }

    fun clearMoneyScreenDataCache() {
        context.applicationContext.deleteFile(MONEY_SCREEN_DATA_FILE_NAME)
    }

    fun getCachedMoneyScreenData(): GetMoneyScreenDataRPC.Response? {
        validateMoneyScreenDataFile()
        return if (moneyScreenDataFile.exists()) {
            val moneyScreenLayoutString = moneyScreenDataFile.readText()
            deserializeMoneyScreenLayout(moneyScreenLayoutString)
        } else {
            Timber.tag(TAG).d("No cached MoneyLayout file found")
            null
        }
    }

    @Suppress("DEPRECATION")
    private fun getFileObserver(file: File): FileObserver {
        val observer = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            object : FileObserver(file, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedMoneyScreenData()
                }
            }
        } else {
            object : FileObserver(file.path, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedMoneyScreenData()
                }
            }
        }
        observer.startWatching()
        return observer
    }

    private fun validateMoneyScreenDataObserver() {
        if (!moneyScreenDataFile.exists()) {
            moneyScreenDataFile = getMoneyScreenDataFile()
            fileObserver = getFileObserver(moneyScreenDataFile)
        }
    }

    private fun getMoneyScreenDataFile(): File {
        val file = File(context.applicationContext.filesDir, MONEY_SCREEN_DATA_FILE_NAME)
        file.createNewFile()
        return file
    }

    private fun validateMoneyScreenDataFile() {
        if (getMoneyScreenDataAgeMillis() > MONEY_SCREEN_DATA_CACHE_VALIDITY_MILLIS) {
            Timber.tag(TAG).w(
                message = """Cached money data is older than
                | $MONEY_SCREEN_DATA_CACHE_VALIDITY_MILLIS milli seconds, it will be cleared.
                """.trimMargin(),
            )
            clearMoneyScreenDataCache()
        } else {
            Timber.tag(TAG).d("Cached money data is valid.")
        }
    }

    private fun getMoneyScreenDataAgeMillis(): Long {
        return Instant.now().toEpochMilli() - moneyScreenDataFile.lastModified()
    }

    private fun deserializeMoneyScreenLayout(moneyScreenDataString: String): GetMoneyScreenDataRPC.Response? {
        return try {
            val retrievedValue: GetMoneyScreenDataRPC.Response = json.decodeFromString<GetMoneyScreenDataRPC.Response>(moneyScreenDataString)
            Timber.tag(TAG).d("Money screen layout Value updated")
            retrievedValue
        } catch (serializationException: SerializationException) {
            Timber.tag(TAG).d("Unable to serialize the object to MoneyLayout")
            clearMoneyScreenDataCache()
            null
        } catch (leoInvalidResponse: LeoInvalidResponseException) {
            Timber.tag(TAG).d("Unable to serialize the object to MoneyLayout")
            clearMoneyScreenDataCache()
            null
        }
    }
}

private const val TAG = "MoneyScreenDataPersistor"
private const val MONEY_SCREEN_DATA_FILE_NAME = "moneyScreenData.json"
private const val MONEY_SCREEN_DATA_CACHE_VALIDITY_MILLIS = 2592000000 * 3 // 3 months.
