package com.resoluttech.core.home

import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.text.HtmlCompat
import androidx.navigation.NavController
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.homeScreen.InAppNotification
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.TYPE_ITEM
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getImage
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.toUri
import java.time.Instant

class NotificationViewHolder(private val view: View, private val controller: NavController) :
    RecyclerView.ViewHolder(view) {

    private val transactionTypeIV: ImageView
    private val titleTV: TextView
    private val messageTV: TextView
    private val timestampTV: TextView
    private val nextArrowIV: ImageView
    private val root: View

    init {
        with(view) {
            transactionTypeIV = findViewById(R.id.transaction_type_iv)
            titleTV = findViewById(R.id.title_tv)
            messageTV = findViewById(R.id.message_tv)
            timestampTV = findViewById(R.id.timestamp_tv)
            nextArrowIV = findViewById(R.id.next_arrow_iv)
            root = findViewById(R.id.root)
        }
    }

    fun bind(notification: InAppNotification) {
        transactionTypeIV.loadImage(
            notification.imageURL.getImage(view.context.resources.configuration.uiMode).toUri(),
        )
        titleTV.text = HtmlCompat.fromHtml(
            notification.title,
            HtmlCompat.FROM_HTML_MODE_LEGACY,
        )
        messageTV.text = HtmlCompat.fromHtml(
            notification.message,
            HtmlCompat.FROM_HTML_MODE_LEGACY,
        )
        timestampTV.text = getTimeStamp(notification.eventOccurredAt)
        if (notification.payload != null) {
            nextArrowIV.visibility = View.VISIBLE
            val notificationDestinationDeeplink =
                getAppNotificationDestination(
                    (
                        notification.payload
                            ?: throw IllegalStateException("Payload cannot be null")
                        ).payload,
                )
            if (notificationDestinationDeeplink != null) {
                nextArrowIV.visibility = View.VISIBLE
            } else {
                nextArrowIV.visibility = View.GONE
            }
            root.setOnClickListener {
                if (notificationDestinationDeeplink != null) {
                    controller.navigate(Uri.parse(notificationDestinationDeeplink))
                }
            }
        } else {
            nextArrowIV.visibility = View.GONE
        }
    }

    private fun getTimeStamp(eventOccurredAt: Instant): String {
        return eventOccurredAt.getFormattedDateTime(
            view.context,
            dateTimeType = DateTimeType.HISTORY,
        )
    }
}

class NotificationAdapter(
    private val notifications: MutableList<InAppNotification>,
    private val controller: NavController,
) : RecyclerView.Adapter<NotificationViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                val view =
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_notification, parent, false)
                NotificationViewHolder(view, controller)
            }

            else -> {
                throw IllegalStateException("Unknown View Type")
            }
        }
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        holder.bind(notifications[position])
    }

    override fun getItemCount(): Int = notifications.size

    override fun getItemViewType(position: Int): Int {
        return TYPE_ITEM
    }

    /**
     * This method uses DiffUtil, a utility class that calculates the difference between two lists
     * and outputs a list of update operations that converts the first list into the second one.
     */
    fun updateNotificationList(notifications: List<InAppNotification>) {
        val notificationDiffUtil = NotificationDiffUtilChecker(this.notifications, notifications)
        val result = DiffUtil.calculateDiff(notificationDiffUtil)
        this.notifications.clear()
        this.notifications.addAll(notifications)
        result.dispatchUpdatesTo(this)
    }
}

class NotificationDiffUtilChecker(
    private val oldList: List<InAppNotification>,
    private val newList: List<InAppNotification>,
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int = oldList.size

    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}
