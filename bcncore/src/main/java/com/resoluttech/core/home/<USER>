package com.resoluttech.core.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.resoluttech.bcn.homeScreen.InAppNotification
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.statements.TYPE_ITEM
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.DividerItemDecorator

class NotificationsFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private lateinit var notificationsRV: RecyclerView
    private lateinit var noNotificationTV: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var notificationAdapter: NotificationAdapter
    private val notificationsVM: NotificationsVM =
        ViewModelProvider.NewInstanceFactory().create(NotificationsVM::class.java)
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_notifications, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupRecyclerView()
        setSwipeToRefreshLayout()
        setDefaultToolbar(getString(R.string.notificationScreenTitle))
        networkListenerCallback = this
        notificationsVM.currentState.observe(viewLifecycleOwner, ::reactToState)
        notificationsVM.fetchNotifications(requireContext())
    }

    private fun reactToState(state: NotificationScreenState) {
        when (state) {
            is NotificationScreenState.Data -> {
                handleDataState(state.notifications)
            }

            is NotificationScreenState.Error -> {
                handleErrorState(state.uiError)
            }

            is NotificationScreenState.Loading -> {
                handleLoadingState()
            }

            is NotificationScreenState.NoData -> {
                handleNoDataState()
            }

            is NotificationScreenState.InlineLoading -> {
                handleInlineLoadingState()
            }
        }
    }

    private fun showViewState(view: View) {
        when (view) {
            progressBar -> {
                noNotificationTV.visibility = View.GONE
                notificationsRV.visibility = View.GONE
                progressBar.visibility = View.VISIBLE
            }

            noNotificationTV -> {
                noNotificationTV.visibility = View.VISIBLE
                notificationsRV.visibility = View.GONE
                progressBar.visibility = View.GONE
            }

            notificationsRV -> {
                noNotificationTV.visibility = View.GONE
                notificationsRV.visibility = View.VISIBLE
                progressBar.visibility = View.GONE
            }
        }
    }

    private fun setSwipeToRefreshLayout() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            notificationsVM.onPullToRefreshAction(requireContext())
        }
    }

    private fun handleSwipeRefreshLoading(shouldShowLoading: Boolean) {
        if (shouldShowLoading && !swipeToRefreshLayout.isRefreshing) {
            swipeToRefreshLayout.isRefreshing = true
        } else if (!shouldShowLoading && swipeToRefreshLayout.isRefreshing) {
            swipeToRefreshLayout.isRefreshing = false
        }
    }

    private fun handleErrorState(uiError: UIError) {
        handleSwipeRefreshLoading(false)
        progressBar.visibility = View.GONE
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    notificationsVM::onErrorDismissed,
                )
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.NOTIFICATION_ERROR_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleInlineLoadingState() {
        handleSwipeRefreshLoading(true)
        progressBar.visibility = View.GONE
    }

    private fun handleNoDataState() {
        handleSwipeRefreshLoading(false)
        showViewState(noNotificationTV)
    }

    private fun handleLoadingState() {
        handleSwipeRefreshLoading(false)
        showViewState(progressBar)
    }

    private fun handleDataState(notifications: List<InAppNotification>) {
        showViewState(notificationsRV)
        handleSwipeRefreshLoading(false)
        notificationAdapter.updateNotificationList(notifications)
    }

    private fun setupRecyclerView() {
        notificationsRV.adapter = notificationAdapter
        val dividerItemDecoration = DividerItemDecorator(
            ContextCompat.getDrawable(requireContext(), R.drawable.divider),
            DividerItemDecorator.LIST_MARGIN_0,
            listOf(TYPE_ITEM),
        )
        notificationsRV.addItemDecoration(dividerItemDecoration)
    }

    private fun initViews(view: View) {
        with(view) {
            notificationsRV = findViewById(R.id.notifications_rv)
            noNotificationTV = findViewById(R.id.no_notification_tv)
            progressBar = findViewById(R.id.progress_bar)
            notificationAdapter = NotificationAdapter(mutableListOf(), findNavController())
            swipeToRefreshLayout = findViewById(R.id.swipe_to_refresh)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        if (dialogId == DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
            leoServerExceptionHandler(findNavController())
        } else {
            notificationsVM.onErrorDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Non-negative dialog has negative action")
    }

    override fun onNetworkAvailable() {
        notificationsVM.fetchNotifications(requireContext())
    }
}
