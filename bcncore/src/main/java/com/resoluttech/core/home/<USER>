package com.resoluttech.core.home

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.homeScreen.GetInAppNotificationsRPC
import com.resoluttech.bcn.homeScreen.InAppNotification
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class NotificationsVM : ViewModel() {

    private val _currentState: MutableLiveData<NotificationScreenState> = MutableLiveData()
    val currentState: LiveData<NotificationScreenState> = _currentState

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val notificationRepository = NotificationsRepository()

    fun fetchNotifications(context: Context) {
        _currentState.postValue(NotificationScreenState.Loading)
        getNotifications(context)
    }

    private fun getNotifications(context: Context) {
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = notificationRepository.getNotifications()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSuccessfulNotification(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            // There is no known error in this RPC
                        }
                    }
                },
                handleException = {
                    _currentState.postValue(NotificationScreenState.Error(it))
                },
            )
        }
    }

    private fun handleSuccessfulNotification(response: GetInAppNotificationsRPC.Response) {
        if (response.notifications.isEmpty()) {
            _currentState.postValue(NotificationScreenState.NoData)
        } else {
            _currentState.postValue(NotificationScreenState.Data(response.notifications))
        }
    }

    override fun onCleared() {
        super.onCleared()
        vmIoScope.cancel()
    }

    fun onPullToRefreshAction(context: Context) {
        _currentState.postValue(NotificationScreenState.InlineLoading)
        getNotifications(context)
    }

    fun onErrorDismissed() {
        _currentState.postValue(NotificationScreenState.NoData)
    }
}

sealed class NotificationScreenState {
    object Loading : NotificationScreenState()
    object NoData : NotificationScreenState()
    object InlineLoading : NotificationScreenState()
    data class Data(val notifications: List<InAppNotification>) : NotificationScreenState()
    data class Error(val uiError: UIError) : NotificationScreenState()
}
