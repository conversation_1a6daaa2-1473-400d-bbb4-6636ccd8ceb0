package com.resoluttech.core.home

import android.content.Context
import android.widget.Toast
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.homeScreen.AccountLayout
import com.resoluttech.bcn.homeScreen.GetHomeDataRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.HomeRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.Country
import com.resoluttech.core.utils.CountryCurrency
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.User
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getImage
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber

class HomeScreenVM : ViewModel() {

    private var refreshHomeDataJob: Job? = null
    private val homeRepository: HomeRepository by lazy(::HomeRepository)
    private var isShowingBalance: Boolean = false
    private var currentState: MutableLiveData<HomeScreenState> =
        MutableLiveData(HomeScreenState.Loading)
    private var vMScopeOnIO = viewModelScope + Dispatchers.IO

    fun getHomeScreenData(context: Context) {
        vMScopeOnIO.launch {
            val cachedHomeData: GetHomeDataRPC.Response? = homeRepository.getCachedHomeData()
            if (cachedHomeData != null) {
                postHomeData(cachedHomeData)
            }
            refreshHomeData(context, cachedHomeData)
        }
    }

    fun getState(): LiveData<HomeScreenState> = currentState

    fun triggerPullToRefresh(context: Context) {
        if (refreshHomeDataJob != null) {
            return
        }
        if (currentState.value !is HomeScreenState.Data) {
            // PTR must only be allowed from a `Data` state.
            return
        }
        val currentData = (currentState.value as HomeScreenState.Data).data
        currentState.postValue(
            HomeScreenState.InlineLoadingWithHomeData(
                currentData,
            ),
        )
        refreshHomeDataJob = vMScopeOnIO.launch {
            refreshHomeData(context, currentData.homeDataResponse)
            refreshHomeDataJob = null
        }
    }

    fun onTapBalance() {
        if (currentState.value !is HomeScreenState.Data) {
            // Balance tapping must only be allowed from a `Data` state.
            return
        }
        val currentData = (currentState.value as HomeScreenState.Data).data.homeDataResponse
        isShowingBalance = !isShowingBalance
        postHomeData(currentData)
    }

    fun onAccountChange(accountIndex: Int, context: Context) {
        if (currentState.value !is HomeScreenState.Data) {
            // Balance tapping must only be allowed from a `Data` state.
            Timber.tag(TAG).d("Avoiding account change because state doesn't allow it.")
            return
        }
        val currentData = (currentState.value as HomeScreenState.Data).data.homeDataResponse
        if (accountIndex <= currentData.accounts.size - 1) {
            val selectedAccount = if (SelectedAccountHelper.getSelectedAccount() == null) {
                getSelectedAccountFromDefaultAccount(currentData)
            } else {
                currentData.accounts[accountIndex]
            }
            Timber.tag(TAG).i("Account Selected : ${selectedAccount.account.accountId}")
            SelectedAccountHelper.setSelectedAccountId(
                context,
                selectedAccount.account.accountId,
            )

            val user = User(
                userId = currentData.user.userId.toString(),
                name = "${currentData.user.firstName} ${currentData.user.lastName ?: ""}",
                emailId = currentData.user.emailId?.value,
                isEmailVerified = currentData.user.isEmailVerified,
                imageUrl = currentData.user.profileImage?.getSpecificResolutionImageURL()
                    .toString(),
                phoneNumber = currentData.user.phoneNumber.value,
                primaryCurrency = currentData.user.primaryCurrency.currencyCode,
                country = Country(
                    currentData.user.country.displayName.en,
                    currentData.user.country.displayName.ny,
                    currentData.user.country.code.code,
                    currentData.user.country.phoneCode,
                ),
                countryCurrencies = currentData.user.supportedCurrencies.map {
                    CountryCurrency(
                        it.currencyCode.currencyCode,
                        it.flag.getImage(context.resources.configuration.uiMode).toString(),
                        it.displayName.en,
                        it.displayName.ny,
                    )
                },
                supportedCountries = currentData.user.supportedCountries.map {
                    Country(
                        it.displayName.en,
                        it.displayName.ny,
                        it.code.code,
                        it.phoneCode,
                    )
                },
                district = currentData.user.district,
            )

            UserSharedPreference.saveUser(user)
            isShowingBalance = false
            postHomeData(currentData)
        } else {
            throw IllegalStateException("Selected account doesn't exist")
        }
    }

    private fun getSelectedAccountFromDefaultAccount(currentData: GetHomeDataRPC.Response): AccountLayout {
        val defaultAccount =
            currentData.user.defaultAccounts.first { account ->
                account.currency == currentData.user.primaryCurrency
            }

        return currentData.accounts.first {
            it.account.accountId == defaultAccount.accountId
        }
    }

    fun onCreateAccountTapped(navController: NavController) {
        navController.navigate(R.id.manage_accounts_nav)
    }

    fun retryGetHomeDataFromFullScreenError(context: Context) {
        if (currentState.value !is HomeScreenState.FullScreenError) {
            return
        }
        currentState.postValue(HomeScreenState.Loading)
        vMScopeOnIO.launch {
            refreshHomeData(context, null)
        }
    }

    fun didTapNotificationsIcon(context: Context) {
        // TODO: Need to implement actual notification feature here
        Toast.makeText(context, "Notification Tapped", Toast.LENGTH_SHORT).show()
    }

    fun inlineErrorDismissed(state: HomeScreenState.InlineErrorWithHomeData) {
        currentState.postValue(
            HomeScreenState.Data(
                state.data,
            ),
        )
    }

    private suspend fun refreshHomeData(
        context: Context,
        currentHomeData: GetHomeDataRPC.Response?,
    ) {
        var selectedAccountIndex = 0
        executeRPC(
            context,
            rpcBlock = {
                val homeDataResponse: GetHomeDataRPC.Response?
                when (val result = homeRepository.refreshGetHomeData(context)) {
                    is LeoRPCResult.LeoResponse -> {
                        homeDataResponse = result.response
                        selectedAccountIndex =
                            SelectedAccountHelper.getSelectedAccountIndex(result.response.accounts)
                        postHomeData(homeDataResponse)
                    }
                    is LeoRPCResult.LeoError -> {
                        val message =
                            HomeRPCExceptionHandler.getCommonHomeRPCErrorMessage(
                                context,
                                result.error,
                            )
                        handleHomeRPCErrorCase(
                            message,
                            currentHomeData?.let {
                                HomeData(
                                    isShowingBalance,
                                    selectedAccountIndex,
                                    it,
                                )
                            },
                        )
                    }
                }
            },
            handleException = {
                handleRPCException(
                    it,
                    currentHomeData?.let { response ->
                        HomeData(
                            isShowingBalance,
                            selectedAccountIndex,
                            response,
                        )
                    },
                )
            },
        )
    }

    private fun handleHomeRPCErrorCase(
        uiError: UIError,
        currentHomeData: HomeData?,
    ) {
        currentState.postValue(
            if (currentHomeData == null) {
                HomeScreenState.FullScreenError(
                    uiError.errorMessage,
                )
            } else {
                HomeScreenState.InlineErrorWithHomeData(
                    currentHomeData,
                    uiError,
                )
            },
        )
    }

    private fun handleRPCException(
        uiError: UIError,
        currentHomeData: HomeData?,
    ) {
        currentState.postValue(
            if (currentHomeData == null) {
                HomeScreenState.FullScreenError(
                    uiError.errorMessage,
                )
            } else {
                HomeScreenState.InlineErrorWithHomeData(
                    currentHomeData,
                    uiError,
                )
            },
        )
    }

    /*Sets the current selected account from Shared Preferences,
     * and posts `Data` as the current state, picking up the current value of balance visibility
     * state.*/
    private fun postHomeData(response: GetHomeDataRPC.Response) {
        val selectedIndex = SelectedAccountHelper.getSelectedAccountIndex(response.accounts)
        currentState.postValue(
            HomeScreenState.Data(
                HomeData(
                    isShowingBalance,
                    selectedIndex,
                    response,
                ),
            ),
        )
    }

    fun onViewAllClicked() {
        /***
         * As of now we are not using this feature and hence the navigation action has been removed
         * from the Project in the following PR: 'https://github.com/Resolut-Tech/Android/pull/1127'
         */
    }

    // TODO: Pending: States related to app versions. Not finalised how this feature will work, yet. See: https://app.clubhouse.io/resolut-tech/story/125/remote-endpoint-for-version-checking-on-home-screen
}

sealed class HomeScreenState {
    /*Default UI being shown to the user.*/
    data class Data(val data: HomeData) : HomeScreenState()

    /*An error shown inline when the user is seeing home_nav_graph data. eg: Network errors.*/
    data class InlineErrorWithHomeData(val data: HomeData, val uiError: UIError) : HomeScreenState()

    /*Loading when there is no cached home_nav_graph data.*/
    object Loading : HomeScreenState()

    /*Basically only used when PTR occurs.*/
    data class InlineLoadingWithHomeData(val data: HomeData) : HomeScreenState()

    /*Useful when an error occurs and there was nothing on the home_nav_graph screen previously.
     * eg: When an error occurs and there's no cached home_nav_graph data.*/
    data class FullScreenError(val error: String) : HomeScreenState()
}

data class HomeData(
    val shouldShowBalance: Boolean,
    val selectedAccountIndex: Int,
    val homeDataResponse: GetHomeDataRPC.Response,
)

private const val TAG = "HomeScreenVM"
