package com.resoluttech.core.home

import android.content.Intent
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.toColorInt
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcn.assets.Background
import com.resoluttech.bcn.assets.CornerRadius
import com.resoluttech.bcn.assets.LayoutSection
import com.resoluttech.bcn.assets.ThemedColor
import com.resoluttech.bcn.homeScreen.HomeLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_ACCOUNT_ID
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_USER_ID
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM.Companion.KEY_USER_NAME
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.CarouselLayoutHelper
import com.resoluttech.core.uicomponents.ConstraintLayoutHelper
import com.resoluttech.core.uicomponents.ImageButtonLayoutHelper
import com.resoluttech.core.utils.DialogCodes.Companion.HOME_FRAGMENT_DIALOG_CODE
import com.resoluttech.core.utils.ItemActionHandlerUseCase
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.UnitConverter.Companion.getPercentValue
import com.resoluttech.core.utils.formattedAmountValue
import com.resoluttech.core.utils.getColor
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.isUIModeNight
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.setupLayoutBackground
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseFragment
import org.koin.android.ext.android.inject
import timber.log.Timber

class HomeFragment : BaseFragment(), AlertDialog.ActionListener {
    private val imageButtonLayoutHelper = ImageButtonLayoutHelper()
    private val carouselLayoutHelper = CarouselLayoutHelper()
    private val constraintLayoutHelper = ConstraintLayoutHelper()
    private val homeScreenVM: HomeScreenVM by navGraphViewModels(R.id.home_nav)
    private val homeDataPersistor: HomeDataPersistor by inject()
    private var inlineErrorSnackBar: Snackbar? = null
    private var homeData: HomeData? = null

    private lateinit var layoutErrorStateView: View
    private lateinit var layoutDataStateView: View
    private lateinit var layoutLoadingStateView: View
    private lateinit var swipeToRefreshView: SwipeRefreshLayout
    private lateinit var barcodeIconIV: ImageView
    private lateinit var notificationIconIV: ImageView
    private lateinit var nationalIdExpiredLayout: ConstraintLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        homeScreenVM.getHomeScreenData(requireContext())
        val rootView = inflater.inflate(R.layout.fragment_home, container, false)
        setStateViews(rootView)
        setToolbar()
        setupBalanceVisibilityButton(rootView)
        setupBackPressed(requireActivity()::finish)
        return rootView
    }

    private fun checkForAppLinkArguments() {
        val uri = requireActivity().intent.data
        if (uri != null && isLaunchedFromHistory()) {
            val username = getQueryParam(uri, KEY_USER_NAME)
            val userId = getQueryParam(uri, KEY_USER_ID)
            val accountId = getQueryParam(uri, KEY_ACCOUNT_ID)
            findNavController().navigate("resoluttechbcn://?username=$username&userId=$userId&accountId=$accountId&shouldNavigate=true".toUri())
        }
        /*
            The intent data should be set to null so when user press on back button then it comes on
            home screen and do not enter the `if` and again navigates user to money transfer screen.
         */
        requireActivity().intent.data = null
    }

    private fun isLaunchedFromHistory(): Boolean {
        /*
            The check `Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY` is added so that once user is entered
            to the app using App Link then, minimizes the app and relaunch from recent apps tabs then
            we don't want to show the same content to user.
            Thanks: https://stackoverflow.com/a/********/********
         */
        return (requireActivity().intent.flags and Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY) != Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY
    }

    private fun getQueryParam(uri: Uri, paramKey: String): String {
        return try {
            uri.getQueryParameter(paramKey) ?: requireContext().getEmptyString()
        } catch (e: IllegalArgumentException) {
            Timber.e(e)
            requireContext().getEmptyString()
        }
    }

    override fun onResume() {
        super.onResume()
        checkForAppLinkArguments()
    }

    override fun onStop() {
        super.onStop()
        homeData = null
    }

    private fun setStateViews(rootView: View) {
        layoutDataStateView = rootView.findViewById(R.id.home_layout_data)
        layoutLoadingStateView = rootView.findViewById(R.id.home_layout_loading)
        layoutErrorStateView = rootView.findViewById(R.id.home_layout_error)
        nationalIdExpiredLayout = rootView.findViewById(R.id.national_id_expired_layout)

        setupSwipeToRefresh(rootView)
    }

    private fun setupSwipeToRefresh(rootView: View) {
        swipeToRefreshView = rootView.findViewById(R.id.home_layout_swipe_to_refresh)
        // Thanks: https://stackoverflow.com/a/5271452/4400607
        swipeToRefreshView.setColorSchemeResources(R.color.colorPrimary)
        if (isUIModeNight(resources.configuration.uiMode)) {
            swipeToRefreshView.setProgressBackgroundColorSchemeResource(R.color.pullToRefreshBgColor)
        } else {
            // The right thing is done by default
        }
        swipeToRefreshView.setOnRefreshListener {
            homeScreenVM.triggerPullToRefresh(requireContext())
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        homeScreenVM.getState().observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(homeScreenState: HomeScreenState) {
        when (homeScreenState) {
            is HomeScreenState.Data -> {
                handleHomeScreenData(homeScreenState)
            }
            is HomeScreenState.Loading -> {
                handleHomeScreenLoading()
            }
            is HomeScreenState.FullScreenError -> {
                handleHomeScreenError(homeScreenState.error)
            }
            is HomeScreenState.InlineLoadingWithHomeData -> {
                handleInlineLoading(homeScreenState)
            }
            is HomeScreenState.InlineErrorWithHomeData -> {
                handleInlineError(homeScreenState)
            }
        }
    }

    private fun showViewForState(stateView: View) {
        when (stateView) {
            layoutLoadingStateView -> {
                layoutLoadingStateView.visibility = View.VISIBLE
                layoutDataStateView.visibility = View.GONE
                layoutErrorStateView.visibility = View.GONE
            }
            layoutDataStateView -> {
                layoutLoadingStateView.visibility = View.GONE
                layoutDataStateView.visibility = View.VISIBLE
                layoutErrorStateView.visibility = View.GONE
            }
            layoutErrorStateView -> {
                layoutLoadingStateView.visibility = View.GONE
                layoutDataStateView.visibility = View.GONE
                layoutErrorStateView.visibility = View.VISIBLE
            }
        }
    }

    private fun handleSwipeRefreshLoading(shouldShowLoading: Boolean) {
        if (shouldShowLoading && !swipeToRefreshView.isRefreshing) {
            swipeToRefreshView.isRefreshing = true
        } else if (!shouldShowLoading && swipeToRefreshView.isRefreshing) {
            swipeToRefreshView.isRefreshing = false
        }
    }

    private fun handleInlineLoading(
        inLineLoadingState: HomeScreenState.InlineLoadingWithHomeData,
    ) {
        hideInlineErrorSnackBar()
        handleSwipeRefreshLoading(true)
        presentHomeScreenData(inLineLoadingState.data)
    }

    private fun handleInlineError(
        inLineErrorState: HomeScreenState.InlineErrorWithHomeData,
    ) {
        when (inLineErrorState.uiError.type) {
            ErrorType.DIALOG -> {
                showInlineErrorDialog(
                    inLineErrorState.uiError.errorTitle,
                    inLineErrorState.uiError.errorMessage,
                    inLineErrorState.uiError.errorCode ?: HOME_FRAGMENT_DIALOG_CODE,
                )
            }
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(inLineErrorState)
            ErrorType.BANNER -> handleNetworkLostState()
        }
        handleSwipeRefreshLoading(false)
        presentHomeScreenData(inLineErrorState.data)
    }

    private fun showInlineErrorDialog(errorTitle: String, errorMessage: String, errorDialog: Int) {
        showErrorDialog(
            title = errorTitle,
            message = errorMessage,
            dialogId = errorDialog,
        )
    }

    private fun handleHomeScreenData(
        homeDataState: HomeScreenState.Data,
    ) {
        hideInlineErrorSnackBar()
        handleSwipeRefreshLoading(false)
        homeDataPersistor.setUserIsAgent(homeDataState.data.homeDataResponse.user.isAgent)
        setupToolBarBarCodeIVVisibility()
        presentHomeScreenData(homeDataState.data)
    }

    private fun setupToolBarBarCodeIVVisibility() {
        if (homeDataPersistor.checkIfUserIsAgent() == true) {
            barcodeIconIV.visibility = View.GONE
        } else {
            barcodeIconIV.visibility = View.VISIBLE
        }
    }

    private fun handleHomeScreenLoading() {
        showViewForState(layoutLoadingStateView)
        hideInlineErrorSnackBar()
        handleSwipeRefreshLoading(false)
    }

    private fun handleHomeScreenError(error: String) {
        showViewForState(layoutErrorStateView)
        hideInlineErrorSnackBar()
        handleSwipeRefreshLoading(false)

        val retryTextView = requireActivity().findViewById<TextView>(R.id.retry_message)
        retryTextView.text = error

        val retryButton = requireActivity().findViewById<MaterialButton>(R.id.retry_button)
        retryButton.setOnClickListener {
            homeScreenVM.retryGetHomeDataFromFullScreenError(requireContext())
        }
    }

    private fun presentHomeScreenData(homeData: HomeData) {
        if (homeData != this.homeData) {
            showViewForState(layoutDataStateView)
            layoutDataStateView.setupLayoutBackground(
                homeData.homeDataResponse.accounts[homeData.selectedAccountIndex].homeLayout.backgroundImageURL,
                resources.configuration.uiMode,
            )
            setActions(homeData.homeDataResponse.accounts[homeData.selectedAccountIndex].homeLayout)
            handleBalanceVisibility(homeData)
            if (homeData.homeDataResponse.user.isNationalIdExpired) {
                nationalIdExpiredLayout.visibility = View.VISIBLE
            } else {
                nationalIdExpiredLayout.visibility = View.GONE
            }
            this.homeData = homeData
            Timber.tag(TAG).d("Homedata has changed.")
        } else {
            Timber.tag(TAG).d("Homedata has not been changed, hence views are retained.")
        }
    }

    private fun showInlineErrorSnackBar(
        inLineErrorState: HomeScreenState.InlineErrorWithHomeData,
    ) {
        inlineErrorSnackBar = Snackbar.make(
            requireView(),
            inLineErrorState.uiError.errorMessage,
            Snackbar.LENGTH_INDEFINITE,
        )
        inlineErrorSnackBar?.let {
            it.setAction(R.string.alertActionDismiss) {
                homeScreenVM.inlineErrorDismissed(inLineErrorState)
            }
            it.show()
        }
    }

    private fun hideInlineErrorSnackBar() {
        inlineErrorSnackBar?.dismiss()
        inlineErrorSnackBar = null
    }

    private fun setupBalanceVisibilityButton(rootLayout: View) {
        val balanceLayout =
            rootLayout.findViewById<ConstraintLayout>(R.id.secondary_balance_layout)

        balanceLayout.setOnClickListener {
            homeScreenVM.onTapBalance()
        }
    }

    private fun handleBalanceVisibility(homeData: HomeData) {
        val balanceVisibilityButton =
            requireView().findViewById<MaterialButton>(R.id.balance_visibility_button)
        val currencyTextView = requireView().findViewById<TextView>(R.id.currency)
        val balanceTextView = requireView().findViewById<TextView>(R.id.balance)

        if (homeData.shouldShowBalance) {
            balanceVisibilityButton.visibility = View.INVISIBLE
            currencyTextView.visibility = View.VISIBLE
            balanceTextView.visibility = View.VISIBLE
            currencyTextView.text =
                homeData.homeDataResponse.accounts[homeData.selectedAccountIndex].account.balance.currency.currencyCode
            balanceTextView.text = requireContext().formattedAmountValue(
                homeData.homeDataResponse.accounts[homeData.selectedAccountIndex].account.balance.amount,
            )
        } else {
            balanceVisibilityButton.visibility = View.VISIBLE
            currencyTextView.visibility = View.INVISIBLE
            balanceTextView.visibility = View.INVISIBLE
        }
    }

    private fun setActions(homeLayout: HomeLayout) {
        val bodyBlockContainer =
            requireView().findViewById<ConstraintLayout>(R.id.body_block_container)
        bodyBlockContainer.removeAllViews()
        val bodyBlockRows = constraintLayoutHelper.addConstrainedRows(
            requireContext(),
            homeLayout.sections.size,
            bodyBlockContainer,
            DEFAULT_LAYOUT_PADDING,
        )
        for ((index, section) in homeLayout.sections.withIndex()) {
            when (section) {
                is LayoutSection.SpaceSeparator -> {
                    constraintLayoutHelper.addSpacer(
                        requireContext(),
                        bodyBlockRows[index],
                    )
                }
                is LayoutSection.ButtonContainer -> {
                    handleBackground(section.background, bodyBlockRows[index])
                    setupImageButtonBlock(
                        bodyBlockRows[index],
                        section,
                    )
                }
                is LayoutSection.LineSeparator -> {
                    constraintLayoutHelper.addLineSpacer(
                        requireContext(),
                        bodyBlockRows[index],
                    )
                }
                is LayoutSection.Carousel -> {
                    handleBackground(section.background, bodyBlockRows[index])
                    carouselLayoutHelper.addImageCarouselLayout(
                        requireContext(),
                        bodyBlockRows[index],
                        section.items,
                        swipeToRefreshView,
                        findNavController(),
                        ItemActionHandlerUseCase.HOME,
                    )
                }
            }
        }
    }

    private fun handleBackground(
        background: Background?,
        backgroundContainer: ConstraintLayout,
    ) {
        background?.let {
            it.backgroundColor?.let { themedColor ->
                backgroundContainer.background = getBackground(
                    themedColor,
                    it.cornerRadius,
                )
            }
        }
    }

    private fun setupImageButtonBlock(
        parentContainer: ConstraintLayout,
        section: LayoutSection.ButtonContainer,
    ) {
        val imageBlockContainer = section.title?.localisedText(
            LocaleManager.getCurrentLocale(requireContext()),
        )?.let {
            val imageBlockRows = constraintLayoutHelper.addConstrainedRows(
                requireContext(),
                IMAGE_BUTTONS_BLOCK_ROW_COUNT_WITH_TITLE,
                parentContainer,
                NO_PADDING,
            )
            constraintLayoutHelper.addTextView(
                requireContext(),
                it,
                imageBlockRows[0],
            )
            if (!section.hiddenButtons.isNullOrEmpty()) {
                constraintLayoutHelper.addViewAll(
                    requireContext(),
                    imageBlockRows[0],
                    section,
                ) {
                    homeScreenVM.onViewAllClicked()
                }
            } else {
                Timber.tag(TAG)
                    .d("Hidden button section do not have any items.")
            }

            imageBlockRows[1]
        } ?: run {
            parentContainer
        }

        section.visibleButtons.let {
            val imageButtons =
                imageButtonLayoutHelper.getImageButtons(
                    requireContext(),
                    it,
                    findNavController(),
                    ItemActionHandlerUseCase.HOME,
                )
            val innerContainers = imageButtonLayoutHelper.addImageButtonsToRoot(
                requireContext(),
                imageButtons,
                imageBlockContainer,
            )
            imageButtonLayoutHelper.createButtonsChain(imageButtons, innerContainers)
        }
    }

    private fun getBackground(
        themedColor: ThemedColor,
        cornerRadius: CornerRadius?,
    ): GradientDrawable {
        val backgroundColor = themedColor.getColor(resources.configuration.uiMode).toColorInt()
        val gradientDrawable = GradientDrawable(
            GradientDrawable.Orientation.BOTTOM_TOP,
            intArrayOf(backgroundColor, backgroundColor),
        )

        @Suppress("DEPRECATION")
        val parentWidth = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            val displayMetrics = DisplayMetrics()
            requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)
            displayMetrics.widthPixels
        } else {
            requireActivity().windowManager.currentWindowMetrics.bounds.width()
        }

        if (cornerRadius != null) {
            val topCornerRadius: Float = cornerRadius.top.getPercentValue(parentWidth)
            val bottomCornerRadius: Float = cornerRadius.bottom.getPercentValue(parentWidth)

            gradientDrawable.cornerRadii = floatArrayOf(
                topCornerRadius,
                topCornerRadius,
                topCornerRadius,
                topCornerRadius,
                bottomCornerRadius,
                bottomCornerRadius,
                bottomCornerRadius,
                bottomCornerRadius,
            )
        }

        return gradientDrawable
    }

    private fun setToolbar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView = layoutInflater.inflate(R.layout.home_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            notificationIconIV =
                toolbarView.findViewById<ImageButton>(R.id.notifications_icon)
            notificationIconIV.setOnClickListener {
                onNotificationIconTap()
            }

            barcodeIconIV = toolbarView.findViewById<ImageButton>(R.id.barcode_icon)
            setupToolBarBarCodeIVVisibility()
            barcodeIconIV.setOnClickListener {
                onBarcodeIconTap()
            }
        }
    }

    private fun onNotificationIconTap() {
        homeScreenVM.didTapNotificationsIcon(requireContext())
    }

    private fun onBarcodeIconTap() {
        findNavController().navigate(R.id.peer_to_peer_transfer_nav)
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("Positive action occurred on alert dialog having id $dialogId.")
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }
}

private const val TAG = "HomeFragment"
private const val IMAGE_BUTTONS_BLOCK_ROW_COUNT_WITH_TITLE = 2
private const val DEFAULT_LAYOUT_PADDING = 10
private const val NO_PADDING = 0
