package com.resoluttech.core.uicomponents

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.core.utils.toPx

/**
 * Thanks: https://stackoverflow.com/questions/45960465/how-to-show-fixed-count-of-items-in-recyclerview
 */
class FixedHeightRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : RecyclerView(context, attrs, defStyleAttr) {

    init {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        val fixedHeightSpec = MeasureSpec.makeMeasureSpec(MAX_HEIGHT_IN_DP.toPx(resources), MeasureSpec.AT_MOST)
        super.onMeasure(widthSpec, fixedHeightSpec)
    }
}

private const val MAX_HEIGHT_IN_DP = 150
