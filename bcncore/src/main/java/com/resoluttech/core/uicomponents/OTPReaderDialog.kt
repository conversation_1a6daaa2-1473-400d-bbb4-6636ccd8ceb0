package com.resoluttech.core.uicomponents

import android.annotation.SuppressLint
import android.app.Activity
import android.app.PendingIntent
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.format.DateUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.graphics.drawable.toDrawable
import androidx.core.text.HtmlCompat
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseDialogFragment
import com.suryadigital.leo.libui.otptextfield.AutoOTPReadListener
import com.suryadigital.leo.libui.otptextfield.OTPConfiguration
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.libui.otptextfield.OTPTextField
import com.suryadigital.leo.libui.otptextfield.OTPTextFieldListener
import com.suryadigital.leo.libui.otptextfield.OtpReadType
import com.suryadigital.leo.libui.otptextfield.parseFirstNdigitOTPNumberInMessage
import timber.log.Timber
import java.time.Instant
import java.util.UUID
import `in`.aabhasjindal.otptextview.R as OTPTextViewResource

class OTPReaderDialog : BaseDialogFragment(), ResendOTPListener, AlertDialog.ActionListener {

    private lateinit var otpReaderProgressContainer: View
    private lateinit var response: OTPResponse
    private lateinit var otpView: OTPTextField
    private lateinit var titleTV: TextView
    private lateinit var resendTV: TextView
    private lateinit var cancelButton: Button
    private lateinit var otpValidTillTV: TextView
    private lateinit var otpLeftTV: TextView
    private lateinit var resendPB: ProgressBar
    private val handler = Handler(Looper.getMainLooper())
    private var resendTimeLeftRunnable: Runnable? = null

    /***
     * 'shouldDismissOTPReaderDialog' variable is set as false whenever a new instance of alert dialog is created
     * So when system calls onDestroy() (Theme or Font Config Change or to free up RAM memory) and user comes back to app the shouldDismissOTPReaderDialog is reset as true and dialog is dismissed.
     * The fragment calling this alert dialog will create a new instance again by restoring VM state and shouldDismissOTPReaderDialog would be set as false.
     */
    private var shouldDismissOTPReaderDialog: Boolean = true

    fun setArguments(shouldDismissOTPReaderDialog: Boolean) {
        this.shouldDismissOTPReaderDialog = shouldDismissOTPReaderDialog
    }

    interface OTPListener {
        fun onResendTapped(otpId: UUID?)
        fun onOTPReadCancelled()
        fun onOTPReadFailed(e: OTPFailureException)
        fun onSuccessfulOTPRead(otp: String, otpId: UUID?)
    }

    private val getOTP =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            it.apply {
                when (resultCode) {
                    Activity.RESULT_OK -> {
                        if (data != null) {
                            val message = data!!.getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE)
                            val otpValue =
                                parseFirstNdigitOTPNumberInMessage(message!!, Config.OTP_LENGTH)
                            if (!otpValue.isNullOrEmpty()) {
                                otpView.otp = otpValue
                            }
                        } else {
                            Timber.tag(TAG).i("Consent denied by user or data is null")
                        }
                    }
                    Activity.RESULT_CANCELED -> {
                        hideAutoReadPB()
                    }
                }
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_otp_reader, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        isCancelable = false
        if (shouldDismissOTPReaderDialog) {
            dismissAllowingStateLoss()
            return null
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupTitle(response.title)
        setOTPDialogActions(response.otpReadMode)
        setupDialogUI()
    }

    private fun setupTitle(title: String?) {
        if (title != null) {
            titleTV.apply {
                visibility = View.VISIBLE
                text = title
            }
        } else {
            titleTV.visibility = View.GONE
        }
    }

    override fun onDestroy() {
        dismissAllowingStateLoss()
        super.onDestroy()
        if (!(resendTimeLeftRunnable == null && shouldDismissOTPReaderDialog)) {
            handler.removeCallbacks(
                resendTimeLeftRunnable
                    ?: throw IllegalStateException("resendTimeLeftRunnable cannot be null while removing callbacks"),
            )
        }
    }

    private fun handleResendOTPResponse(response: OTPResponse) {
        response.otpLeft?.apply {
            updateOTPLeft(this)
        }
        setOTPValidTime(response.expiresAt)
        if (response.otpLeft == 0) {
            hideResendCounter()
            resendTV.disable()
        } else {
            launchResendCounter(response.nextResendAt)
        }
        hideResendProgressBar()
        when (response.otpReadMode) {
            OTPReadMode.MANUAL -> {
                hideAutoReadPB()
            }

            OTPReadMode.AUTO_MANUAL -> {
                showAutoReadPB()
            }
        }
    }

    private fun setupDialogUI() {
        setOTPValidTime(response.expiresAt)
        launchResendCounter(response.nextResendAt)
        setupResendAction()
    }

    private fun setOTPValidTime(time: Instant) {
        otpValidTillTV.text = HtmlCompat.fromHtml(
            String.format(
                getString(R.string.otpEntryOTPValidTill),
                time.getFormattedDateTime(
                    requireContext(),
                    dateTimeType = DateTimeType.TIME_WITH_SECONDS,
                ),
            ),
            HtmlCompat.FROM_HTML_MODE_LEGACY,
        )
    }

    private fun updateOTPLeft(numberOfResendsLeft: Int) {
        otpLeftTV.text = requireContext().resources.getQuantityString(
            R.plurals.otpEntryOTPLeft,
            numberOfResendsLeft,
            numberOfResendsLeft,
        )
    }

    private fun setupResendAction() {
        resendTV.setOnClickListener {
            showResendProgressBar()
            hideAutoReadPB()
            otpView.otp = requireContext().getEmptyString()
            (parentFragment as OTPListener).onResendTapped(response.otpId)
        }
    }

    private fun showResendProgressBar() {
        resendTV.visibility = View.GONE
        resendPB.visibility = View.VISIBLE
    }

    private fun hideResendProgressBar() {
        resendTV.visibility = View.VISIBLE
        resendPB.visibility = View.GONE
    }

    private fun launchResendCounter(time: Instant) {
        /***
         * We are adding extra 2 seconds to the resend timer because there is chance that device and
         * server clock are not synced and can have a gap of 1 second or so. We don't want user to
         * experience WAIT_FOR_RESEND Error Code.
         */
        var seconds: Long = calculateSecondsTillResend(time.plusSeconds(2))
        showResendCounter()
        resendTimeLeftRunnable = object : Runnable {
            override fun run() {
                if (seconds <= 0L) {
                    handler.removeCallbacks(resendTimeLeftRunnable!!)
                    hideResendCounter()
                    return
                }
                resendTV.text = String.format(
                    getString(R.string.otpEntryResendOTPLabelTimer),
                    DateUtils.formatElapsedTime(seconds--),
                )
                handler.postDelayed(
                    this,
                    1000,
                )
            }
        }
        handler.post(resendTimeLeftRunnable!!)
    }

    private fun hideResendCounter() {
        resendTV.text = getString(R.string.otpEntryResendOTPLabel)
        resendTV.enable()
    }

    private fun showResendCounter() {
        resendTV.disable()
    }

    private fun calculateSecondsTillResend(time: Instant): Long {
        val diff = time.toEpochMilli() - Instant.now().toEpochMilli()
        return diff / 1000
    }

    private fun setOTPDialogActions(otpReadMode: OTPReadMode) {
        handleOTPReadingCancel()
        setupOTPReading(otpReadMode)
        otpManualListener()
    }

    private fun otpManualListener() {
        otpView.otpListener = object : OTPTextFieldListener {
            override fun onOTPComplete(otp: String) {
                if (isAdded) {
                    resendTimeLeftRunnable?.apply {
                        handler.removeCallbacks(this)
                    }
                    (parentFragment as OTPListener).onSuccessfulOTPRead(otp, response.otpId)
                }
            }

            override fun onInteractionListener() {
                // Do nothing
            }
        }
    }

    // Suppress this because we can't perform click listener as it's overridden in library.
    // We are using touch listener which produces the warning for `performClick` which is not required.
    @SuppressLint("ClickableViewAccessibility")
    private fun setupOTPReading(otpReadMode: OTPReadMode) {
        val otpConfiguration = when (otpReadMode) {
            OTPReadMode.MANUAL -> {
                hideAutoReadPB()
                OTPConfiguration(
                    otpReadType = OtpReadType.MANUAL_ENTRY,
                )
            }

            OTPReadMode.AUTO_MANUAL -> {
                OTPConfiguration(
                    otpReadType = OtpReadType.AUTO_READ_WITH_CONSENT_AND_MANUAL_ENTRY,
                )
            }
        }
        otpView.setConfiguration(otpConfiguration)
        otpView.setAutoReadOtpListener(object : AutoOTPReadListener {
            override fun onAutoReadOTPFailed(e: OTPFailureException) {
                if (isAdded) {
                    hideAutoReadPB()
                    Timber.tag("OTPReaderDialog").e(e)
                }
            }

            override fun onConsentIntentGenerated(intent: Intent, requestCode: Int) {
                if (isAdded) {
                    getOTP.launch(intent)
                }
            }

            override fun onFailure(e: OTPFailureException) {
                if (isAdded) {
                    (parentFragment as OTPListener).onOTPReadFailed(e)
                }
            }

            override fun onMobileHintPickerIntentGenerated(
                intent: PendingIntent,
                requestCode: Int,
            ) {
                // Do Nothing as mobile number picker is disabled.
            }

            override fun onSuccess(message: String) {
                if (isAdded) {
                    val otp = parseFirstNdigitOTPNumberInMessage(message, Config.OTP_LENGTH)
                    otpView.otp = otp
                    (parentFragment as OTPListener).onSuccessfulOTPRead(otp!!, response.otpId)
                }
            }
        })
        otpView.startOtpReading()
        otpView.setOnTouchListener { _, _ ->
            false
        }
    }

    private fun hideAutoReadPB() {
        otpReaderProgressContainer.visibility = View.GONE
    }

    private fun showAutoReadPB() {
        otpReaderProgressContainer.visibility = View.VISIBLE
    }

    private fun handleOTPReadingCancel() {
        cancelButton.setOnClickListener {
            resendTimeLeftRunnable?.apply {
                handler.removeCallbacks(this)
            }
            (parentFragment as OTPListener).onOTPReadCancelled()
        }
    }

    private fun initViews(view: View) {
        view.apply {
            otpReaderProgressContainer = findViewById(R.id.otp_reading_container)
            otpView = findViewById(R.id.otp_et)
            titleTV = findViewById(R.id.title_tv)
            resendTV = findViewById(R.id.resend_tv)
            cancelButton = findViewById(R.id.cancel_button)
            otpLeftTV = findViewById(R.id.otp_left_tv)
            otpValidTillTV = findViewById(R.id.otp_valid_till_tv)
            resendPB = findViewById(R.id.resend_progress_bar)
        }
    }

    companion object {
        fun newInstance(response: OTPResponse): OTPReaderDialog {
            val otpReaderDialog = OTPReaderDialog()
            otpReaderDialog.setResponse(response)
            return otpReaderDialog
        }

        const val TAG: String = "com.bcn.OTPReaderDialog"
    }

    private fun setResponse(response: OTPResponse) {
        this.response = response
    }

    override fun onSuccessfulResendOTP(response: OTPResponse) {
        handleResendOTPResponse(response)
    }

    override fun onResendOTPFailed(error: String) {
        showErrorDialog(requireContext().getEmptyString(), error, DialogCodes.OTP_DIALOG_ERROR_CODE)
    }

    override fun onPositiveAction(dialogId: Int) {
        // OTP dialog is dumb about the client which uses it, so if there is any error it will dismiss the
        // OTP dialog and user has to restart the flow.
        dismiss()
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }
}

private fun TextView.disable() {
    isEnabled = false
    setTextColor(resources.getColor(OTPTextViewResource.color.grey, null))
}

private fun TextView.enable() {
    isEnabled = true
    setTextColor(resources.getColor(R.color.colorPrimaryLight, null))
}

/**
 * This interface listens to resend OTP service. The fragment/activity which made the RPC call to resend OTP needs to update the OTP
 * dialog using this interface. The [OTPReaderDialog] implements it to receive the result back from parent fragment.
 * */
interface ResendOTPListener {
    fun onSuccessfulResendOTP(response: OTPResponse)
    fun onResendOTPFailed(error: String)
}

data class OTPResponse(
    val title: String? = null,
    val otpId: UUID?,
    val nextResendAt: Instant,
    val expiresAt: Instant,
    val otpLeft: Int? = null,
    val otpReadMode: OTPReadMode = OTPReadMode.AUTO_MANUAL,
)

enum class OTPReadMode {
    MANUAL,
    AUTO_MANUAL,
}
