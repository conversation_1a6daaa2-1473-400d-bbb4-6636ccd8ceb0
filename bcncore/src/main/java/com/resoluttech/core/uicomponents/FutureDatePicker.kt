package com.resoluttech.core.uicomponents

import android.app.DatePickerDialog
import android.app.Dialog
import android.os.Bundle
import androidx.annotation.StyleRes
import androidx.fragment.app.DialogFragment
import com.resoluttech.core.config.Config
import com.resoluttech.core.utils.setMaxDate
import com.resoluttech.core.utils.setMinDate
import com.suryadigital.leo.libui.R.style.DatePickerDialogTheme
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId

/**
 * Implements the future date picker dialog fragment that can be used as a dialog as well as an embedded date picker fragment.
 * */
class FutureDatePicker(private val startDate: LocalDate, private val frequencyType: Int) :
    DialogFragment(),
    DatePickerDialog.OnDateSetListener,
    FutureDatePickerDialogInterface {

    private var onDateChangeListener: OnDateChangeListener? = null
    private var selectedTheme: Int = DatePickerDialogTheme
    private var datePickerDialog: DatePickerDialog? = null
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        datePickerDialog = DatePickerDialog(
            requireContext(),
            selectedTheme,
            this,
            startDate.year,
            startDate.monthValue - 1,
            startDate.dayOfMonth + 1,
        )
        limitCalendarRange()
        return datePickerDialog!!
    }

    private fun limitCalendarRange() {
        datePickerDialog!!.setMinDate(getMinDateMillis())
        if (frequencyType == 0) {
            datePickerDialog!!.setMaxDate(
                getMaxDateMillis(
                    LocalDateTime.of(startDate, LocalTime.now())
                        .plusMonths(Config.MAX_ALLOWED_MONTHLY_RECURRENCE),
                ),
            )
        } else {
            datePickerDialog!!.setMaxDate(
                getMaxDateMillis(
                    LocalDateTime.of(startDate, LocalTime.now())
                        .plusWeeks(Config.MAX_ALLOWED_WEEKLY_RECURRENCE),
                ),
            )
        }
    }

    private fun getMinDateMillis(): Long {
        val date = LocalDateTime.of(startDate, LocalTime.now())
        val millis = date.atZone(ZoneId.systemDefault())
            .plusDays(1)
            .toInstant()
            .toEpochMilli()
        return millis - 1000
    }

    private fun getMaxDateMillis(date: LocalDateTime): Long {
        val millis = date.atZone(ZoneId.systemDefault())
            .toInstant()
            .toEpochMilli()
        return millis - 1000
    }

    /**
     * @param dateChangeListener : Sets the date change listener.
     * */
    override fun setDateChangeListener(dateChangeListener: OnDateChangeListener) {
        onDateChangeListener = dateChangeListener
    }

    /**
     * This method set the custom color theme to the date picker dialog.
     * @param theme : Resource id of custom theme.
     * */
    override fun setDatePickerTheme(@StyleRes theme: Int) {
        selectedTheme = theme
    }

    /**
     * An interface which provides the callback when the user taps OK/Cancel in the date picker dialog.
     * @return date : A selected local date object.
     * */
    interface OnDateChangeListener {
        fun onDateChanged(date: LocalDate)
    }

    override fun onDateSet(
        view: android.widget.DatePicker?,
        year: Int,
        month: Int,
        dayOfMonth: Int,
    ) {
        onDateChangeListener?.onDateChanged(LocalDate.of(year, month + 1, dayOfMonth))
    }

    companion object {
        const val TAG: String = "FutureDatePicker.dialog"
    }
}
