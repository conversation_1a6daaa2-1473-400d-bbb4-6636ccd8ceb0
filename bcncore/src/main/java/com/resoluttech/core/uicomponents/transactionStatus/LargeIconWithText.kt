package com.resoluttech.core.uicomponents.transactionStatus

import android.content.Context
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.imageview.ShapeableImageView
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.centerCrop
import com.resoluttech.core.utils.loadImage

class LargeIconWithText(
    context: Context,
) : LinearLayout(context) {

    private val contactIconView: ShapeableImageView
    private val sectionLabelTV: TextView
    private val sectionTitleTV: TextView
    private val sectionDescriptionTV: TextView

    init {
        val view =
            LayoutInflater.from(context).inflate(R.layout.view_large_icon_with_text, this, true)
        contactIconView = view.findViewById(R.id.icon_view)
        sectionLabelTV = view.findViewById(R.id.section_label_tv)
        sectionTitleTV = view.findViewById(R.id.section_title_tv)
        sectionDescriptionTV = view.findViewById(R.id.section_description_tv)
    }

    fun setLayoutData(imageUri: Uri, label: String, title: String, description: String? = null) {
        sectionLabelTV.text = label
        sectionTitleTV.text = title
        if (description != null) {
            sectionDescriptionTV.visibility = View.VISIBLE
            sectionDescriptionTV.text = description
        } else {
            sectionDescriptionTV.visibility = View.GONE
        }
        contactIconView.loadImage(
            imageUri,
        )
        contactIconView.centerCrop()
    }
}
