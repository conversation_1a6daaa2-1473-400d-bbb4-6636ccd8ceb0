package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.views.BaseDialogFragment
import timber.log.Timber

class ResetPasswordAlertDialog : BaseDialogFragment() {

    interface ActionListener {
        fun onPositiveAction()
    }

    private val title: String by lazy {
        arguments?.getString(KEY_DIALOG_TITLE) ?: requireContext().getEmptyString()
    }
    private val message: String by lazy {
        arguments?.getString(KEY_DIALOG_MESSAGE)
            ?: throw IllegalStateException("Message body cannot be null")
    }
    private val positiveActionLabel: String by lazy {
        arguments?.getString(
            KEY_DIALOG_POSITIVE_ACTION_LABEL,
        ) ?: requireContext().getString(R.string.alertActionDismiss)
    }
    private val positiveActionColor: Int by lazy {
        arguments?.getInt(
            KEY_DIALOG_POSITIVE_ACTION_COLOR,
        ) ?: requireContext().getColor(R.color.colorPrimaryLight)
    }
    private val negativeActionColor: Int by lazy {
        arguments?.getInt(
            KEY_DIALOG_NEGATIVE_ACTION_COLOR,
        ) ?: requireContext().getColor(R.color.colorPrimaryLight)
    }
    private val negativeActionLabel: String by lazy {
        arguments?.getString(
            KEY_DIALOG_NEGATIVE_ACTION_LABEL,
        ) ?: requireContext().getEmptyString()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_dialog_alert, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }

        view.findViewById<TextView>(R.id.dialog_title).apply {
            if (title.isNotEmpty()) {
                text = title
            } else {
                visibility = View.GONE
            }
        }
        view.findViewById<TextView>(R.id.dialog_message).apply {
            if (message.isNotEmpty()) {
                text = message
            } else {
                visibility = View.GONE
            }
        }
        isCancelable = false
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        view.findViewById<MaterialButton>(R.id.ok_button).apply {
            if (positiveActionLabel.isNotEmpty()) {
                text = positiveActionLabel
            }
            if (positiveActionColor == 0) {
                setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
            } else {
                setTextColor(positiveActionColor)
            }
            setOnClickListener {
                dismiss()
                (parentFragment as ActionListener).onPositiveAction()
            }
        }

        view.findViewById<MaterialButton>(R.id.cancel_button).apply {
            if (negativeActionLabel.isNotEmpty()) {
                text = negativeActionLabel
            }
            if (negativeActionColor == 0) {
                setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
            } else {
                setTextColor(negativeActionColor)
            }
            setOnClickListener {
                dismiss()
            }
        }
    }

    companion object {

        const val DIALOG_TAG: String = "ResetPasswordAlertDialog"

        fun newInstance(
            title: String,
            message: String,
            positiveActionLabel: String,
            negativeActionLabel: String,
            alertDialogButtonColor: ResetPasswordAlertDialogButtonColor? = null,
            isUserSignedIn: Boolean,
        ): ResetPasswordAlertDialog {
            val alertDialog = ResetPasswordAlertDialog()
            val args = Bundle()
            args.putString(KEY_DIALOG_TITLE, title)
            args.putString(KEY_DIALOG_MESSAGE, message)
            args.putString(KEY_DIALOG_POSITIVE_ACTION_LABEL, positiveActionLabel)
            args.putString(KEY_DIALOG_NEGATIVE_ACTION_LABEL, negativeActionLabel)
            if (alertDialogButtonColor != null) {
                args.putInt(KEY_DIALOG_POSITIVE_ACTION_COLOR, alertDialogButtonColor.positiveColor)
                args.putInt(KEY_DIALOG_NEGATIVE_ACTION_COLOR, alertDialogButtonColor.negativeColor)
            } else {
                Timber.tag(TAG).d("There is no button action color change requested.")
            }
            args.putBoolean(KEY_DIALOG_IS_USER_SIGNED_IN, isUserSignedIn)
            alertDialog.arguments = args
            return alertDialog
        }
    }
}

data class ResetPasswordAlertDialogButtonColor(
    val positiveColor: Int,
    val negativeColor: Int,
)

private const val KEY_DIALOG_TITLE = "ResetPasswordAlertDialog.title"
private const val KEY_DIALOG_MESSAGE = "ResetPasswordAlertDialog.message"
private const val KEY_DIALOG_POSITIVE_ACTION_LABEL =
    "ResetPasswordAlertDialog.positive_action_label"
private const val KEY_DIALOG_NEGATIVE_ACTION_LABEL =
    "ResetPasswordAlertDialog.negative_action_label"
private const val KEY_DIALOG_POSITIVE_ACTION_COLOR =
    "ResetPasswordAlertDialog.positive_action_colors"
private const val KEY_DIALOG_NEGATIVE_ACTION_COLOR =
    "ResetPasswordAlertDialog.negative_action_colors"
private const val KEY_DIALOG_IS_USER_SIGNED_IN = "ResetPasswordAlertDialog.is_user_signed_in"
private const val TAG = "ResetPasswordAlertDialog"
