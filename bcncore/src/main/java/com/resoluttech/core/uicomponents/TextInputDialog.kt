package com.resoluttech.core.uicomponents

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config.Companion.MAX_REMARKS_LENGTH
import com.resoluttech.core.views.BaseDialogFragment
import com.resoluttech.core.views.LimitedCharacterEditText

class TextInputDialog : BaseDialogFragment() {

    private lateinit var saveBT: MaterialButton
    private lateinit var cancelBT: MaterialButton
    private lateinit var inputTIL: TextInputLayout
    private lateinit var inputET: LimitedCharacterEditText
    private lateinit var textInputListener: TextInputListener
    private lateinit var useCase: TextInputDialogUseCase

    interface TextInputListener {
        fun onTextInputSave(text: String)
        fun dismissed()
    }

    fun setArguments(useCase: TextInputDialogUseCase) {
        this.useCase = useCase
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (parentFragment is TextInputListener) {
            textInputListener = parentFragment as TextInputListener
        } else {
            throw IllegalArgumentException("Text input listener is not attached to parent")
        }
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        if (!::useCase.isInitialized) {
            dismissAllowingStateLoss()
            return null
        }
        dialog?.setOnCancelListener {
            (parentFragment as TextInputListener).dismissed()
        }
        return inflater.inflate(R.layout.layout_text_input_dialog, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        inputET.requestFocus()
        saveBT.setOnClickListener {
            saveTextInput()
        }
        cancelBT.setOnClickListener {
            (parentFragment as TextInputListener).dismissed()
            dismiss()
        }
    }

    private fun saveTextInput() {
        val remark = inputET.text.toString()
        if (remark.isEmpty()) {
            when (useCase) {
                TextInputDialogUseCase.REMARKS -> {
                    inputTIL.error = getString(R.string.addPrivateRemarkEmptyTextAlertMessage)
                }
            }
            return
        }
        textInputListener.onTextInputSave(remark)
        dismiss()
    }

    private fun initView(view: View) {
        saveBT = view.findViewById(R.id.save_button)
        cancelBT = view.findViewById(R.id.cancel_button)
        inputET = view.findViewById(R.id.input_et)
        inputTIL = view.findViewById(R.id.input_til)
        when (useCase) {
            TextInputDialogUseCase.REMARKS -> {
                inputET.setTextInputLayout(inputTIL, MAX_REMARKS_LENGTH)
                inputTIL.hint = getString(R.string.addPrivateRemarkPlaceholder)
            }
        }
    }

    companion object {
        const val TAG: String = "TextInputDialog"
    }
}

enum class TextInputDialogUseCase {
    REMARKS,
}
