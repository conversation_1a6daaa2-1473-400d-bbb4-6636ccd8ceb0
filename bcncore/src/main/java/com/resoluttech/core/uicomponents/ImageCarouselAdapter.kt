package com.resoluttech.core.uicomponents

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.assets.LocalizedImage
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.getCarouselImageWidth
import com.resoluttech.core.utils.getSingleCarouselImageWidth
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.loadImage
import com.suryadigital.leo.libui.carouselview.CarouselAdapter

class ViewHolder(view: View, private val singleItemCarousel: Boolean) :
    RecyclerView.ViewHolder(view) {
    private val imageView: ImageView = view.findViewById(R.id.carousel_image_view)

    init {
        setCarouselWidth(view, imageView)
    }

    private fun setCarouselWidth(view: View, imageView: ImageView) {
        val params: RecyclerView.LayoutParams = imageView.layoutParams as RecyclerView.LayoutParams
        if (singleItemCarousel) {
            params.width = getSingleCarouselImageWidth(view.context)
            imageView.setPadding(10, 0, 10, 0)
        } else {
            params.setMargins(10, 0, 10, 0)
            params.width = getCarouselImageWidth(view.context)
        }
        imageView.layoutParams = params
    }

    fun bind(path: LocalizedImage) {
        imageView.loadImage(path.getUri(imageView.context))
    }
}

class ImageCarouselAdapter(
    private val context: Context,
    private val urls: List<LocalizedImage>,
) :
    CarouselAdapter<LocalizedImage, ViewHolder>(urls) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(
                context,
            ).inflate(
                R.layout.carousel_recycler_item,
                parent,
                false,
            ),
            urls.size == 1,
        )
    }

    override fun onBindView(holder: ViewHolder, position: Int) {
        holder.bind(urls[position])
    }
}
