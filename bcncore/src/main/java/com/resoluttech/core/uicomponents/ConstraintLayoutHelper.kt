package com.resoluttech.core.uicomponents

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.resoluttech.bcn.assets.LayoutSection
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue

/**
 * This is a helper class that handles the creation and constraining views to the given layout
 * bounds.
 */
class ConstraintLayoutHelper {

    /**
     * This method created adds [numberOfRows] to the [parentContainer], the newly created
     * rows will be constrainedLayouts arranged in a vertical chain.
     *
     * @param context: Context of an activity or fragment in order to inflate new views.
     * @param numberOfRows: Rows of element to be added.
     * @param parentContainer: Parent view for the new views.
     * @param padding: Padding that should be assigned to the each of the new views.
     *
     * @return: The method return list of the newly created constraintLayout.
     */
    fun addConstrainedRows(
        context: Context,
        numberOfRows: Int,
        parentContainer: ConstraintLayout,
        padding: Int,
    ): ArrayList<ConstraintLayout> {
        val constraintSet = ConstraintSet()
        val constrainedRows = ArrayList<ConstraintLayout>()

        for (rowIndex in 0 until numberOfRows) {
            val containerLayout = ConstraintLayout(context)
            containerLayout.id = View.generateViewId()
            parentContainer.addView(containerLayout)
            val paddingPx = padding.getDPPixelValue()
            containerLayout.setPadding(
                paddingPx,
                NO_PADDING.getDPPixelValue(),
                paddingPx,
                NO_PADDING.getDPPixelValue(),
            )

            constraintSet.connect(
                containerLayout.id,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START,
                DEFAULT_MARGIN,
            )
            constraintSet.connect(
                containerLayout.id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END,
                DEFAULT_MARGIN,
            )

            constraintSet.constrainWidth(containerLayout.id, 0)
            constraintSet.constrainHeight(containerLayout.id, ConstraintSet.WRAP_CONTENT)

            if (constrainedRows.isEmpty()) {
                constraintSet.connect(
                    containerLayout.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP,
                    DEFAULT_MARGIN,
                )
            } else {
                constraintSet.connect(
                    containerLayout.id,
                    ConstraintSet.TOP,
                    constrainedRows.last().id,
                    ConstraintSet.BOTTOM,
                    DEFAULT_MARGIN,
                )
            }

            constrainedRows.add(containerLayout)
        }

        constraintSet.applyTo(parentContainer)
        return constrainedRows
    }

    /**
     * Adds a line spacer to the [parentContainer].
     * Note: line width will be 1dp and color will be [Color.GRAY]
     *
     * @param context: Context of an activity or fragment in order to inflate new views.
     * @param parentContainer: Parent view for the spacer.
     */
    fun addLineSpacer(context: Context, parentContainer: ConstraintLayout) {
        val view = View(context)
        view.id = View.generateViewId()
        view.setBackgroundColor(Color.GRAY)

        addElementToParent(
            view,
            parentContainer,
            ConstraintSet.MATCH_CONSTRAINT,
            1.getDPPixelValue(),
            NO_PADDING.getDPPixelValue(),
        )
    }

    /**
     * Adds a spacer to the [parentContainer].
     *
     * @param context: Context of an activity or fragment in order to inflate new views.
     * @param parentContainer: Parent view for the spacer.
     * @param backgroundColor: Specifies the color of the spacer view, by default it will
     * be [Color.TRANSPARENT]
     */
    fun addSpacer(
        context: Context,
        parentContainer: ConstraintLayout,
        backgroundColor: Int = Color.TRANSPARENT,
    ) {
        val view = View(context)
        view.id = View.generateViewId()
        view.setBackgroundColor(backgroundColor)

        addElementToParent(
            view,
            parentContainer,
            ConstraintSet.MATCH_CONSTRAINT,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
        )
    }

    /**
     * Adds a text view to the [parentContainer] with specified [text].
     *
     * Note: The font size will be 14SP by default and will be BOLD.
     * It will also have bottom padding of 10DP.
     */
    fun addTextView(context: Context, text: String, parentContainer: ConstraintLayout) {
        val textView = TextView(context)
        textView.text = text
        textView.id = View.generateViewId()
        textView.setTypeface(
            ResourcesCompat.getFont(
                context,
                R.font.pt_sans_regular,
            ),
            Typeface.BOLD,
        )
        textView.setTextColor(ContextCompat.getColor(context, R.color.sectionHeadingColor))
        textView.textSize = 14f
        textView.setPadding(
            0,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
            0,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
        )

        addElementToParent(
            textView,
            parentContainer,
            DEFAULT_MARGIN,
            ConstraintSet.WRAP_CONTENT,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
        )
    }

    /**
     * Adds View All text to the [parentContainer].
     *
     * Note: The font size will be 14SP by default and will be BOLD.
     * It will also have bottom padding of 10DP.
     */
    fun addViewAll(
        context: Context,
        parentContainer: ConstraintLayout,
        section: LayoutSection.ButtonContainer,
        handleItemAction: (LayoutSection.ButtonContainer) -> Unit,
    ) {
        val textView = TextView(context)
        textView.text = context.getString(R.string.moneyScreenViewAll)
        textView.id = View.generateViewId()
        textView.setTypeface(
            ResourcesCompat.getFont(
                context,
                R.font.pt_sans_regular,
            ),
            Typeface.BOLD,
        )
        textView.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
        textView.textSize = 14f
        textView.setPadding(
            0,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
            DEFAULT_SIDE_PADDING.getDPPixelValue(),
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
        )
        textView.setOnClickListener {
            handleItemAction(section)
        }
        connectViewAllToParent(parentContainer, textView)
    }

    private fun connectViewAllToParent(parentContainer: ConstraintLayout, textView: TextView) {
        val constraintSet = ConstraintSet()
        parentContainer.addView(textView)
        constraintSet.clone(parentContainer)
        constraintSet.connect(
            textView.id,
            ConstraintSet.RIGHT,
            ConstraintSet.PARENT_ID,
            ConstraintSet.RIGHT,
        )
        constraintSet.connect(
            textView.id,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP,
            DEFAULT_SPACER_HEIGHT.getDPPixelValue(),
        )
        constraintSet.applyTo(parentContainer)
    }

    /**
     * Adds [view] of dimension [width] * [height] to [parentContainer]
     */
    fun addElementToParent(
        view: View,
        parentContainer: ConstraintLayout,
        width: Int,
        height: Int,
        padding: Int,
    ) {
        val constraintSet = ConstraintSet()

        parentContainer.addView(view)
        connectToParent(view.id, constraintSet, padding)

        constraintSet.constrainWidth(view.id, width)
        constraintSet.constrainHeight(view.id, height)

        constraintSet.applyTo(parentContainer)
    }

    private fun connectToParent(
        viewId: Int,
        constraintSet: ConstraintSet,
        padding: Int,
    ) {
        constraintSet.connect(
            viewId,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START,
            DEFAULT_SIDE_PADDING.getDPPixelValue(),
        )

        constraintSet.connect(
            viewId,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END,
            DEFAULT_SIDE_PADDING.getDPPixelValue(),
        )

        constraintSet.connect(
            viewId,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP,
            padding,
        )

        constraintSet.connect(
            viewId,
            ConstraintSet.BOTTOM,
            ConstraintSet.PARENT_ID,
            ConstraintSet.BOTTOM,
            padding,
        )
    }
}

private const val DEFAULT_MARGIN = 0
private const val DEFAULT_SPACER_HEIGHT = 5
private const val DEFAULT_SIDE_PADDING = 10
private const val NO_PADDING = 0
