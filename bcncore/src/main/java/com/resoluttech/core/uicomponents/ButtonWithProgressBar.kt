package com.resoluttech.core.uicomponents

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.ProgressBar
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R

class ButtonWithProgressBar : LinearLayout {

    private lateinit var positiveButton: MaterialButton
    private lateinit var negativeButton: MaterialButton
    lateinit var progressBar: ProgressBar

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        initView(context)
    }

    constructor(
        context: Context,
        attrs: AttributeSet,
        defStyleAttr: Int,
    ) : super(context, attrs, defStyleAttr, 0) {
        initView(context)
    }

    private fun initView(
        context: Context,
    ) {
        val view = LayoutInflater.from(context).inflate(R.layout.component_buttons_with_progress_bar, this, true)
        positiveButton = view.findViewById(R.id.positive_button)
        negativeButton = view.findViewById(R.id.negative_button)
        progressBar = view.findViewById(R.id.progress_bar)
    }

    fun setButtonListeners(listener: ButtonListener) {
        setupPositiveButton(listener)
        setupNegativeButton(listener)
    }

    private fun setupPositiveButton(listener: ButtonListener) {
        positiveButton.setOnClickListener {
            listener.onPositiveButtonClicked()
        }
    }

    private fun setupNegativeButton(listener: ButtonListener) {
        negativeButton.setOnClickListener {
            listener.onNegativeButtonClicked()
        }
    }

    fun setPositiveButtonText(text: String) {
        positiveButton.text = text
    }

    fun disablePositiveButton() {
        positiveButton.apply {
            isEnabled = false
            alpha = 0.65f
        }
    }

    fun showProgressBar() {
        progressBar.visibility = View.VISIBLE
        hidePositiveButton()
        hideNegativeButton()
    }

    fun hideProgressBar() {
        progressBar.visibility = View.GONE
        showNegativeButton()
        showPositiveButton()
    }

    fun enablePositiveButton() {
        positiveButton.apply {
            isEnabled = true
            alpha = 1f
        }
    }

    private fun showPositiveButton() {
        positiveButton.visibility = View.VISIBLE
    }

    private fun hidePositiveButton() {
        positiveButton.visibility = View.GONE
    }

    private fun showNegativeButton() {
        positiveButton.visibility = View.VISIBLE
    }

    fun hideNegativeButton() {
        negativeButton.visibility = View.GONE
    }

    interface ButtonListener {
        fun onPositiveButtonClicked()
        fun onNegativeButtonClicked()
    }
}
