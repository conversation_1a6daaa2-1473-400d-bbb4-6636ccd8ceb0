package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.views.BaseDialogFragment

class ErrorMessageDialog : BaseDialogFragment() {

    interface ErrorMessageDialogListener {
        fun onErrorDialogDismiss()
    }

    private lateinit var errorMessageTV: TextView
    private lateinit var dismissBT: MaterialButton
    private val errorMessage: String by lazy { arguments?.getString(KEY_ERROR_MESSAGE) ?: getString(R.string.alertMessageGenericError) }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_error_message, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    private fun initViews(rootView: View) {
        errorMessageTV = rootView.findViewById(R.id.errorMessage)
        dismissBT = rootView.findViewById(R.id.dismiss)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        errorMessageTV.text = errorMessage
        dismissBT.setOnClickListener {
            if (parentFragment != null) {
                (parentFragment as ErrorMessageDialogListener).onErrorDialogDismiss()
            }
        }
    }

    companion object {

        const val TAG: String = "ErrorMessageDialog"

        /*
            Using starter pattern.
            Thanks: https://blog.mindorks.com/learn-to-write-good-code-in-android-starter-pattern
         */
        fun newInstance(errorMessage: String): ErrorMessageDialog {
            val fragment = ErrorMessageDialog()
            val args = Bundle()
            args.putString(KEY_ERROR_MESSAGE, errorMessage)
            fragment.arguments = args
            return fragment
        }
    }
}
private const val KEY_ERROR_MESSAGE = "ErrorMessageDialog.errorMessage"
