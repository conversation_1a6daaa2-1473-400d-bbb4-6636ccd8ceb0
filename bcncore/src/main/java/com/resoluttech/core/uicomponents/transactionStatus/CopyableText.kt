package com.resoluttech.core.uicomponents.transactionStatus

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.getEmptyString

class CopyableText(
    context: Context,
) : LinearLayout(context) {

    private val copyIV: ImageView
    private val sectionLabelTV: TextView
    private val sectionTitleTV: TextView

    init {
        val view =
            LayoutInflater.from(context).inflate(R.layout.view_copyable_text, this, true)
        copyIV = view.findViewById(R.id.copy)
        sectionLabelTV = view.findViewById(R.id.section_label_tv)
        sectionTitleTV = view.findViewById(R.id.section_title_tv)
    }

    fun setLayoutData(label: String, text: String) {
        sectionLabelTV.text = label
        sectionTitleTV.text = text

        copyIV.setOnClickListener {
            val clipboard = context
                .getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData
                .newPlainText(
                    context.getEmptyString(),
                    sectionTitleTV.text,
                )
            clipboard.setPrimaryClip(clip)
            Toast.makeText(
                context,
                context.getString(
                    R.string.transactionStatusMessageCopiedClipboard,
                    sectionLabelTV.text.toString()
                        .lowercase().replaceFirstChar { if (it.isLowerCase()) it.titlecase() else "$it" },
                ),
                Toast.LENGTH_SHORT,
            ).show()
        }
    }
}
