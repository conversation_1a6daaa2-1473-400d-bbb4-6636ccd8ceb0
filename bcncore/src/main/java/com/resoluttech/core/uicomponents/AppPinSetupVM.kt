package com.resoluttech.core.uicomponents

import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.auth.biometric.BiometricPreferenceHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class AppPinSetupVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _state: MutableLiveData<AppPinSetupScreenState> =
        MutableLiveData(AppPinSetupScreenState.AcceptInput)
    val state: LiveData<AppPinSetupScreenState> = _state
    var sessionPIN: String = ""
    var isConfirmPasswordOptionEnabled: Boolean = false

    private var biometricAuthenticationTriesLeft = 3

    init {
        vmIoScope.launch {
            _state.postValue(
                AppPinSetupScreenState.Data(
                    BiometricPreferenceHelper.getBiometricOption() and Biometric.isBiometricHardwareAvailable(),
                ),
            )
        }
    }

    private fun saveBiometricOption(checked: Boolean) {
        vmIoScope.launch {
            BiometricPreferenceHelper.setBiometricOption(checked)
        }
    }

    fun canUserUseBiometricAuthentication() {
        vmIoScope.launch {
            _state.postValue(
                AppPinSetupScreenState.BiometricAuthentication(
                    Biometric.isBiometricHardwareAvailable(),
                ),
            )
        }
    }

    fun onConfirmPassword(
        confirmPin: String,
        newPin: String,
        parentFragmentManager: FragmentManager,
        isBiometricEnabled: Boolean,
        controller: NavController,
    ) {
        if (newPin == confirmPin) {
            parentFragmentManager.setFragmentResult(
                AppPinSetupFragment.PIN_KEY,
                bundleOf(
                    AppPinSetupFragment.PIN_KEY to newPin,
                ),
            )
            saveBiometricOption(isBiometricEnabled)
            isConfirmPasswordOptionEnabled = false
            sessionPIN = ""
            if (isBiometricEnabled) {
                _state.postValue(AppPinSetupScreenState.AuthenticateBiometrics)
            } else {
                controller.popBackStack()
            }
        } else {
            _state.postValue(AppPinSetupScreenState.Error(isPinMismatch = true))
        }
    }

    fun onErrorDismissed() {
        _state.postValue(AppPinSetupScreenState.AcceptInput)
    }

    fun onBiometricAuthenticationSuccessful(navController: NavController) {
        navController.popBackStack()
    }

    fun onAuthenticationFailed() {
        biometricAuthenticationTriesLeft--
        if (biometricAuthenticationTriesLeft <= 0) {
            saveBiometricOption(false)
            _state.postValue(AppPinSetupScreenState.BiometricRetriesExhausted)
        }
    }

    fun onAuthenticationError() {
        saveBiometricOption(false)
        _state.postValue(AppPinSetupScreenState.BiometricFailed)
    }

    fun onBiometricAuthenticationTriesExhausted(navController: NavController) {
        saveBiometricOption(false)
        navController.popBackStack()
    }
}

sealed class AppPinSetupScreenState {
    object AcceptInput : AppPinSetupScreenState()
    data class Data(val isBiometricEnabled: Boolean) : AppPinSetupScreenState()
    data class Error(val isPinMismatch: Boolean) : AppPinSetupScreenState()
    data class BiometricAuthentication(val isBiometricPossible: Boolean) : AppPinSetupScreenState()
    object AuthenticateBiometrics : AppPinSetupScreenState()
    object BiometricRetriesExhausted : AppPinSetupScreenState()
    object BiometricFailed : AppPinSetupScreenState()
}
