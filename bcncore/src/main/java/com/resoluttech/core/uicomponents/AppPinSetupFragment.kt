package com.resoluttech.core.uicomponents

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.poovam.pinedittextfield.CirclePinField
import com.poovam.pinedittextfield.PinField
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.utils.DialogCodes.Companion.BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showKeyboard
import timber.log.Timber

/**
 * This fragment setup the app PIN feature.
 *
 * This fragment can be started from any of nav graph or fragment manager. The fragment which starts
 * this fragment has to listen to the result using result APIs.
 *
 * for example:
 * If the fragment A started this fragment and A is on the same level as this fragment then A can listen to results
 * as follows
 * parentFragmentManager.setFragmentResultListener(
 *     AppPinSetupFragment.PIN_KEY,
 *     viewLifecycleOwner,
 *     { _, result ->
 *        Timber.tag("TAG").i { ">>> ${result.getString(AppPinSetupFragment.PIN_KEY)}" }
 *      }
 *)
 *
 * If fragment A is parent of this fragment then fragment A can get the result as follows
 * childFragmentManager.setFragmentResultListener(
 *     AppPinSetupFragment.PIN_KEY,
 *     viewLifecycleOwner,
 *     { _, result ->
 *        Timber.tag("TAG").i { ">>> ${result.getString(AppPinSetupFragment.PIN_KEY)}" }
 *      }
 *)
 *
 * @returns If the pin setup is correct then this fragment will set the result as bundle with
 * key [AppPinSetupFragment.PIN_KEY].
 * */
class AppPinSetupFragment :
    Fragment(R.layout.fragment_app_pin_setup),
    AlertDialog.ActionListener,
    Biometric.AppAuthenticationListener {

    private lateinit var enterPinET: CirclePinField
    private lateinit var titleTV: TextView
    private lateinit var biometricSwitch: SwitchCompat
    private lateinit var errorTV: TextView
    private var isBiometricsAvailable = false
    private val appPinSetupVM: AppPinSetupVM by navGraphViewModels(R.id.home_nav)
    private lateinit var alertDialog: AlertDialog

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        Biometric.setAuthenticationListener(this)
        setupPinInputListener()
        appPinSetupVM.state.observe(viewLifecycleOwner, ::reactToState)
        hideToolbar()
        showKeyboard(enterPinET)
        if (appPinSetupVM.isConfirmPasswordOptionEnabled) {
            titleTV.text = getString(R.string.confirmSessionPinTitle)
            showBiometricOption(
                shouldShowOption = true,
            )
        } else {
            titleTV.text = getString(R.string.setupSessionPinTitle)
        }
        setupBackPressed {
            if (appPinSetupVM.isConfirmPasswordOptionEnabled) {
                appPinSetupVM.isConfirmPasswordOptionEnabled = false
                titleTV.text = getString(R.string.setupSessionPinTitle)
                enterPinET.text?.clear()
                showBiometricOption(
                    shouldShowOption = false,
                )
            } else {
                findNavController().navigateUp()
            }
        }
    }

    private fun reactToState(state: AppPinSetupScreenState) {
        when (state) {
            is AppPinSetupScreenState.AcceptInput -> {
                // Default action is handled already
            }

            is AppPinSetupScreenState.Data -> {
                handleDataState(state)
            }

            is AppPinSetupScreenState.Error -> {
                handleErrorState(state.isPinMismatch)
            }

            is AppPinSetupScreenState.BiometricAuthentication -> {
                handleBiometricAuthenticationOption(
                    isBiometricPossible = state.isBiometricPossible,
                )
            }
            is AppPinSetupScreenState.AuthenticateBiometrics -> {
                handleBiometricAuthentication()
            }
            is AppPinSetupScreenState.BiometricFailed -> {
                handleBiometricFailed()
            }
            AppPinSetupScreenState.BiometricRetriesExhausted -> {
                handleBiometricRetriesExhausted()
            }
        }
    }

    private fun handleBiometricRetriesExhausted() {
        Biometric.cancelAuthentication()
        handleBiometricFailed()
    }

    private fun handleBiometricFailed() {
        if (::alertDialog.isInitialized && alertDialog.isAdded) {
            alertDialog.dismiss()
        }
        alertDialog = AlertDialog.newInstance(
            title = requireContext().getString(R.string.alertTitleBiometryAuthenticationFailure),
            message = getString(R.string.alertMessageBiometricAuthenticationFailure),
            dialogId = BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleBiometricAuthentication() {
        if (Biometric.isUserEnrolled()) {
            Biometric.authenticate(this, null)
        }
    }

    override fun onResume() {
        super.onResume()
        appPinSetupVM.canUserUseBiometricAuthentication()
    }

    private fun handleBiometricAuthenticationOption(
        isBiometricPossible: Boolean,
    ) {
        isBiometricsAvailable = isBiometricPossible
        showBiometricOption(appPinSetupVM.isConfirmPasswordOptionEnabled)
    }

    private fun showBiometricOption(shouldShowOption: Boolean) {
        if (isBiometricsAvailable && shouldShowOption) {
            biometricSwitch.visibility = View.VISIBLE
            biometricSwitch.isChecked = Biometric.isUserEnrolled()
            if (Biometric.isUserEnrolled()) {
                errorTV.visibility = View.GONE
            }
            biometricSwitch.setOnClickListener {
                if (biometricSwitch.isChecked) {
                    biometricSwitch.isChecked = Biometric.isUserEnrolled()
                }
                if (!Biometric.isUserEnrolled()) {
                    errorTV.visibility = View.VISIBLE
                    errorTV.text = getString(R.string.alertMessageDeviceNotConfiguredForBiometric)
                } else {
                    errorTV.visibility = View.GONE
                }
            }
        } else {
            biometricSwitch.visibility = View.GONE
            biometricSwitch.isChecked = false
        }
    }

    private fun handleErrorState(pinMismatch: Boolean) {
        if (pinMismatch) {
            enterPinET.text?.clear()
            errorTV.text = getString(R.string.pinDoesntMatchLabel)
            errorTV.visibility = View.VISIBLE
        }
    }

    private fun handleDataState(state: AppPinSetupScreenState.Data) {
        biometricSwitch.isChecked = state.isBiometricEnabled
    }

    private fun setupPinInputListener() {
        enterPinET.onTextCompleteListener = object : PinField.OnTextCompleteListener {
            override fun onTextComplete(enteredText: String): Boolean {
                if (appPinSetupVM.isConfirmPasswordOptionEnabled) {
                    appPinSetupVM.onConfirmPassword(
                        enteredText,
                        appPinSetupVM.sessionPIN,
                        parentFragmentManager,
                        biometricSwitch.isChecked,
                        findNavController(),
                    )
                } else {
                    appPinSetupVM.sessionPIN = enteredText
                    appPinSetupVM.isConfirmPasswordOptionEnabled = true
                    titleTV.text = getString(R.string.confirmSessionPinTitle)
                    enterPinET.text?.clear()
                    showBiometricOption(
                        shouldShowOption = true,
                    )
                }
                return true
            }
        }
    }

    private fun initViews(view: View) {
        view.apply {
            enterPinET = findViewById(R.id.password_et)
            errorTV = findViewById(R.id.error_tv)
            enterPinET.transformationMethod = PinTransformationMethod()
            titleTV = findViewById(R.id.title)
            biometricSwitch = findViewById(R.id.biometric_switch)
            enterPinET.requestFocus()
        }
    }

    companion object {
        const val PIN_KEY: String = "com.fdh.pin.key"
    }

    override fun onPositiveAction(dialogId: Int) {
        appPinSetupVM.onErrorDismissed()
        Timber.tag(TAG).i("onPositiveAction called for dialogId: $dialogId")
        if (dialogId == BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE) {
            appPinSetupVM.onBiometricAuthenticationTriesExhausted(findNavController())
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        appPinSetupVM.onErrorDismissed()
        Timber.tag(TAG).i("onNegativeAction called for dialogId: $dialogId")
    }

    override fun onBiometricAuthenticationSuccessful() {
        appPinSetupVM.onBiometricAuthenticationSuccessful(findNavController())
        Timber.tag(TAG)
            .i("onBiometricAuthenticationSuccessful: Biometric authentication successful while setting up session pin")
    }

    override fun onBiometricAuthenticationFailed() {
        appPinSetupVM.onAuthenticationFailed()
        Timber.tag(TAG)
            .i("onBiometricAuthenticationFailed: Biometric authentication failed while setting up session pin")
    }

    override fun onBiometricAuthenticationError() {
        appPinSetupVM.onAuthenticationError()
        Timber.tag(TAG)
            .i("onBiometricAuthenticationError: Biometric authentication error while setting up session pin")
    }
}

private const val TAG: String = "AppPinSetupFragment"
