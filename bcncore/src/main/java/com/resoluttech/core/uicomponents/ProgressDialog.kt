package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.resoluttech.bcncore.R
import com.resoluttech.core.views.BaseDialogFragment

class ProgressDialog : BaseDialogFragment() {

    private lateinit var loadingPB: ProgressBar
    private lateinit var messageTV: TextView
    private val message: String by lazy {
        arguments?.getString(KEY_MESSAGE) ?: getString(R.string.alertMessageGenericError)
    }

    /***
     * 'shouldDismissProgressDialog' variable is set as false whenever a new instance of alert dialog is created
     * So when system calls onDestroy() (Theme or Font Config Change or to free up RAM memory) and user comes back to app the shouldDismissProgressDialog is reset as true and dialog is dismissed.
     * The fragment calling this alert dialog will create a new instance again by restoring VM state and shouldDismissProgressDialog would be set as false.
     */
    private var shouldDismissProgressDialog: Boolean = true

    fun setArguments(shouldDismissProgressDialog: Boolean) {
        this.shouldDismissProgressDialog = shouldDismissProgressDialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_progress, container)
        if (shouldDismissProgressDialog) {
            dismissAllowingStateLoss()
        }
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    private fun initViews(view: View) {
        loadingPB = view.findViewById(R.id.loading)
        messageTV = view.findViewById(R.id.message)
        messageTV.text = message
    }

    companion object {

        const val TAG: String = "ProgressDialog"

        /*
            Using starter pattern.
            Thanks: https://blog.mindorks.com/learn-to-write-good-code-in-android-starter-pattern
         */
        fun newInstance(message: String): ProgressDialog {
            val fragment = ProgressDialog()
            val args = Bundle()
            args.putString(KEY_MESSAGE, message)
            fragment.arguments = args
            return fragment
        }
    }
}

private const val KEY_MESSAGE = "ErrorMessageDialog.message"
