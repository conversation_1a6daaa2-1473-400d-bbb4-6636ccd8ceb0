package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.navigation.fragment.findNavController
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes.Companion.FORCE_USER_OUT_OF_FLOW
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.views.BaseDialogFragment
import timber.log.Timber

/**
 * We are using public constructor because when an Alert Dialog is shown on any screen, if user goes
 * to some other application without dismissing the Alert Dialog, 'onStop' is called after sometime.
 * Once the user comes back, the 'onCreate' of Main Activity is called in which 'saveBundle' is
 * storing state of the view and thus Main Activity tries to call default constructor of Alert Dialog
 * which wasn't accessible when constructor is private constructor and thus app crashes.
 *
 * WARNING: Now that primary constructor is publicly available, a developer can directly create instance
 * of this class using the constructor.
 *
 * ALWAYS USE 'newInstance' TO CREATE AN INSTANCE OF THIS CLASS AND NEVER USE PRIMARY CONSTRUCTOR.
 *
 */
class AlertDialog : BaseDialogFragment() {

    interface ActionListener {
        fun onPositiveAction(dialogId: Int)
        fun onNegativeAction(dialogId: Int)
    }

    fun setArguments(shouldDismissAlertDialog: Boolean) {
        this.shouldDismissAlertDialog = shouldDismissAlertDialog
    }

    /***
     * 'shouldDismissAlertDialog' variable is set as false whenever a new instance of alert dialog is created
     * So when system calls onDestroy() (Theme or Font Config Change or to free up RAM memory) and user comes back to app the shouldDismissAlertDialog is reset as true and dialog is dismissed.
     * The fragment calling this alert dialog will create a new instance again by restoring VM state and shouldDismissAlertDialog would be set as false.
     */
    private var shouldDismissAlertDialog: Boolean = true

    private val title: String by lazy {
        arguments?.getString(KEY_DIALOG_TITLE) ?: requireContext().getEmptyString()
    }
    private val message: String by lazy {
        arguments?.getString(KEY_DIALOG_MESSAGE) ?: requireContext().getEmptyString()
    }
    private val positiveActionColor: Int by lazy {
        arguments?.getInt(
            KEY_DIALOG_POSITIVE_ACTION_COLOR,
        ) ?: requireContext().getColor(R.color.colorPrimaryLight)
    }
    private val negativeActionColor: Int by lazy {
        arguments?.getInt(
            KEY_DIALOG_NEGATIVE_ACTION_COLOR,
        ) ?: requireContext().getColor(R.color.colorPrimaryLight)
    }
    private val positiveActionLabel: String by lazy {
        arguments?.getString(
            KEY_DIALOG_POSITIVE_ACTION_LABEL,
        ) ?: requireContext().getEmptyString()
    }
    private val negativeActionLabel: String by lazy {
        arguments?.getString(
            KEY_DIALOG_NEGATIVE_ACTION_LABEL,
        ) ?: requireContext().getEmptyString()
    }
    private val forceOutUserDestination: String? by lazy {
        arguments?.getString(
            KEY_FORCE_OUT_USER_DESTINATION,
        )
    }
    private val dialogId: Int by lazy { arguments?.getInt(KEY_DIALOG_ID) ?: 0 }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_dialog_alert, container)
        if (shouldDismissAlertDialog) {
            dismissAllowingStateLoss()
        }
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }

        view.findViewById<TextView>(R.id.dialog_title).apply {
            if (title.isNotEmpty()) {
                text = title
            } else {
                visibility = View.GONE
            }
        }
        view.findViewById<TextView>(R.id.dialog_message).apply {
            if (message.isNotEmpty()) {
                text = message
            } else {
                visibility = View.GONE
            }
        }
        view.findViewById<MaterialButton>(R.id.cancel_button).apply {
            if (negativeActionLabel.isNotEmpty()) {
                text = negativeActionLabel
            } else {
                visibility = View.GONE
            }
        }
        view.findViewById<MaterialButton>(R.id.ok_button).apply {
            if (positiveActionLabel.isNotEmpty()) {
                text = positiveActionLabel
            } else {
                visibility = View.GONE
            }
        }
        isCancelable = false
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        view.findViewById<MaterialButton>(R.id.ok_button).apply {
            if (positiveActionLabel.isNotEmpty()) text = positiveActionLabel
            if (positiveActionColor == 0) {
                setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
            } else {
                setTextColor(positiveActionColor)
            }
            setOnClickListener {
                if (parentFragment != null && parentFragment is ActionListener) {
                    dismiss()
                    if (dialogId == FORCE_USER_OUT_OF_FLOW) {
                        forceOutUserDestination?.let {
                            when (ForceOutUserDestination.valueOf(it)) {
                                ForceOutUserDestination.HOME -> findNavController().popBackStack(R.id.moneyScreenFragment, false)
                                ForceOutUserDestination.PROFILE -> findNavController().popBackStack(R.id.profileFragment, false)
                                ForceOutUserDestination.SIGN_UP_IN -> restartActivity()
                                ForceOutUserDestination.MANAGE_WALLET -> findNavController().popBackStack(R.id.manageAccountFragment, false)
                                ForceOutUserDestination.PAYMENT -> findNavController().popBackStack(R.id.billersFragment, false)
                            }
                        }
                    }
                    (parentFragment as ActionListener).onPositiveAction(dialogId)
                } else {
                    throw IllegalStateException("$parentFragment must implement AlertDialog.ActionListener.")
                }
            }
        }

        view.findViewById<MaterialButton>(R.id.cancel_button).apply {
            if (negativeActionLabel.isNotEmpty()) text = negativeActionLabel
            if (negativeActionColor == 0) {
                setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
            } else {
                setTextColor(negativeActionColor)
            }
            setOnClickListener {
                if (parentFragment != null && parentFragment is ActionListener) {
                    dismiss()
                    (parentFragment as ActionListener).onNegativeAction(dialogId)
                } else {
                    throw IllegalStateException("$parentFragment must implement AlertDialog.ActionListener.")
                }
            }
        }
    }

    override fun onDestroy() {
        dismissAllowingStateLoss()
        super.onDestroy()
    }

    companion object {

        const val DIALOG_TAG: String = "PermissionAlertDialog"
        fun newInstance(
            title: String,
            message: String,
            dialogId: Int,
            positiveActionLabel: String,
            negativeActionLabel: String,
            alertDialogButtonColor: AlertDialogButtonColor? = null,
            forceOutUserDestination: ForceOutUserDestination? = null,
        ): AlertDialog {
            val alertDialog = AlertDialog()
            val args = Bundle()
            args.putString(KEY_DIALOG_TITLE, title)
            args.putString(KEY_DIALOG_MESSAGE, message)
            args.putInt(KEY_DIALOG_ID, dialogId)
            args.putString(KEY_DIALOG_POSITIVE_ACTION_LABEL, positiveActionLabel)
            args.putString(KEY_DIALOG_NEGATIVE_ACTION_LABEL, negativeActionLabel)
            forceOutUserDestination?.let {
                args.putString(KEY_FORCE_OUT_USER_DESTINATION, forceOutUserDestination.name)
            }
            if (alertDialogButtonColor != null) {
                args.putInt(KEY_DIALOG_POSITIVE_ACTION_COLOR, alertDialogButtonColor.positiveColor)
                args.putInt(KEY_DIALOG_NEGATIVE_ACTION_COLOR, alertDialogButtonColor.negativeColor)
            } else {
                Timber.tag(TAG).d("There is no button action color change requested.")
            }
            alertDialog.arguments = args
            return alertDialog
        }
    }
}

data class AlertDialogButtonColor(
    val positiveColor: Int,
    val negativeColor: Int,
)

enum class ForceOutUserDestination {
    HOME, PROFILE, SIGN_UP_IN, MANAGE_WALLET, PAYMENT
}

private const val KEY_DIALOG_TITLE = "AlertDialog.title"
private const val KEY_DIALOG_MESSAGE = "AlertDialog.message"
private const val KEY_DIALOG_POSITIVE_ACTION_LABEL = "AlertDialog.positive_action_label"
private const val KEY_DIALOG_NEGATIVE_ACTION_LABEL = "AlertDialog.negative_action_label"
private const val KEY_DIALOG_POSITIVE_ACTION_COLOR = "AlertDialog.positive_action_colors"
private const val KEY_DIALOG_NEGATIVE_ACTION_COLOR = "AlertDialog.negative_action_colors"
private const val KEY_FORCE_OUT_USER_DESTINATION = "AlertDialog.force_out_user_destination"
private const val KEY_DIALOG_ID = "AlertDialog.code"
private const val TAG = "AlertDialog"
