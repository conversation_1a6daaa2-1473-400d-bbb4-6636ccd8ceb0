package com.resoluttech.core.uicomponents

import android.content.Context
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.navigation.NavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.resoluttech.bcn.assets.Item
import com.resoluttech.bcn.assets.LocalizedImage
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.ItemActionHandlerUseCase
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.utils.executeActionWith
import com.resoluttech.core.utils.getCarouselImageHeight
import com.suryadigital.leo.libui.carouselview.CarouselRecyclerView
import com.suryadigital.leo.libui.carouselview.OnCarouselTouchListener

class CarouselLayoutHelper {

    fun addImageCarouselLayout(
        context: Context,
        parentContainer: ConstraintLayout,
        items: List<Item>,
        swipeRefreshLayout: SwipeRefreshLayout,
        navController: NavController,
        useCase: ItemActionHandlerUseCase,
    ) {
        val carouselView = CarouselRecyclerView(context)
        carouselView.id = View.generateViewId()

        ConstraintLayoutHelper().addElementToParent(
            carouselView,
            parentContainer,
            ConstraintSet.MATCH_CONSTRAINT,
            getCarouselImageHeight(context),
            NO_PADDING.getDPPixelValue(),
        )

        carouselView.layoutParams = getConstrainedLayoutParams(
            ConstraintLayout.LayoutParams.MATCH_PARENT,
            getCarouselImageHeight(context),
            NO_MARGIN,
            NO_MARGIN,
        )
        parentContainer.setPadding(
            NO_PADDING,
            NO_PADDING,
            NO_PADDING,
            NO_PADDING,
        )

        setCarousel(context, carouselView, items, swipeRefreshLayout, navController, useCase)
    }

    private fun getCarouselURLList(items: List<Item>): ArrayList<LocalizedImage> {
        val urlList = ArrayList<LocalizedImage>()
        items.forEach {
            it.imageURL?.let(urlList::add)
        }
        return urlList
    }

    private fun getConstrainedLayoutParams(
        width: Int,
        height: Int,
        startMargin: Int,
        endMargin: Int,
    ): ConstraintLayout.LayoutParams {
        val layoutParam = ConstraintLayout.LayoutParams(width, height)
        layoutParam.marginStart = startMargin
        layoutParam.marginEnd = endMargin
        return layoutParam
    }

    private fun setCarousel(
        context: Context,
        carouselView: CarouselRecyclerView,
        items: List<Item>,
        swipeToRefresh: SwipeRefreshLayout,
        navController: NavController,
        useCase: ItemActionHandlerUseCase,
    ) {
        val carouselTouchListener = object : OnCarouselTouchListener() {
            override fun onDownTouchAction() {
                if (!swipeToRefresh.isRefreshing) {
                    swipeToRefresh.isEnabled = false
                }
            }

            override fun onUpTouchAction() {
                if (!swipeToRefresh.isRefreshing) {
                    swipeToRefresh.isEnabled = true
                }
            }

            override fun onCancelTouchAction() {
                if (!swipeToRefresh.isRefreshing) {
                    swipeToRefresh.isEnabled = true
                }
            }

            override fun onMoveTouchAction() {
                if (!swipeToRefresh.isRefreshing) {
                    swipeToRefresh.isEnabled = false
                }
            }
        }
        val imageURLs = getCarouselURLList(items)
        carouselView.run {
            setAdapter(
                ImageCarouselAdapter(
                    context,
                    imageURLs,
                ),
            )
            if (imageURLs.size > 1) {
                showIndicator()
                playCarousel()
            }
            setSelectedIndicatorColor(context.getColor(R.color.selectedPageIndicatorColor))
            setUnselectedIndicatorColor(context.getColor(R.color.unselectedPageIndicatorColor))
            setSelectedIndicatorRadius(SELECTED_INDICATOR_RADIUS)
            setCarouselTouchListener(carouselTouchListener)
            setOnCarouselItemClickListener(object : CarouselRecyclerView.OnCarouselClickListener {
                override fun onClickItem(pos: Int) {
                    items[pos].action.executeActionWith(navController, context, useCase)
                }
            })
            setUnselectedIndicatorRadius(UNSELECTED_INDICATOR_RADIUS)
        }
    }
}

private const val SELECTED_INDICATOR_RADIUS = 8f
private const val UNSELECTED_INDICATOR_RADIUS = 7f
private const val NO_MARGIN = 0
private const val NO_PADDING = 0
