package com.resoluttech.core.uicomponents

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getFlagFromCurrency
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.setSelectionSelectedAccountForMultiCurrencyDropdown
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import timber.log.Timber
import java.util.UUID
import java.util.stream.Collectors

class MultiCurrencyAccountPickerDropdownAdapter(
    private val context: Context,
    private val multiCurrencyAccounts: List<MultiCurrencyAccountDropdownListItem>,
) : AbstractTextDropDownAdapter<MultiCurrencyAccountDropdownListItem>(multiCurrencyAccounts) {

    override fun getCount(): Int {
        return multiCurrencyAccounts.size - 1
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        val itemView = getViewHolder(multiCurrencyAccounts[position], parent)
        when (val item = multiCurrencyAccounts[position]) {
            is MultiCurrencyAccountDropdownListItem.AvailableAccount -> {
                handleAccountItem(item, AvailableAccount(itemView))
            }
            is MultiCurrencyAccountDropdownListItem.AvailableAccountsHeader -> {
                handleHeadingItem(item, AvailableAccountsHeader(itemView))
            }
            is MultiCurrencyAccountDropdownListItem.UnavailableAccountsHeader -> {
                handleUnavailableHeaderItem(item, UnavailableAccountsHeader(itemView))
            }
            is MultiCurrencyAccountDropdownListItem.UnavailableAccount -> {
                handleUnavailableAccountItem(item, UnavailableAccount(itemView))
            }
            else -> {
                Timber.tag(TAG).d("Possible actions are already handled.")
            }
        }
        return itemView
    }

    private fun getViewHolder(item: MultiCurrencyAccountDropdownListItem, parent: ViewGroup): View {
        return when (item) {
            is MultiCurrencyAccountDropdownListItem.AvailableAccount -> {
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_multi_currency_dropdown_availble_account, parent, false)
            }
            is MultiCurrencyAccountDropdownListItem.AvailableAccountsHeader -> {
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_multi_currency_dropdown_flag_header, parent, false)
            }
            is MultiCurrencyAccountDropdownListItem.UnavailableAccount -> {
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_multi_currency_dropdown_unavailable_account,
                    parent,
                    false,
                )
            }
            is MultiCurrencyAccountDropdownListItem.UnavailableAccountsHeader -> {
                LayoutInflater.from(parent.context).inflate(
                    R.layout.item_multi_currency_dropdown_unavailable_header,
                    parent,
                    false,
                )
            }
            is MultiCurrencyAccountDropdownListItem.Header -> {
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_multi_currency_default_header, parent, false)
            }
            is MultiCurrencyAccountDropdownListItem.Separator -> {
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_multi_currency_dropdown_separator, parent, false)
            }
            is MultiCurrencyAccountDropdownListItem.SubSeparator -> {
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_multi_currency_dropdown_sub_separator, parent, false)
            }
        }
    }

    private fun handleHeadingItem(
        item: MultiCurrencyAccountDropdownListItem.AvailableAccountsHeader,
        view: AvailableAccountsHeader,
    ) {
        view.apply {
            headingTV.text = item.displayLabel
            flagIV.loadImage(getFlagFromCurrency(item.acceptedCurrency.currencyCode))
        }
    }

    private fun handleAccountItem(
        item: MultiCurrencyAccountDropdownListItem.AvailableAccount,
        view: AvailableAccount,
    ) {
        view.apply {
            accountNameTV.text = item.accountName
            if (item.showAccountId) {
                accountId.text =
                    String.format(
                        context.getString(R.string.selectAccountAccountID),
                        item.accountId.toString().takeLast(4),
                    )
                accountId.visibility = View.VISIBLE
            }
        }
    }

    private fun handleUnavailableHeaderItem(
        item: MultiCurrencyAccountDropdownListItem.UnavailableAccountsHeader,
        view: UnavailableAccountsHeader,
    ) {
        view.apply {
            unavailableMessageTV.text = item.unavailableCurrenciesDescription
            handleInfoImageViewTap(infoIV, unavailableMessageTV)
        }
    }

    private fun handleUnavailableAccountItem(
        item: MultiCurrencyAccountDropdownListItem.UnavailableAccount,
        view: UnavailableAccount,
    ) {
        view.apply {
            unavailableAccountTV.text = item.accountName
            unavailableCurrencyTV.text = item.accountCurrency.currencyCode
        }
    }

    private fun handleInfoImageViewTap(infoIV: ImageView, unavailablleMessageTV: TextView) {
        infoIV.setOnClickListener {
            if (unavailablleMessageTV.isVisible) {
                unavailablleMessageTV.visibility = View.GONE
            } else {
                unavailablleMessageTV.visibility = View.VISIBLE
            }
        }
    }

    private class AvailableAccountsHeader(view: View) : RecyclerView.ViewHolder(view) {
        val headingTV: TextView = view.findViewById(R.id.heading_tv)
        val flagIV: ImageView = view.findViewById(R.id.flag_iv)
    }

    private class AvailableAccount(view: View) : RecyclerView.ViewHolder(view) {
        val accountNameTV: TextView = view.findViewById(R.id.account_name_tv)
        val accountId: TextView = view.findViewById(R.id.account_id_tv)
    }

    private class UnavailableAccountsHeader(view: View) : RecyclerView.ViewHolder(view) {
        val infoIV: ImageView = view.findViewById(R.id.info_IV)
        val unavailableMessageTV: TextView = view.findViewById(R.id.unavailable_message_tv)
    }

    private class UnavailableAccount(view: View) : RecyclerView.ViewHolder(view) {
        val unavailableAccountTV: TextView = view.findViewById(R.id.unavailable_account_name_tv)
        val unavailableCurrencyTV: TextView =
            view.findViewById(R.id.unavailable_account_currency_tv)
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.account_spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            when (val item = multiCurrencyAccounts[position]) {
                is MultiCurrencyAccountDropdownListItem.AvailableAccount -> {
                    accountName.text = item.accountName
                }
                is MultiCurrencyAccountDropdownListItem.Header -> {
                    accountName.text = item.label
                }
                else -> {
                    // do nothing
                }
            }
        }
        return view
    }

    private class ViewHolder(view: View) {
        var accountName: TextView = view.findViewById(R.id.dropdown_item_text_view)
    }

    override fun isEnabled(position: Int): Boolean {
        return when (multiCurrencyAccounts[position]) {
            is MultiCurrencyAccountDropdownListItem.AvailableAccount -> true
            else -> false
        }
    }

    companion object {

        /**
         * This method will be called in order to update the dropdown, the currency will be filtered
         * based on [acceptsCurrencies] params.
         *
         * @param context: Context is required to fetch the required string resources.
         * @param acceptsCurrencies: The list of supported currency by recipient.
         * @param accountDropdown: The account dropdown which needs to be updated.
         * */
        fun updateAccountDropdown(
            context: Context,
            recipientName: String?,
            accountDropdown: TextDropdown,
            acceptsCurrencies: List<Currency>,
        ) {
            val multiCurrencyAccounts =
                getMultiCurrencyDropdownList(context, recipientName, acceptsCurrencies)

            val adapter = MultiCurrencyAccountPickerDropdownAdapter(
                context,
                multiCurrencyAccounts,
            )
            accountDropdown.setAdapter(adapter)
            accountDropdown.setSelectionSelectedAccountForMultiCurrencyDropdown(
                multiCurrencyAccounts.toMutableList(),
                Currency(UserSharedPreference.getUserPrimaryCurrency()),
            )
        }

        private fun getMultiCurrencyDropdownList(
            context: Context,
            recipientName: String?,
            acceptsCurrencies: List<Currency>,
        ): List<MultiCurrencyAccountDropdownListItem> {
            val multiCurrencyAccounts: MutableList<MultiCurrencyAccountDropdownListItem> =
                mutableListOf()

            val availableCurrenciesSet = mutableSetOf<String>()
            var isUnavailableAccountsHeaderAdded = false

            val storedAccountList = SelectedAccountHelper.getPersistedAccounts()
            val accountList = mutableListOf<AccountItem>()
            storedAccountList.forEach { account ->
                accountList.add(
                    AccountItem(
                        UUID.fromString(account.id),
                        account.name,
                        Currency(account.currencyCode),
                    ),
                )
            }
            accountList.sortBy { it.currency.currencyCode }

            var acceptsCurrenciesMessage = context.getEmptyString()
            acceptsCurrencies.forEach {
                acceptsCurrenciesMessage += if (acceptsCurrencies.last() == it) {
                    it.currencyCode
                } else {
                    context.getString(
                        R.string.selectAccountAcceptsMultiCurrencies,
                        it.currencyCode,
                    )
                }
            }

            val availableAccountsTempList =
                accountList.stream().filter { acceptsCurrencies.contains(it.currency) }.collect(
                    Collectors.toList(),
                )
            availableAccountsTempList.forEach { accountItem ->
                if (!availableCurrenciesSet.contains(accountItem.currency.currencyCode)) {
                    val country = UserSharedPreference.getUserCountryCurrency()
                        .find { it.currencyCode == accountItem.currency.currencyCode }
                    val displayLabel = when (LocaleManager.getCurrentLocale(context)) {
                        SupportedLocale.EN_US -> country!!.displayNameEn
                        SupportedLocale.NYANJA ->
                            country!!.displayNameNy
                                ?: country.displayNameEn
                    }
                    multiCurrencyAccounts.add(
                        MultiCurrencyAccountDropdownListItem.AvailableAccountsHeader(
                            displayLabel,
                            accountItem.currency,
                        ),
                    )
                    availableCurrenciesSet.add(accountItem.currency.currencyCode)
                }
                multiCurrencyAccounts.add(
                    MultiCurrencyAccountDropdownListItem.AvailableAccount(
                        accountItem.name,
                        accountItem.id,
                        accountItem.currency.currencyCode,
                        showAccountId = true,
                    ),
                )
                if (availableAccountsTempList.last() != accountItem) {
                    multiCurrencyAccounts.add(MultiCurrencyAccountDropdownListItem.SubSeparator)
                }
            }

            val unavailableAccountsTempList =
                accountList.stream().filter { !acceptsCurrencies.contains(it.currency) }.collect(
                    Collectors.toList(),
                )
            unavailableAccountsTempList.forEach { accountItem ->
                if (!isUnavailableAccountsHeaderAdded) {
                    multiCurrencyAccounts.add(MultiCurrencyAccountDropdownListItem.Separator)
                    multiCurrencyAccounts.add(
                        MultiCurrencyAccountDropdownListItem.UnavailableAccountsHeader(
                            context.getString(
                                R.string.selectAccountAcceptTransfers,
                                recipientName,
                                acceptsCurrenciesMessage,
                            ),
                        ),
                    )
                    isUnavailableAccountsHeaderAdded = true
                }
                multiCurrencyAccounts.add(
                    MultiCurrencyAccountDropdownListItem.UnavailableAccount(
                        accountItem.name,
                        accountItem.currency,
                    ),
                )
                if (unavailableAccountsTempList.last() != accountItem) {
                    multiCurrencyAccounts.add(MultiCurrencyAccountDropdownListItem.SubSeparator)
                }
            }
            multiCurrencyAccounts.add(
                MultiCurrencyAccountDropdownListItem.Header(
                    context.getString(
                        R.string.selectAccountViewTitle,
                    ),
                ),
            )
            return multiCurrencyAccounts
        }
    }
}

sealed class MultiCurrencyAccountDropdownListItem {
    data class Header(val label: String) :
        MultiCurrencyAccountDropdownListItem()

    data class AvailableAccountsHeader(val displayLabel: String, val acceptedCurrency: Currency) :
        MultiCurrencyAccountDropdownListItem()

    data class AvailableAccount(
        val accountName: String,
        val accountId: UUID,
        val currency: String,
        val showAccountId: Boolean = false,
    ) :
        MultiCurrencyAccountDropdownListItem()

    data class UnavailableAccountsHeader(
        val unavailableCurrenciesDescription: String,
    ) : MultiCurrencyAccountDropdownListItem()

    data class UnavailableAccount(val accountName: String, val accountCurrency: Currency) :
        MultiCurrencyAccountDropdownListItem()

    object Separator : MultiCurrencyAccountDropdownListItem()

    object SubSeparator : MultiCurrencyAccountDropdownListItem()
}

data class AccountItem(val id: UUID, val name: String, val currency: Currency)

private const val TAG = "MultiCurrencyPickerDropdownAdapter"
