package com.resoluttech.core.uicomponents.transactionStatus

import android.content.Context
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import com.resoluttech.bcncore.R

class LabelWithText(
    context: Context,
) : LinearLayout(context) {

    private val sectionLabelTV: TextView
    private val sectionTitleTV: TextView

    init {
        val view =
            LayoutInflater.from(context).inflate(R.layout.view_label_with_text, this, true)
        sectionLabelTV = view.findViewById(R.id.section_label_tv)
        sectionTitleTV = view.findViewById(R.id.section_title_tv)
    }

    fun setLayoutData(label: String, text: String) {
        sectionLabelTV.text = label
        sectionTitleTV.text = text
    }
}
