package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.style.UnderlineSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcncore.R
import com.resoluttech.core.listeners.DialogListener
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.views.BaseDialogFragment

class OTPChargesAlertDialog : BaseDialogFragment() {

    /***
     * 'shouldDismissOTPChargesAlertDialog' variable is set as false whenever a new instance of alert dialog is created
     * So when system calls onDestroy() (Theme or Font Config Change or to free up RAM memory) and user comes back to app the shouldDismissOTPChargesAlertDialog is reset as true and dialog is dismissed.
     * The fragment calling this alert dialog will create a new instance again by restoring VM state and shouldDismissOTPChargesAlertDialog would be set as false.
     */
    private var shouldDismissOTPChargesAlertDialog: Boolean = true

    fun setArguments(shouldDismissOTPChargesAlertDialog: Boolean) {
        this.shouldDismissOTPChargesAlertDialog = shouldDismissOTPChargesAlertDialog
    }

    private lateinit var otpChargesTV: TextView
    private lateinit var enteredAmountTV: TextView
    private lateinit var enteredAmountLabelTV: TextView
    private lateinit var payableAmountTV: TextView
    private lateinit var receiveAmountTV: TextView
    private lateinit var payableAmountLabelTV: TextView
    private lateinit var payFromAccountTV: TextView
    private lateinit var informationMessageTV: TextView
    private lateinit var termsAndConditionTV: TextView
    private lateinit var informationLayout: LinearLayout
    private lateinit var transactionChargeLayout: LinearLayout
    private lateinit var receivingAmountLayout: LinearLayout
    private lateinit var declineBT: MaterialButton
    private lateinit var proceedBT: MaterialButton
    private val otpCharges: Double? by lazy { arguments?.getDouble(KEY_OTP_CHARGES_AMOUNT) }
    private val otpChargesCurrencyStr: String? by lazy {
        arguments?.getString(
            KEY_OTP_CHARGES_CURRENCY_CURRENCY,
        )
    }
    private val enteredAmount: Double by lazy {
        arguments?.getDouble(KEY_ENTERED_AMOUNT)
            ?: throw IllegalArgumentException("Payable amount should not be null")
    }
    private val amountCurrencyStr: String by lazy {
        arguments?.getString(KEY_AMOUNT_CURRENCY)
            ?: throw IllegalArgumentException("Currency should not be null")
    }
    private val exchangeRateAmount: Double? by lazy {
        arguments?.getDouble(KEY_EXCHANGE_RATE_AMOUNT)
    }
    private val exchangeRateAmountCurrencyStr: String? by lazy {
        arguments?.getString(KEY_EXCHANGE_RATE_AMOUNT_CURRENCY)
    }
    private val accountName: String by lazy {
        arguments?.getString(KEY_PAY_FROM_ACCOUNT)
            ?: throw IllegalArgumentException("Account name should not be null")
    }
    private val isReceiving: Boolean by lazy { arguments?.getBoolean(KEY_IS_RECEIVING) ?: false }
    private val informationMessage: String? by lazy { arguments?.getString(KEY_INFORMATION_MESSAGE) }
    private val termsAndConditionLink: String? by lazy {
        arguments?.getString(
            KEY_TERMS_AND_CONDITIONS,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.otp_charges_alert, container)
        if (shouldDismissOTPChargesAlertDialog) {
            dismissAllowingStateLoss()
            return null
        }
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    private fun initViews(rootView: View) {
        otpChargesTV = rootView.findViewById(R.id.otpChangesAmount)
        payableAmountTV = rootView.findViewById(R.id.total_amount_tv)
        payableAmountLabelTV = rootView.findViewById(R.id.payment_amount_label)
        enteredAmountTV = rootView.findViewById(R.id.entered_amount_tv)
        enteredAmountLabelTV = rootView.findViewById(R.id.entered_amount_label_tv)
        payFromAccountTV = rootView.findViewById(R.id.pay_from_account)
        declineBT = rootView.findViewById(R.id.decline)
        proceedBT = rootView.findViewById(R.id.proceed)
        informationMessageTV = rootView.findViewById(R.id.information_tv)
        informationLayout = rootView.findViewById(R.id.information_layout)
        transactionChargeLayout = rootView.findViewById(R.id.transaction_charges_layout)
        receivingAmountLayout = rootView.findViewById(R.id.receiving_amount_layout)
        termsAndConditionTV = rootView.findViewById(R.id.terms_and_condition_tv)
        receiveAmountTV = rootView.findViewById(R.id.receivingAmount)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupOTPCharges()
        setupEnteredAmount()
        setupPayFromAccount()
        setupPayableAmount()
        setupButtons()
        setupInformationMessage()
        setupTermsAndConditions()
        setupReceiveAmount()
    }

    private fun setupReceiveAmount() {
        if (exchangeRateAmount != null && exchangeRateAmountCurrencyStr != null) {
            receivingAmountLayout.visibility = View.VISIBLE
            receiveAmountTV.text = displayAmountWithCurrency(
                exchangeRateAmountCurrencyStr!!,
                exchangeRateAmount!!,
            )
            receiveAmountTV.addContentDescriptionString(
                R.string.axTransactionAlertRecipientReceives,
                requireContext(),
                receiveAmountTV.text.toString(),
            )
        } else {
            receivingAmountLayout.visibility = View.GONE
        }
    }

    private fun setupOTPCharges() {
        if (otpCharges != null && otpChargesCurrencyStr != null) {
            transactionChargeLayout.visibility = View.VISIBLE
            otpChargesTV.text = displayAmountWithCurrency(
                otpChargesCurrencyStr!!,
                otpCharges!!,
            )
            otpChargesTV.addContentDescriptionString(
                R.string.axTransactionAlertFee,
                requireContext(),
                otpChargesTV.text.toString(),
            )
        } else {
            transactionChargeLayout.visibility = View.GONE
        }
    }

    private fun setupEnteredAmount() {
        enteredAmountTV.text = displayAmountWithCurrency(
            amountCurrencyStr,
            enteredAmount,
        )
        enteredAmountTV.addContentDescriptionString(
            R.string.axTransactiontatusAmountLabel,
            requireContext(),
            enteredAmountTV.text.toString(),
        )
        enteredAmountLabelTV.text = requireContext().getString(R.string.alertTransactionDetailAmount)
    }

    private fun setupPayFromAccount() {
        payFromAccountTV.text = accountName
        payFromAccountTV.addContentDescriptionString(
            R.string.axTransactionAlertWalletName,
            requireContext(),
            accountName,
        )
    }

    private fun setupPayableAmount() {
        payableAmountTV.text =
            displayAmountWithCurrency(
                amountCurrencyStr,
                (otpCharges ?: 0.0) + enteredAmount,
            )
        if (isReceiving) {
            payableAmountLabelTV.text = requireContext().getString(R.string.alertTransactionDetailPayableAmount)
            payableAmountTV.addContentDescriptionString(
                R.string.axTransactionAlertPayableAmount,
                requireContext(),
                payableAmountTV.text.toString(),
            )
        } else {
            payableAmountLabelTV.text = requireContext().getString(R.string.alertTransactionDetailPayableAmount)
            payableAmountTV.addContentDescriptionString(
                R.string.axTransactionAlertPayableAmount,
                requireContext(),
                payableAmountTV.text.toString(),
            )
        }
    }

    private fun setupInformationMessage() {
        if (informationMessage != null) {
            informationLayout.visibility = View.VISIBLE
            informationMessageTV.text = informationMessage
        } else {
            informationLayout.visibility = View.GONE
        }
    }

    private fun setupButtons() {
        declineBT.setOnClickListener {
            if (parentFragment != null) {
                (parentFragment as DialogListener).onDeclined()
            }
        }

        proceedBT.setOnClickListener {
            if (parentFragment != null) {
                (parentFragment as DialogListener).onProceed()
            }
        }
    }

    private fun setupTermsAndConditions() {
        if (termsAndConditionLink != null) {
            termsAndConditionTV.visibility = View.VISIBLE
            val tAndCString = getString(R.string.alertTransactionTermsAndConditions)
            val content = SpannableString(tAndCString)
            content.setSpan(UnderlineSpan(), 0, tAndCString.length, 0)
            termsAndConditionTV.text = content
            termsAndConditionTV.setOnClickListener {
                if (parentFragment != null) {
                    (parentFragment as DialogListener).onClickTermsAndCondition()
                }
            }
        } else {
            termsAndConditionTV.visibility = View.GONE
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissAllowingStateLoss()
    }

    companion object {

        const val TAG: String = "OTPChargesAlertDialog"

        /*
            Using starter pattern.
            Thanks: https://blog.mindorks.com/learn-to-write-good-code-in-android-starter-pattern
         */
        fun newInstance(
            otpCharges: Amount?,
            amount: Amount,
            accountName: String,
            isReceiving: Boolean = false,
            informationMessage: String? = null,
            termsAndConditionLink: String? = null,
            exchangeRateAmount: Amount? = null,
        ): OTPChargesAlertDialog {
            val fragment = OTPChargesAlertDialog()
            val args = Bundle()
            if (otpCharges != null) {
                args.putDouble(KEY_OTP_CHARGES_AMOUNT, otpCharges.amount.getUserFacingValue())
                args.putString(KEY_OTP_CHARGES_CURRENCY_CURRENCY, otpCharges.currency.currencyCode)
            }
            args.putString(KEY_AMOUNT_CURRENCY, amount.currency.currencyCode)
            args.putDouble(KEY_ENTERED_AMOUNT, amount.amount.getUserFacingValue())
            exchangeRateAmount?.let {
                args.putString(KEY_EXCHANGE_RATE_AMOUNT_CURRENCY, it.currency.currencyCode)
                args.putDouble(KEY_EXCHANGE_RATE_AMOUNT, it.amount.getUserFacingValue())
            }
            args.putString(KEY_PAY_FROM_ACCOUNT, accountName)
            args.putBoolean(KEY_IS_RECEIVING, isReceiving)
            args.putString(KEY_INFORMATION_MESSAGE, informationMessage)
            args.putString(KEY_TERMS_AND_CONDITIONS, termsAndConditionLink)
            fragment.arguments = args
            return fragment
        }
    }
}

private const val KEY_OTP_CHARGES_AMOUNT = "OTPChargesAlertDialog.amount"
private const val KEY_ENTERED_AMOUNT = "OTPChargesAlertDialog.entered_amount"
private const val KEY_AMOUNT_CURRENCY = "OTPChargesAlertDialog.amount_currency"
private const val KEY_EXCHANGE_RATE_AMOUNT = "OTPChargesAlertDialog.exchange_rate_amount"
private const val KEY_EXCHANGE_RATE_AMOUNT_CURRENCY =
    "OTPChargesAlertDialog.exchange_rate_amount_currency"
private const val KEY_OTP_CHARGES_CURRENCY_CURRENCY = "OTPChargesAlertDialog.otp_charges_currency"
private const val KEY_PAY_FROM_ACCOUNT = "OTPChargesAlertDialog.pay_from_account"
private const val KEY_IS_RECEIVING = "OTPChargesAlertDialog.is_receiving"
private const val KEY_INFORMATION_MESSAGE = "OTPChargesAlertDialog.information_message"
private const val KEY_TERMS_AND_CONDITIONS = "OTPChargesAlertDialog.terms_and_conditions"
