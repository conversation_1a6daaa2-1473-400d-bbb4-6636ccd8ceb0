package com.resoluttech.core.uicomponents

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import coil.load
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.CountryCurrency
import com.resoluttech.core.utils.getFlagFromCurrency
import com.resoluttech.core.utils.loadImage
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class CurrencyDropdownAdapter(private val items: List<CountryCurrency>) : AbstractTextDropDownAdapter<CountryCurrency>(items) {

    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: AccountViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_currency_dropdown_item, parent, false)
            viewHolder =
                AccountViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as AccountViewHolder
        }
        viewHolder.apply {
            accountName.text = items[position].currencyCode
            currencyCountryFlag.loadImage(getFlagFromCurrency(items[position].currencyCode))
        }
        return view
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_currency_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            accountName.setTextColor(
                view?.resources!!.getColor(
                    R.color.titleTextColor,
                    null,

                ),
            )
            dropdownIcon.setImageResource(R.drawable.ic_arrow_account_drop_down)
            accountName.text = items[position].currencyCode
            currencyCountryFlag.load(getFlagFromCurrency(items[position].currencyCode))
        }
        return view
    }

    private class AccountViewHolder(view: View) {
        var currencyCountryFlag: ImageView = view.findViewById(R.id.currency_country_flag_iv)
        var accountName: TextView = view.findViewById(R.id.dropdown_item_text_view)
    }

    private class ViewHolder(view: View) {
        var currencyCountryFlag: ImageView = view.findViewById(R.id.currency_country_flag_iv)
        var accountName: TextView = view.findViewById(R.id.dropdown_item_text_view)
        var dropdownIcon: ImageButton = view.findViewById(R.id.dropdown_icon)
    }
}
