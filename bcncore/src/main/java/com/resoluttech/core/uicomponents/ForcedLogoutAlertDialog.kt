package com.resoluttech.core.uicomponents

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.clearAllUserData
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.views.BaseDialogFragment
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * This dialog should be used when we need to force the user to sign in again. The user will be forced
 * to sign in when there are exceptions like `InvalidLlt`, `UserDisabled`, etc.
 * All the user tokens and cache are cleared when the user clicks on `positiveActionButton` and the user will be taken to the
 * sign-in screen.
 */
class ForcedLogoutAlertDialog : BaseDialogFragment() {

    private val title: String by lazy {
        arguments?.getString(KEY_DIALOG_TITLE) ?: requireContext().getEmptyString()
    }
    private val message: String by lazy {
        arguments?.getString(KEY_DIALOG_MESSAGE)
            ?: throw IllegalStateException("Message body cannot be null")
    }
    private val positiveActionLabel: String by lazy {
        arguments?.getString(
            KEY_DIALOG_POSITIVE_ACTION_LABEL,
        ) ?: requireContext().getString(R.string.alertActionDismiss)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_dialog_alert, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }

        view.findViewById<TextView>(R.id.dialog_title).apply {
            if (title.isNotEmpty()) {
                text = title
            } else {
                visibility = View.GONE
            }
        }
        view.findViewById<TextView>(R.id.dialog_message).apply {
            if (message.isNotEmpty()) {
                text = message
            } else {
                visibility = View.GONE
            }
        }
        isCancelable = false
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        view.findViewById<MaterialButton>(R.id.ok_button).apply {
            if (positiveActionLabel.isNotEmpty()) text = positiveActionLabel
            setOnClickListener {
                CoroutineScope(Dispatchers.IO).launch {
                    Timber.tag(TAG).i("Clearing all the tokens and cashed data.")
                    clearData()
                }
                restart(requireActivity() as AppCompatActivity)
            }
        }

        view.findViewById<MaterialButton>(R.id.cancel_button).visibility = View.GONE
    }

    private fun restart(activity: AppCompatActivity) {
        activity.intent?.data = null
        activity.finish()
        activity.startActivity(activity.intent)
    }

    private suspend fun clearData() {
        clearAllUserData()
    }

    companion object {

        const val DIALOG_TAG: String = "ForcedLogoutAlertDialog"

        fun newInstance(
            title: String,
            message: String,
            positiveActionLabel: String,
        ): ForcedLogoutAlertDialog {
            val alertDialog = ForcedLogoutAlertDialog()
            val args = Bundle()
            args.putString(KEY_DIALOG_TITLE, title)
            args.putString(KEY_DIALOG_MESSAGE, message)
            args.putString(KEY_DIALOG_POSITIVE_ACTION_LABEL, positiveActionLabel)
            alertDialog.arguments = args
            return alertDialog
        }
    }
}

private const val KEY_DIALOG_TITLE = "ForcedLogoutAlertDialog.title"
private const val KEY_DIALOG_MESSAGE = "ForcedLogoutAlertDialog.message"
private const val KEY_DIALOG_POSITIVE_ACTION_LABEL = "ForcedLogoutAlertDialog.positive_action_label"
private const val TAG = "ForcedLogoutAlertDialog"
