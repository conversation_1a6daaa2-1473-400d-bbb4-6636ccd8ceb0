package com.resoluttech.core.uicomponents

import android.content.Context
import android.graphics.Typeface
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Xml
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.res.ResourcesCompat
import androidx.navigation.NavController
import com.resoluttech.bcn.assets.Item
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.ItemActionHandlerUseCase
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.utils.executeActionWith
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.localisedText
import com.suryadigital.leo.libui.verticalimagebutton.VerticalImageButton
import org.xmlpull.v1.XmlPullParser

/**
 * This class helps in inflating [VerticalImageButton] of [IMAGE_BUTTON_WIDTH] * [IMAGE_BUTTON_HEIGHT]
 * dimension and arranging them in the rows with maximum of [MAX_BUTTONS_PER_ROW].
 */
class ImageButtonLayoutHelper {

    /**
     * This inflates list of the [VerticalImageButton] required in presenting the [buttonItems].
     *
     * Note: All the buttons inflated here uses [R.xml.vertical_image_button_attributes] as
     * base attributes.
     */
    @Throws(Exception::class)
    fun getImageButtons(
        context: Context,
        buttonItems: List<Item>,
        navController: NavController,
        useCase: ItemActionHandlerUseCase,
    ): List<VerticalImageButton> {
        val imageButtons = ArrayList<VerticalImageButton>()

        val parser: XmlPullParser = context.resources.getXml(R.xml.vertical_image_button_attributes)
        parser.next()
        parser.nextTag()
        val attr: AttributeSet = Xml.asAttributeSet(parser)

        for (item in buttonItems) {
            val imageButton = VerticalImageButton(context, attr)

            imageButton.labelTextView.maxLines = 2
            imageButton.labelTextView.ellipsize = TextUtils.TruncateAt.END

            item.imageURL?.let {
                imageButton.iconImageView.loadImage(it.getUri(context))
            }
            item.title?.let {
                imageButton.labelTextView.text = it
                    .localisedText(LocaleManager.getCurrentLocale(context))
            }
            item.title?.let {
                imageButton.labelTextView.setTypeface(
                    ResourcesCompat.getFont(
                        context,
                        R.font.pt_sans_regular,
                    ),
                    Typeface.NORMAL,
                )
            }
            imageButton.setBackgroundRipple(getRippleRadius())
            imageButton.id = View.generateViewId()
            imageButton.setOnClickListener {
                item.action.executeActionWith(navController, context, useCase)
            }
            imageButtons.add(imageButton)
        }

        return imageButtons
    }

    private fun getRippleRadius(): Int {
        // Extra radius is added so that the ripple radius is more than the height of the image button.
        return (IMAGE_BUTTON_HEIGHT / 2).getDPPixelValue() + EXTRA_RADIUS.getDPPixelValue()
    }

    /**
     * This method would add [imageButtons] to [containerLayout], with limit of [MAX_BUTTONS_PER_ROW].
     */
    fun addImageButtonsToRoot(
        context: Context,
        imageButtons: List<VerticalImageButton>,
        containerLayout: ConstraintLayout,
    ): ArrayList<ConstraintLayout> {
        val innerContainers = ArrayList<ConstraintLayout>()
        val constraintSet = ConstraintSet()

        innerContainers.add(
            getInnerContainer(context, containerLayout, constraintSet),
        )

        for ((index, imageButton) in imageButtons.withIndex()) {
            if (index != 0 && index % MAX_BUTTONS_PER_ROW == 0) {
                innerContainers.add(
                    getInnerContainer(
                        context,
                        containerLayout,
                        constraintSet,
                        innerContainers.last(),
                    ),
                )
            }
            innerContainers.last().addView(imageButton)
        }

        constraintSet.connect(
            innerContainers.last().id,
            ConstraintSet.BOTTOM,
            ConstraintSet.PARENT_ID,
            ConstraintSet.BOTTOM,
            DEFAULT_SPACING.getDPPixelValue(),
        )

        constraintSet.applyTo(containerLayout)
        return innerContainers
    }

    /**
     * This method would constrain the [imageButtons] as chain of [MAX_BUTTONS_PER_ROW] elements
     * with the [innerContainers].
     *
     * @param imageButtons: list of all the buttons that has to be constrained.
     * @param innerContainers: The list of container to hold [MAX_BUTTONS_PER_ROW] elements,
     * each container will form a separate row.
     */
    fun createButtonsChain(
        imageButtons: List<VerticalImageButton>,
        innerContainers: ArrayList<ConstraintLayout>,
        alignCenter: Boolean = false,
    ) {
        val constraintSet = ConstraintSet()

        for ((index, imageButton) in imageButtons.withIndex()) {
            constraintSet.constrainWidth(imageButton.id, IMAGE_BUTTON_WIDTH.getDPPixelValue())
            constraintSet.constrainHeight(imageButton.id, IMAGE_BUTTON_HEIGHT.getDPPixelValue())

            if (isFirstElementOfRow(index)) {
                // Connects the first and last element of each row to the start and end of the parent respectively.
                constrainFirstElementOfRow(imageButton, constraintSet)
                constrainLastElementOfRow(index, imageButtons, constraintSet, alignCenter)
            }

            if (isNotLastItemOfRow(index, imageButtons.size)) {
                chainElementHorizontally(
                    imageButton.id,
                    imageButtons[index + 1].id,
                    constraintSet,
                )
            } else {
                constraintSet.applyTo(
                    innerContainers[getInnerContainerIndex(index)],
                )
            }
        }
    }

    private fun chainElementHorizontally(
        startElementId: Int,
        endElementId: Int,
        constraintSet: ConstraintSet,
    ) {
        constraintSet.connect(
            startElementId,
            ConstraintSet.END,
            endElementId,
            ConstraintSet.START,
            0,
        )

        constraintSet.connect(
            endElementId,
            ConstraintSet.START,
            startElementId,
            ConstraintSet.END,
            0,
        )
    }

    private fun constrainFirstElementOfRow(
        imageButton: VerticalImageButton,
        constraintSet: ConstraintSet,
    ) {
        constraintSet.connect(
            imageButton.id,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START,
            0,
        )
    }

    private fun constrainLastElementOfRow(
        index: Int,
        imageButtons: List<VerticalImageButton>,
        constraintSet: ConstraintSet,
        alignCenter: Boolean,
    ) {
        if (isNotLastElementOfList(index + MAX_BUTTONS_PER_ROW - 1, imageButtons.size)) {
            constraintSet.connect(
                imageButtons[index + MAX_BUTTONS_PER_ROW - 1].id,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END,
                0,
            )
        } else {
            if (alignCenter) {
                constraintSet.connect(
                    imageButtons.last().id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END,
                    0,
                )
            } else {
                constraintSet.create(R.id.guideline, ConstraintSet.VERTICAL_GUIDELINE)
                constraintSet.setGuidelinePercent(
                    R.id.guideline,
                    ((imageButtons.size % MAX_BUTTONS_PER_ROW).toDouble() / MAX_BUTTONS_PER_ROW).toFloat(),
                )
                constraintSet.connect(
                    imageButtons.last().id,
                    ConstraintSet.END,
                    if (imageButtons.size % MAX_BUTTONS_PER_ROW != 0) R.id.guideline else ConstraintSet.PARENT_ID,
                    ConstraintSet.END,
                    0,
                )
            }
        }
    }

    private fun getInnerContainer(
        context: Context,
        wrapperContainer: ConstraintLayout,
        constraintSet: ConstraintSet,
        topContainer: ConstraintLayout? = null,
    ): ConstraintLayout {
        val containerLayout = ConstraintLayout(context)
        containerLayout.id = View.generateViewId()
        wrapperContainer.addView(containerLayout)

        constraintSet.connect(
            containerLayout.id,
            ConstraintSet.START,
            ConstraintSet.PARENT_ID,
            ConstraintSet.START,
            NO_MARGIN,
        )
        constraintSet.connect(
            containerLayout.id,
            ConstraintSet.END,
            ConstraintSet.PARENT_ID,
            ConstraintSet.END,
            NO_MARGIN,
        )

        if (topContainer != null) {
            constraintSet.connect(
                containerLayout.id,
                ConstraintSet.TOP,
                topContainer.id,
                ConstraintSet.BOTTOM,
                DEFAULT_TOP_MARGIN.getDPPixelValue(),
            )
        } else {
            constraintSet.connect(
                containerLayout.id,
                ConstraintSet.TOP,
                ConstraintSet.PARENT_ID,
                ConstraintSet.TOP,
                DEFAULT_SPACING.getDPPixelValue(),
            )
        }

        constraintSet.constrainWidth(containerLayout.id, 0)
        constraintSet.constrainHeight(containerLayout.id, ConstraintSet.WRAP_CONTENT)
        constraintSet.applyTo(containerLayout)
        return containerLayout
    }

    private fun getInnerContainerIndex(index: Int): Int {
        return (index / MAX_BUTTONS_PER_ROW)
    }

    private fun isFirstElementOfRow(index: Int): Boolean {
        return index % MAX_BUTTONS_PER_ROW == 0
    }

    private fun isNotLastElementOfList(index: Int, listSize: Int): Boolean {
        return index < listSize - 1
    }

    private fun isNotLastItemOfRow(index: Int, listSize: Int): Boolean {
        val isNotLastElementOfList = isNotLastElementOfList(index, listSize)
        val isNotLastElementOfRow = (index + 1) % MAX_BUTTONS_PER_ROW != 0
        return isNotLastElementOfRow && isNotLastElementOfList
    }
}

private const val MAX_BUTTONS_PER_ROW = 4
private const val IMAGE_BUTTON_WIDTH = 80
private const val IMAGE_BUTTON_HEIGHT = 100
private const val DEFAULT_TOP_MARGIN = 15
private const val NO_MARGIN = 0
private const val EXTRA_RADIUS = 5
private const val DEFAULT_SPACING = 10
