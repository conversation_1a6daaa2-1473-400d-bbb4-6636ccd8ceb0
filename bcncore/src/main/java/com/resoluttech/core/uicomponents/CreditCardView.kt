package com.resoluttech.core.uicomponents

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import com.braintreepayments.cardform.view.CardForm
import com.google.android.material.textfield.TextInputEditText
import com.resoluttech.bcncore.R
import java.time.YearMonth

class CreditCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : CardView(context, attrs, defStyleAttr) {
    private val cardForm: CardForm
    private val nickNameEditText: TextInputEditText
    private val expandedStateView: ConstraintLayout

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.credit_card_form, this, true)
        cardForm = view.findViewById(R.id.cardForm)
        nickNameEditText = view.findViewById(R.id.nickname_edit_text)
        expandedStateView = view.findViewById(R.id.expanded_view)
    }

    fun clear() {
        cardForm.cardEditText.text?.clear()
        cardForm.cvvEditText.text?.clear()
        cardForm.expirationDateEditText.text?.clear()
        nickNameEditText.text?.clear()
        clearError()
    }

    private fun clearError() {
        cardForm.cardEditText.setError(null)
        cardForm.cvvEditText.setError(null)
        cardForm.expirationDateEditText.setError(null)
    }
}

data class EnteredCardData(
    val cardNumber: Long,
    val expiry: YearMonth,
    val cvv: Int,
    val nickName: String?,
)
