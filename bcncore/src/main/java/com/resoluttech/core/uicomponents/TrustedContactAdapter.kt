package com.resoluttech.core.uicomponents

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.listview.ListAdapter
import java.util.Locale
import java.util.UUID

class TrustedContactAdapter(
    private val listener: TrustedContactsListListener,
    private val listOfTrustedContacts: List<TrustedContact>,
) : ListAdapter<TrustedContact, RecyclerView.ViewHolder>(listOfTrustedContacts) {

    interface TrustedContactsListListener {
        fun onTrustedContactRemoved(trustedContactId: UUID, name: String)
    }

    override fun filter(query: String) {
        // Default action is already handled
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val item = listOfTrustedContacts[position]
        val viewHolder = (holder as TrustedContactViewHolder)
        viewHolder.apply {
            numbersTV.text = String.format(Locale.getDefault(), "%d", position.inc())
            val name = if (item.lastName != null) {
                viewHolder.itemView.context.getString(
                    R.string.profileUserDisplayName,
                    item.firstName,
                    item.lastName,
                )
            } else {
                item.firstName
            }
            nameTV.text = name
            phoneNumberTV.text = item.phoneNumber.getFormattedPhoneNumber()
            if (item.emailId != null) {
                emailIdTV.visibility = View.VISIBLE
                emailIdTV.text = item.emailId!!.value
            } else {
                emailIdTV.visibility = View.GONE
            }
            if (listOfTrustedContacts.size < 3) {
                removeIV.visibility = View.GONE
            } else {
                removeIV.visibility = View.VISIBLE
            }
            removeIV.setOnClickListener {
                listener.onTrustedContactRemoved(item.id, name)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                TrustedContactViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_trusted_contact_sign_up, parent, false),
                )
            }
            else -> {
                throw IllegalStateException("Unknown View Type")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return TYPE_ITEM
    }

    private class TrustedContactViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val numbersTV: TextView = itemView.findViewById(R.id.numbers_tv)
        val nameTV: TextView = itemView.findViewById(R.id.name_tv)
        val phoneNumberTV: TextView = itemView.findViewById(R.id.phone_number_tv)
        val emailIdTV: TextView = itemView.findViewById(R.id.email_id_tv)
        val removeIV: ImageView = itemView.findViewById(R.id.remove_iv)
    }
}

const val TYPE_ITEM: Int = 1
