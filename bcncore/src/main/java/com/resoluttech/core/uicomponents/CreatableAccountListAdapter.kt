package com.resoluttech.core.uicomponents

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter
import java.util.UUID
import `in`.aabhasjindal.otptextview.R as OTPTextViewResource

class CreatableAccountListAdapter(private val items: List<CreatableAccountDropdownItem>, private val config: AccountItemConfig) :
    AbstractTextDropDownAdapter<CreatableAccountListAdapter.CreatableAccountDropdownItem>(items) {

    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: AccountViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.account_spinner_dropdown_item, parent, false)
            viewHolder =
                AccountViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as AccountViewHolder
        }
        viewHolder.apply {
            when (config) {
                AccountItemConfig.ACCOUNT_NAME_AND_BALANCE -> {
                    showAccountWithBalance(position)
                }
                AccountItemConfig.ACCOUNT_NAME -> {
                    showTopLevelAccountName(position)
                }
            }
        }
        return view
    }

    private fun AccountViewHolder.showAccountWithBalance(
        position: Int,
    ) {
        when (val item = items[position]) {
            is CreatableAccountDropdownItem.Account -> {
                createAnotherAccountTV.visibility = View.GONE
                accountLayout.visibility = View.VISIBLE
                titleTV.text = item.displayName
                subtitleTV.text = item.balance
            }
            is CreatableAccountDropdownItem.CreateAccount -> {
                handleCreateAccount(this)
            }
        }
    }

    private fun handleCreateAccount(accountViewHolder: AccountViewHolder) {
        accountViewHolder.createAnotherAccountTV.visibility = View.VISIBLE
        accountViewHolder.accountLayout.visibility = View.GONE
    }

    private fun AccountViewHolder.showTopLevelAccountName(
        position: Int,
    ) {
        when (val item = items[position]) {
            is CreatableAccountDropdownItem.Account -> {
                createAnotherAccountTV.visibility = View.GONE
                titleTV.visibility = View.VISIBLE
                titleTV.text = item.displayName
                subtitleTV.visibility = View.GONE
            }
            is CreatableAccountDropdownItem.CreateAccount -> {
                handleCreateAccount(this)
            }
        }
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.home_account_spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            when (config) {
                AccountItemConfig.ACCOUNT_NAME_AND_BALANCE -> {
                    // Do Nothing
                }
                AccountItemConfig.ACCOUNT_NAME -> {
                    accountName.setTextColor(
                        view?.resources!!.getColor(
                            OTPTextViewResource.color.white,
                            null,
                        ),
                    )
                }
            }
            when (val item = items[position]) {
                is CreatableAccountDropdownItem.Account -> {
                    accountName.text = item.displayName
                }
                is CreatableAccountDropdownItem.CreateAccount -> {
                    // do nothing
                }
            }
        }
        return view
    }

    private class AccountViewHolder(view: View) {
        var titleTV: TextView = view.findViewById(R.id.account_holder_name)
        var subtitleTV: TextView = view.findViewById(R.id.account_number)
        var createAnotherAccountTV: TextView = view.findViewById(R.id.create_account_tv)
        var accountLayout: LinearLayout = view.findViewById(R.id.account_layout)
    }

    private class ViewHolder(view: View) {
        var accountName: TextView = view.findViewById(R.id.dropdown_item_text_view)
    }

    companion object {
        enum class AccountItemConfig {
            /**
             *  Displays both account name and balance
             */
            ACCOUNT_NAME_AND_BALANCE,

            /**
             *  Displays account name in toolbar
             */
            ACCOUNT_NAME,
        }
    }

    sealed class CreatableAccountDropdownItem {
        data class Account(
            val displayName: String,
            val balance: String?,
            val accountId: UUID,
        ) : CreatableAccountDropdownItem()

        object CreateAccount : CreatableAccountDropdownItem()
    }
}
