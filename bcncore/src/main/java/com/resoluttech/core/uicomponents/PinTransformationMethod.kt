package com.resoluttech.core.uicomponents

import android.text.method.PasswordTransformationMethod
import android.view.View

// Thanks : https://stackoverflow.com/a/14052431/5163725
class PinTransformationMethod : PasswordTransformationMethod() {

    override fun getTransformation(source: CharSequence, view: View): CharSequence {
        return PINSourceTransformation(source)
    }

    class PINSourceTransformation(private val source: CharSequence) : CharSequence {
        override val length: Int
            get() = source.length

        override fun get(index: Int): Char {
            return '*'
        }

        override fun subSequence(startIndex: Int, endIndex: Int): CharSequence {
            return source.subSequence(startIndex, endIndex)
        }
    }
}
