package com.resoluttech.core.uicomponents

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter
import java.util.UUID

class AccountDropdownAdapter(private val items: List<AccountDropdownItem>) :
    AbstractTextDropDownAdapter<AccountDropdownItem>(items) {

    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: AccountViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.account_spinner_dropdown_item, parent, false)
            viewHolder =
                AccountViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as AccountViewHolder
        }
        viewHolder.apply {
            showAccountName(position, view)
        }
        return view
    }

    private fun AccountViewHolder.showAccountName(
        position: Int,
        view: View?,
    ) {
        showTopLevelAccountName(position)
        titleTV.setTextColor(view?.resources!!.getColor(R.color.titleTextColor, null))
        subtitleTV.setTextColor(view.resources.getColor(R.color.titleTextColor, null))
    }

    private fun AccountViewHolder.showTopLevelAccountName(
        position: Int,
    ) {
        titleTV.text = items[position].displayName
        subtitleTV.visibility = View.GONE
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.home_account_spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            accountName.setTextColor(
                view?.resources!!.getColor(
                    R.color.titleTextColor,
                    null,

                ),
            )
            dropdownIcon.setImageResource(R.drawable.ic_arrow_account_drop_down)

            accountName.text = items[position].displayName
        }
        return view
    }

    private class AccountViewHolder(view: View) {
        var titleTV: TextView = view.findViewById(R.id.account_holder_name)
        var subtitleTV: TextView = view.findViewById(R.id.account_number)
    }

    private class ViewHolder(view: View) {
        var accountName: TextView = view.findViewById(R.id.dropdown_item_text_view)
        var dropdownIcon: ImageButton = view.findViewById(R.id.dropdown_icon)
    }
}

data class AccountDropdownItem(val displayName: String, val balance: String?, val accountId: UUID)
