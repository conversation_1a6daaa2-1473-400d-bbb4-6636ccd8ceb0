package com.resoluttech.core.rpcexceptionhandlers

/**
 * This defines the type of error that can occur.
 * [SNACKBAR] type show the error in bottom using snackbar view.
 * [DIALOG] type error shows the error message in a dialog view.
 * [BANNER] type error shows the error message in a banner view. This should only be used for
 * network error.
 * */
enum class ErrorType {
    SNACKBAR,
    DIALOG,
    BANNER,
}

/**
 * This data class models the error and is being used for wrapping error type and message together.
 *
 * @param type: Sets the [ErrorType].
 * @param errorMessage: The error message that needs to be displayed for user.
 * */
data class UIError(val type: ErrorType, val errorTitle: String, val errorMessage: String, val errorCode: Int? = null)
