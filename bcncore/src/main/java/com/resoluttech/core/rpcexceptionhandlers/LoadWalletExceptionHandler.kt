package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.transfers.ConfirmLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.CreateLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.LoadMoneyFromMPGSRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes.Companion.FORCE_USER_OUT_OF_FLOW
import com.resoluttech.core.utils.getAmountTooLargeString
import com.resoluttech.core.utils.getAmountTooLessString

class LoadWalletExceptionHandler {
    companion object {
        fun getCreateLoadMoneyFromMPGSErrorMessage(
            context: Context,
            error: CreateLoadMoneyFromMPGSRPC.Error,
            accountName: String? = null,
        ): UIError {
            return when (error) {
                is CreateLoadMoneyFromMPGSRPC.Error.InvalidAccountId -> {
                    throw IllegalArgumentException("We are unable to process this transaction as account id is not valid")
                }
                is CreateLoadMoneyFromMPGSRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.AmountTooLarge -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooLarge),
                        context.getAmountTooLargeString(error.maximumAllowedAmount),
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.AmountTooLess -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooSmall),
                        context.getAmountTooLessString(error.minimumAllowedAmount),
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.BalanceLimitWillExceed -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleLoadWalletBalanceLimitExceed),
                        context.getString(R.string.alertMessageLoadWalletBalanceLimitExceed),
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.AccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleLoadWalletAccountDeactivated),
                        context.getString(
                            R.string.alertMessageLoadWalletAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is CreateLoadMoneyFromMPGSRPC.Error.CurrencyMismatch -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCurrencyMismatch),
                        context.getString(R.string.alertMessageInvalidCurrencyMismatch),
                    )
                }
            }
        }

        fun getLoadMoneyFromMPGSErrorMessage(
            context: Context,
            error: LoadMoneyFromMPGSRPC.Error,
            accountName: String?,
        ): UIError {
            return when (error) {
                is LoadMoneyFromMPGSRPC.Error.InvalidRecordId -> {
                    throw IllegalArgumentException("Unable to transfer money as record id is not valid")
                }
                is LoadMoneyFromMPGSRPC.Error.BalanceLimitWillExceed -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleLoadWalletBalanceLimitExceed),
                        context.getString(R.string.alertMessageLoadWalletBalanceLimitExceed),
                    )
                }
                is LoadMoneyFromMPGSRPC.Error.TransactionConfirmationTimeout -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionFeeExpired),
                        context.getString(R.string.alertMessageTransactionFeeExpired),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is LoadMoneyFromMPGSRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is LoadMoneyFromMPGSRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is LoadMoneyFromMPGSRPC.Error.MpgsUnreachable -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleLoadWalletMPGSUnreachable),
                        context.getString(R.string.alertMessageLoadWalletMPGSUnreachable),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is LoadMoneyFromMPGSRPC.Error.AccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleLoadWalletAccountDeactivated),
                        context.getString(
                            R.string.alertMessageLoadWalletAccountDeactivated,
                            accountName,
                        ),
                    )
                }
            }
        }

        fun getConfirmLoadMoneyFromMPGSErrorMessage(error: ConfirmLoadMoneyFromMPGSRPC.Error): UIError {
            when (error) {
                is ConfirmLoadMoneyFromMPGSRPC.Error.InvalidRecordId -> {
                    throw IllegalArgumentException("Unable to transfer money as record id is not valid")
                }
            }
        }
    }
}
