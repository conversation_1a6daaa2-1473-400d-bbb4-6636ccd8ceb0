package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.resoluttech.bcncore.R

class TransactionRPCExceptionHandler {
    companion object {
        fun getTransactionRPCExceptionMessage(
            context: Context,
            error: GetTransactionsRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetTransactionsRPC.Error.InvalidAccountId -> {
                    throw IllegalAccessException("We are unable to process this transaction as account id is not valid")
                }
                is GetTransactionsRPC.Error.AccountDeactivated -> UIError(
                    ErrorType.SNACKBAR,
                    context.getString(R.string.alertTitleGenericError),
                    context.getString(R.string.alertMessageGenericError),
                )
            }
        }
    }
}
