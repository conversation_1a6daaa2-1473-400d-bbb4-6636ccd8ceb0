package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.transfers.ConfirmAccountToAccountRequestRPC
import com.resoluttech.bcn.transfers.CreateAccountToAccountRequestRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.formatToAmount
import com.resoluttech.core.utils.getAmountTooLargeString
import com.resoluttech.core.utils.getAmountTooLessString
import com.resoluttech.core.utils.getUserFacingValue

class AccountToAccountRPCExceptionHandler {
    companion object {
        fun getCreateAccountToAccountRPCErrorMessage(
            context: Context,
            error: CreateAccountToAccountRequestRPC.Error,
            creditedAccountName: String,
            debitedAccountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is CreateAccountToAccountRequestRPC.Error.InvalidSendingAccountId -> {
                    throw IllegalArgumentException("InvalidSendingAccountId")
                }
                is CreateAccountToAccountRequestRPC.Error.InvalidReceivingAccountId -> {
                    throw IllegalArgumentException("InvalidReceivingAccountId")
                }
                is CreateAccountToAccountRequestRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.AmountTooLarge -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooLarge),
                        context.getAmountTooLargeString(error.maximumAllowedAmount),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.ReceivingAccountWouldCrossLimit -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.AmountTooLess -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooSmall),
                        context.getAmountTooLessString(error.minimumAllowedAmount),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.ReceivingAccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceivingAccountDeactivated),
                        context.getString(R.string.alertMessageReceivingAccountDeactivated, creditedAccountName),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.SendingAccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(R.string.alertMessageSendingAccountDeactivated, debitedAccountName),
                    )
                }
                is CreateAccountToAccountRequestRPC.Error.SendingAccountAndAmountCurrencyMismatch -> {
                    throw IllegalArgumentException("Sending Account And Amount Currency Mismatch")
                }
                is CreateAccountToAccountRequestRPC.Error.UnableToPerformExchange -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        fun getConfirmAccountToAccountRPCErrorMessage(
            context: Context,
            error: ConfirmAccountToAccountRequestRPC.Error,
            accountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ConfirmAccountToAccountRequestRPC.Error.InvalidRecordId -> {
                    throw IllegalArgumentException("InvalidRecordId")
                }
                is ConfirmAccountToAccountRequestRPC.Error.ConfirmationTimeout -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionFeeExpired),
                        context.getString(R.string.alertMessageTransactionFeeExpired),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.ReceivingAccountWouldCrossLimit -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.ReceivingAccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceivingAccountDeactivated),
                        context.getString(R.string.alertMessageReceivingAccountDeactivated, accountName),
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.SendingAccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(R.string.alertMessageSendingAccountDeactivated, accountName),
                    )
                }
                is ConfirmAccountToAccountRequestRPC.Error.UnableToPerformExchange -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }
    }
}
