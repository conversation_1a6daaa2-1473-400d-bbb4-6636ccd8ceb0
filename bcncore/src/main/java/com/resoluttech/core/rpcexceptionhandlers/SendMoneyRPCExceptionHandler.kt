package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.ConfirmSendMoneyToExternalUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToBCNUserRPC
import com.resoluttech.bcn.transfers.CreateSendMoneyToExternalUserRequestRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromAccountIdRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromPhoneNumberRPC
import com.resoluttech.bcn.transfers.GetBCNRecipientFromUserIdRPC
import com.resoluttech.bcn.transfers.GetSendMoneyToExternalUserCounterpartiesRPC
import com.resoluttech.bcn.transfers.LookupRecipientAtCounterpartyRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.sendmoney.SendMoneyConterparty
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.formatToAmount
import com.resoluttech.core.utils.getAmountTooLargeString
import com.resoluttech.core.utils.getAmountTooLessString
import com.resoluttech.core.utils.getUserFacingValue
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import timber.log.Timber

class SendMoneyRPCExceptionHandler {
    companion object {

        fun getUserFromUserIdErrorMessage(
            error: GetBCNRecipientFromUserIdRPC.Error,
            context: Context,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetBCNRecipientFromUserIdRPC.Error.InvalidRecipientId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidUser),
                        context.getString(R.string.alertMessageInvalidUser),
                    )
                }
                is GetBCNRecipientFromUserIdRPC.Error.SenderAndRecipientAreSame -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderRecipientSame),
                        context.getString(R.string.alertMessageTransactionSenderRecipientSame),
                    )
                }
            }
        }

        fun getSendMoneyToExternalUserRPCErrorMessage(
            context: Context,
            error: CreateSendMoneyToExternalUserRequestRPC.Error,
            accountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is CreateSendMoneyToExternalUserRequestRPC.Error.AmountTooLarge -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooLarge),
                        context.getAmountTooLargeString(error.maximumAllowedAmount),
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.AmountTooLess -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooSmall),
                        context.getAmountTooLessString(error.minimumAllowedAmount),
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountLimit),
                        context.getString(R.string.alertMessageTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.InvalidSenderAccountId -> {
                    throw DeveloperErrorException("Sender account id is not valid")
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.AccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(
                            R.string.alertMessageSendingAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.CurrencyMismatchForSenderAccountAndAmount -> {
                    throw DeveloperErrorException("Currency Mismatch For Sender Account And Amount")
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.CurrencyMismatchForSenderAccountAndCounterparty -> {
                    throw DeveloperErrorException("Currency Mismatch For Sender Account And Counterparty")
                }
                is CreateSendMoneyToExternalUserRequestRPC.Error.MissingRecipientName -> {
                    throw DeveloperErrorException("Recipient name was set when the counterparty does not support lookup.")
                }
            }
        }

        fun getConfirmSendMoneyToUserRPCErrorMessage(
            context: Context,
            error: ConfirmSendMoneyToExternalUserRPC.Error,
            accountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ConfirmSendMoneyToExternalUserRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.TransactionFeeConfirmationTimeout -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionFeeExpired),
                        context.getString(R.string.alertMessageTransactionFeeExpired),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.InvalidRecordId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("Unable to transfer money to user as record id is not valid")
                    throw DeveloperErrorException("Unable to transfer money to user as record id is not valid")
                }

                is ConfirmSendMoneyToExternalUserRPC.Error.PublicRemarkCharacterLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInvalidNarration),
                        context.getString(R.string.alertMessageTransactionInvalidNarration),
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.InactiveSenderAccount -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(
                            R.string.alertMessageSendingAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is ConfirmSendMoneyToExternalUserRPC.Error.SendMoneyToCounterpartyFailed, is ConfirmSendMoneyToExternalUserRPC.Error.UnableToReachCounterparty -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        fun getSendMoneyToUserRPCErrorMessage(
            context: Context,
            error: ConfirmSendMoneyToBCNUserRPC.Error,
            user: String,
            accountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ConfirmSendMoneyToBCNUserRPC.Error.InvalidRecordId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("We are unable to transfer money to user as record id is not valid")
                    throw DeveloperErrorException("We are unable to transfer money to user as record id is not valid")
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.TransactionFeeConfirmationTimeout -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionFeeExpired),
                        context.getString(R.string.alertMessageTransactionFeeExpired),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.InactiveSenderAccount -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(
                            R.string.alertMessageSendingAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.InactiveRecipient -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInactiveRecipient),
                        context.getString(R.string.alertMessageTransactionInactiveRecipient, user),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.ReceivingAccountWouldCrossLimit -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.SenderPeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.SenderMonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.SenderIsAgent -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderIsAgent),
                        context.getString(R.string.alertMessageTransactionSenderIsAgent),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.RecipientIsAgent -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderIsAgent),
                        context.getString(R.string.alertMessageTransactionSenderIsAgent),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.UnableToPerformExchange -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUnableToPerformTransaction),
                        context.getString(R.string.alertMessageUnableToPerformTransaction),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.RecipientPeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionsLimit),
                        context.getString(R.string.alertMessageReceiverTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmSendMoneyToBCNUserRPC.Error.RecipientMonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        fun getSendMoneyToExternalUserDestinationErrorMessage(
            error: GetSendMoneyToExternalUserCounterpartiesRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            when (error) {
                is GetSendMoneyToExternalUserCounterpartiesRPC.Error.NotSupported -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("Client does not have access to money transfer to user feature.")
                    throw LeoUnsupportedClientException("Client does not have access to money transfer to user feature.")
                }
            }
        }

        fun getUserFromPhoneNumberErrorMessage(
            error: GetBCNRecipientFromPhoneNumberRPC.Error,
            context: Context,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetBCNRecipientFromPhoneNumberRPC.Error.InvalidRecipientPhoneNumber -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidUser),
                        context.getString(R.string.alertMessageInvalidUser),
                    )
                }
                is GetBCNRecipientFromPhoneNumberRPC.Error.SenderAndRecipientAreSame -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderRecipientSame),
                        context.getString(R.string.alertMessageTransactionSenderRecipientSame),
                    )
                }
            }
        }

        fun getUserFromAccountIdErrorMessage(
            error: GetBCNRecipientFromAccountIdRPC.Error,
            context: Context,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetBCNRecipientFromAccountIdRPC.Error.InvalidRecipientId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidUser),
                        context.getString(R.string.alertMessageInvalidUser),
                    )
                }
                is GetBCNRecipientFromAccountIdRPC.Error.SenderAndRecipientAreSame -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderRecipientSame),
                        context.getString(R.string.alertMessageTransactionSenderRecipientSame),
                    )
                }
            }
        }

        fun lookupRecipientAtCounterpartyRPC(
            error: LookupRecipientAtCounterpartyRPC.Error,
            context: Context,
            sendMoneyCounterparty: SendMoneyConterparty,
            identifier: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is LookupRecipientAtCounterpartyRPC.Error.InvalidCounterpartyId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("Counterparty identified by counterpartyId is unknown.")
                    throw DeveloperErrorException("Counterparty identified by counterpartyId is unknown.")
                }
                is LookupRecipientAtCounterpartyRPC.Error.LookupNotSupported -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("Counterparty identified by `counterpartyId` doesn't support lookups.")
                    throw DeveloperErrorException("Counterparty identified by `counterpartyId` doesn't support lookups.")
                }
                is LookupRecipientAtCounterpartyRPC.Error.RecipientNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleRecipientNotFound),
                        context.getString(
                            R.string.alertMessageRecipientNotFound,
                            sendMoneyCounterparty.displayName,
                            identifier,
                        ),
                    )
                }
                is LookupRecipientAtCounterpartyRPC.Error.CounterpartyServerError -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUnableToReachCounterparty),
                        context.getString(R.string.alertMessageUnableToReachCounterparty),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is LookupRecipientAtCounterpartyRPC.Error.UnableToReachCounterparty -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUnableToReachCounterparty),
                        context.getString(R.string.alertMessageUnableToReachCounterparty),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        fun getCreateSendMoneyToBCNUserErrorMessage(
            error: CreateSendMoneyToBCNUserRPC.Error,
            context: Context,
            accountName: String,
            recipientDisplayName: String,
        ): UIError {
            return when (error) {
                is CreateSendMoneyToBCNUserRPC.Error.AmountTooLarge -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooLarge),
                        context.getAmountTooLargeString(error.maximumAllowedAmount),
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.AmountTooLess -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountTooSmall),
                        context.getAmountTooLessString(error.minimumAllowedAmount),
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.SenderMonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.SenderPeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.CurrencyMismatchForSenderAccountAndAmount -> {
                    throw DeveloperErrorException("Currency Mismatch For Sender Account And Amount")
                }
                is CreateSendMoneyToBCNUserRPC.Error.InvalidSenderAccountId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("We are unable to transfer money to user as account id is not valid")
                    throw DeveloperErrorException("Sender account id is not valid")
                }
                is CreateSendMoneyToBCNUserRPC.Error.InvalidRecipientId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("We are unable to transfer money to recipient as account id is not valid")
                    throw DeveloperErrorException("Recipient account id is not valid")
                }
                is CreateSendMoneyToBCNUserRPC.Error.SenderAccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(
                            R.string.alertMessageSendingAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.SenderAndRecipientAreSame -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderRecipientSame),
                        context.getString(R.string.alertMessageTransactionSenderRecipientSame),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.ReceivingAccountWouldCrossLimit -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.SenderIsAgent -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderIsAgent),
                        context.getString(R.string.alertMessageTransactionSenderIsAgent),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.RecipientIsAgent -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionSenderIsAgent),
                        context.getString(R.string.alertMessageTransactionSenderIsAgent),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.InactiveRecipient -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInactiveRecipient),
                        context.getString(
                            R.string.alertMessageTransactionInactiveRecipient,
                            recipientDisplayName,
                        ),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.UnableToPerformExchange -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUnableToPerformTransaction),
                        context.getString(R.string.alertMessageUnableToPerformTransaction),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.RecipientPeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateSendMoneyToBCNUserRPC.Error.RecipientMonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleReceiverTransactionAmountLimit),
                        context.getString(R.string.alertMessageReceiverTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }
    }

    private class DeveloperErrorException(msg: String) : Exception(msg)
}

private const val TAG = "SendMoneyRPCExceptionHandler"
