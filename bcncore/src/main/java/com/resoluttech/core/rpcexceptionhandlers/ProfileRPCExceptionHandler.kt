package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.profile.ArchiveBcnUserRPC
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPC
import com.resoluttech.bcn.profile.ChangeProfileImageRPC
import com.resoluttech.bcn.profile.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.GetTrustedContactsRPC
import com.resoluttech.bcn.profile.RemoveTrustedContactRPC
import com.resoluttech.bcn.profile.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.ResendVerificationEmailRPC
import com.resoluttech.bcn.profile.UpdateEmailIdRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes
import timber.log.Timber

class ProfileRPCExceptionHandler {

    companion object {
        fun getChangeDefaultAccountErrorMessage(
            context: Context,
            error: ChangeDefaultAccountRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ChangeDefaultAccountRPC.Error.IncorrectAccountId -> {
                    Timber.tag(ErrorHandler.TAG)
                        .e("We are unable to process this request as account id is incorrect.")
                    throw IllegalAccessException("We are unable to process this request as account id is incorrect")
                }
                is ChangeDefaultAccountRPC.Error.AccountDisabled -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleAccountAlreadyDisabled),
                        context.getString(R.string.alertMessageAccountAlreadyDisabled),
                    )
                }
                is ChangeDefaultAccountRPC.Error.CurrencyMismatch -> {
                    throw IllegalArgumentException("Change Default Account RPC Currency Mismatch")
                }
            }
        }

        fun getChangeProfileImageErrorMessage(
            error: ChangeProfileImageRPC.Error,
        ) {
            ErrorHandler.logRPCError(error)
            when (error) {
                is ChangeProfileImageRPC.Error.InvalidProfileImageId -> {
                    throw IllegalArgumentException("The upload image id is invalid.")
                }
                is ChangeProfileImageRPC.Error.IncorrectProfileImageResolution -> {
                    // Considering as developer because irrespective of user's image selection it
                    // should be downscaled to requirement.
                    throw IllegalArgumentException("The provided Resolution[${error.foundHeight} , ${error.foundWidth}] is incorrect.")
                }
            }
        }

        fun getDeactivateBcnUserErrorMessage(
            context: Context,
            error: ArchiveBcnUserRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ArchiveBcnUserRPC.Error.WalletBalanceNotZero -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleNonZeroAccount),
                        context.getString(R.string.alertMessageNonZeroAccount),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ArchiveBcnUserRPC.Error.UserAlreadyArchived -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleAccountAlreadyClosed),
                        context.getString(R.string.alertMessageAccountAlreadyClosed),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ArchiveBcnUserRPC.Error.IncorrectPassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleIncorrectPassword),
                        context.getString(R.string.alertMessageIncorrectPassword),
                    )
                }
                is ArchiveBcnUserRPC.Error.PendingTransactionsFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePendingTransactions),
                        context.getString(R.string.alertMessagePendingTransactions),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ArchiveBcnUserRPC.Error.UserIsAgent -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserIsAgent),
                        context.getString(R.string.alertMessageUserIsAgent),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        fun handleUpdateEmailIDRPCErrors(context: Context, error: UpdateEmailIdRPC.Error): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is UpdateEmailIdRPC.Error.EmailIdSameAsExisting -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleEmailIdSameAsExisting),
                        context.getString(R.string.alertMessageEmailIdSameAsExisting),
                    )
                }
            }
        }

        fun handleResendVerificationEmailRPCErrors(
            context: Context,
            error: ResendVerificationEmailRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ResendVerificationEmailRPC.Error.EmailAlreadyVerfied -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleEmailIdAlreadyVerified),
                        context.getString(R.string.alertMessageEmailIdAlreadyVerified),
                    )
                }
                is ResendVerificationEmailRPC.Error.EmailNotAdded -> {
                    throw IllegalStateException("Email Id cannot be null for sending verification mail")
                }
            }
        }

        fun handleGetTrustedContactsRPCErrors(
            error: GetTrustedContactsRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            throw IllegalStateException(error.code)
        }

        fun handleRemoveTrustedContactRPCErrors(
            error: RemoveTrustedContactRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            when (error) {
                is RemoveTrustedContactRPC.Error.MinTrustedContactsCannotBeRemoved -> {
                    throw IllegalStateException("MinTrustedCannotBeRemoved is not possible because delete button is disabled")
                }
                is RemoveTrustedContactRPC.Error.InvalidTrustedContactId -> {
                    throw IncorrectIdException("InvalidTrustedContactId")
                }
            }
        }

        fun handleRequestAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: RequestAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is RequestAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.TrustedContactAlreadyAdded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTrustedContactAlreadyAdded),
                        context.getString(R.string.alertMessageTrustedContactAlreadyAdded),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.TrustedContactsLimitReached -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTrustedContactLimitReached),
                        context.getString(R.string.alertMessageTrustedContactLimitReached),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.UserIsTrustedContact -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserIsTrustedContact),
                        context.getString(R.string.alertMessageUserIsTrustedContact),
                    )
                }
            }
        }

        fun handleResendAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: ResendAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ResendAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ResendAddTrustedContactOTPRPC.Error.IncorrectId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ResendAddTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is ResendAddTrustedContactOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
            }
        }

        fun handleConfirmAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: ConfirmAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ConfirmAddTrustedContactOTPRPC.Error.IncorrectOtpId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ConfirmAddTrustedContactOTPRPC.Error.InvalidOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TrustedContactsLimitReached -> {
                    throw IllegalArgumentException("TrustedContactLimitReached not possible because add trusted contact is disabled")
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TrustedContactAlreadyAdded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTrustedContactAlreadyAdded),
                        context.getString(R.string.alertMessageTrustedContactAlreadyAdded),
                    )
                }
            }
        }
    }
}
