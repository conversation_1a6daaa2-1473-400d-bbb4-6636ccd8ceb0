package com.resoluttech.core.rpcexceptionhandlers

import com.resoluttech.bcn.transactions.AddPrivateRemarkRPC
import timber.log.Timber

class RemarkRPCExceptionHandler {
    companion object {
        fun getRemarkRPCErrorMessage(
            error: AddPrivateRemarkRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            when (error) {
                is AddPrivateRemarkRPC.Error.RemarkAlreadyPresent -> {
                    throw IllegalStateException("Private remark already added for this transaction")
                }
                is AddPrivateRemarkRPC.Error.PrivateRemarkNotSupported -> {
                    Timber.tag(TAG).e("Private remark not supported on client end")
                    throw IllegalStateException("Private remark not supported on client end")
                }
            }
        }
    }
}

private const val TAG = "RemarkRPCExceptionHandler"
