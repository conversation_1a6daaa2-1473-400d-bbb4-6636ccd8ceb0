package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetDocumentIdRPC
import com.resoluttech.bcn.document.GetUrlRPC
import com.resoluttech.bcn.profile.ChangePasswordRPC
import com.resoluttech.bcn.profile.RequestChangePasswordOTPRPC
import com.resoluttech.bcn.profile.ResendChangePasswordOTPRPC
import com.resoluttech.bcn.profile.UpdateKYCDataRPC
import com.resoluttech.bcn.signUpIn.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmForgotPasswordAnswersRPC
import com.resoluttech.bcn.signUpIn.ConfirmResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.GetForgotPasswordQuestionsRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.RemoveTrustedContactRPC
import com.resoluttech.bcn.signUpIn.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.RequestResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPC
import com.resoluttech.bcn.signUpIn.RequestSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignInOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ResetForgottenPasswordRPC
import com.resoluttech.bcn.signUpIn.SignInUserRPC
import com.resoluttech.bcn.signUpIn.SubmitKYCDataRPC
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signup.SetupPasswordVM
import com.resoluttech.core.kyc.KYCDataVM
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.ERROR_CODE_SIGN_IN_AGAIN
import com.resoluttech.core.utils.DialogCodes.Companion.FORCE_USER_OUT_OF_FLOW
import com.resoluttech.core.utils.DialogCodes.Companion.SIGN_UP_IN_SESSION_EXPIRED
import com.resoluttech.bcn.profile.VerifyKYCDataRPC as ProfileVerifyKYCDataRPC
import com.resoluttech.bcn.signUpIn.VerifyKYCDataRPC as SignUpInVerifyKYCDataRPC

class AuthExceptionHandler {
    companion object {

        fun getChangePasswordRPCErrorMessage(
            context: Context,
            error: RequestChangePasswordOTPRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is RequestChangePasswordOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is RequestChangePasswordOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestChangePasswordOTPRPC.Error.PasswordNotSecure -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePasswordWeak),
                        context.getString(R.string.alertMessagePasswordWeak),
                    )
                }
                is RequestChangePasswordOTPRPC.Error.IncorrectOldPassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleIncorrectCurrentPassword),
                        context.getString(R.string.alertMessageIncorrectCurrentPassword),
                    )
                }
                is RequestChangePasswordOTPRPC.Error.ReusedPassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleDuplicatePassword),
                        context.getString(R.string.alertMessageDuplicatePassword), // Does not specify last 5 passwords
                    )
                }
            }
        }

        fun getResendOTPChangePasswordRPCErrorMessage(
            context: Context,
            error: ResendChangePasswordOTPRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ResendChangePasswordOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ResendChangePasswordOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is ResendChangePasswordOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
                is ResendChangePasswordOTPRPC.Error.OtpNeverRequested -> {
                    throw IllegalStateException("Otp Never Requested: Request OTP before requesting resend.")
                }
            }
        }

        fun getChangePasswordRPCErrorMessage(
            context: Context,
            error: ChangePasswordRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ChangePasswordRPC.Error.IncorrectOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ChangePasswordRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ChangePasswordRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ChangePasswordRPC.Error.OtpNeverRequested -> {
                    throw IllegalStateException("Otp Never Requested: Request OTP before changing password.")
                }
            }
        }

        fun requestSignInOTPRPCExceptions(
            context: Context,
            error: RequestSignInOtpRPC.Error,
        ): UIError {
            return when (error) {
                is RequestSignInOtpRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestSignInOtpRPC.Error.PhoneNumberUnknown -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleAccountDoesNotExist),
                        context.getString(R.string.alertMessageAccountDoesNotExist),
                    )
                }
                is RequestSignInOtpRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is RequestSignInOtpRPC.Error.SignInTemporarilyBlocked -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSignInBlocked),
                        context.getString(R.string.alertMessageSignInBlocked),
                    )
                }
                is RequestSignInOtpRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        fun requestSignUpOTPRPCExceptions(
            context: Context,
            error: RequestSignUpOTPRPC.Error,
        ): UIError {
            return when (error) {
                is RequestSignUpOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestSignUpOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is RequestSignUpOTPRPC.Error.UserAlreadyExists -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserAlreadyExists),
                        context.getString(R.string.alertMessageUserAlreadyExists),
                    )
                }
                is RequestSignUpOTPRPC.Error.SignUpTemporarilyBlocked -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSignUpBlocked),
                        context.getString(R.string.alertMessageSignUpBlocked),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun confirmSignInOTPRPCExceptions(
            context: Context,
            error: ConfirmSignInOTPRPC.Error,
        ): UIError? {
            return when (error) {
                is ConfirmSignInOTPRPC.Error.IncorrectOtpId -> {
                    throw IncorrectIdException("The otp id is not valid.")
                }
                is ConfirmSignInOTPRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ConfirmSignInOTPRPC.Error.IncorrectOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ConfirmSignInOTPRPC.Error.TooManyRequests, is ConfirmSignInOTPRPC.Error.TooManyOtpAttempts -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTooManyRequests),
                        context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ConfirmSignInOTPRPC.Error.InactiveState -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCredentials),
                        context.getString(R.string.alertMessageInvalidCredentials),
                        DialogCodes.SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ConfirmSignInOTPRPC.Error.UserDisabled -> {
                    ErrorHandler.showForcedLogoutDialog(
                        context,
                        context.getString(R.string.alertMessageUserDisabled),
                        context.getString(R.string.alertTitleUserDisabled),
                    )
                    null
                }
                is ConfirmSignInOTPRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        fun confirmSignUpOTPRPCExceptions(
            context: Context,
            error: ConfirmSignUpOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ConfirmSignUpOTPRPC.Error.IncorrectOtpId -> {
                    throw IncorrectIdException("The otp id is not valid.")
                }
                is ConfirmSignUpOTPRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ConfirmSignUpOTPRPC.Error.IncorrectOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ConfirmSignUpOTPRPC.Error.UserAlreadyExists -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserAlreadyExists),
                        context.getString(R.string.alertMessageUserAlreadyExists),
                    )
                }
                is ConfirmSignUpOTPRPC.Error.InactiveState -> {
                    throw IllegalStateException("Try to update user data which is marked as inactive")
                }
                is ConfirmSignUpOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }

                is ConfirmSignUpOTPRPC.Error.TooManyOtpAttempts -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPLimitReached),
                        context.getString(R.string.alertMessageOTPLimitReached),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun submitKYCDataRPCException(
            error: SubmitKYCDataRPC.Error,
            context: Context,
        ): UIError {
            return when (error) {
                is SubmitKYCDataRPC.Error.PasswordValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleInvalidCredentials),
                        errorMessage = context.getString(R.string.alertMessageInvalidCredentials),
                        KYCDataVM.ERROR_CODE_TOKEN_EXPIRED,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidToken -> {
                    throw IllegalStateException("Received INVALID_TOKEN on SubmitKYCDataRPC")
                }
                is SubmitKYCDataRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidSignatureImageId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_SIGNATURE_IMAGE_ID_DIALOG_CODE,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidPassportFrontId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PASSPORT_FRONT_ID_DIALOG_CODE,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidPassportBackId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PASSPORT_BACK_ID_DIALOG_CODE,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidProofOfResidencePhotoId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PROOF_OF_RESIDENCE_ID_DIALOG_CODE,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidNationalIdFrontId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_NATIONAL_ID_FRONT_DIALOG_CODE,
                    )
                }
                is SubmitKYCDataRPC.Error.InvalidNationalIdBackId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_NATIONAL_ID_BACK_DIALOG_CODE,
                    )
                }
            }
        }

        fun updateKYCDataRPCException(
            context: Context,
            error: UpdateKYCDataRPC.Error,
        ): UIError {
            return when (error) {
                is UpdateKYCDataRPC.Error.InvalidSignatureImageId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_SIGNATURE_IMAGE_ID_DIALOG_CODE,
                    )
                }
                is UpdateKYCDataRPC.Error.InvalidPassportFrontId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PASSPORT_FRONT_ID_DIALOG_CODE,
                    )
                }
                is UpdateKYCDataRPC.Error.InvalidPassportBackId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PASSPORT_BACK_ID_DIALOG_CODE,
                    )
                }
                is UpdateKYCDataRPC.Error.InvalidProofOfResidencePhotoId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_PROOF_OF_RESIDENCE_ID_DIALOG_CODE,
                    )
                }
                is UpdateKYCDataRPC.Error.InvalidNationalIdFrontId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_NATIONAL_ID_FRONT_DIALOG_CODE,
                    )
                }
                is UpdateKYCDataRPC.Error.InvalidNationalIdBackId -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.INVALID_NATIONAL_ID_BACK_DIALOG_CODE,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun verifyKYCData(
            context: Context,
            error: SignUpInVerifyKYCDataRPC.Error,
        ): UIError {
            return when (error) {
                is SignUpInVerifyKYCDataRPC.Error.PasswordValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleInvalidCredentials),
                        errorMessage = context.getString(R.string.alertMessageInvalidCredentials),
                        KYCDataVM.ERROR_CODE_TOKEN_EXPIRED,
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.IncorrectProfileImageResolution -> {
                    // Considering as developer because irrespective of user's image selection it
                    // should be downscaled to requirement.
                    throw IllegalArgumentException("The provided Resolution[${error.foundHeight} , ${error.foundWidth}] is incorrect.")
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidToken -> {
                    throw IllegalStateException("Received INVALID_TOKEN error on SignUpInVerifyKYCDataRPC")
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidProfileImageId -> {
                    throw IllegalArgumentException("The upload image id is invalid.")
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidAge -> {
                    throw SignUpInVerifyKYCDataRPC.InvalidAgeException("The provided age is invalid.")
                }
                is SignUpInVerifyKYCDataRPC.Error.UserTooYoung -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserTooYoung),
                        context.getString(R.string.alertMessageUserTooYoung, error.minimumAgeInYears),
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidKycDataInputFormat -> {
                    throw IllegalStateException("Received INVALID_KYC_DATA_INPUT_FORMAT on SignUpInVerifyKYCDataRPC")
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidKycData -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidKYCData),
                        context.getString(R.string.alertMessageInvalidKYCData),
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.KycDataNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleKYCDataNotFound),
                        context.getString(R.string.alertMessageKYCDataNotFound),
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.InvalidIssueDate -> throw IllegalStateException("The national id issue date is in the future.")
                is SignUpInVerifyKYCDataRPC.Error.KycExpired -> throw IllegalStateException("The national id expiry date is in the past.")
                is SignUpInVerifyKYCDataRPC.Error.KycApiUnreachable -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.UnableToVerifyKycData -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SignUpInVerifyKYCDataRPC.Error.SignUpTemporarilyBlocked ->
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSignUpBlocked),
                        context.getString(R.string.alertMessageSignUpBlocked),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                is SignUpInVerifyKYCDataRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        fun verifyKYCData(
            context: Context,
            error: ProfileVerifyKYCDataRPC.Error,
        ): UIError {
            return when (error) {
                is ProfileVerifyKYCDataRPC.Error.IncorrectProfileImageResolution -> {
                    // Considering as developer because irrespective of user's image selection it
                    // should be downscaled to requirement.
                    throw IllegalArgumentException("The provided Resolution[${error.foundHeight} , ${error.foundWidth}] is incorrect.")
                }
                is ProfileVerifyKYCDataRPC.Error.InvalidProfileImageId -> {
                    throw IllegalArgumentException("The upload image id is invalid.")
                }
                is ProfileVerifyKYCDataRPC.Error.InvalidAge -> {
                    throw ProfileVerifyKYCDataRPC.InvalidAgeException("The provided age is invalid.")
                }
                is ProfileVerifyKYCDataRPC.Error.UserTooYoung -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserTooYoung),
                        context.getString(R.string.alertMessageUserTooYoung, error.minimumAgeInYears),
                    )
                }
                is ProfileVerifyKYCDataRPC.Error.InvalidKycDataInputFormat,
                is ProfileVerifyKYCDataRPC.Error.InvalidKycData,
                -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidKYCData),
                        context.getString(R.string.alertMessageInvalidKYCData),
                    )
                }
                is ProfileVerifyKYCDataRPC.Error.KycDataNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleKYCDataNotFound),
                        context.getString(R.string.alertMessageKYCDataNotFound),
                    )
                }
                is ProfileVerifyKYCDataRPC.Error.InvalidIssueDate -> throw IllegalStateException("The national id issue date is in the future.")
                is ProfileVerifyKYCDataRPC.Error.KycExpired -> throw IllegalStateException("The national id expiry date is in the past.")
                is ProfileVerifyKYCDataRPC.Error.KycApiUnreachable -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ProfileVerifyKYCDataRPC.Error.UnableToVerifyKycData -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun signInUserRPCException(
            context: Context,
            error: SignInUserRPC.Error,
        ): UIError? {
            return when (error) {
                is SignInUserRPC.Error.IncorrectPassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleIncorrectPassword),
                        context.getString(R.string.alertMessageIncorrectPassword),
                    )
                }
                is SignInUserRPC.Error.InvalidToken -> {
                    throw IllegalStateException("Received INVALID_TOKEN exception on SignInUserRPC")
                }
                is SignInUserRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SignInUserRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SignInUserRPC.Error.TooManyRequests -> UIError(
                    ErrorType.DIALOG,
                    errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                    errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    SIGN_UP_IN_SESSION_EXPIRED,
                )
                is SignInUserRPC.Error.UserDisabled -> {
                    ErrorHandler.showForcedLogoutDialog(
                        context,
                        context.getString(R.string.alertTitleUserDisabled),
                        context.getString(R.string.alertMessageUserDisabled),
                    )
                    null
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun resendSignInOTPRPCExceptions(
            context: Context,
            error: ResendSignInOTPRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ResendSignInOTPRPC.Error.IncorrectId -> {
                    // This can only happen due to developer error or outsider malpractice.
                    throw IncorrectIdException("The otp id is not valid.")
                }
                is ResendSignInOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ResendSignInOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }

                is ResendSignInOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
                is ResendSignInOTPRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun getForgetPasswordQuestionRPCException(
            error: GetForgotPasswordQuestionsRPC.Error,
            context: Context,
        ): UIError? {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetForgotPasswordQuestionsRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        ERROR_CODE_SIGN_IN_AGAIN,
                    )
                }
                is GetForgotPasswordQuestionsRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IllegalStateException("Received INVALID_PHONENUMBER_VALIDATED_TOKEN on GetForgotPasswordQuestionsRPC")
                }
                is GetForgotPasswordQuestionsRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        ERROR_CODE_SIGN_IN_AGAIN,
                    )
                }
                is GetForgotPasswordQuestionsRPC.Error.InactiveState -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCredentials),
                        context.getString(R.string.alertMessageInvalidCredentials),
                        DialogCodes.SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is GetForgotPasswordQuestionsRPC.Error.UserDisabled -> {
                    ErrorHandler.showForcedLogoutDialog(
                        context,
                        context.getString(R.string.alertMessageUserDisabled),
                        context.getString(R.string.alertTitleUserDisabled),
                    )
                    null
                }
                is GetForgotPasswordQuestionsRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun getSetupPasswordRPCException(
            context: Context,
            error: SubmitSecurityQuestionAnswersAndPasswordRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.UserAlreadyExists -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserAlreadyExists),
                        context.getString(R.string.alertMessageUserAlreadyExists),
                    )
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.InactiveState -> {
                    throw IllegalStateException("Try to update user data which is marked as inactive")
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.InsecurePassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePasswordWeak),
                        context.getString(R.string.alertMessagePasswordWeak),
                    )
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.IncorrectQuestionId -> {
                    throw IllegalStateException("The client answered a question whose identifier was incorrect.")
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.IncorrectAnswerCount -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleIncorrectAnswers),
                        errorMessage = context.getString(R.string.alertMessageIncorrectAnswers),
                    )
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.RepeatedQuestionId -> {
                    throw IllegalStateException("The client answered a question more than once.")
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        errorCode = SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.InvalidTrustedContactsValidatedToken -> {
                    throw IncorrectIdException("InvalidTrustedContactsValidatedToken")
                }
                is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.TrustedContactsValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun getResetPasswordRPCException(
            context: Context,
            error: ResetForgottenPasswordRPC.Error,
        ): UIError? {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ResetForgottenPasswordRPC.Error.InsecurePassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePasswordWeak),
                        context.getString(R.string.alertMessagePasswordWeak),
                    )
                }
                is ResetForgottenPasswordRPC.Error.InvalidResetForgotPasswordToken -> {
                    throw IllegalStateException("Reset forget password token is invalid")
                }
                is ResetForgottenPasswordRPC.Error.ResetForgotPasswordTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionTimedOut),
                        context.getString(R.string.alertMessageSessionTimedOut),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ResetForgottenPasswordRPC.Error.ReusedPassword -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleDuplicatePassword),
                        context.getString(R.string.alertMessageDuplicatePassword),
                    )
                }
                is ResetForgottenPasswordRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ResetForgottenPasswordRPC.Error.InactiveState -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCredentials),
                        context.getString(R.string.alertMessageInvalidCredentials),
                        DialogCodes.SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ResetForgottenPasswordRPC.Error.UserDisabled -> {
                    ErrorHandler.showForcedLogoutDialog(
                        context,
                        context.getString(R.string.alertMessageUserDisabled),
                        context.getString(R.string.alertTitleUserDisabled),
                    )
                    null
                }
                is ResetForgottenPasswordRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        fun resendSignUpOTPRPCExceptions(
            context: Context,
            error: ResendSignUpOTPRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ResendSignUpOTPRPC.Error.IncorrectOtpId -> {
                    // This can only happen due to developer error or outsider malpractice.
                    throw IncorrectIdException("The otp id is not valid.")
                }
                is ResendSignUpOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ResendSignUpOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is ResendSignUpOTPRPC.Error.InactiveState -> {
                    throw IllegalStateException("Trying to update user data which is marked as inactive")
                }
                is ResendSignUpOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun getURLRPCExceptions(
            error: GetUrlRPC.Error,
            context: Context,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetUrlRPC.Error.InvalidCredentials -> {
                    throw IllegalArgumentException("The provided PasswordValidatedTokenExpired & PhoneNumber is unknown.")
                }
                is GetUrlRPC.Error.PasswordValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleSessionExpired),
                        errorMessage = context.getString(R.string.alertMessageSessionExpired),
                        KYCDataVM.ERROR_CODE_TOKEN_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun getDocumentIdRPCExceptions(
            context: Context,
            error: GetDocumentIdRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetDocumentIdRPC.Error.InvalidCredentials -> {
                    throw IllegalArgumentException("The provided PasswordValidatedTokenExpired & PhoneNumber is unknown.")
                }
                is GetDocumentIdRPC.Error.PasswordValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleSessionExpired),
                        errorMessage = context.getString(R.string.alertMessageSessionExpired),
                        KYCDataVM.ERROR_CODE_TOKEN_EXPIRED,
                    )
                }
                is GetDocumentIdRPC.Error.RecordNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleFailedToUpload),
                        context.getString(R.string.alertMessageFailedToUpload),
                    )
                }
                is GetDocumentIdRPC.Error.FileNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleFailedToUpload),
                        context.getString(R.string.alertMessageFailedToUpload),
                    )
                }
            }
        }

        fun getDocumentIdForSignedInUserRPCExceptions(
            context: Context,
            error: GetDocumentIdForSignedInUserRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetDocumentIdForSignedInUserRPC.Error.DocumentNotFound -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleFailedToUpload),
                        context.getString(R.string.alertMessageFailedToUpload),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun confirmForgotPasswordAnswers(
            context: Context,
            error: ConfirmForgotPasswordAnswersRPC.Error,
        ): UIError? {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ConfirmForgotPasswordAnswersRPC.Error.InactiveState -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCredentials),
                        context.getString(R.string.alertMessageInvalidCredentials),
                        DialogCodes.SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }

                is ConfirmForgotPasswordAnswersRPC.Error.IncorrectQuestionId -> {
                    throw IllegalStateException("The client answered a question whose identifier was incorrect.")
                }
                is ConfirmForgotPasswordAnswersRPC.Error.AnswerCountIncorrect -> {
                    throw IllegalStateException("Received ANSWER_COUNT_INCORRECT on ConfirmForgotPasswordAnswersRPC.")
                }
                is ConfirmForgotPasswordAnswersRPC.Error.RepeatedQuestionId -> {
                    throw IllegalStateException("The client answered a question more than once.")
                }
                is ConfirmForgotPasswordAnswersRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ConfirmForgotPasswordAnswersRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
                is ConfirmForgotPasswordAnswersRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IllegalStateException("The provided validation token is invalid.")
                }
                is ConfirmForgotPasswordAnswersRPC.Error.IncorrectAnswer -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleInvalidAnswers),
                        errorMessage = context.getString(R.string.alertMessageInvalidAnswers),
                    )
                }
                is ConfirmForgotPasswordAnswersRPC.Error.UserDisabled -> {
                    ErrorHandler.showForcedLogoutDialog(
                        context,
                        context.getString(R.string.alertMessageUserDisabled),
                        context.getString(R.string.alertTitleUserDisabled),
                    )
                    null
                }
                is ConfirmForgotPasswordAnswersRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleGetTrustedContactsRPCErrors(
            context: Context,
            error: GetTrustedContactsRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetTrustedContactsRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is GetTrustedContactsRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IncorrectIdException("InvalidPhoneNumberValidatedToken")
                }
            }
        }

        fun handleRemoveTrustedContactRPCErrors(
            error: RemoveTrustedContactRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            throw IllegalStateException("This RPC cannot be called in Sign Up Flow")
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleGetTrustedContactValidatedTokenRPCErrors(
            context: Context,
            error: GetTrustedContactValidatedTokenRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is GetTrustedContactValidatedTokenRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is GetTrustedContactValidatedTokenRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IncorrectIdException("InvalidPhoneNumberValidatedToken")
                }
                is GetTrustedContactValidatedTokenRPC.Error.MinTrustedContactsNotAdded -> {
                    throw IllegalStateException("MinTrustedContactsNotAdded")
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleRequestAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: RequestAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is RequestAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IncorrectIdException("InvalidPhoneNumberValidatedToken")
                }
                is RequestAddTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.TrustedContactAlreadyAdded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTrustedContactAlreadyAdded),
                        context.getString(R.string.alertMessageTrustedContactAlreadyAdded),
                    )
                }
                is RequestAddTrustedContactOTPRPC.Error.TrustedContactsLimitReached -> {
                    throw IllegalStateException("TrustedContactsLimitReached")
                }
                is RequestAddTrustedContactOTPRPC.Error.UserIsTrustedContact -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserIsTrustedContact),
                        context.getString(R.string.alertMessageUserIsTrustedContact),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleResendAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: ResendAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ResendAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ResendAddTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ResendAddTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IncorrectIdException("InvalidPhoneNumberValidatedToken")
                }
                is ResendAddTrustedContactOTPRPC.Error.IncorrectId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ResendAddTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is ResendAddTrustedContactOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleConfirmAddTrustedContactOTPRPCExceptions(
            context: Context,
            error: ConfirmAddTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ConfirmAddTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IncorrectIdException("InvalidPhoneNumberValidatedToken")
                }
                is ConfirmAddTrustedContactOTPRPC.Error.IncorrectOtpId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ConfirmAddTrustedContactOTPRPC.Error.InvalidOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                    )
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TrustedContactsLimitReached -> {
                    throw IllegalStateException("TrustedContactsLimitReached")
                }
                is ConfirmAddTrustedContactOTPRPC.Error.TrustedContactAlreadyAdded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTrustedContactAlreadyAdded),
                        context.getString(R.string.alertMessageTrustedContactAlreadyAdded),
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleRequestResetPasswordByTrustedContactOTPRPCExceptions(
            context: Context,
            error: RequestResetPasswordByTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is RequestResetPasswordByTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is RequestResetPasswordByTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is RequestResetPasswordByTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IllegalStateException("Received INVALID_PHONENUMBER_VALIDATED_TOKEN on RequestResetPasswordByTrustedContactOTPRPC")
                }
                is RequestResetPasswordByTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is RequestResetPasswordByTrustedContactOTPRPC.Error.TrustedContactNotFound -> {
                    throw IncorrectIdException("TrustedContactNotFound")
                }
                is RequestResetPasswordByTrustedContactOTPRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleResendResetPasswordByTrustedContactOTPRPCExceptions(
            context: Context,
            error: ResendResetPasswordByTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ResendResetPasswordByTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IllegalStateException("Received INVALID_PHONENUMBER_VALIDATED_TOKEN on ResendResetPasswordByTrustedContactOTPRPC")
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.IncorrectId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.CouldNotSendOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleResendOTPFailed),
                        context.getString(R.string.alertMessageResendOTPFailed),
                    )
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.WaitForResend -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                    )
                }
                is ResendResetPasswordByTrustedContactOTPRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }

        // This is not updated to have FORCE_USER_OUT_OF_FLOW in the error handling, as this is already handled by
        // ERROR_CODE_SIGN_UP_AGAIN and SIGN_UP_IN_SESSION_EXPIRED. Updating this flow to use FORCE_USER_OUT_OF_FLOW might break something
        // which we can't afford right now.
        fun handleConfirmResetPasswordByTrustedContactOTPRPCExceptions(
            context: Context,
            error: ConfirmResetPasswordByTrustedContactOTPRPC.Error,
        ): UIError {
            return when (error) {
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.PhonenumberValidatedTokenExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.InvalidPhonenumberValidatedToken -> {
                    throw IllegalStateException("Received INVALID_PHONENUMBER_VALIDATED_TOKEN on ConfirmResetPasswordByTrustedContactOTPRPC")
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.InvalidOtp -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidOTP),
                        context.getString(R.string.alertMessageInvalidOTP),
                    )
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.OtpExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleOTPExpired),
                        context.getString(R.string.alertMessageOTPExpired),
                    )
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.TooManyRequests -> {
                    UIError(
                        ErrorType.DIALOG,
                        errorTitle = context.getString(R.string.alertTitleTooManyRequests),
                        errorMessage = context.getString(R.string.alertMessageTooManyRequests),
                        SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN,
                    )
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.IncorrectOtpId -> {
                    throw IncorrectIdException("IncorrectOTPId")
                }
                is ConfirmResetPasswordByTrustedContactOTPRPC.Error.SignupSessionExpired -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSessionExpired),
                        context.getString(R.string.alertMessageSessionExpired),
                        SIGN_UP_IN_SESSION_EXPIRED,
                    )
                }
            }
        }
    }
}

class IncorrectIdException(val error: String) : Exception(error)
