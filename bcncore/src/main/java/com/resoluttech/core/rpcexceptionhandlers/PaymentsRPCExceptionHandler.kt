package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.payments.ConfirmBillPaymentRequestRPC
import com.resoluttech.bcn.payments.CreateBillPaymentRequestRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.formatToAmount
import com.resoluttech.core.utils.getUserFacingValue

class PaymentsRPCExceptionHandler {
    companion object {

        fun createBillerPaymentRequestRPCErrorMessage(
            context: Context,
            error: CreateBillPaymentRequestRPC.Error,
            accountName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is CreateBillPaymentRequestRPC.Error.AccountDeactivated -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSendingAccountDeactivated),
                        context.getString(
                            R.string.alertMessageSendingAccountDeactivated,
                            accountName,
                        ),
                    )
                }
                is CreateBillPaymentRequestRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is CreateBillPaymentRequestRPC.Error.InvalidInputFields -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePaymentsMissingFields),
                        context.getString(R.string.alertMessagePaymentsMissingFields),
                    )
                }
                is CreateBillPaymentRequestRPC.Error.InvalidSenderAccountId -> {
                    throw DeveloperErrorException("Sender account id is not valid")
                }
                is CreateBillPaymentRequestRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateBillPaymentRequestRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateBillPaymentRequestRPC.Error.InvalidBillerId -> {
                    throw DeveloperErrorException("Biller id is not valid")
                }
                is CreateBillPaymentRequestRPC.Error.UnableToValidateRecipient -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is CreateBillPaymentRequestRPC.Error.CurrencyMismatchForSenderAccountAndBiller -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidCurrencyMismatch),
                        context.getString(R.string.alertMessageInvalidCurrencyMismatch),
                    )
                }
            }
        }
        fun confirmBillerPaymentRequestRPCErrorMessage(
            context: Context,
            error: ConfirmBillPaymentRequestRPC.Error,
            walletName: String,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return when (error) {
                is ConfirmBillPaymentRequestRPC.Error.InvalidRecordId -> {
                    throw DeveloperErrorException("We are unable to transfer money to user as record id is not valid")
                }
                is ConfirmBillPaymentRequestRPC.Error.InsufficientBalance -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInsufficientBalance),
                        context.getString(R.string.alertMessageTransactionInsufficientBalanceWithFee, context.getString(R.string.displayAmount, error.transactionFee.currency.currencyCode, error.transactionFee.amount.getUserFacingValue().formatToAmount())),
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.TransactionFeeConfirmationTimeout -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionFeeExpired),
                        context.getString(R.string.alertMessageTransactionFeeExpired),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.PaymentFailed -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePaymentFailed),
                        context.getString(R.string.alertMessagePaymentFailed),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.MonetaryTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSenderTransactionAmountLimit),
                        context.getString(R.string.alertMessageSenderTransactionAmountLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.PeriodicTransactionLimitExceeded -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionsLimit),
                        context.getString(R.string.alertMessageTransactionsLimit),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.InactiveSenderAccount -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAccountDeactivated),
                        context.getString(
                            R.string.alertMessageTransactionAccountDeactivated,
                            walletName,
                        ),
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.UnableToReachBiller -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUnableToReachProvider),
                        context.getString(R.string.alertMessageUnableToReachProvider),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
                is ConfirmBillPaymentRequestRPC.Error.UnknownBillerServerError -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleGenericError),
                        context.getString(R.string.alertMessageGenericError),
                        errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                    )
                }
            }
        }
    }

    private class DeveloperErrorException(msg: String) : Exception(msg)
}
