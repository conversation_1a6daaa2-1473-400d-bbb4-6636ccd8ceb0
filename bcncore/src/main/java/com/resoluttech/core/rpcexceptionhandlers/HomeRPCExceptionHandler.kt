package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.homeScreen.GetHomeDataRPC
import com.resoluttech.bcncore.R

class HomeRPCExceptionHandler {
    companion object {
        fun getCommonHomeRPCErrorMessage(
            context: Context,
            error: GetHomeDataRPC.Error,
        ): UIError {
            ErrorHandler.logRPCError(error)
            return UIError(
                ErrorType.SNACKBAR,
                context.getString(R.string.alertTitleGenericError),
                context.getString(R.string.alertMessageGenericError),
            )
        }
    }
}
