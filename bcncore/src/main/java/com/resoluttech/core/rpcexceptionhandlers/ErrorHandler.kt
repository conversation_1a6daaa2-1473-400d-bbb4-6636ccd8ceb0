package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.ForcedLogoutAlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.UnsupportedClientHandler
import com.suryadigital.leo.kedwig.APIClientException
import com.suryadigital.leo.kedwig.NetworkException
import com.suryadigital.leo.kedwig.TimeoutException
import com.suryadigital.leo.rpc.LeoInvalidLLTException
import com.suryadigital.leo.rpc.LeoRPCError
import com.suryadigital.leo.rpc.LeoRPCException
import com.suryadigital.leo.rpc.LeoServerException
import com.suryadigital.leo.rpc.LeoUnauthenticatedException
import com.suryadigital.leo.rpc.LeoUnauthorizedException
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import com.suryadigital.leo.rpc.LeoUserDisabledException
import timber.log.Timber

class ErrorHandler {
    companion object {
        const val TAG: String = "ErrorHandler"

        fun logRPCError(error: LeoRPCError) {
            Timber.tag(TAG).e("Got RPC error : ${error.javaClass.name}")
        }

        private fun logRPCException(exception: Exception) {
            Timber.tag(TAG).e("Got exception : ${exception.javaClass.name}")
        }

        fun handleRPCException(rpcException: LeoRPCException, context: Context): UIError? {
            logRPCException(rpcException)
            return when (rpcException) {
                is LeoUnsupportedClientException -> {
                    UnsupportedClientHandler.handleIfUnsupportedClient(rpcException)
                    null
                }
                is LeoUnauthenticatedException -> {
                    // TODO("https://app.clubhouse.io/resolut-tech/story/2072/handle-invalid-authentication-token")
                    // throw UnauthenticatedException("Authentication failed. Requires re-authentication.")
                    return UIError(ErrorType.DIALOG, context.getString(R.string.alertTitleServerError), context.getString(R.string.alertMessageServerError))
                }
                is LeoUnauthorizedException,
                is LeoServerException,
                -> {
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageGenericError),
                        DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID,
                    )
                }
                is LeoInvalidLLTException -> {
                    showForcedLogoutDialog(context, context.getString(R.string.alertMessageInvalidCredentials), context.getString(R.string.alertTitleInvalidCredentials))
                    null
                }
                is LeoUserDisabledException -> {
                    showForcedLogoutDialog(context, context.getString(R.string.alertMessageUserDisabled), context.getString(R.string.alertTitleUserDisabled))
                    null
                }
                // Removed the exceptions which were getting thrown[ex: InvalidRequestException]
                // because that will be handled by else statement.
                else -> throw rpcException
            }
        }

        fun showForcedLogoutDialog(context: Context, message: String, title: String) {
            val alertDialog = ForcedLogoutAlertDialog.newInstance(
                title = title,
                message = message,
                positiveActionLabel = context.getString(R.string.alertActionDismiss),
            )

            // If app is in the background we cannot show a dialog on the screen.
            // Once user opens the app again RPC would be called and this dialog would be shown at that time.
            if (context is AppCompatActivity && !context.supportFragmentManager.isStateSaved) {
                alertDialog.show(
                    context.supportFragmentManager,
                    ForcedLogoutAlertDialog.DIALOG_TAG,
                )
            }
        }

        fun handleAPIClientException(clientException: APIClientException, context: Context): UIError {
            return when (clientException) {
                is TimeoutException -> {
                    UIError(
                        ErrorType.SNACKBAR,
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageServerError),
                    )
                }
                is NetworkException -> {
                    UIError(
                        ErrorType.BANNER,
                        context.getString(R.string.alertTitleNoInternet),
                        context.getString(R.string.alertMessageNoInternet),
                    )
                }
                else -> throw clientException
            }
        }
    }
}
