package com.resoluttech.core.rpcexceptionhandlers

import android.content.Context
import com.resoluttech.bcn.profile.ChangeAccountDisplayNameRPC
import com.resoluttech.bcn.profile.ChangeAccountStateRPC
import com.resoluttech.bcn.profile.CreateNewAccountRPC
import com.resoluttech.bcn.profile.GetAllAccountsRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.DialogCodes

object ManageAccountRPCExceptionHandler {

    fun getGetAllAccountsRPCErrorMessage(
        error: GetAllAccountsRPC.Error,
    ) {
        when (error) {
            is GetAllAccountsRPC.Error.NoActivePrimaryCurrencyAccount -> {
                throw IllegalStateException("No Active Account found in primary currency")
            }
        }
    }

    fun getChangeAccountDisplayNameRPCErrorMessage(
        context: Context,
        error: ChangeAccountDisplayNameRPC.Error,
    ): UIError {
        return when (error) {
            is ChangeAccountDisplayNameRPC.Error.InvalidAccountId -> {
                throw IllegalAccessException("We are unable to process this request as account id is not valid")
            }
            is ChangeAccountDisplayNameRPC.Error.InactiveAccount -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleManageAccountInactive),
                    context.getString(R.string.alertMessageManageAccountInactive),
                )
            }
            is ChangeAccountDisplayNameRPC.Error.DisplayNameAlreadyTaken -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleAccountNameExists),
                    context.getString(R.string.alertMessageAccountNameExists),
                )
            }
        }
    }

    fun getChangeAccountStateRPCErrorMessage(
        context: Context,
        error: ChangeAccountStateRPC.Error,
    ): UIError {
        return when (error) {
            is ChangeAccountStateRPC.Error.InvalidAccountId -> {
                throw IllegalAccessException("We are unable to process this request as account id is not valid")
            }
            is ChangeAccountStateRPC.Error.PrimaryCurrencyAccountsCannotBeInactive -> {
                throw IllegalAccessException("We are unable to process this request as primary accounts cannot be made inactive")
            }
            is ChangeAccountStateRPC.Error.AgentAccountCannotBeMadeInactive -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleAgentAccountCannotBeMadeInactive),
                    context.getString(R.string.alertMessageAgentAccountCannotBeMadeInactive),
                )
            }
        }
    }

    fun getCreateNewAccountRPCErrorMessage(
        context: Context,
        error: CreateNewAccountRPC.Error,
    ): UIError {
        return when (error) {
            is CreateNewAccountRPC.Error.MaximumAccountLimitReached -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleMaximumAccountLimitReached),
                    context.getString(R.string.alertMessageMaximumAccountLimitReached),
                    errorCode = DialogCodes.FORCE_USER_OUT_OF_FLOW,
                )
            }
            is CreateNewAccountRPC.Error.DisplayNameAlreadyTaken -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleAccountNameExists),
                    context.getString(R.string.alertMessageAccountNameExists),
                )
            }
        }
    }
}
