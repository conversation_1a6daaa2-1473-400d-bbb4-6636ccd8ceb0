package com.resoluttech.core.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.views.BaseFragment

class ShowQRCodeFragment : BaseFragment() {

    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        val view = inflater.inflate(R.layout.fragment_show_qr_code, container, false)
        setDefaultToolbar(getString(R.string.showQRCodeScreenTitle))
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val tabHeading = getTabHeadings()
        initViews(view)
        tabLayout = view.findViewById(R.id.tab_layout)
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = tabHeading[position]
        }.attach()
    }

    private fun getTabHeadings(): List<String> {
        return when (LocaleManager.getCurrentLocale(requireContext())) {
            SupportedLocale.EN_US -> listOf("My Code", "My Wallets")
            SupportedLocale.NYANJA -> listOf("Khodi Yanga", "Mai Wallets")
        }
    }

    private fun initViews(view: View) {
        viewPager = view.findViewById(R.id.pager)
        viewPager.adapter = ShowQRCodeViewPagerAdapter(this)
    }
}

private class ShowQRCodeViewPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        val fragment = ShowQRCodeContent()
        fragment.arguments = Bundle().apply {
            putInt(POSITION_KEY, position)
        }
        return fragment
    }
}

const val POSITION_KEY: String = "position"
