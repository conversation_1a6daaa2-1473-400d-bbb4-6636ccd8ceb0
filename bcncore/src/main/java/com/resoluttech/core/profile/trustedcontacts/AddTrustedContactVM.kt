package com.resoluttech.core.profile.trustedcontacts

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.types.Otp
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.OTPReadMode
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.utils.ContactPickerFragment
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import com.suryadigital.leo.types.LeoInvalidLeoEmailIdException
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class AddTrustedContactVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<AddTrustedContactState> =
        MutableLiveData(AddTrustedContactState.AcceptInput)
    val currentState: LiveData<AddTrustedContactState> = _currentState
    private val repository = TrustedContactRepository()

    fun onAddTrustedContactTapped(phoneNumber: LeoPhoneNumber) {
        _currentState.postValue(AddTrustedContactState.ConfirmPhoneNumber(phoneNumber))
    }

    fun requestAddTrustedContactOTP(
        context: Context,
        firstName: String,
        lastName: String?,
        phoneNumber: String,
        emailId: String?,
    ) {
        _currentState.postValue(AddTrustedContactState.Loading)
        try {
            if (emailId != null) LeoEmailId(emailId)
        } catch (e: LeoInvalidLeoEmailIdException) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleInvalidEmailId),
                context.getString(R.string.alertMessageInvalidEmailId),
            ).showError()
            return
        }
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    val trustedContact = TrustedContact(
                        UUID.randomUUID(),
                        firstName,
                        lastName,
                        LeoPhoneNumber(phoneNumber),
                        if (emailId != null) LeoEmailId(emailId) else null,
                    )
                    when (val result = repository.requestAddTrustedContactOTP(trustedContact)) {
                        is LeoRPCResult.LeoResponse -> {
                            val fullName = if (lastName != null) {
                                context.getString(
                                    R.string.trustedContactFullName,
                                    firstName,
                                    lastName,
                                )
                            } else {
                                firstName
                            }
                            handleRequestAddTrustedContactOTPResponse(
                                result.response,
                                context,
                                fullName,
                                LeoPhoneNumber(phoneNumber).getFormattedPhoneNumber(),
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleRequestAddTrustedContactOTPError(context, result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleRequestAddTrustedContactOTPResponse(
        response: RequestAddTrustedContactOTPRPC.Response,
        context: Context,
        trustedContactName: String,
        trustedPhoneNumber: String,
    ) {
        val otpDetails = response.otpDetails
        _currentState.postValue(
            AddTrustedContactState.OTPGenerated(
                OTPResponse(
                    title = context.getString(
                        R.string.otpTrustedContact,
                        trustedContactName,
                        trustedPhoneNumber,
                    ),
                    otpId = otpDetails.otpId,
                    nextResendAt = otpDetails.validityDetails.nextResendAt,
                    expiresAt = otpDetails.validityDetails.expiresAt,
                    otpReadMode = OTPReadMode.MANUAL,
                ),
            ),
        )
    }

    private fun handleRequestAddTrustedContactOTPError(
        context: Context,
        error: RequestAddTrustedContactOTPRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleRequestAddTrustedContactOTPRPCExceptions(context, error)
            .showError()
    }

    fun resendAddTrustedContactOTP(
        context: Context,
        otpId: UUID?,
    ) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.resendAddTrustedContactOTP(otpId)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleResendAddTrustedContactOTPResponse(otpId, result.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleResendAddTrustedContactOTPError(context, result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleResendAddTrustedContactOTPResponse(
        otpId: UUID,
        response: ResendAddTrustedContactOTPRPC.Response,
    ) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            otpId = otpId,
            nextResendAt = otpDetails.validityDetails.nextResendAt,
            expiresAt = otpDetails.validityDetails.expiresAt,
            otpLeft = otpDetails.numberOfResendsLeft,
            otpReadMode = OTPReadMode.MANUAL,
        )
        _currentState.postValue(AddTrustedContactState.ResendOTPSuccess(otpResponse))
    }

    private fun handleResendAddTrustedContactOTPError(
        context: Context,
        error: ResendAddTrustedContactOTPRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleResendAddTrustedContactOTPRPCExceptions(context, error)
            .showError()
    }

    fun onPickContactClicked(navController: NavController) {
        val args = Bundle()
        args.putSerializable(
            ContactPickerFragment.USE_CASE,
            ContactPickerFragment.Companion.ContactPickerUseCase.TRUSTED_CONTACTS,
        )
        navController.navigate(R.id.action_start_contact_picker, args)
    }

    fun confirmAddTrustedContactOTP(
        context: Context,
        otpId: UUID?,
        otp: String,
    ) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        _currentState.postValue(AddTrustedContactState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.confirmAddTrustedContactOTP(otpId, Otp(otp))) {
                        is LeoRPCResult.LeoResponse -> {
                            handleConfirmAddTrustedContactOTPResponse()
                        }
                        is LeoRPCResult.LeoError -> {
                            handleConfirmAddTrustedContactOTPError(context, result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleConfirmAddTrustedContactOTPResponse() {
        _currentState.postValue(AddTrustedContactState.OTPConfirmed)
    }

    private fun handleConfirmAddTrustedContactOTPError(
        context: Context,
        error: ConfirmAddTrustedContactOTPRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleConfirmAddTrustedContactOTPRPCExceptions(context, error)
            .showError()
    }

    private fun UIError.showError() {
        _currentState.postValue(AddTrustedContactState.Error(this))
    }

    fun onInvalidPhoneNumberInput(title: String, message: String) {
        _currentState.postValue(
            AddTrustedContactState.Error(
                UIError(ErrorType.DIALOG, title, message),
            ),
        )
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(AddTrustedContactState.AcceptInput)
    }

    fun onOTPReadCancelled(otpReaderDialog: OTPReaderDialog) {
        otpReaderDialog.dismiss()
        _currentState.postValue(AddTrustedContactState.AcceptInput)
    }

    fun onTrustedContactConfirmationDismissed() {
        _currentState.postValue(AddTrustedContactState.AcceptInput)
    }
}

sealed class AddTrustedContactState {
    data class Error(val error: UIError) : AddTrustedContactState()
    object AcceptInput : AddTrustedContactState()
    object Loading : AddTrustedContactState()
    data class ConfirmPhoneNumber(val phoneNumber: LeoPhoneNumber) : AddTrustedContactState()
    data class OTPGenerated(val response: OTPResponse) : AddTrustedContactState()
    object OTPConfirmed : AddTrustedContactState()
    data class ResendOTPSuccess(val response: OTPResponse) : AddTrustedContactState()
}
