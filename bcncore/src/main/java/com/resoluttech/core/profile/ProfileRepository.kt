package com.resoluttech.core.profile

import com.resoluttech.bcn.document.FileAttributes
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPC
import com.resoluttech.bcn.homeScreen.GetAgentListRPC
import com.resoluttech.bcn.profile.ArchiveBcnUserRPC
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPC
import com.resoluttech.bcn.profile.ChangeLocaleRPC
import com.resoluttech.bcn.profile.ChangeProfileImageRPC
import com.resoluttech.bcn.profile.GetPasswordPolicyRPC
import com.resoluttech.bcn.profile.ResendVerificationEmailRPC
import com.resoluttech.bcn.profile.UpdateEmailIdRPC
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcn.types.Locale
import com.resoluttech.bcn.types.Password
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import org.koin.java.KoinJavaComponent
import java.util.UUID

class ProfileRepository {

    private val changeDefaultAccountRPC: ChangeDefaultAccountRPC by KoinJavaComponent.inject(
        ChangeDefaultAccountRPC::class.java,
    )
    private val getPasswordPolicy: GetPasswordPolicyRPC by KoinJavaComponent.inject(
        GetPasswordPolicyRPC::class.java,
    )
    private val getAgentListRPC: GetAgentListRPC by KoinJavaComponent.inject(GetAgentListRPC::class.java)
    private val getDocumentIdForSignedInUserRPC: GetDocumentIdForSignedInUserRPC by KoinJavaComponent.inject(
        GetDocumentIdForSignedInUserRPC::class.java,
    )
    private val getUrlForSignedInUserRPC: GetUrlForSignedInUserRPC by KoinJavaComponent.inject(
        GetUrlForSignedInUserRPC::class.java,
    )
    private val changeProfileImageRPC: ChangeProfileImageRPC by KoinJavaComponent.inject(
        ChangeProfileImageRPC::class.java,
    )
    private val archiveBcnUserRPC: ArchiveBcnUserRPC by KoinJavaComponent.inject(
        ArchiveBcnUserRPC::class.java,
    )

    private val changeLocaleRPC: ChangeLocaleRPC by KoinJavaComponent.inject(
        ChangeLocaleRPC::class.java,
    )

    private val resendVerificationEmailRPC: ResendVerificationEmailRPC by KoinJavaComponent.inject(
        ResendVerificationEmailRPC::class.java,
    )

    private val updateEmailIdRPC: UpdateEmailIdRPC by KoinJavaComponent.inject(UpdateEmailIdRPC::class.java)

    suspend fun updateEmailID(
        emailId: LeoEmailId,
    ): LeoRPCResult<UpdateEmailIdRPC.Response, UpdateEmailIdRPC.Error> {
        return updateEmailIdRPC.execute(UpdateEmailIdRPC.Request(emailId))
    }

    suspend fun resendVerificationEmail(): LeoRPCResult<ResendVerificationEmailRPC.Response, ResendVerificationEmailRPC.Error> {
        return resendVerificationEmailRPC.execute(ResendVerificationEmailRPC.Request)
    }

    suspend fun getDocumentUploadUrl(
        fileAttributes: FileAttributes,
    ): LeoRPCResult<GetUrlForSignedInUserRPC.Response, GetUrlForSignedInUserRPC.Error> {
        return getUrlForSignedInUserRPC.execute(
            GetUrlForSignedInUserRPC.Request(
                fileAttributes,
            ),
        )
    }

    suspend fun getDocumentIdForSignedInUser(sha256: String): LeoRPCResult<GetDocumentIdForSignedInUserRPC.Response, GetDocumentIdForSignedInUserRPC.Error> {
        return getDocumentIdForSignedInUserRPC.execute(
            GetDocumentIdForSignedInUserRPC.Request(
                sha256,
            ),
        )
    }

    suspend fun changeProfileImage(documentId: UUID): LeoRPCResult<ChangeProfileImageRPC.Response, ChangeProfileImageRPC.Error> {
        return changeProfileImageRPC.execute(
            ChangeProfileImageRPC.Request(
                documentId,
            ),
        )
    }

    suspend fun changeLocale(locale: Locale): LeoRPCResult<ChangeLocaleRPC.Response, ChangeLocaleRPC.Error> {
        return changeLocaleRPC.execute(
            ChangeLocaleRPC.Request(
                locale,
            ),
        )
    }

    suspend fun changeDefaultAccount(
        accountId: UUID,
        currency: Currency,
    ): LeoRPCResult<ChangeDefaultAccountRPC.Response, ChangeDefaultAccountRPC.Error> {
        return changeDefaultAccountRPC.execute(
            ChangeDefaultAccountRPC.Request(
                accountId,
                currency,
            ),
        )
    }

    suspend fun getPasswordPolicy(): LeoRPCResult<GetPasswordPolicyRPC.Response, GetPasswordPolicyRPC.Error> {
        return getPasswordPolicy.execute(
            GetPasswordPolicyRPC.Request,
        )
    }

    suspend fun getAgentList(): LeoRPCResult<GetAgentListRPC.Response, GetAgentListRPC.Error> {
        return getAgentListRPC.execute(
            GetAgentListRPC.Request,
        )
    }

    suspend fun archiveBCNUser(password: Password): LeoRPCResult<ArchiveBcnUserRPC.Response, ArchiveBcnUserRPC.Error> {
        return archiveBcnUserRPC.execute(
            ArchiveBcnUserRPC.Request(
                password,
            ),
        )
    }
}
