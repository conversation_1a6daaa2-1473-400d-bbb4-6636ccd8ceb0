package com.resoluttech.core.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher

class ValidatePasswordFragment : BaseFragment(), AlertDialog.ActionListener {

    private val profileScreenVM: ProfileScreenVM by navGraphViewModels(R.id.home_nav)

    private lateinit var passwordET: TextInputEditText
    private lateinit var passwordTIL: TextInputLayout
    private lateinit var closeAccountMB: MaterialButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_validate_password, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(requireContext().getString(R.string.closeMyAccountPasswordViewTitle))
        initView()
        setupListeners()
        profileScreenVM.currentValidatePasswordState.observe(
            viewLifecycleOwner,
            Observer(::reactToState),
        )
    }

    private fun setupListeners() {
        closeAccountMB.setOnClickListener {
            profileScreenVM.onValidatePasswordToDeactivateAccount(
                requireContext(),
                passwordET.text.toString().trim(),
            )
        }
    }

    private fun initView() {
        view?.apply {
            passwordET = findViewById(R.id.enter_password_tiet)
            passwordTIL = findViewById(R.id.enter_password_til)
            closeAccountMB = findViewById(R.id.close_account_pb)
            closeAccountMB.disable()
            passwordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = passwordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                    afterTextChange = {
                        validateInput()
                    },
                ),
            )
        }
    }

    private fun validateInput() {
        if (passwordET.text?.isNotBlank() == true && passwordET.text.toString().length >= Config.MIN_PASSWORD_LENGTH
        ) {
            closeAccountMB.enable()
        } else {
            closeAccountMB.disable()
        }
    }

    private fun reactToState(state: ValidatePasswordScreenState) {
        when (state) {
            is ValidatePasswordScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            is ValidatePasswordScreenState.Loading -> {
                handleLoadingState()
            }

            is ValidatePasswordScreenState.AccountDeactivated -> {
                handleAccountDeactivatedState()
            }

            is ValidatePasswordScreenState.Error -> {
                handleErrorState(state.uiError)
            }
        }
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                profileScreenVM::onInlineErrorDismissedOnValidatePassword,
            )

            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.MANAGE_ACCOUNT_ERROR_CODE,
                    forceOutUserDestination = ForceOutUserDestination.PROFILE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleAccountDeactivatedState() {
        dismissProgressDialog()
        showAlertDialog(
            requireContext().getString(R.string.alertTitleAccountClosed),
            getString(R.string.alertMessageAccountClosed),
            CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID,
            getString(R.string.alertActionDismiss),
        )
    }

    override fun onPositiveAction(dialogId: Int) {
        if (dialogId == CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID) {
            restartActivity()
        } else {
            profileScreenVM.onInlineErrorDismissedOnValidatePassword()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        // Default action is already handled
    }
}

private const val CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID = 200
