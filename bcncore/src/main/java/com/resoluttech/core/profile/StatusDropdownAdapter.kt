package com.resoluttech.core.profile

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter
import `in`.aabhasjindal.otptextview.R as OTPTextViewResource

class StatusDropdownAdapter(private val items: List<String>, private val context: Context) :
    AbstractTextDropDownAdapter<String>(items) {

    override fun getDropDownView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_dropdown_item, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as ViewHolder
        }
        viewHolder.apply {
            LocaleManager.getCurrentLocale(context)
            textView.text = items[position]
            textView.setTextColor(context.getColor(R.color.subtitleTextColor))
        }
        return view
    }

    override fun getView(
        position: Int,
        convertView: View?,
        parent: ViewGroup,
    ): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView?.tag == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.home_account_spinner_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            textView.text = items[position]
            textView.setTextColor(context.getColor(OTPTextViewResource.color.white))
        }
        return view
    }

    private class ViewHolder(view: View) {
        var textView: TextView = view.findViewById(
            R.id.dropdown_item_text_view,
        )
    }
}
