package com.resoluttech.core.profile.manageaccount

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.profile.CreateNewAccountRPC
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.home.HomeRepository
import com.resoluttech.core.rpcexceptionhandlers.ManageAccountRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class CreateNewAccountVM : ViewModel() {

    private var _currentState: MutableLiveData<CreateNewAccountScreenState> =
        MutableLiveData(CreateNewAccountScreenState.AcceptInput)
    val currentState: LiveData<CreateNewAccountScreenState> = _currentState
    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val manageAccountRepository = ManageAccountRepository()
    private val homeRepository = HomeRepository()

    fun createAccount(context: Context, accountName: String, currency: Currency) {
        _currentState.postValue(CreateNewAccountScreenState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result =
                            manageAccountRepository.createNewAccount(accountName, currency)
                    ) {
                        is LeoRPCResult.LeoResponse -> handleCreateAccountRPCResponse(context)
                        is LeoRPCResult.LeoError -> handleCreateAccountRPCError(
                            context,
                            result.error,
                        )
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleCreateAccountRPCResponse(context: Context) {
        getAccountsAndUpdateSavedAccounts(context)
    }

    private fun getAccountsAndUpdateSavedAccounts(context: Context) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = manageAccountRepository.getAllAccounts()) {
                        is LeoRPCResult.LeoResponse -> {
                            val accounts =
                                result.response.accounts.filter { it.accountState == AccountState.ACTIVE }
                                    .map {
                                        com.resoluttech.core.utils.Account(
                                            it.displayName.name,
                                            it.accountId.toString(),
                                            it.balance.currency.currencyCode,
                                            it.isDefault,
                                            it.balance.amount,
                                        )
                                    }
                            homeRepository.refreshSavedAccountList(context, accounts)
                        }
                        is LeoRPCResult.LeoError -> ManageAccountRPCExceptionHandler.getGetAllAccountsRPCErrorMessage(
                            result.error,
                        )
                    }
                },
                {
                    it.showError()
                },
            )
            _currentState.postValue(CreateNewAccountScreenState.AccountCreated)
        }
    }

    private fun handleCreateAccountRPCError(context: Context, error: CreateNewAccountRPC.Error) {
        ManageAccountRPCExceptionHandler.getCreateNewAccountRPCErrorMessage(context, error)
            .showError()
    }

    private fun UIError.showError() {
        _currentState.postValue(CreateNewAccountScreenState.Error(this))
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(CreateNewAccountScreenState.AcceptInput)
    }

    fun onNavigatedUp() {
        _currentState.postValue(CreateNewAccountScreenState.AcceptInput)
    }
}

sealed class CreateNewAccountScreenState {
    object AcceptInput : CreateNewAccountScreenState()
    object Loading : CreateNewAccountScreenState()
    object AccountCreated : CreateNewAccountScreenState()
    data class Error(val uiError: UIError) : CreateNewAccountScreenState()
}
