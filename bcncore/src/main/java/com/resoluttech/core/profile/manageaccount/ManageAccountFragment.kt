package com.resoluttech.core.profile.manageaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.POSITION_KEY
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment

class ManageAccountFragment : BaseFragment() {

    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private lateinit var createAccountFAB: ExtendedFloatingActionButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_manage_account, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.manageAccountsViewTitle))
        if (arguments?.getString(KEY_PREVIOUS_PATH, null) == ManageAccountPreviousPath.WALLET_SELECTOR.name) {
            val args = bundleOf(
                KEY_PREVIOUS_PATH to ManageAccountPreviousPath.WALLET_SELECTOR.name,
            )
            findNavController().navigate(R.id.action_manageAccountFragment_to_createNewAccountFragment, args)
        }
        val tabHeading = getTabHeadings()
        initViews(view)
        tabLayout = view.findViewById(R.id.tab_layout)
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = tabHeading[position]
        }.attach()
    }

    override fun onResume() {
        super.onResume()
        arguments?.clear()
    }

    private fun getTabHeadings(): List<String> {
        return listOf(getString(R.string.manageAccountsActiveAccounts), getString(R.string.manageAccountsInactiveAccounts))
    }

    private fun initViews(view: View) {
        viewPager = view.findViewById(R.id.pager)
        viewPager.adapter = ManageAccountViewPagerAdapter(this)
        createAccountFAB = view.findViewById(R.id.create_account_fab)

        createAccountFAB.setOnClickListener {
            findNavController().navigate(R.id.action_manageAccountFragment_to_createNewAccountFragment)
        }
    }
}

private class ManageAccountViewPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        val fragment = ManageAccountContent()
        fragment.arguments = Bundle().apply {
            putInt(POSITION_KEY, position)
        }
        return fragment
    }
}

enum class ManageAccountPreviousPath {
    WALLET_SELECTOR,
}

const val KEY_PREVIOUS_PATH: String = "previousPath"
