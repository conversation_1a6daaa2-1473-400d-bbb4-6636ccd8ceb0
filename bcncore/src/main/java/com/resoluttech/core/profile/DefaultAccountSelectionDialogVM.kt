package com.resoluttech.core.profile

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.Account
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.launch
import java.util.UUID
import kotlin.properties.Delegates

class DefaultAccountSelectionDialogVM : ViewModel() {
    private var _currentState: MutableLiveData<DefaultAccountSelectionDialogState> =
        MutableLiveData(DefaultAccountSelectionDialogState.AcceptInput)
    val state: LiveData<DefaultAccountSelectionDialogState> = _currentState
    lateinit var accountsList: List<Account>
    private val repository: ProfileRepository = ProfileRepository()
    private var position by Delegates.notNull<Int>()

    fun initAccountList() {
        accountsList = SelectedAccountHelper.getPersistedAccounts()
    }

    fun onPositiveButtonClicked(context: Context) {
        if (state.value is DefaultAccountSelectionDialogState.AccountChanged) {
            _currentState.postValue(DefaultAccountSelectionDialogState.DismissDialog)
        } else {
            onDefaultAccountChanged(context)
        }
    }

    private fun onDefaultAccountChanged(context: Context) {
        _currentState.postValue(DefaultAccountSelectionDialogState.Loading)
        viewModelScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = repository.changeDefaultAccount(UUID.fromString(accountsList[position].id), Currency(accountsList[position].currencyCode))) {
                        is LeoRPCResult.LeoResponse -> {
                            _currentState.postValue(DefaultAccountSelectionDialogState.AccountChanged)
                        }
                        is LeoRPCResult.LeoError -> {
                            ProfileRPCExceptionHandler.getChangeDefaultAccountErrorMessage(context, result.error).apply {
                                showError(this)
                            }
                        }
                    }
                },
                handleException = ::showError,
            )
        }
    }

    private fun showError(error: UIError) {
        _currentState.postValue(DefaultAccountSelectionDialogState.Error(error))
    }

    fun onDialogDismissed() {
        _currentState.postValue(DefaultAccountSelectionDialogState.AcceptInput)
    }

    fun onAccountDropdownItemSelected(position: Int) {
        this.position = position
    }

    fun onNegativeButtonClicked() {
        _currentState.postValue(DefaultAccountSelectionDialogState.DismissDialog)
    }
}

sealed class DefaultAccountSelectionDialogState {
    object Loading : DefaultAccountSelectionDialogState()
    object AcceptInput : DefaultAccountSelectionDialogState()
    object AccountChanged : DefaultAccountSelectionDialogState()
    object DismissDialog : DefaultAccountSelectionDialogState()
    data class Error(val error: UIError) : DefaultAccountSelectionDialogState()
}
