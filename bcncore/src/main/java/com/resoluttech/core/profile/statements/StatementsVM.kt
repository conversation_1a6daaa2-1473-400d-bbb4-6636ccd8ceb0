package com.resoluttech.core.profile.statements

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.GetListOfStatementsDateRPC
import com.resoluttech.bcn.profile.GetStatementUrlRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DownloadManager
import com.resoluttech.core.utils.PDFRendererPreviousPath
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.io.File
import java.net.URL
import java.util.UUID

class StatementsVM : ViewModel() {

    private var _currentState: MutableLiveData<ListOfStatementMonthScreenState> =
        MutableLiveData(ListOfStatementMonthScreenState.Waiting)
    val currentState: LiveData<ListOfStatementMonthScreenState> = _currentState

    private val vmIoScope = viewModelScope + Dispatchers.IO

    private val repository = StatementsRepository()

    fun getListOfStatementMonth(context: Context) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.getListOfStatementsDate()) {
                        is LeoRPCResult.LeoResponse -> handleListOfStatementsDateRPCResponse(
                            context,
                            result.response,
                        )

                        is LeoRPCResult.LeoError -> handleListOfStatementsDateRPCError()
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleListOfStatementsDateRPCResponse(
        context: Context,
        response: GetListOfStatementsDateRPC.Response,
    ) {
        if (response.listOfStatementsDate.isNotEmpty()) {
            val monthsArray = context.resources.getStringArray(R.array.months_array)
            val listOfStatementMonths = response.listOfStatementsDate.map {
                StatementMonth(
                    it.documentId,
                    context.getString(
                        R.string.statementTitle,
                        monthsArray[it.endDate.monthValue - 1],
                    ),
                    it.startDate.getFormattedDateTime(
                        context = context,
                        dateTimeType = DateTimeType.DATE,
                    ),
                    it.endDate.getFormattedDateTime(
                        context = context,
                        dateTimeType = DateTimeType.DATE,
                    ),
                )
            }
            _currentState.postValue(ListOfStatementMonthScreenState.Data(listOfStatementMonths))
        } else {
            _currentState.postValue(ListOfStatementMonthScreenState.NoStatementsAvailable)
        }
    }

    private fun handleListOfStatementsDateRPCError() {
        throw IllegalStateException("There is no error in GetListOfStatementsDateRPC.Error")
    }

    fun getStatementURL(context: Context, documentId: UUID, statementMonth: String) {
        _currentState.postValue(ListOfStatementMonthScreenState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.getStatementUrl(documentId)) {
                        is LeoRPCResult.LeoResponse -> handleLGetStatementUrlRPCResponse(
                            context,
                            statementMonth.replace(",", ""),
                            result.response,
                        )

                        is LeoRPCResult.LeoError -> handleGetStatementUrlRPCError(
                            context,
                            result.error,
                        )
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleLGetStatementUrlRPCResponse(
        context: Context,
        statementMonth: String,
        response: GetStatementUrlRPC.Response,
    ) {
        val file = File(
            context.applicationContext.filesDir.path + File.separator + "${
                statementMonth.replace(
                    " ",
                    "_",
                )
            }.pdf",
        )
        DownloadManager.download(
            response.statementUrl.toString(),
            file.path,
            context,
            {
                _currentState.postValue(
                    ListOfStatementMonthScreenState.StatementDocumentDownloaded(
                        statementMonth,
                        response.statementUrl,
                    ),
                )
            },
            { it.showError() },
        )
    }

    private fun handleGetStatementUrlRPCError(
        context: Context,
        error: GetStatementUrlRPC.Error,
    ) {
        when (error) {
            is GetStatementUrlRPC.Error.DocumentNotFound -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleGenericError),
                    context.getString(R.string.alertMessageGenericError),
                    DialogCodes.STATEMENT_DOCUMENT_NOT_FOUND,
                ).showError()
            }
        }
    }

    private fun UIError.showError() {
        _currentState.postValue(ListOfStatementMonthScreenState.Error(this))
    }

    fun onInlineErrorDismiss() {
        _currentState.postValue(ListOfStatementMonthScreenState.AcceptInput)
    }

    fun onServerErrorDismissed(navController: NavController) {
        navController.navigateUp()
    }

    fun onStatementNotFound(navController: NavController) {
        navController.navigateUp()
    }

    fun onStatementDownloaded(navController: NavController, statementMonth: String) {
        val action =
            ListOfStatementMonthsFragmentDirections.actionListOfStatementMonthsFragmentToPDFRendererFragment(
                statementMonth,
                PDFRendererPreviousPath.STATEMENT.name,
            )
        _currentState.postValue(ListOfStatementMonthScreenState.AcceptInput)
        navController.navigate(action)
    }

    fun onShareStatement(context: Context, file: File) {
        vmIoScope.launch {
            val fileURI = FileProvider.getUriForFile(
                context,
                context.packageName + ".provider",
                file,
            )
            val shareStatementURL = Intent(Intent.ACTION_SEND)
            shareStatementURL.apply {
                type = "application/pdf"
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                putExtra(Intent.EXTRA_SUBJECT, context.getString(R.string.statementsTitle))
                putExtra(Intent.EXTRA_STREAM, fileURI)
            }
            context.startActivity(
                Intent.createChooser(
                    shareStatementURL,
                    context.getString(R.string.statementsTitle),
                ),
            )
        }
    }
}

sealed class ListOfStatementMonthScreenState {
    object AcceptInput : ListOfStatementMonthScreenState()
    object Loading : ListOfStatementMonthScreenState()
    object Waiting : ListOfStatementMonthScreenState()
    data class Data(val listOfStatementMonths: List<StatementMonth>) :
        ListOfStatementMonthScreenState()

    data class StatementDocumentDownloaded(val statementMonth: String, val statementUrl: URL) :
        ListOfStatementMonthScreenState()

    object NoStatementsAvailable : ListOfStatementMonthScreenState()
    data class Error(val uiError: UIError) : ListOfStatementMonthScreenState()
}
