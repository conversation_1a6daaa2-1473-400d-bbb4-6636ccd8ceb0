package com.resoluttech.core.profile.manageaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.EditText
import android.widget.LinearLayout
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.CurrencyDropdownAdapter
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.suryadigital.leo.libui.textdropdown.TextDropdown

class CreateNewAccountFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val createNewAccountVM by navGraphViewModels<CreateNewAccountVM>(R.id.manage_accounts_nav)

    private lateinit var accountNameET: EditText
    private lateinit var accountNameTIL: TextInputLayout
    private lateinit var currencyDropdown: TextDropdown
    private lateinit var dropdownSection: LinearLayout
    private lateinit var createAccountButton: MaterialButton
    private var selectedCurrency: Currency? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_create_new_account_fragment, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.createNewAccountViewTitle), true)
        setupBackPressed {
            if (requireArguments().getString(
                    KEY_PREVIOUS_PATH,
                    null,
                ) == ManageAccountPreviousPath.WALLET_SELECTOR.name
            ) {
                findNavController().popBackStack(R.id.manage_accounts_nav, true)
            } else {
                findNavController().navigateUp()
            }
        }
        initViews()
        setupViews()
        networkListenerCallback = this
        createNewAccountVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onPause() {
        super.onPause()
        currencyDropdown.dismiss()
    }

    private fun reactToState(state: CreateNewAccountScreenState) {
        when (state) {
            is CreateNewAccountScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            is CreateNewAccountScreenState.AccountCreated -> {
                handleAccountCreatedState()
            }

            is CreateNewAccountScreenState.Error -> {
                handleErrorState(state)
            }

            is CreateNewAccountScreenState.Loading -> {
                handleLoadingState()
            }
        }
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleAccountCreatedState() {
        if (requireArguments().getString(
                KEY_PREVIOUS_PATH,
                null,
            ) == ManageAccountPreviousPath.WALLET_SELECTOR.name
        ) {
            findNavController().popBackStack(R.id.manage_accounts_nav, true)
        } else {
            findNavController().navigateUp()
        }
        createNewAccountVM.onNavigatedUp()
    }

    private fun handleErrorState(state: CreateNewAccountScreenState.Error) {
        when (state.uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                state.uiError.errorMessage,
                requireView(),
                createNewAccountVM::onInlineErrorDismissed,
            )

            ErrorType.DIALOG -> {
                showErrorDialog(
                    state.uiError.errorTitle,
                    state.uiError.errorMessage,
                    state.uiError.errorCode ?: DialogCodes.MANAGE_ACCOUNT_ERROR_CODE,
                    forceOutUserDestination = ForceOutUserDestination.MANAGE_WALLET,
                )
            }

            ErrorType.BANNER -> {
                dismissProgressDialog()
                handleNetworkLostState()
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun setupViews() {
        createAccountButton.disable()
        createAccountButton.setOnClickListener {
            createNewAccountVM.createAccount(
                requireContext(),
                accountNameET.text.toString().trim(),
                selectedCurrency!!,
            )
        }
        val countryCurrencies = UserSharedPreference.getUserCountryCurrency()
        if (countryCurrencies.size == 1) {
            dropdownSection.visibility = View.GONE
            selectedCurrency = Currency(countryCurrencies.first().currencyCode)
            accountNameTIL.suffixText = selectedCurrency!!.currencyCode
        } else {
            accountNameTIL.suffixText = requireContext().getEmptyString()
            dropdownSection.visibility = View.VISIBLE
            currencyDropdown.adapter = CurrencyDropdownAdapter(countryCurrencies)
            currencyDropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
                override fun onNothingSelected(parent: AdapterView<*>?) {}

                override fun onItemSelected(
                    parent: AdapterView<*>?,
                    view: View?,
                    position: Int,
                    id: Long,
                ) {
                    selectedCurrency = Currency(countryCurrencies[position].currencyCode)
                }
            }
        }
        accountNameET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = accountNameTIL,
                maxLength = Config.MAX_WALLET_DISPLAY_NAME_LENGTH,
                onTextChanged = {
                    if (it?.isNotBlank() == true) {
                        createAccountButton.addContentDescriptionString(
                            R.string.axAddNewWalletEnabledButtonHint,
                            requireContext(),
                        )
                        createAccountButton.enable()
                    } else {
                        createAccountButton.addContentDescriptionString(
                            R.string.axAddNewWalletNameDisabledHint,
                            requireContext(),
                        )
                        createAccountButton.disable()
                    }
                },
            ),
        )
    }

    private fun initViews() {
        view?.apply {
            accountNameET = findViewById(R.id.account_name_et)
            accountNameTIL = findViewById(R.id.account_name_til)
            currencyDropdown = findViewById(R.id.currency_dropdown)
            dropdownSection = findViewById(R.id.dropdown_section)
            createAccountButton = findViewById(R.id.create_account_button)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        createNewAccountVM.onInlineErrorDismissed()
    }

    override fun onNegativeAction(dialogId: Int) {
        // Default action is already handled
    }

    override fun onNetworkAvailable() {
        createNewAccountVM.onInlineErrorDismissed()
    }
}
