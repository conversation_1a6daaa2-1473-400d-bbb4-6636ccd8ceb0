package com.resoluttech.core.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment

class ThirdPartySoftwareFragment : BaseFragment() {
    private lateinit var coilWebLink: TextView
    private lateinit var coilLicenseLink: TextView
    private lateinit var gsonWebLink: TextView
    private lateinit var gsonLicenseLink: TextView
    private lateinit var okhttpWebLink: TextView
    private lateinit var okhttpLicenseLink: TextView
    private lateinit var uCropWebLink: TextView
    private lateinit var uCropLicenseLink: TextView
    private lateinit var visionCommonWebLink: TextView
    private lateinit var mlKitBarcodeWebLink: TextView
    private lateinit var visionCommonLicenseLink: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_third_party, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        setupWebLinkTexts()
        setupInAppBrowserNavigation()
        showToolbar()
        setDefaultToolbar(requireContext().getString(R.string.profileScreenOptionThirdPartySoftware))
    }

    private fun initView(view: View) {
        view.apply {
            coilWebLink = findViewById(R.id.coil_link_tv)
            coilLicenseLink = findViewById(R.id.coil_license_link_tv)
            gsonWebLink = findViewById(R.id.gson_link_tv)
            gsonLicenseLink = findViewById(R.id.gson_license_link_tv)
            okhttpWebLink = findViewById(R.id.okHttp_link_tv)
            okhttpLicenseLink = findViewById(R.id.okHttp_license_link_tv)
            uCropWebLink = findViewById(R.id.uCrop_link_tv)
            uCropLicenseLink = findViewById(R.id.uCrop_license_link_tv)
            visionCommonWebLink = findViewById(R.id.vision_common_link_tv)
            mlKitBarcodeWebLink = findViewById(R.id.barcode_scanning_link_tv)
            visionCommonLicenseLink = findViewById(R.id.vision_common_license_link_tv)
        }
    }

    private fun setupWebLinkTexts() {
        coilWebLink.text = COIL_WEBLINK
        gsonWebLink.text = GSON_WEBLINK
        okhttpWebLink.text = OKHTTP_WEBLINK
        uCropWebLink.text = UCROP_WEBLINK
        visionCommonWebLink.text = VISION_COMMON_WEBLINK
        mlKitBarcodeWebLink.text = MLKIT_BARCODE_WEBLINK
        coilLicenseLink.text = APACHE_LICENSE_WEBLINK
        gsonLicenseLink.text = APACHE_LICENSE_WEBLINK
        okhttpLicenseLink.text = APACHE_LICENSE_WEBLINK
        uCropLicenseLink.text = APACHE_LICENSE_WEBLINK
        visionCommonLicenseLink.text = APACHE_LICENSE_WEBLINK
    }

    private fun setupInAppBrowserNavigation() {
        coilWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(COIL_WEBLINK))
        }
        gsonWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(GSON_WEBLINK))
        }
        okhttpWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(OKHTTP_WEBLINK))
        }
        uCropWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(UCROP_WEBLINK))
        }
        visionCommonWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(VISION_COMMON_WEBLINK))
        }
        mlKitBarcodeWebLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(MLKIT_BARCODE_WEBLINK))
        }
        coilLicenseLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(APACHE_LICENSE_WEBLINK))
        }
        gsonLicenseLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(APACHE_LICENSE_WEBLINK))
        }
        okhttpLicenseLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(APACHE_LICENSE_WEBLINK))
        }
        uCropLicenseLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(APACHE_LICENSE_WEBLINK))
        }
        visionCommonLicenseLink.setOnClickListener {
            findNavController().navigate(getActionToInAppFragment(APACHE_LICENSE_WEBLINK))
        }
    }

    private fun getActionToInAppFragment(destinationUrl: String): NavDirections {
        return ThirdPartySoftwareFragmentDirections.actionThirdPartySoftwareFragmentsToInAppBrowserFragment(
            destinationUrl,
            shouldShowBackButton = true,
            requireContext().getString(R.string.profileScreenOptionThirdPartySoftware),
        )
    }
}

private const val COIL_WEBLINK: String = "https://coil-kt.github.io/coil"
private const val GSON_WEBLINK: String = "https://github.com/google/gson"
private const val OKHTTP_WEBLINK: String = "https://github.com/square/okhttp"
private const val UCROP_WEBLINK: String = "https://github.com/Yalantis/uCrop"
private const val VISION_COMMON_WEBLINK: String = "https://developers.google.com/ml-kit/terms"
private const val MLKIT_BARCODE_WEBLINK: String =
    "https://developers.google.com/ml-kit/vision/barcode-scanning"
private const val APACHE_LICENSE_WEBLINK: String = "https://www.apache.org/licenses/LICENSE-2.0"
