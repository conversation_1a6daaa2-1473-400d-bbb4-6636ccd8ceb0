package com.resoluttech.core.profile

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.UpdateEmailIdRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import com.suryadigital.leo.types.LeoInvalidLeoEmailIdException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class AddEmailAddressVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<AddEmailAddressState> =
        MutableLiveData(AddEmailAddressState.AcceptInput)
    val currentState: LiveData<AddEmailAddressState> = _currentState
    private val profileRepository = ProfileRepository()
    lateinit var emailAddress: String

    fun onSendEmailVerificationClicked(
        context: Context,
        emailId: String,
    ) {
        emailAddress = emailId
        _currentState.postValue(AddEmailAddressState.Loading)
        val leoEmailID: LeoEmailId
        try {
            leoEmailID = LeoEmailId(emailId)
        } catch (e: LeoInvalidLeoEmailIdException) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleInvalidEmailId),
                context.getString(R.string.alertMessageInvalidEmailId),
            ).showError()
            return
        }
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = profileRepository.updateEmailID(leoEmailID)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSuccessfulResponse()
                        }
                        is LeoRPCResult.LeoError -> {
                            handleErrorState(context, response.error)
                        }
                    }
                },
                handleException = {
                    _currentState.postValue(AddEmailAddressState.Error(it))
                },
            )
        }
    }

    private fun handleSuccessfulResponse() {
        _currentState.postValue(AddEmailAddressState.EmailVerified)
    }

    private fun handleErrorState(context: Context, error: UpdateEmailIdRPC.Error) {
        ProfileRPCExceptionHandler.handleUpdateEmailIDRPCErrors(context, error).showError()
    }

    fun UIError.showError() {
        _currentState.postValue(AddEmailAddressState.Error(this))
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(AddEmailAddressState.AcceptInput)
    }

    fun onSendVerificationSuccessful(navController: NavController) {
        _currentState.postValue(AddEmailAddressState.AcceptInput)
        navController.navigateUp()
    }
}

sealed class AddEmailAddressState {
    data class Error(val error: UIError) : AddEmailAddressState()
    object AcceptInput : AddEmailAddressState()
    object Loading : AddEmailAddressState()
    object EmailVerified : AddEmailAddressState()
}
