package com.resoluttech.core.profile.trustedcontacts

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.GetTrustedContactsRPC
import com.resoluttech.bcn.profile.RemoveTrustedContactRPC
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class ManageTrustedContactsVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<ManageTrustedContactScreenState> =
        MutableLiveData(ManageTrustedContactScreenState.AcceptInput)
    val currentState: LiveData<ManageTrustedContactScreenState> = _currentState
    private val repository = TrustedContactRepository()
    var trustedContactId: UUID? = null
    var trustedContacts: List<TrustedContact>? = null
    var maxNumberOfTrustedContact: Int? = null

    fun getTrustedContacts(context: Context) {
        if (_currentState.value !is ManageTrustedContactScreenState.RemoveTrustedContactConfirmation) {
            _currentState.postValue(ManageTrustedContactScreenState.Loading)
            trustedContacts = null
            maxNumberOfTrustedContact = null
            getTrustedContactsRPC(context)
        }
    }

    private fun getTrustedContactsRPC(context: Context) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.getTrustedContactsRPC()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetTrustedContactsRPCResponse(result.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetTrustedContactsRPCError(result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleGetTrustedContactsRPCResponse(
        response: GetTrustedContactsRPC.Response,
    ) {
        if (response.trustedContacts.isEmpty()) {
            throw IllegalStateException("Trusted Contact's list cannot be empty in profile")
        } else {
            trustedContacts = response.trustedContacts
            maxNumberOfTrustedContact = response.maxNumberOfTrustedContact
            _currentState.postValue(
                ManageTrustedContactScreenState.Data(
                    response.trustedContacts,
                    response.maxNumberOfTrustedContact,
                ),
            )
        }
    }

    private fun handleGetTrustedContactsRPCError(
        error: GetTrustedContactsRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleGetTrustedContactsRPCErrors(error).showError()
    }

    fun onRemoveTrustedContactTapped(context: Context, trustedContactId: UUID) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = repository.removeTrustedContactRPC(trustedContactId)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleRemoveTrustedContactRPCResponse(context)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleRemoveTrustedContactRPCError(result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleRemoveTrustedContactRPCResponse(context: Context) {
        _currentState.postValue(ManageTrustedContactScreenState.Loading)
        getTrustedContactsRPC(context)
    }

    private fun handleRemoveTrustedContactRPCError(
        error: RemoveTrustedContactRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleRemoveTrustedContactRPCErrors(error).showError()
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(ManageTrustedContactScreenState.AcceptInput)
    }

    fun onServerErrorDismissed(navController: NavController) {
        navController.navigateUp()
    }

    private fun UIError.showError() {
        _currentState.postValue(ManageTrustedContactScreenState.Error(this))
    }

    fun onBackPressed(navController: NavController) {
        _currentState.postValue(ManageTrustedContactScreenState.AcceptInput)
        trustedContacts = null
        maxNumberOfTrustedContact = null
        navController.navigateUp()
    }

    fun onAddContactButtonTapped(navController: NavController) {
        trustedContacts = null
        maxNumberOfTrustedContact = null
        _currentState.postValue(ManageTrustedContactScreenState.AcceptInput)
        navController.navigate(R.id.action_manageTrustedContactsFragment_to_addTrustedContactFragmentProfile)
    }

    fun onRemoveContactTapped(trustedContactName: String) {
        _currentState.postValue(
            ManageTrustedContactScreenState.RemoveTrustedContactConfirmation(
                trustedContactName,
            ),
        )
    }
}

sealed class ManageTrustedContactScreenState {
    data class Error(val error: UIError) : ManageTrustedContactScreenState()
    object AcceptInput : ManageTrustedContactScreenState()
    object Loading : ManageTrustedContactScreenState()
    data class Data(val listOfTrusted: List<TrustedContact>, val maxNumberOfTrustedContact: Int) :
        ManageTrustedContactScreenState()

    data class RemoveTrustedContactConfirmation(val trustedContactName: String) :
        ManageTrustedContactScreenState()
}
