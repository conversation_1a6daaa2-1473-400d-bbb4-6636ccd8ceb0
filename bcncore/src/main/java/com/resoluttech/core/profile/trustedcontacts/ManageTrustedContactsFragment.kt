package com.resoluttech.core.profile.trustedcontacts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.core.view.isEmpty
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.uicomponents.TrustedContactAdapter
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import java.util.UUID

class ManageTrustedContactsFragment :
    BaseFragment(),
    TrustedContactAdapter.TrustedContactsListListener,
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val manageTrustedContactsVM: ManageTrustedContactsVM by navGraphViewModels(R.id.home_nav)
    private val addTrustedContactCCPVM: AddTrustedContactCCPVM by activityViewModels()

    private lateinit var addContactFAB: ExtendedFloatingActionButton
    private lateinit var dataRV: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_manage_trusted_contacts, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        resetAddTrustedContactCCPVM()
        showToolbar()
        setDefaultToolbar(getString(R.string.manageTrustedContactsTitle))
        setupBackPressed { manageTrustedContactsVM.onBackPressed(findNavController()) }
        initViews()
        setSwipeToRefreshLayout()
        networkListenerCallback = this
        manageTrustedContactsVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        manageTrustedContactsVM.getTrustedContacts(requireContext())
    }

    private fun resetAddTrustedContactCCPVM() {
        addTrustedContactCCPVM.profileAddContactSelectedCountryCode = null
        addTrustedContactCCPVM.singUpAddTrustedContactSelectedCountryCode = null
    }

    private fun setupListener(maxNumberOfTrustedContact: Int, numberOfTrustedContact: Int) {
        addContactFAB.setOnClickListener {
            if (numberOfTrustedContact == maxNumberOfTrustedContact) {
                showErrorDialog(
                    requireContext().getString(R.string.alertTitleTrustedContactLimitReached),
                    requireContext().getString(R.string.alertMessageTrustedContactLimitReached),
                    DialogCodes.TRUSTED_CONTACTS_ERROR_CODE,
                )
            } else {
                manageTrustedContactsVM.onAddContactButtonTapped(findNavController())
            }
        }
    }

    private fun reactToState(state: ManageTrustedContactScreenState) {
        when (state) {
            is ManageTrustedContactScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is ManageTrustedContactScreenState.Data -> {
                handleDataState(state)
            }
            is ManageTrustedContactScreenState.Error -> {
                handleErrorState(state)
            }
            is ManageTrustedContactScreenState.Loading -> {
                handleLoadingState()
            }
            is ManageTrustedContactScreenState.RemoveTrustedContactConfirmation -> {
                handleRemoveTrustedContactState(state.trustedContactName)
            }
        }
    }

    private fun handleLoadingState() {
        toggleSwipeToRefreshState(isEnable = false, isRefreshing = false)
        showViewForState(progressBar)
    }

    private fun handleAcceptInputState() {
        toggleSwipeToRefreshState(isEnable = true, isRefreshing = false)
        dismissProgressDialog()
    }

    private fun handleErrorState(state: ManageTrustedContactScreenState.Error) {
        val uiError = state.error
        progressBar.visibility = View.GONE
        toggleSwipeToRefreshState(isEnable = true, isRefreshing = false)
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    manageTrustedContactsVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showCachedTrustedContact(
                    manageTrustedContactsVM.trustedContacts,
                    manageTrustedContactsVM.maxNumberOfTrustedContact,
                )
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.TRUSTED_CONTACTS_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleDataState(state: ManageTrustedContactScreenState.Data) {
        showTrustedContactList(state.listOfTrusted, state.maxNumberOfTrustedContact)
    }

    private fun handleRemoveTrustedContactState(trustedContactName: String) {
        showCachedTrustedContact(
            manageTrustedContactsVM.trustedContacts,
            manageTrustedContactsVM.maxNumberOfTrustedContact,
        )
        showAlertDialog(
            requireContext().getString(R.string.alertTitleRemoveTrustedContact),
            requireContext().getString(
                R.string.alertMessageRemoveTrustedContact,
                trustedContactName,
            ),
            DialogCodes.REMOVE_TRUSTED_CONTACTS_CODE,
            getString(R.string.alertActionRemove),
            getString(R.string.alertActionCancel),
            AlertDialogButtonColor(
                requireContext().getColor(R.color.destructiveActionColor),
                requireContext().getColor(R.color.colorPrimaryLight),
            ),
        )
    }

    private fun showAlertDialog(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun showTrustedContactList(
        trustedContacts: List<TrustedContact>,
        maxNumberOfTrustedContact: Int,
    ) {
        setupListener(maxNumberOfTrustedContact, trustedContacts.size)
        if (maxNumberOfTrustedContact == trustedContacts.size) {
            addContactFAB.disable()
        } else {
            addContactFAB.enable()
        }
        toggleSwipeToRefreshState(isEnable = true, isRefreshing = false)
        showViewForState(dataRV)
        dataRV.adapter = TrustedContactAdapter(this, trustedContacts)
    }

    private fun initViews() {
        view?.apply {
            addContactFAB = findViewById(R.id.add_tc_fab)
            dataRV = findViewById(R.id.tc_rv)
            swipeToRefreshLayout = findViewById(R.id.swipe_to_refresh)
            progressBar = findViewById(R.id.progressBar)
        }
    }

    private fun setSwipeToRefreshLayout() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            if (manageTrustedContactsVM.currentState.value is ManageTrustedContactScreenState.Loading) {
                swipeToRefreshLayout.isRefreshing = false
            } else {
                manageTrustedContactsVM.getTrustedContacts(requireContext())
            }
        }
    }

    private fun showCachedTrustedContact(
        trustedContacts: List<TrustedContact>?,
        maxNumberOfTrustedContact: Int?,
    ) {
        if (dataRV.isEmpty()) {
            if (trustedContacts != null && maxNumberOfTrustedContact != null) {
                showTrustedContactList(trustedContacts, maxNumberOfTrustedContact)
            }
        }
    }

    private fun toggleSwipeToRefreshState(isEnable: Boolean, isRefreshing: Boolean) {
        swipeToRefreshLayout.isEnabled = isEnable
        swipeToRefreshLayout.isRefreshing = isRefreshing
    }

    private fun showViewForState(view: View) {
        when (view) {
            dataRV -> {
                progressBar.visibility = View.INVISIBLE
                dataRV.visibility = View.VISIBLE
            }
            progressBar -> {
                dataRV.visibility = View.INVISIBLE
                progressBar.visibility = View.VISIBLE
            }
        }
    }

    override fun onTrustedContactRemoved(trustedContactId: UUID, name: String) {
        manageTrustedContactsVM.trustedContactId = trustedContactId
        manageTrustedContactsVM.onRemoveContactTapped(name)
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                manageTrustedContactsVM.onServerErrorDismissed(findNavController())
            }
            DialogCodes.REMOVE_TRUSTED_CONTACTS_CODE -> {
                manageTrustedContactsVM.onRemoveTrustedContactTapped(
                    requireContext(),
                    manageTrustedContactsVM.trustedContactId
                        ?: throw IllegalStateException("trustedContactId cannot be null for removing a trusted contact"),
                )
            }
            else -> {
                manageTrustedContactsVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        manageTrustedContactsVM.onInlineErrorDismissed()
        manageTrustedContactsVM.trustedContactId = null
    }

    override fun onNetworkAvailable() {
        manageTrustedContactsVM.getTrustedContacts(requireContext())
    }
}
