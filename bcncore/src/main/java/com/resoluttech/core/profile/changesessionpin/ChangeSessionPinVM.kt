package com.resoluttech.core.profile.changesessionpin

import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.auth.app.AppPinHelper
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.auth.biometric.BiometricPreferenceHelper
import com.resoluttech.core.config.Config
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.peertopeer.SendMoneyVM
import com.resoluttech.core.utils.SignOutRepository
import com.resoluttech.core.utils.clearAllUserData
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.logout
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber

class ChangeSessionPinVM : ViewModel() {

    private var _currentState: MutableLiveData<ChangeSessionPinScreenState> =
        MutableLiveData(ChangeSessionPinScreenState.AcceptInput)
    val currentState: LiveData<ChangeSessionPinScreenState> = _currentState
    private val context: Context by KoinJavaComponent.inject(Context::class.java)

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private var currentSessionPin: String? = null
    private var newSessionPin: String? = null
    private var biometricAuthenticationTriesLeft = 3
    private var canAuthenticateByBiometric: Boolean = false

    init {
        vmIoScope.launch {
            canAuthenticateByBiometric = Biometric.isBiometricHardwareAvailable()
            _currentState.postValue(
                ChangeSessionPinScreenState.Data(
                    canAuthenticateByBiometric,
                ),
            )
        }
    }

    fun canUserUseBiometricAuthentication() {
        vmIoScope.launch {
            _currentState.postValue(
                ChangeSessionPinScreenState.BiometricAuthentication(
                    Biometric.isBiometricHardwareAvailable(),
                ),
            )
            val appPinAuthenticationTriesLeft = AppPinHelper.getAppPinAttemptLeft()
            if (appPinAuthenticationTriesLeft == Config.SESSION_PIN_WARNING_ATTEMPT_LEFT && currentSessionPin == null) {
                _currentState.postValue(
                    ChangeSessionPinScreenState.AppPinWarningAttemptLeft,
                )
            }
        }
    }

    fun onBiometricAuthenticationSuccessful() {
        _currentState.postValue(ChangeSessionPinScreenState.AppPinUpdatedSuccessfully)
    }

    fun onAuthenticationFailed() {
        biometricAuthenticationTriesLeft--
        if (biometricAuthenticationTriesLeft == 0) {
            saveBiometricOption(false)
            _currentState.postValue(ChangeSessionPinScreenState.BiometricRetriesExhausted)
        }
    }

    fun onAuthenticationError() {
        saveBiometricOption(false)
        _currentState.postValue(ChangeSessionPinScreenState.BiometricRetriesExhausted)
    }

    private fun saveBiometricOption(checked: Boolean) {
        vmIoScope.launch {
            BiometricPreferenceHelper.setBiometricOption(checked)
        }
    }

    fun onAppPinEntered(appPin: String?, context: Context) {
        vmIoScope.launch {
            if (currentSessionPin == null) {
                if (appPin == AppPinHelper.getAppPin()) {
                    currentSessionPin = appPin
                    _currentState.postValue(ChangeSessionPinScreenState.CurrentAppPinConfirmed)
                    AppPinHelper.resetAppPinAttemptLeft()
                } else {
                    var appPinAuthenticationTriesLeft = AppPinHelper.getAppPinAttemptLeft()
                    appPinAuthenticationTriesLeft -= 1
                    AppPinHelper.setAppPinAttemptLeft(appPinAuthenticationTriesLeft)
                    if (appPinAuthenticationTriesLeft > 0) {
                        _currentState.postValue(
                            ChangeSessionPinScreenState.IncorrectPin(
                                context.resources.getQuantityString(
                                    R.plurals.wrongSessionPinEnteredLabelPlural,
                                    appPinAuthenticationTriesLeft,
                                    appPinAuthenticationTriesLeft,
                                ),
                            ),
                        )
                    } else {
                        AppPinHelper.resetAppPinAttemptLeft()
                        onAppPinRetriesExhausted()
                    }
                }
            } else {
                newSessionPin = appPin
                _currentState.postValue(ChangeSessionPinScreenState.EnterNewAppPin(appPin))
            }
        }
    }

    private fun onAppPinRetriesExhausted() {
        vmIoScope.launch {
            val llt: String? = KeyStoreHelper.getLLT()
            val pushToken = PushTokenHelper.getPushToken(context)
            clearAllUserData()
            executeRPC(
                context = context,
                rpcBlock = {
                    when (
                        val response =
                            SignOutRepository.signOut(pushToken, llt)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            Timber.tag(TAG)
                                .i("signOutUserRPC Called Successfully ${Json.encodeToString(response.response)}")
                        }
                        is LeoRPCResult.LeoError -> {
                            Timber.tag(TAG)
                                .w("signOutUserRPC thrown and exception ${Json.encodeToString(response.error)}")
                        }
                    }
                },
                handleException = {
                    Timber.tag(TAG).e("Server error happened on signOutUserRPC ${it.errorMessage}")
                },
            )
            _currentState.postValue(
                ChangeSessionPinScreenState.AppPinRetriesExhaustedDialog(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePinAttemptsExhausted),
                        context.getString(R.string.alertMessagePinAttemptsExhausted),
                        SendMoneyVM.ERROR_CODE_APP_AUTHENTICATION_FAILED,
                    ),
                ),
            )
        }
    }

    fun onErrorDismissed() {
        _currentState.postValue(ChangeSessionPinScreenState.AcceptInput)
    }

    fun onDialogPositiveAction(activity: FragmentActivity?) {
        vmIoScope.launch {
            logout(activity)
        }
    }

    fun onConfirmPassword(
        confirmPin: String,
        newPin: String,
        isBiometricEnabled: Boolean,
        context: Context,
    ) {
        if (newPin == confirmPin) {
            vmIoScope.launch {
                AppPinHelper.storeAppPin(confirmPin)
                BiometricPreferenceHelper.setBiometricOption(isBiometricEnabled)
            }
            if (isBiometricEnabled) {
                _currentState.postValue(ChangeSessionPinScreenState.AuthenticateBiometrics)
            } else {
                _currentState.postValue(ChangeSessionPinScreenState.AppPinUpdatedSuccessfully)
            }
        } else {
            _currentState.postValue(ChangeSessionPinScreenState.IncorrectPin(context.getString(R.string.alertMessagePinDoesntMatch)))
        }
    }

    fun onAppPinChangedSuccessfully(navController: NavController) {
        navController.navigateUp()
        _currentState.postValue((ChangeSessionPinScreenState.AcceptInput))
    }
}

sealed class ChangeSessionPinScreenState {
    object AcceptInput : ChangeSessionPinScreenState()
    data class Data(val isBiometricEnabled: Boolean) : ChangeSessionPinScreenState()
    object CurrentAppPinConfirmed : ChangeSessionPinScreenState()
    data class EnterNewAppPin(val newAppPin: String?) : ChangeSessionPinScreenState()
    object AppPinUpdatedSuccessfully : ChangeSessionPinScreenState()
    object AuthenticateBiometrics : ChangeSessionPinScreenState()
    data class IncorrectPin(val errorMessage: String) : ChangeSessionPinScreenState()
    data class AppPinRetriesExhaustedDialog(val uiError: UIError) : ChangeSessionPinScreenState()
    data class BiometricAuthentication(val isBiometricPossible: Boolean) :
        ChangeSessionPinScreenState()

    object BiometricRetriesExhausted : ChangeSessionPinScreenState()
    object BiometricFailed : ChangeSessionPinScreenState()
    object AppPinWarningAttemptLeft : ChangeSessionPinScreenState()
}

private const val TAG = "AppPinAuthVM"
