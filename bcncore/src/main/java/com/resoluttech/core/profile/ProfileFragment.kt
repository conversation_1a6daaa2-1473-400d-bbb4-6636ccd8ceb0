package com.resoluttech.core.profile

import android.Manifest
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import coil.load
import com.google.android.material.switchmaterial.SwitchMaterial
import com.resoluttech.bcncore.R
import com.resoluttech.core.kyc.CAMERA_PERMISSION_DIALOG_CODE
import com.resoluttech.core.kyc.DocumentUploadTarget
import com.resoluttech.core.kyc.KYCDataCameraState
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.User
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.ContactSupport
import com.resoluttech.core.views.DocumentPickerDialog
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.imagecrop.ImagePicker
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber

class ProfileFragment :
    BaseFragment(),
    DocumentPickerDialog.DocumentPickerListener,
    AlertDialog.ActionListener,
    ImagePicker.ImagePickerFailureListener,
    BaseFragment.NetworkListener,
    ContactSupport.ActionListener {

    private val profileScreenVM: ProfileScreenVM by navGraphViewModels(R.id.home_nav)

    private lateinit var loadingPB: ProgressBar
    private lateinit var dataView: ScrollView
    private lateinit var appPinTV: TextView
    private lateinit var trustedContactTV: TextView
    private lateinit var biometricSwitch: SwitchMaterial
    private lateinit var biometricRowView: LinearLayout
    private lateinit var profileCIV: ContactIconView
    private lateinit var recurringTransfersTV: TextView
    private lateinit var showQRCodeTV: TextView
    private lateinit var appInfoTv: TextView
    private lateinit var findAgentTV: TextView
    private lateinit var manageAccountTV: TextView
    private lateinit var changeLanguageLL: LinearLayout
    private lateinit var cameraProfileImageIV: ImageButton
    private lateinit var usernameTV: TextView
    private lateinit var userEmailTV: TextView
    private lateinit var userPhoneNumberTV: TextView
    private lateinit var changePasswordTV: TextView
    private lateinit var statementTV: TextView
    private lateinit var shareAppTV: TextView
    private lateinit var signOutTV: TextView
    private lateinit var privacyPolicyTV: TextView
    private lateinit var contactSupportTV: TextView
    private lateinit var thirdPartySoftwareTV: TextView
    private lateinit var closeMyAccountTV: TextView
    private lateinit var updateKYCTV: TextView
    private lateinit var verifyEmailLayout: ConstraintLayout
    private lateinit var sendVerificationLinkTV: TextView
    private lateinit var editEmailIV: ImageView

    private var user = UserSharedPreference.getUser()

    private val startForCameraPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                profileScreenVM.onCameraPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                profileScreenVM.onCameraPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Camera permission denied, unable to proceed with image capture.")
                profileScreenVM.onCameraPermissionDenied()
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_profile, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        profileScreenVM.init()
        initViews(view)
        setToolbar()
        updateView()
        setupProfileImageChange()
        setupShowQRCode()
        setupFindAgent()
        setupManageAccounts()
        setupChangeLanguage()
        setupChangeAppPin()
        setupTrustedContact()
        setupBiometricAuthentication()
        setupChangePassword()
        setupUpdateKYC()
        setupStatementsListener()
        setupLogout()
        setupShareApp()
        setupCloseMyAccount()
        setupPrivacyPolicy()
        setupAppInfo()
        setupContactSupport()
        setupThirdPartySoftware()
        networkListenerCallback = this
        setupContentDescription()
        profileScreenVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        profileScreenVM.currentCameraState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onResume() {
        super.onResume()
        user = UserSharedPreference.getUser()
        updateView()
        shareAppTV.isEnabled = true
    }

    private fun setupContentDescription() {
        usernameTV.addContentDescriptionString(
            R.string.axProfileDetailsUsernameLabel,
            requireContext(),
            usernameTV.text.toString(),
        )
        userPhoneNumberTV.addContentDescriptionString(
            R.string.axProfileDetailsMobileNumberLabel,
            requireContext(),
            userPhoneNumberTV.text.toString(),
        )
    }

    private fun setupStatementsListener() {
        statementTV.setOnClickListener {
            profileScreenVM.onStatementsTapped(findNavController())
        }
    }

    private fun setupShareApp() {
        shareAppTV.setOnClickListener {
            shareAppTV.isEnabled = false
            profileScreenVM.onShareAppTapped(requireActivity(), YAFIKA_MOBILE_PLAY_STORE_LINK)
        }
    }

    private fun setupAddEmail() {
        userEmailTV.setOnClickListener {
            profileScreenVM.onAddEmailTapped(findNavController())
        }
    }

    private fun setupManageAccounts() {
        manageAccountTV.setOnClickListener {
            profileScreenVM.onManageAccountsTapped(findNavController())
        }
    }

    private fun setupCloseMyAccount() {
        closeMyAccountTV.setOnClickListener {
            profileScreenVM.onCloseMyAccountTapped()
        }
    }

    private fun setupPrivacyPolicy() {
        privacyPolicyTV.setOnClickListener {
            dismissProgressDialog()
            profileScreenVM.onPrivacyPolicyTapped(navController = findNavController(), context = requireContext())
        }
    }

    private fun setupContactSupport() {
        contactSupportTV.setOnClickListener {
            val contactSupport = ContactSupport()
            contactSupport.show(childFragmentManager, ContactSupport.TAG)
        }
    }

    private fun setupThirdPartySoftware() {
        thirdPartySoftwareTV.setOnClickListener {
            profileScreenVM.onThirdPartySoftwareTapped(findNavController())
        }
    }

    private fun setupProfileImageChange() {
        cameraProfileImageIV.setOnClickListener {
            profileScreenVM.onProfileImageChangeTapped()
        }
    }

    private fun setupLogout() {
        signOutTV.setOnClickListener {
            profileScreenVM.onLogoutAccountTapped()
        }
    }

    private fun setupChangePassword() {
        changePasswordTV.setOnClickListener {
            profileScreenVM.onChangePasswordTapped(requireContext())
        }
    }

    private fun setupUpdateKYC() {
        KYCSharedPreference.saveKYCDetails(
            KYCDetails(
                previousPath = KYCPreviousPath.PROFILE,
            ),
        )
        updateKYCTV.setOnClickListener {
            profileScreenVM.onUpdateKYCTapped(findNavController())
        }
    }

    private fun reactToState(state: ProfileScreenState) {
        when (state) {
            is ProfileScreenState.FullScreenLoading -> {
                handlingLoadingState()
            }
            is ProfileScreenState.InlineLoading -> {
                handleInlineLoadingState()
            }
            is ProfileScreenState.Data -> {
                handleDataState(state)
            }
            is ProfileScreenState.Error -> {
                handleErrorState(state.uiError)
            }
            is ProfileScreenState.ProfileImagePickerDialog -> {
                handleProfileImagePickerDialogState()
            }
            is ProfileScreenState.Waiting -> {
                handleWaitingState()
            }
            is ProfileScreenState.AccountCloseConfirmation -> {
                handleAccountCloseConfirmation()
            }
            is ProfileScreenState.LogoutAccountConfirmation -> {
                handleLogoutConfirmation()
            }
            is ProfileScreenState.LanguageChanged -> {
                handleLanguageChangedState(state)
            }
            is ProfileScreenState.EmailVerificationSent -> {
                handleEmailVerificationSentState()
            }
            is ProfileScreenState.ProfileImageChange -> {
                handleProfileImageChanged(state.imageURL)
            }
            is ProfileScreenState.PasswordPolicySuccessful -> {
                handlePasswordPolicySuccessState(state)
            }
        }
    }

    private fun handlePasswordPolicySuccessState(state: ProfileScreenState.PasswordPolicySuccessful) {
        profileScreenVM.onPasswordPolicyRPCSuccess(
            findNavController(),
            state.passwordPolicy,
            requireContext(),
        )
    }

    private fun handleLanguageChangedState(state: ProfileScreenState.LanguageChanged) {
        LocaleManager.setLocale(state.supportedLocale, requireContext())
        restartActivity()
    }

    private fun handleAccountCloseConfirmation() {
        showAlertDialog(
            getString(R.string.alertTitleCloseAccount),
            getString(R.string.alertMessageCloseAccount),
            CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID,
            getString(R.string.alertActionConfirm),
            getString(R.string.alertActionCancel),
            AlertDialogButtonColor(
                requireContext().getColor(R.color.destructiveActionColor),
                requireContext().getColor(R.color.colorPrimaryLight),
            ),
        )
    }

    private fun handleLogoutConfirmation() {
        showAlertDialog(
            getString(R.string.alertTitleSignOut),
            getString(R.string.alertMessageSignOut),
            LOGOUT_ACCOUNT_CONFIRMATION_DIALOG_ID,
            getString(R.string.alertActionSignOut),
            getString(R.string.alertActionCancel),
            AlertDialogButtonColor(
                requireContext().getColor(R.color.destructiveActionColor),
                requireContext().getColor(R.color.colorPrimaryLight),
            ),
        )
    }

    private fun handleWaitingState() {
        dismissProgressDialog()
        updateView()
    }

    private fun handleEmailVerificationSentState() {
        user!!.apply {
            val alertDialog = AlertDialog.newInstance(
                getString(R.string.alertTitleEmailSent),
                getString(R.string.alertMessageEmailSent, emailId),
                EMAIL_VERIFICATION_SENT_DIALOG_ID,
                getString(R.string.alertActionDismiss),
                requireContext().getEmptyString(),
            )
            alertDialog.setArguments(false)
            alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
        }
    }

    private fun handleProfileImageChanged(imageUrl: String) {
        if (imageUrl.isNotEmpty() && imageUrl != "null") {
            profileCIV.imageView.loadImage(imageUrl.toUri())
        } else {
            profileCIV.imageView.load(R.drawable.ic_person_black_24dp)
        }
        updateUserImage(imageUrl)
        dismissProgressDialog()
        showAlertDialog(
            getString(R.string.alertTitleProfileImageUploadSuccess),
            getString(R.string.alertMessageProfileImageUploadSuccess),
            PROFILE_IMAGE_UPLOADED,
            requireContext().getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
    }

    private fun reactToState(state: KYCDataCameraState) {
        when (state) {
            is KYCDataCameraState.CameraPermissionAlert -> {
                handleCameraPermissionAlertState()
            }
            is KYCDataCameraState.CameraPermissionDenied -> {
                showCameraPermissionDeniedError()
            }
            is KYCDataCameraState.CameraPermissionGranted -> {
                startCamera()
            }
            is KYCDataCameraState.CameraPermissionRequested -> {
                requestCameraPermission()
            }
            is KYCDataCameraState.Waiting -> {
                handleWaitingOnCameraState()
            }
        }
    }

    private fun handleWaitingOnCameraState() {
        // Nothing to do. Waiting for user to tap on `Capture an Image` from dialog.
    }

    private fun requestCameraPermission() {
        startForCameraPermissionResult.launch(Manifest.permission.CAMERA)
    }

    private fun startCamera() {
        ImagePicker.openCamera(this)
        profileScreenVM.onCameraPermissionDialogDismissed()
    }

    private fun showCameraPermissionDeniedError() {
        showAlertDialog(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            message = getString(R.string.kycCameraPermissionDeniedSubtitle),
            dialogId = com.resoluttech.core.kyc.CAMERA_PERMISSION_DENIED_DIALOG_CODE,
            positiveActionLabel = getString(R.string.alertActionDismiss),
            negativeActionLabel = requireContext().getEmptyString(),
        )
    }

    private fun handleCameraPermissionAlertState() {
        showAlertDialog(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            message = getString(R.string.kycCameraPermissionDeniedSubtitle),
            dialogId = com.resoluttech.core.kyc.CAMERA_PERMISSION_DIALOG_CODE,
            positiveActionLabel = getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun showAlertDialog(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleProfileImagePickerDialogState() {
        val documentPickerDialog = DocumentPickerDialog()
        documentPickerDialog.setArguments(
            showDocumentOption = false,
            showRemoveOption = false,
            documentUploadTarget = DocumentUploadTarget.PROFILE_IMAGE,
        )
        documentPickerDialog.show(childFragmentManager, DocumentPickerDialog.TAG)
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                ) {
                    showViewForState(dataView)
                }
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.SIGN_OUT_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> {
                showViewForState(dataView)
                handleNetworkLostState()
            }
        }
    }

    private fun handlingLoadingState() {
        showViewForState(loadingPB)
    }

    private fun handleInlineLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleDataState(state: ProfileScreenState.Data) {
        showViewForState(dataView)
        if (state.isBiometricAvailable) {
            biometricSwitch.isChecked = state.isBiometricEnabled
        } else {
            hideBiometricRow()
        }
    }

    private fun hideBiometricRow() {
        biometricRowView.visibility = View.GONE
    }

    private fun setupBiometricAuthentication() {
        biometricSwitch.setOnClickListener {
            profileScreenVM.onBiometricAuthOptionChecked(biometricSwitch.isChecked)
        }
    }

    private fun setupChangeAppPin() {
        appPinTV.setOnClickListener {
            profileScreenVM.onChangeAppPinTapped(findNavController())
        }
    }

    private fun setupTrustedContact() {
        trustedContactTV.setOnClickListener {
            profileScreenVM.onTrustedContactsTapped(findNavController())
        }
    }

    private fun setToolbar() {
        setDefaultToolbar(getString(R.string.homeScreenProfileTitle), false)
    }

    private fun updateView() {
        user?.apply {
            if (imageUrl.isNotEmpty() && imageUrl != "null") {
                profileCIV.imageView.loadImage(imageUrl.toUri())
            } else {
                profileCIV.imageView.load(R.drawable.ic_person_black_24dp)
            }
            if (!emailId.isNullOrEmpty()) {
                userEmailTV.text = emailId
                userEmailTV.typeface = Typeface.DEFAULT
                if (!isEmailVerified) {
                    verifyEmailLayout.visibility = View.VISIBLE
                    setupSendVerificationLink()
                }
                userEmailTV.addContentDescriptionString(
                    R.string.axProfileDetailsEmailIDLabel,
                    requireContext(),
                    emailId,
                )
                setupEditEmail()
            } else {
                userEmailTV.setText(R.string.emailVerificationAddEmailTitle)
                userEmailTV.typeface = Typeface.DEFAULT_BOLD
                userEmailTV.setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
                editEmailIV.visibility = View.GONE
                userEmailTV.addContentDescriptionString(
                    R.string.axPrivateAddEmailLabel,
                    requireContext(),
                )
                setupAddEmail()
            }
            usernameTV.text = name
            userPhoneNumberTV.text = LeoPhoneNumber(
                (
                    user
                        ?: throw IllegalStateException("User data cannot be null on Profile Screen")
                    ).phoneNumber,
            ).getFormattedPhoneNumber()
        }
    }

    private fun setupAppInfo() {
        appInfoTv.setOnClickListener {
            profileScreenVM.onAppInfoClicked(findNavController())
        }
    }

    private fun setupSendVerificationLink() {
        sendVerificationLinkTV.setOnClickListener {
            profileScreenVM.requestSendVerification(requireContext())
        }
    }

    private fun setupShowQRCode() {
        showQRCodeTV.setOnClickListener {
            profileScreenVM.onShowQRCodeClicked(findNavController())
        }
    }

    private fun setupFindAgent() {
        findAgentTV.setOnClickListener {
            profileScreenVM.onFindAgentClicked(findNavController())
        }
    }

    private fun setupChangeLanguage() {
        changeLanguageLL.setOnClickListener {
            profileScreenVM.onChangeLanguageClicked(findNavController())
        }
    }

    private fun setupEditEmail() {
        editEmailIV.visibility = View.VISIBLE
        editEmailIV.setOnClickListener {
            profileScreenVM.onEditEmailTapped(findNavController())
        }
    }

    private fun initViews(view: View) {
        view.apply {
            loadingPB = findViewById(R.id.loading)
            dataView = findViewById(R.id.data_layout)
            profileCIV = findViewById(R.id.profile_iv)
            recurringTransfersTV = findViewById(R.id.reccuring_transfers_tv)
            showQRCodeTV = findViewById(R.id.qr_code_tv)
            findAgentTV = findViewById(R.id.find_agent_tv)
            manageAccountTV = findViewById(R.id.manage_accounts_tv)
            changeLanguageLL = findViewById(R.id.change_language_ll)
            cameraProfileImageIV = view.findViewById(R.id.camera_iv)
            usernameTV = findViewById(R.id.user_name_tv)
            userEmailTV = findViewById(R.id.user_id_tv)
            userPhoneNumberTV = findViewById(R.id.phonenumber_tv)
            changePasswordTV = findViewById(R.id.change_password_tv)
            statementTV = findViewById(R.id.statements_tv)
            shareAppTV = findViewById(R.id.share_app_tv)
            appPinTV = findViewById(R.id.app_pin_tv)
            trustedContactTV = findViewById(R.id.trusted_contacts_tv)
            biometricSwitch = findViewById(R.id.biometric_switch)
            biometricRowView = findViewById(R.id.biometric_switch_layout)
            signOutTV = findViewById(R.id.sign_out_tv)
            closeMyAccountTV = findViewById(R.id.close_account_tv)
            privacyPolicyTV = findViewById(R.id.privacy_policy_tv)
            appInfoTv = findViewById(R.id.app_info_tv)
            contactSupportTV = findViewById(R.id.contact_support_tv)
            thirdPartySoftwareTV = findViewById(R.id.third_party_software_tv)
            updateKYCTV = findViewById(R.id.update_kyc_tv)
            verifyEmailLayout = findViewById(R.id.verifyEmail)
            sendVerificationLinkTV = findViewById(R.id.verification_link)
            editEmailIV = findViewById(R.id.edit_email_iv)
        }
    }

    private fun showViewForState(stateView: View) {
        when (stateView) {
            loadingPB -> {
                loadingPB.visibility = View.VISIBLE
                dataView.visibility = View.GONE
            }
            dataView -> {
                loadingPB.visibility = View.GONE
                dataView.visibility = View.VISIBLE
            }
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> profileScreenVM.onOpenSettingsClicked(
                requireContext(),
            )
            CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID -> {
                profileScreenVM.onCloseMyAccountConfirmed(findNavController())
            }
            LOGOUT_ACCOUNT_CONFIRMATION_DIALOG_ID -> {
                profileScreenVM.onSignOutTapped(
                    requireContext(),
                    PushTokenHelper.getPushToken(requireContext()),
                    ::restartActivity,
                )
            }
            EMAIL_VERIFICATION_SENT_DIALOG_ID -> profileScreenVM.onDialogDismissed()
            DialogCodes.CONTACT_SUPPORT_ERROR_DIALOG_ID -> {
                // "Nothing to handle when dialog dismisses"
            }
            CAMERA_PERMISSION_DENIED_DIALOG_CODE -> {
                profileScreenVM.onCameraPermissionDialogDismissed()
                profileScreenVM.onDialogDismissed()
            }
            else -> profileScreenVM.onDialogDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> profileScreenVM.onCameraPermissionDialogDismissed()
            CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID -> profileScreenVM.onDialogDismissed()
            LOGOUT_ACCOUNT_CONFIRMATION_DIALOG_ID -> profileScreenVM.onDialogDismissed()
            EMAIL_VERIFICATION_SENT_DIALOG_ID -> profileScreenVM.onDialogDismissed()
            DialogCodes.CONTACT_SUPPORT_ERROR_DIALOG_ID -> {
                // Default action is already handled
            }
        }
    }

    override fun imagePickerFailure(error: Exception) {
        profileScreenVM.onDocumentPickerFailure(requireContext())
        Timber.tag(TAG).e(error)
    }

    override fun captureImageFromCamera(documentUploadTarget: DocumentUploadTarget) {
        if (!checkCameraPermissionGranted(requireContext())) {
            profileScreenVM.onCameraPermissionRequested()
        } else {
            showImagePickingInstructionToast(documentUploadTarget)
            startCamera()
            profileScreenVM.onDocumentPickerDismissed()
        }
    }

    private fun showImagePickingInstructionToast(documentUploadTarget: DocumentUploadTarget) {
        if (documentUploadTarget == DocumentUploadTarget.PROFILE_IMAGE) {
            Toast.makeText(
                requireContext(),
                requireContext().getString(R.string.toastMessageImageDimensions),
                Toast.LENGTH_LONG,
            ).show()
        }
    }

    override fun pickImageFromGallery(documentUploadTarget: DocumentUploadTarget) {
        showImagePickingInstructionToast(documentUploadTarget)
        ImagePicker.openImageGallery(this)
        profileScreenVM.onDocumentPickerDismissed()
    }

    override fun pickDocumentFromStorage(documentUploadTarget: DocumentUploadTarget) {
        // Not needed as we are not uploading PDFs for profile picture.
    }

    override fun removeDocument(documentUploadTarget: DocumentUploadTarget) {
        // Not needed as we are not removing profile picture.
    }

    override fun dismissed() {
        profileScreenVM.onDocumentPickerDismissed()
    }

    private fun updateUserImage(updatedImageUrl: String) {
        user?.apply {
            val updatedUser = User(
                userId = userId,
                name = name,
                emailId = emailId,
                isEmailVerified = isEmailVerified,
                imageUrl = updatedImageUrl,
                phoneNumber = phoneNumber,
                primaryCurrency = primaryCurrency,
                country = country,
                countryCurrencies = countryCurrencies,
                supportedCountries = supportedCountries,
                district = district,
            )
            UserSharedPreference.saveUser(updatedUser)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == AppCompatActivity.RESULT_OK) {
            when (requestCode) {
                ImagePicker.IMAGE_PICKER_GALLERY_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        data,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = false,
                    )
                }
                ImagePicker.IMAGE_PICKER_CAMERA_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        null,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = false,
                    )
                }
                ImagePicker.IMAGE_CROP_REQUEST_CODE -> {
                    dismissProgressDialog()
                    showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
                    val resultUri = ImagePicker.getOutput(data!!)!!
                    profileScreenVM.onDocumentSelected(
                        resultUri,
                        requireContext(),
                    )
                }
                ImagePicker.IMAGE_CROP_RESULT_ERROR_CODE -> {
                    dismissProgressDialog()
                    val cropError = ImagePicker.getError(data!!)
                    cropError?.message?.let {
                        Timber.tag(TAG).d("ImagePicker: $it")
                    }
                }
            }
        }
    }

    override fun onNetworkAvailable() {
        profileScreenVM.onDialogDismissed()
    }

    override fun handleNoEmailAppInstalled() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.transactionStatusContactSupportButtonTitle),
            getString(
                R.string.contactSupportNoEmailApp,
            ),
            DialogCodes.CONTACT_SUPPORT_ERROR_DIALOG_ID,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }
}

private const val TAG = "ProfileFragment"
private const val PRIVACY_POLICY_URL =
    "https://s3.af-south-1.amazonaws.com/images.resoluttech.link/Privacy-Policy.pdf"
private const val YAFIKA_MOBILE_PLAY_STORE_LINK =
    "https://play.google.com/store/apps/details?id=com.resoluttech.barcelona.bcn.prod.release"
private const val CLOSE_ACCOUNT_CONFIRMATION_DIALOG_ID = 202
private const val LOGOUT_ACCOUNT_CONFIRMATION_DIALOG_ID = 203
private const val IMAGE_COMPRESSION_QUALITY: Int = 100
private const val CAMERA_PERMISSION_DENIED_DIALOG_CODE: Int = 116
private const val PROFILE_IMAGE_UPLOADED: Int = 227
private const val EMAIL_VERIFICATION_SENT_DIALOG_ID = 223
