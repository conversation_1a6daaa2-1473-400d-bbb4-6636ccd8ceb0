package com.resoluttech.core.profile

import android.content.Context
import androidx.core.content.edit
import com.google.firebase.messaging.FirebaseMessaging
import com.resoluttech.core.config.Config.Companion.PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS
import timber.log.Timber
import java.time.Instant
import java.time.temporal.ChronoUnit

private const val PREFS = "PushTokenPref"
private const val PREFS_TOKEN_KEY = "PushTokenPref.key"
private const val PUSH_TOKEN_UPDATE_TIME_KEY = "PushTokenUpdatePref.key"

object PushTokenHelper {

    fun init(context: Context, onSuccess: (token: String) -> Unit) {
        FirebaseMessaging.getInstance().token
            .addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Timber.w("Fetching FCM registration token failed")
                } else {
                    onSuccess(task.result!!)
                    updateToken(context, task.result!!)
                }
            }
    }

    fun getPushTokenUpdateTime(context: Context): Long {
        return context.getSharedPreferences(PREFS, Context.MODE_PRIVATE).getLong(
            PUSH_TOKEN_UPDATE_TIME_KEY,
            Instant.now()
                .minus(PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS.toLong(), ChronoUnit.DAYS)
                .toEpochMilli(),
        )
    }

    fun setPushTokenUpdateTime(context: Context) {
        context.getSharedPreferences(PREFS, Context.MODE_PRIVATE).edit() {
            putLong(
                PUSH_TOKEN_UPDATE_TIME_KEY,
                Instant.now().toEpochMilli(),
            )
        }
    }

    fun updateToken(context: Context, token: String) {
        context.getSharedPreferences(PREFS, Context.MODE_PRIVATE).edit() {
            putString(PREFS_TOKEN_KEY, token)
        }
    }

    fun getPushToken(context: Context): String? {
        return context.getSharedPreferences(PREFS, Context.MODE_PRIVATE)
            .getString(PREFS_TOKEN_KEY, null)
    }
}
