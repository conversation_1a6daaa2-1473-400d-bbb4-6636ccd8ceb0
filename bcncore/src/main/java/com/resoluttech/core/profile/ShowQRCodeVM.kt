package com.resoluttech.core.profile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.getAccountQRCodeDeeplink
import com.resoluttech.core.utils.getQRCodeBitmap
import com.resoluttech.core.utils.getUserQRCodeDeeplink
import com.resoluttech.core.utils.shareQRCode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import java.util.UUID

class ShowQRCodeVM : ViewModel() {

    private var _currentState: MutableLiveData<ShowQRCodeDialogState> =
        MutableLiveData(ShowQRCodeDialogState.Waiting)
    val state: LiveData<ShowQRCodeDialogState> = _currentState
    private val vmIoScope = viewModelScope + Dispatchers.IO
    lateinit var selectedAccountId: UUID

    fun generateAccountQRCodeImage(userName: String, accountId: UUID) {
        _currentState.postValue(ShowQRCodeDialogState.Waiting)
        vmIoScope.launch {
            val qrCodeBitmap: Bitmap =
                getQRCodeBitmap(getAccountQRCodeDeeplink(userName, accountId))
            _currentState.postValue(ShowQRCodeDialogState.Data(qrCodeBitmap))
        }
    }

    fun generateUserQRCodeImage(userName: String, userId: UUID) {
        _currentState.postValue(ShowQRCodeDialogState.Waiting)
        vmIoScope.launch {
            val qrCodeBitmap: Bitmap = getQRCodeBitmap(getUserQRCodeDeeplink(userName, userId))
            _currentState.postValue(ShowQRCodeDialogState.Data(qrCodeBitmap))
        }
    }

    fun onShareClicked(activity: Activity, userName: String) {
        if (_currentState.value is ShowQRCodeDialogState.Data) {
            shareQRCode(
                activity,
                (_currentState.value as ShowQRCodeDialogState.Data).qrCode,
                QR_CODE_FILE_NAME,
                activity.getString(R.string.shareQRCodeMessageText, userName),
            )
        } else {
            Timber.tag(TAG).i("Share can happen only in the Data state.")
            return
        }
    }

    fun onGetLinkTapped(context: Context, userName: String, appLink: String?) {
        val shareAppLink = Intent(Intent.ACTION_SEND)
        shareAppLink.apply {
            type = "text/plain"
            putExtra(
                Intent.EXTRA_TEXT,
                context.getString(R.string.showQRCodeLinkValue, userName, appLink),
            )
        }
        context.startActivity(
            Intent.createChooser(
                shareAppLink,
                context.getString(R.string.showQRCodeShareCodeLabelText),
            ),
        )
    }
}

sealed class ShowQRCodeDialogState {
    object Waiting : ShowQRCodeDialogState()
    data class Data(val qrCode: Bitmap) : ShowQRCodeDialogState()
}

private const val QR_CODE_FILE_NAME = "qrcode"
private const val TAG = "ShowQRCodeVM"
