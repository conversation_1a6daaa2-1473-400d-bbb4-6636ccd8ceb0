package com.resoluttech.core.profile

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.AdapterView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.lifecycle.Observer
import androidx.navigation.navGraphViewModels
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AccountDropdownAdapter
import com.resoluttech.core.uicomponents.AccountDropdownItem
import com.resoluttech.core.uicomponents.ButtonWithProgressBar
import com.resoluttech.core.utils.setSelectionDefaultAccount
import com.resoluttech.core.views.BaseDialogFragment
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import java.util.UUID

class DefaultAccountSelectionDialog : BaseDialogFragment(), ButtonWithProgressBar.ButtonListener {

    interface DefaultAccountDialogListener {
        fun onDefaultAccountDialogDismissed()
    }

    private lateinit var accountDropdown: TextDropdown
    private lateinit var messageTV: TextView
    private lateinit var defaultAccountLayout: LinearLayout
    private lateinit var buttonWithProgressBar: ButtonWithProgressBar
    private val accountDropdownList: MutableList<AccountDropdownItem> = mutableListOf()
    private val defaultAccountSelectionDialogVM: DefaultAccountSelectionDialogVM by navGraphViewModels(R.id.home_nav)
    private val defaultAccountId: String by lazy { arguments?.getString(KEY_DEFAULT_ACCOUNT) ?: throw IllegalArgumentException("defaultAccountId cannot be null.") }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.default_account_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(view)
        defaultAccountSelectionDialogVM.initAccountList()
        setupAccountDropdown()
        isCancelable = false
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        defaultAccountSelectionDialogVM.state.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onPause() {
        super.onPause()
        accountDropdown.dismiss()
    }

    private fun reactToState(state: DefaultAccountSelectionDialogState) {
        when (state) {
            is DefaultAccountSelectionDialogState.AcceptInput -> {
                handleAcceptInputState()
            }
            is DefaultAccountSelectionDialogState.Loading -> {
                handleLoadingState()
            }
            is DefaultAccountSelectionDialogState.Error -> {
                handleErrorState(state.error)
            }
            is DefaultAccountSelectionDialogState.AccountChanged -> {
                handleAccountChangedState()
            }
            is DefaultAccountSelectionDialogState.DismissDialog -> {
                dismissDialog()
            }
        }
    }

    private fun initViews(view: View) {
        accountDropdown = view.findViewById(R.id.default_account_dropdown)
        buttonWithProgressBar = view.findViewById(R.id.buttons_progress_bar_layout)
        messageTV = view.findViewById(R.id.success_tv)
        defaultAccountLayout = view.findViewById(R.id.default_account_dropdown_layout)
        buttonWithProgressBar.setButtonListeners(this)
    }

    private fun handleErrorState(error: UIError) {
        messageTV.text = error.errorMessage
        showViewForState(messageTV)
    }

    private fun handleAccountChangedState() {
        messageTV.text = requireContext().getString(R.string.alertMessageDefaultWalletChanged)
        showViewForState(messageTV)
    }

    private fun handleAcceptInputState() {
        showViewForState(accountDropdown)
    }

    private fun handleLoadingState() {
        showViewForState(buttonWithProgressBar.progressBar)
    }

    private fun showViewForState(view: View) {
        when (view) {
            messageTV -> {
                buttonWithProgressBar.hideProgressBar()
                messageTV.visibility = View.VISIBLE
                defaultAccountLayout.visibility = View.GONE
                buttonWithProgressBar.setPositiveButtonText(requireContext().getString(R.string.alertActionDismiss))
                buttonWithProgressBar.hideNegativeButton()
            }
            buttonWithProgressBar.progressBar -> {
                buttonWithProgressBar.showProgressBar()
                accountDropdown.isEnabled = false
                messageTV.visibility = View.GONE
            }
            accountDropdown -> {
                buttonWithProgressBar.hideProgressBar()
                messageTV.visibility = View.GONE
            }
        }
    }

    private fun setupAccountDropdown() {
        val storedAccountList = defaultAccountSelectionDialogVM.accountsList
        storedAccountList.forEach {
            accountDropdownList.add(
                AccountDropdownItem(
                    it.name,
                    null,
                    UUID.fromString(it.id),
                ),
            )
        }
        accountDropdown.setAdapter(
            AccountDropdownAdapter(
                accountDropdownList,
            ),
        )
        accountDropdown.setSelectionDefaultAccount(storedAccountList, defaultAccountId)

        accountDropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}

            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                if (defaultAccountId == defaultAccountSelectionDialogVM.accountsList[position].id) {
                    buttonWithProgressBar.disablePositiveButton()
                } else {
                    buttonWithProgressBar.enablePositiveButton()
                    defaultAccountSelectionDialogVM.onAccountDropdownItemSelected(position)
                }
            }
        }
    }

    companion object {

        const val TAG: String = "com.bcn.DefaultAccountSelectionDialog"

        fun newInstance(defaultAccountId: String): DefaultAccountSelectionDialog {
            val defaultAccountSelectionDialog = DefaultAccountSelectionDialog()
            val args = Bundle()
            args.putString(KEY_DEFAULT_ACCOUNT, defaultAccountId)
            defaultAccountSelectionDialog.arguments = args
            return defaultAccountSelectionDialog
        }
    }

    override fun onPositiveButtonClicked() {
        defaultAccountSelectionDialogVM.onPositiveButtonClicked(requireContext())
    }

    override fun onNegativeButtonClicked() {
        defaultAccountSelectionDialogVM.onNegativeButtonClicked()
    }

    private fun dismissDialog() {
        dismiss()
        defaultAccountSelectionDialogVM.onDialogDismissed()
        (parentFragment as DefaultAccountDialogListener).onDefaultAccountDialogDismissed()
    }
}

private const val KEY_DEFAULT_ACCOUNT = "KEY_DEFAULT_ACCOUNT"
