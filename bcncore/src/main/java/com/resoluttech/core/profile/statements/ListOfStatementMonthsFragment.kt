package com.resoluttech.core.profile.statements

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import java.util.UUID

class ListOfStatementMonthsFragment :
    BaseFragment(),
    StatementMonthAdapter.StatementMonthListener,
    AlertDialog.ActionListener {

    private val statementsVM: StatementsVM by navGraphViewModels(R.id.home_nav)

    private lateinit var listOfStatementMonthsRV: RecyclerView
    private lateinit var noStatementsFoundTV: TextView
    private lateinit var waitingPB: ProgressBar

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_list_of_statement_months, container, false)
        showToolbar()
        setDefaultToolbar(getString(R.string.statementsTitle))
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        statementsVM.getListOfStatementMonth(requireContext())
        statementsVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(state: ListOfStatementMonthScreenState) {
        when (state) {
            is ListOfStatementMonthScreenState.Data -> {
                handleDataState(state.listOfStatementMonths)
            }
            is ListOfStatementMonthScreenState.Error -> {
                handleErrorState(state.uiError)
            }
            is ListOfStatementMonthScreenState.NoStatementsAvailable -> {
                handleNoStatementAvailableState()
            }
            is ListOfStatementMonthScreenState.Waiting -> {
                handleWaitingState()
            }
            is ListOfStatementMonthScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is ListOfStatementMonthScreenState.Loading -> {
                handleLoadingState()
            }
            is ListOfStatementMonthScreenState.StatementDocumentDownloaded -> {
                handleStatementDocumentDownloaded(state)
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleStatementDocumentDownloaded(state: ListOfStatementMonthScreenState.StatementDocumentDownloaded) {
        dismissProgressDialog()
        statementsVM.onStatementDownloaded(findNavController(), state.statementMonth)
    }

    private fun handleDataState(listOfStatementMonths: List<StatementMonth>) {
        showViewForState(listOfStatementMonthsRV)
        val statementMonthAdapter = StatementMonthAdapter(
            this,
            listOfStatementMonths,
        )
        listOfStatementMonthsRV.adapter = statementMonthAdapter
    }

    private fun handleErrorState(uiError: UIError) {
        waitingPB.visibility = View.GONE
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.STATEMENT_ERROR_CODE,
                )
            }
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    onDismiss = {
                        statementsVM.onInlineErrorDismiss()
                    },
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleNoStatementAvailableState() {
        showViewForState(noStatementsFoundTV)
    }

    private fun handleWaitingState() {
        showViewForState(waitingPB)
    }

    private fun initViews() {
        view?.apply {
            listOfStatementMonthsRV = findViewById(R.id.list_of_statement_months_rv)
            noStatementsFoundTV = findViewById(R.id.no_statements_found_tv)
            waitingPB = findViewById(R.id.waiting_pg)
        }
    }

    private fun showViewForState(view: View) {
        when (view) {
            listOfStatementMonthsRV -> {
                listOfStatementMonthsRV.visibility = View.VISIBLE
                noStatementsFoundTV.visibility = View.GONE
                waitingPB.visibility = View.GONE
            }
            noStatementsFoundTV -> {
                listOfStatementMonthsRV.visibility = View.GONE
                noStatementsFoundTV.visibility = View.VISIBLE
                waitingPB.visibility = View.GONE
            }
            waitingPB -> {
                listOfStatementMonthsRV.visibility = View.GONE
                noStatementsFoundTV.visibility = View.GONE
                waitingPB.visibility = View.VISIBLE
            }
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                statementsVM.onServerErrorDismissed(findNavController())
            }
            DialogCodes.STATEMENT_DOCUMENT_NOT_FOUND -> {
                statementsVM.onStatementNotFound(findNavController())
            }
            else -> {
                statementsVM.onInlineErrorDismiss()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        // Default action is already handled
    }

    override fun onMonthSelected(documentId: UUID, statementMonth: String) {
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
        statementsVM.getStatementURL(requireContext(), documentId, statementMonth)
    }
}
