package com.resoluttech.core.profile.statements

import com.resoluttech.bcn.profile.GetListOfStatementsDateRPC
import com.resoluttech.bcn.profile.GetStatementUrlRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

class StatementsRepository {

    private val getListOfStatementsDateRPC: GetListOfStatementsDateRPC by KoinJavaComponent.inject(
        GetListOfStatementsDateRPC::class.java,
    )

    private val getStatementUrlRPC: GetStatementUrlRPC by KoinJavaComponent.inject(
        GetStatementUrlRPC::class.java,
    )

    suspend fun getListOfStatementsDate(): LeoRPCResult<GetListOfStatementsDateRPC.Response, GetListOfStatementsDateRPC.Error> {
        return getListOfStatementsDateRPC.execute(GetListOfStatementsDateRPC.Request)
    }

    suspend fun getStatementUrl(documentId: UUID): LeoRPCResult<GetStatementUrlRPC.Response, GetStatementUrlRPC.Error> {
        return getStatementUrlRPC.execute(GetStatementUrlRPC.Request(documentId))
    }
}
