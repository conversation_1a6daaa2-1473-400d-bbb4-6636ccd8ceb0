package com.resoluttech.core.profile

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.transfers.config.QRCodeConfig
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.User
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_NAME
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import org.koin.java.KoinJavaComponent
import java.util.UUID
import kotlin.IllegalStateException

class ShowQRCodeContent : BaseFragment() {

    private lateinit var itemWalletSelector: ConstraintLayout
    private lateinit var accountName: TextView
    private lateinit var selectWallet: TextView
    private lateinit var qrCodeIV: ImageView
    private lateinit var nameTV: TextView
    private lateinit var messageTV: TextView
    private lateinit var shareMB: MaterialButton
    private lateinit var getLinkMB: MaterialButton
    private lateinit var progressBar: ProgressBar
    private lateinit var user: User
    private var appLink: String? = null
    private val showQRCodeDialogVM: ShowQRCodeVM by navGraphViewModels(R.id.home_nav)
    private val qrCodeConfig: QRCodeConfig by KoinJavaComponent.inject(QRCodeConfig::class.java)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        val view = inflater.inflate(R.layout.show_qr_code_tab_content, container, false)
        initUser()
        initViews(view)
        setupShareButton()
        return view
    }

    override fun onResume() {
        super.onResume()
        generateQRCode()
        shareMB.enable()
        getLinkMB.enable()
    }

    private fun generateQRCode() {
        val position = arguments?.getInt(POSITION_KEY)
        if (position == 0) {
            nameTV.visibility = View.VISIBLE
            itemWalletSelector.visibility = View.GONE
            messageTV.text = getString(R.string.showQRCodeUserMessageText)
            nameTV.text = user.name
            showQRCodeDialogVM.generateUserQRCodeImage(
                user.name,
                UUID.fromString(user.userId),
            )
            appLink = "http://${qrCodeConfig.scheme}/${qrCodeConfig.identifier}?username=${
                user.name.replace(
                    " ",
                    "%20",
                )
            }&userId=${user.userId}&shouldNavigate=true"
        } else {
            nameTV.visibility = View.GONE
            itemWalletSelector.visibility = View.VISIBLE
            messageTV.text = getString(R.string.showQRCodeAccountMessageText)
            setupWalletSelector()
            setupWalletSelectorListeners()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showQRCodeDialogVM.state.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(state: ShowQRCodeDialogState) {
        when (state) {
            is ShowQRCodeDialogState.Data -> handleDataState(state.qrCode)
            ShowQRCodeDialogState.Waiting -> handleWaitingState()
        }
    }

    private fun handleWaitingState() {
        // Nothing to handle
    }

    private fun handleDataState(qrCodeBitmap: Bitmap) {
        shareMB.enable()
        getLinkMB.enable()
        qrCodeIV.setImageBitmap(qrCodeBitmap)
    }

    private fun initViews(view: View) {
        itemWalletSelector = view.findViewById(R.id.item_select_wallet)
        accountName = view.findViewById(R.id.account_name_tv)
        selectWallet = view.findViewById(R.id.pay_from_account_label)
        qrCodeIV = view.findViewById(R.id.qr_code_image)
        progressBar = view.findViewById(R.id.progress_bar)
        nameTV = view.findViewById(R.id.name)
        messageTV = view.findViewById(R.id.message)
        shareMB = view.findViewById(R.id.share_button)
        getLinkMB = view.findViewById(R.id.get_link_button)
    }

    private fun setupWalletSelector() {
        val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            ?: throw IllegalStateException("Selected account cannot be null")
        accountName.text = selectedAccount.name
        selectWallet.text = getString(R.string.showQRCodeSelectAccountTitle)
        showQRCodeDialogVM.selectedAccountId = UUID.fromString(selectedAccount.id)
        showQRCodeDialogVM.generateAccountQRCodeImage(
            user.name,
            UUID.fromString(selectedAccount.id),
        )
        appLink = "http://${qrCodeConfig.scheme}/${qrCodeConfig.identifier}?username=${
            user.name.replace(
                " ",
                "%20",
            )
        }&accountId=${UUID.fromString(selectedAccount.id)}&shouldNavigate=true"
        itemWalletSelector.setOnClickListener {
            findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}&selectedAccountId=${showQRCodeDialogVM.selectedAccountId}".toUri())
        }
    }

    private fun setupWalletSelectorListeners() {
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            showQRCodeDialogVM.selectedAccountId = it
            showQRCodeDialogVM.generateAccountQRCodeImage(
                user.name,
                it,
            )
            appLink = "http://${qrCodeConfig.scheme}/${qrCodeConfig.identifier}?username=${
                user.name.replace(
                    " ",
                    "%20",
                )
            }&accountId=$it&shouldNavigate=true"
        }
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>(
            KEY_SELECTED_ACCOUNT_NAME,
        )?.observe(
            viewLifecycleOwner,
        ) {
            accountName.text = it
        }
    }

    private fun initUser() {
        user = UserSharedPreference.getUser()
            ?: throw IllegalStateException("User details cannot be null.")
    }

    private fun setupShareButton() {
        shareMB.setOnClickListener {
            disableButtons()
            showQRCodeDialogVM.onShareClicked(
                requireActivity(),
                user.name,
            )
        }

        getLinkMB.setOnClickListener {
            disableButtons()
            showQRCodeDialogVM.onGetLinkTapped(
                requireActivity(),
                user.name,
                appLink,
            )
        }
    }

    private fun disableButtons() {
        shareMB.disable()
        getLinkMB.disable()
    }
}
