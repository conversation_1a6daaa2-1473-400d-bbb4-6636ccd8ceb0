package com.resoluttech.core.profile

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.cardview.widget.CardView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import coil.load
import com.google.android.material.button.MaterialButton
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.resoluttech.bcn.homeScreen.Agent
import com.resoluttech.bcn.types.ShopStatus
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.uicomponents.ErrorMessageDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.utils.centerCrop
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.location.MapFragment
import com.suryadigital.leo.libui.location.model.IntSource
import com.suryadigital.leo.libui.location.model.Location
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import timber.log.Timber

class FindAgentFragment :
    BaseFragment(),
    MapFragment.OnMarkerClickListener,
    MapFragment.OnMapClickListener,
    ErrorMessageDialog.ErrorMessageDialogListener,
    AlertDialog.ActionListener {

    private val findAgentVM by lazy { ViewModelProvider(this)[FindAgentVM::class.java] }
    private lateinit var mapFragment: MapFragment
    private lateinit var agentDetailsCV: CardView
    private lateinit var agentProfileCIV: ContactIconView
    private lateinit var agentNameTV: TextView
    private lateinit var shopNameTV: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var navigateToDialerButton: MaterialButton
    private lateinit var navigateToMapButton: MaterialButton
    private lateinit var shopStatusDropdown: TextDropdown
    private lateinit var recenterIV: FloatingActionButton
    private lateinit var shopStatusTV: TextView
    private lateinit var agentsList: List<Agent>
    private lateinit var agentsListShopOpen: List<Agent>
    private lateinit var errorBlock: ConstraintLayout
    private lateinit var mapBlock: ConstraintLayout
    private lateinit var requestLocationButton: MaterialButton
    private var currentLocation: Location? = null
    private lateinit var locationManager: LocationManager
    private var locationByGPS: android.location.Location? = null
    private var locationByNetwork: android.location.Location? = null

    private val requestPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
        ) { permissions ->
            repeat(permissions.entries.size) {
                if (
                    permissions[Manifest.permission.ACCESS_COARSE_LOCATION] == true && permissions[Manifest.permission.ACCESS_FINE_LOCATION] == true
                ) {
                    findAgentVM.onLocationPermissionGranted()
                } else if (!shouldShowRequestPermissionRationale(Manifest.permission.ACCESS_COARSE_LOCATION) || !shouldShowRequestPermissionRationale(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                    )
                ) {
                    Timber.tag(TAG)
                        .e("Location permission never ask again selected.")
                    findAgentVM.onLocationPermissionNeverAskAgainSelected()
                } else {
                    Timber.tag(TAG)
                        .e("Location permission denied.")
                    findAgentVM.onLocationPermissionDenied()
                }
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_find_agent, container, false)
        setupMapFragment()
        initViews(rootView)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setToolBar()
        findAgentVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onPause() {
        super.onPause()
        shopStatusDropdown.dismiss()
    }

    private fun checkLocationPermissions(): Boolean {
        return ActivityCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_COARSE_LOCATION,
        ) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_FINE_LOCATION,
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun setToolBar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.find_agent_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            setStatusDropdown(toolbarView)
            setArrowBackIcon(toolbarView)
        }
    }

    private fun setArrowBackIcon(toolbar: View) {
        val arrowBackButton = toolbar.findViewById<ImageButton>(R.id.arrow_back_icon)
        arrowBackButton.setOnClickListener {
            findAgentVM.onBackArrowTapped(findNavController())
        }
    }

    private fun setStatusDropdown(toolbar: View) {
        val shopStatus = listOf(
            requireContext().getString(R.string.findAgentSectionAll),
            requireContext().getString(R.string.findAgentSectionOpen),
        )
        shopStatusDropdown = toolbar.findViewById(R.id.status_dropdown)
        shopStatusDropdown.dropDownVerticalOffset = 48.getDPPixelValue()
        shopStatusDropdown.dropDownHorizontalOffset = (-48).getDPPixelValue()
        shopStatusDropdown.setAdapter(
            StatusDropdownAdapter(shopStatus, requireContext()),
        )
        shopStatusDropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}

            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                when (position) {
                    0 -> handleShowAllShop()
                    1 -> handleShowOpenShop()
                }
            }
        }
    }

    private fun disableStatusDropdown() {
        shopStatusDropdown.disable()
    }

    private fun enableStatusDropdown() {
        shopStatusDropdown.enable()
    }

    private fun handleShowAllShop() {
        agentDetailsCV.visibility = View.GONE
        recenterIV.visibility = View.VISIBLE
        if (::agentsList.isInitialized && agentsList.isNotEmpty()) {
            val locationsList = getAgentLocationList(agentsList)
            if (locationsList.isNotEmpty()) {
                mapFragment.setLocations(locationsList, false)
            }
            findAgentVM.isAgentSelected = false
            findAgentVM.selectedAgent = null
            findAgentVM.onMapTapped()
        }
    }

    private fun handleShowOpenShop() {
        agentDetailsCV.visibility = View.GONE
        recenterIV.visibility = View.VISIBLE
        if (::agentsList.isInitialized && agentsList.isNotEmpty()) {
            val locationsList = getAgentLocationList(
                agentsListShopOpen,
            )
            if (locationsList.isNotEmpty()) {
                mapFragment.setLocations(locationsList, false)
            }
            findAgentVM.isAgentSelected = false
            findAgentVM.selectedAgent = null
            findAgentVM.onMapTapped()
        }
    }

    private fun reactToState(state: FindAgentState) {
        when (state) {
            is FindAgentState.Waiting -> {
                handleWaitingState()
            }
            is FindAgentState.LocationPermissionRequest -> {
                handleLocationPermissionRequestedState()
            }
            is FindAgentState.LocationPermissionGranted -> {
                handleLocationPermissionGrantedState()
            }
            is FindAgentState.LocationPermissionDenied -> {
                handleLocationPermissionDeniedState()
            }
            is FindAgentState.LocationPermissionAlert -> {
                handleLocationPermissionAlertState()
            }
            is FindAgentState.DisplayMap -> {
                handleDisplayMapState()
            }
            is FindAgentState.AgentSelected -> {
                handleAgentSelectedState(state.agent)
            }
            is FindAgentState.Loading -> {
                handleLoadingState()
            }
            is FindAgentState.Data -> {
                handleDataState(state.agentList)
            }
            is FindAgentState.Error -> {
                handleErrorState(state.uiError)
            }
        }
    }

    private fun handleLocationPermissionRequestedState() {
        showErrorBlock()
        disableStatusDropdown()
        requestLocationPermission()
    }

    private fun handleLocationPermissionDeniedState() {
        showErrorBlock()
        disableStatusDropdown()
    }

    private fun handleLocationPermissionAlertState() {
        disableStatusDropdown()
        showCameraPermissionAlert(
            getString(R.string.alertTitleLocationAccessDenied),
            getString(R.string.alertMessageLocationAccessDenied),
            LOCATION_PERMISSION_DIALOG_CODE,
            getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun requestLocationPermission() {
        requestPermissionLauncher.launch(
            arrayOf(
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION,
            ),
        )
    }

    private fun handleLocationPermissionGrantedState() {
        shopStatusDropdown.visibility = View.VISIBLE
        setCurrentLocation()
        showMapBlock()
        disableStatusDropdown()
        findAgentVM.getAgentList(requireContext())
    }

    private fun showMapBlock() {
        errorBlock.visibility = View.GONE
        mapBlock.visibility = View.VISIBLE
    }

    private fun showErrorBlock() {
        errorBlock.visibility = View.VISIBLE
        mapBlock.visibility = View.GONE
    }

    private fun showCameraPermissionAlert(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleWaitingState() {
        disableStatusDropdown()

        if (!checkLocationPermissions()) {
            findAgentVM.onLocationPermissionRequested()
        }
    }

    private fun handleDisplayMapState() {
        enableStatusDropdown()
        setCurrentLocationMarker()
        showMapBlock()
        agentDetailsCV.visibility = View.GONE
        recenterIV.visibility = View.VISIBLE
        findAgentVM.isAgentSelected = false
        findAgentVM.selectedAgent = null
    }

    private fun handleAgentSelectedState(agent: Agent) {
        enableStatusDropdown()
        findAgentVM.isAgentSelected = true
        findAgentVM.selectedAgent = agent
        if (currentLocation == null) {
            setCurrentLocation()
        }
        val profileURL = agent.profileImage.mdpi.imageURL.toString()
        agentDetailsCV.visibility = View.VISIBLE
        recenterIV.visibility = View.GONE
        if (profileURL.isNotEmpty()) {
            agentProfileCIV.imageView.load(profileURL)
        } else {
            agentProfileCIV.imageView.load(R.drawable.ic_point_of_interest_type_agent)
        }
        agentProfileCIV.imageView.centerCrop()
        when (agent.shopStatus) {
            ShopStatus.SHOP_OPEN -> {
                shopStatusTV.text = requireContext().getString(R.string.findAgentShopStatusOpen)
                shopStatusTV.setTextColor(requireContext().getColor(R.color.positiveIndicatorColor))
            }
            ShopStatus.SHOP_CLOSED -> {
                shopStatusTV.text = requireContext().getString(R.string.findAgentShopStatusClosed)
                shopStatusTV.setTextColor(requireContext().getColor(R.color.agentClosed))
            }
        }
        agentNameTV.text = agent.displayName
        shopNameTV.text = agent.shopName
        navigateToMapButton.setOnClickListener {
            findAgentVM.onNavigateToMapButtonTap(
                requireContext(),
                getAgentLocation(
                    agent,
                ),
            )
        }
        navigateToDialerButton.setOnClickListener {
            findAgentVM.onNavigateToDialerTap(
                requireContext(),
                agent.phoneNumber,
            )
        }
    }

    private fun handleLoadingState() {
        mapBlock.visibility = View.GONE
        errorBlock.visibility = View.GONE
        agentDetailsCV.visibility = View.GONE
        recenterIV.visibility = View.VISIBLE
        progressBar.visibility = View.VISIBLE
        disableStatusDropdown()
    }

    private fun handleDataState(agentsList: List<Agent>) {
        enableStatusDropdown()
        showMapBlock()
        progressBar.visibility = View.GONE
        agentDetailsCV.visibility = View.GONE
        recenterIV.visibility = View.VISIBLE
        this.agentsList = agentsList
        agentsListShopOpen = agentsList.filter { it.shopStatus == ShopStatus.SHOP_OPEN }
        val locationsList = getAgentLocationList(agentsList)
        if (locationsList.isNotEmpty()) {
            mapFragment.setLocations(locationsList, false)
        }
        setCurrentLocationMarker()
        if (findAgentVM.isAgentSelected && findAgentVM.selectedAgent != null) {
            findAgentVM.onMarkerTapped(
                findAgentVM.selectedAgent
                    ?: throw IllegalStateException("Selected Agent cannot be null"),
            )
        }
    }

    private fun setCurrentLocationMarker() {
        if (currentLocation != null) {
            mapFragment.setBoundWithMarkerAndZoomLevel(
                Location(
                    requireContext().getEmptyString(),
                    currentLocation!!.lat,
                    currentLocation!!.long,
                    IntSource(R.drawable.ic_current_location),
                ),
                MapFragment.ZoomLevel.STREETS,
            )
        } else {
            findAgentVM.onGPSAndNetworkProviderNotEnabled(requireContext())
        }
    }

    @SuppressLint("MissingPermission")
    private fun setCurrentLocation() {
        locationManager =
            requireContext().getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val isGPSEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        val isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        if (isGPSEnabled) {
            locationManager.requestLocationUpdates(
                LocationManager.GPS_PROVIDER,
                5000,
                0F,
                gpsLocationListener,
            )
        }
        if (isNetworkEnabled) {
            locationManager.requestLocationUpdates(
                LocationManager.NETWORK_PROVIDER,
                5000,
                0F,
                networkLocationListener,
            )
        }
        val lastKnownLocationByGps =
            locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
        lastKnownLocationByGps?.let {
            locationByGPS = lastKnownLocationByGps
        }

        val lastKnownLocationByNetwork =
            locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
        lastKnownLocationByNetwork?.let {
            locationByNetwork = lastKnownLocationByNetwork
        }
        when {
            locationByGPS != null -> currentLocation = Location(
                "",
                locationByGPS!!.latitude,
                locationByGPS!!.longitude,
                IntSource(R.drawable.ic_current_location),
            )
            locationByNetwork != null -> currentLocation = Location(
                "",
                locationByNetwork!!.latitude,
                locationByNetwork!!.longitude,
                IntSource(R.drawable.ic_current_location),
            )
            else -> findAgentVM.onGPSAndNetworkProviderNotEnabled(requireContext())
        }
    }

    private val gpsLocationListener: LocationListener = object : LocationListener {
        override fun onLocationChanged(location: android.location.Location) {
            locationByGPS = location
        }

        override fun onProviderEnabled(provider: String) {}
        override fun onProviderDisabled(provider: String) {}

        /***
         * Android version 9 and below, it is mandatory to override the callback `onStatusChanged`.
         * Above Android version 9 the implementation already exists from the SDK itself and thus
         * not mandatory to override.
         */
        @Deprecated("Deprecated in Java")
        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {}
    }

    private val networkLocationListener: LocationListener = object : LocationListener {
        override fun onLocationChanged(location: android.location.Location) {
            locationByNetwork = location
        }

        override fun onProviderEnabled(provider: String) {}
        override fun onProviderDisabled(provider: String) {}
    }

    private fun handleErrorState(uiError: UIError) {
        progressBar.visibility = View.GONE
        disableStatusDropdown()
        when (uiError.type) {
            ErrorType.DIALOG -> {
                when (uiError.errorCode) {
                    DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                        showErrorDialog(
                            uiError.errorTitle,
                            uiError.errorMessage,
                            uiError.errorCode,
                        )
                    }
                    else -> {
                        val errorMessageDialog =
                            ErrorMessageDialog.newInstance(uiError.errorMessage)
                        errorMessageDialog.show(childFragmentManager, ErrorMessageDialog.TAG)
                    }
                }
            }
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    onDismiss = {
                        findAgentVM.onInlineErrorDismiss(findNavController())
                    },
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    override fun onErrorDialogDismiss() {
        dismissDialog()
        findAgentVM.onDialogDismiss(findNavController())
    }

    private fun dismissDialog() {
        val frag = childFragmentManager.findFragmentByTag(ErrorMessageDialog.TAG)
        if (frag is DialogFragment && isVisible) {
            frag.dismiss()
        } else {
            throw IllegalStateException("Trying to dismiss dialog when it is not showing.")
        }
    }

    private fun setupMapFragment() {
        mapFragment = childFragmentManager.findFragmentById(R.id.findAgent) as MapFragment
        mapFragment.setOnMarkerClickListener(this)
        mapFragment.setOnMapClickListener(this)
    }

    private fun initViews(view: View) {
        errorBlock = view.findViewById(R.id.location_error_block)
        mapBlock = view.findViewById(R.id.map_block)
        requestLocationButton = view.findViewById(R.id.request_location_permission)
        agentDetailsCV = view.findViewById(R.id.findAgentCard)
        agentProfileCIV = view.findViewById(R.id.agentProfileImage)
        agentNameTV = view.findViewById(R.id.agentName)
        shopNameTV = view.findViewById(R.id.shopNameTV)
        navigateToDialerButton = view.findViewById(R.id.navigateToDialerScreen)
        navigateToMapButton = view.findViewById(R.id.navigateToMapButton)
        recenterIV = view.findViewById(R.id.recenter_iv)
        shopStatusTV = view.findViewById(R.id.shopStatusTV)
        progressBar = view.findViewById(R.id.progress_bar)

        requestLocationButton.setOnClickListener {
            requestLocationPermission()
        }

        recenterIV.setOnClickListener {
            setCurrentLocationMarker()
        }
    }

    override fun onMarkerClick(index: Int) {
        if (index != -1) {
            val agent = if (shopStatusDropdown.selectedItemPosition == 0) {
                agentsList[index]
            } else {
                agentsListShopOpen[index]
            }
            findAgentVM.onMarkerTapped(agent)
        }
    }

    override fun onMapClick() {
        findAgentVM.onMapTapped()
    }

    private fun getAgentLocationList(agentList: List<Agent>): List<Location> =
        agentList.map(::getAgentLocation)

    private fun getAgentLocation(agent: Agent): Location {
        return Location(
            agent.displayName,
            agent.shopCoordinate.latitude,
            agent.shopCoordinate.longitude,
            IntSource(
                when (agent.shopStatus) {
                    ShopStatus.SHOP_OPEN -> R.drawable.ic_shop_open
                    ShopStatus.SHOP_CLOSED -> R.drawable.ic_shop_close
                },
            ),
        )
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            LOCATION_PERMISSION_DIALOG_CODE -> findAgentVM.onOpenSettingsClicked(requireContext())
            else -> Timber.tag(TAG)
                .i("Positive action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            LOCATION_PERMISSION_DIALOG_CODE -> findAgentVM.onLocationPermissionAlertDismissed()
            else -> Timber.tag(TAG)
                .i("Positive action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onResume() {
        super.onResume()
        if (checkLocationPermissions()) {
            findAgentVM.onLocationPermissionGranted()
        }
    }
}

private const val TAG = "FindAgentFragment"
private const val LOCATION_PERMISSION_DIALOG_CODE = 101
