package com.resoluttech.core.profile

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.document.FileAttributes
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPC
import com.resoluttech.bcn.profile.ArchiveBcnUserRPC
import com.resoluttech.bcn.profile.ChangeProfileImageRPC
import com.resoluttech.bcn.profile.ResendVerificationEmailRPC
import com.resoluttech.bcn.types.Locale
import com.resoluttech.bcn.types.Password
import com.resoluttech.bcn.types.PasswordPolicy
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.auth.biometric.BiometricPreferenceHelper
import com.resoluttech.core.config.Config.Companion.PRIVACY_POLICY_URL
import com.resoluttech.core.config.LanguageConfig
import com.resoluttech.core.kyc.KYCDataCameraState
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.Account
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.LocaleManager.Statics.EN_US
import com.resoluttech.core.utils.LocaleManager.Statics.NY
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.SignOutRepository
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.clearAllUserData
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.MAX_DOCUMENT_SIZE
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.MIN_DOCUMENT_SIZE
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.ONE_KB_IN_BYTES
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getAssetSize
import com.resoluttech.core.utils.getByteArray
import com.resoluttech.core.utils.getSHA256
import com.resoluttech.core.utils.getSpecificResolutionImageURL
import com.resoluttech.core.utils.localiseTextArray
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.utils.openSystemSettings
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_DESCRIPTION
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_TITLE
import com.suryadigital.leo.kedwig.Response
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import org.koin.java.KoinJavaComponent
import timber.log.Timber

class ProfileScreenVM : ViewModel() {

    private var _currentState: MutableLiveData<ProfileScreenState> =
        MutableLiveData(ProfileScreenState.FullScreenLoading)
    val currentState: LiveData<ProfileScreenState> = _currentState
    private val _currentCameraState: MutableLiveData<KYCDataCameraState> =
        MutableLiveData(KYCDataCameraState.Waiting)
    val currentCameraState: LiveData<KYCDataCameraState> = _currentCameraState

    private var _currentValidatePasswordState: MutableLiveData<ValidatePasswordScreenState> =
        MutableLiveData(ValidatePasswordScreenState.AcceptInput)
    val currentValidatePasswordState: LiveData<ValidatePasswordScreenState> =
        _currentValidatePasswordState

    private val profileRepository = ProfileRepository()

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val languageConfig: LanguageConfig by KoinJavaComponent.inject(
        LanguageConfig::class.java,
    )
    private val languageSupportedList: Array<SupportedLocale> = languageConfig.languageSupported
    private val languageDisplayNameList: ArrayList<String> = arrayListOf()
    private var selectedAccount: Account? = null

    init {
        languageSupportedList.forEach {
            languageDisplayNameList.add(it.displayName)
        }
    }

    fun init() {
        selectedAccount = SelectedAccountHelper.getSelectedAccount()!!
        initialState()
    }

    fun onFindAgentClicked(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_findAgentFragment)
    }

    fun onShowQRCodeClicked(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_showQRCodeFragment)
    }

    fun onAppInfoClicked(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_appInfoFragment)
    }

    fun onChangeLanguageClicked(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_changeLanguageFragment)
    }

    fun onDialogDismissed() {
        vmIoScope.launch {
            _currentState.postValue(
                ProfileScreenState.Data(
                    Biometric.isBiometricHardwareAvailable(),
                    BiometricPreferenceHelper.getBiometricOption(),
                ),
            )
        }
    }

    fun onChangePasswordTapped(context: Context) {
        _currentState.postValue(ProfileScreenState.InlineLoading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result = profileRepository.getPasswordPolicy()
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetPasswordPolicyResponse(
                                result.response.passwordPolicy,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetPasswordPolicyError(context)
                        }
                    }
                },
                handleException = ::handleException,
            )
        }
    }

    private fun handleGetPasswordPolicyResponse(
        passwordPolicy: PasswordPolicy,
    ) {
        _currentState.postValue(ProfileScreenState.PasswordPolicySuccessful(passwordPolicy))
    }

    private fun handleGetPasswordPolicyError(context: Context) {
        _currentState.postValue(
            ProfileScreenState.Error(
                UIError(
                    ErrorType.DIALOG,
                    errorTitle = context.getString(R.string.alertTitleGenericError),
                    context.getString(R.string.alertMessageGenericError),
                ),
            ),
        )
    }

    fun onPasswordPolicyRPCSuccess(
        navController: NavController,
        passwordPolicy: PasswordPolicy,
        context: Context,
    ) {
        val args = Bundle()
        args.putString(
            KEY_PRIVACY_POLICY_TITLE,
            passwordPolicy.title.localisedText(LocaleManager.getCurrentLocale(context)),
        )
        args.putStringArrayList(
            KEY_PRIVACY_POLICY_DESCRIPTION,
            passwordPolicy.description.localiseTextArray(LocaleManager.getCurrentLocale(context)),
        )
        navController.navigateSafe(R.id.action_profileFragment_to_changePasswordFragment, args)
        _currentState.postValue(ProfileScreenState.Waiting)
    }

    fun onUpdateKYCTapped(controller: NavController) {
        controller.navigateSafe(R.id.action_profileFragment_to_kycNav)
    }

    fun onBiometricAuthOptionChecked(isEnabled: Boolean) {
        vmIoScope.launch {
            BiometricPreferenceHelper.setBiometricOption(isEnabled)
        }
    }

    fun onSignOutTapped(
        context: Context,
        pushToken: String?,
        restartActivity: () -> Unit,
    ) {
        _currentState.postValue(ProfileScreenState.FullScreenLoading)
        vmIoScope.launch {
            executeRPC(
                context = context,
                rpcBlock = {
                    when (SignOutRepository.signOut(pushToken, KeyStoreHelper.getLLT())) {
                        is LeoRPCResult.LeoResponse -> {
                            clearAllUserData()
                            restartActivity()
                        }
                        is LeoRPCResult.LeoError -> {
                            handleLogoutRPCError(context)
                        }
                    }
                },
                handleException = ::handleException,
            )
        }
    }

    private fun handleLogoutRPCError(context: Context) {
        _currentState.postValue(
            ProfileScreenState.Error(
                UIError(
                    ErrorType.SNACKBAR,
                    errorTitle = context.getString(R.string.alertTitleGenericError),
                    context.getString(R.string.alertMessageGenericError),
                ),
            ),
        )
    }

    fun onProfileImageChangeTapped() {
        _currentState.postValue(ProfileScreenState.ProfileImagePickerDialog)
    }

    fun onDocumentPickerFailure(context: Context) {
        _currentState.postValue(
            ProfileScreenState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleFailedToPickDocument),
                    context.getString(R.string.alertMessageFailedToPickDocument),
                ),
            ),
        )
    }

    fun onCameraPermissionRequested() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionRequested)
    }

    fun onCameraPermissionGranted() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionGranted)
    }

    fun onCameraPermissionNeverAskAgainSelected() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionAlert)
    }

    fun onCameraPermissionDenied() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionDenied)
    }

    fun onOpenSettingsClicked(context: Context) {
        _currentState.postValue(ProfileScreenState.Waiting)
        openSystemSettings(context)
        _currentCameraState.postValue(KYCDataCameraState.Waiting)
        onDialogDismissed()
    }

    fun onCameraPermissionAlertDismissed() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionDenied)
    }

    fun onCameraPermissionDialogDismissed() {
        _currentState.postValue(ProfileScreenState.Waiting)
        _currentCameraState.postValue(KYCDataCameraState.Waiting)
    }

    fun onDocumentSelected(
        documentUri: Uri,
        context: Context,
    ) {
        if (!validateProfileImageFileSize(documentUri, context)) {
            _currentState.postValue(
                ProfileScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidFileSize),
                        context.getString(
                            R.string.alertMessageInvalidFileSize,
                            MIN_DOCUMENT_SIZE,
                            MAX_DOCUMENT_SIZE,
                        ),
                    ),
                ),
            )
            return
        }
        if (!validateProfileImageDimension(documentUri, context)) {
            val inputStream = context.contentResolver.openInputStream(documentUri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            _currentState.postValue(
                ProfileScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidImageDimensions),
                        context.getString(
                            R.string.alertMessageInvalidImageDimensions,
                            bitmap.width,
                            bitmap.height,
                        ),
                    ),
                ),
            )
            return
        }

        vmIoScope.launch {
            val documentSHA256 = getSHA256(getByteArray(context, documentUri))
            val fileAttributes = FileAttributes(
                documentSHA256,
                documentUri.lastPathSegment,
                FileAttributes.FileExtension.JPG,
                context.getAssetSize(documentUri)!!.toInt(),
            )

            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result = profileRepository.getDocumentUploadUrl(
                            fileAttributes,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetUrlRPCResponse(
                                result.response,
                                documentUri,
                                documentSHA256,
                                context,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleException(
                                UIError(
                                    ErrorType.DIALOG,
                                    context.getString(R.string.alertTitleFailedToUpload),
                                    context.getString(R.string.alertMessageFailedToUpload),
                                ),
                            )
                        }
                    }
                },
                handleException = ::handleException,
            )
        }
    }

    private fun handleGetUrlRPCResponse(
        response: GetUrlForSignedInUserRPC.Response,
        documentUri: Uri,
        sha256: String,
        context: Context,
    ) {
        vmIoScope.launch {
            DocumentUploadHelper.uploadDocument(
                response.uploadDestinationURL,
                documentUri,
                {
                    handleException(
                        UIError(
                            ErrorType.DIALOG,
                            context.getString(R.string.alertTitleServerError),
                            context.getString(R.string.alertMessageServerError),
                        ),
                    )
                },
                {
                    handleImageUploadedResponse(
                        it,
                        sha256,
                        context,
                    )
                },
                {
                    handleException(
                        UIError(
                            ErrorType.DIALOG,
                            context.getString(R.string.alertTitleProfileImageUploadFailed),
                            context.getString(R.string.alertMessageProfileImageUploadFailed),
                        ),
                    )
                },
            )
        }
    }

    private fun handleImageUploadedResponse(
        response: Response,
        sha256: String,
        context: Context,
    ) {
        if (response.statusCode == 200) {
            getDocumentId(sha256, context)
        } else {
            handleException(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleFailedToUpload),
                    context.getString(R.string.alertMessageFailedToUpload),
                ),
            )
        }
    }

    private fun getDocumentId(
        sha256: String,
        context: Context,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            profileRepository.getDocumentIdForSignedInUser(
                                sha256,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetDocumentIdResponse(
                                context,
                                result.response,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetDocumentIdError(
                                context,
                                result.error,
                            )
                        }
                    }
                },
                handleException = ::handleException,
            )
        }
    }

    private fun handleGetDocumentIdResponse(
        context: Context,
        response: GetDocumentIdForSignedInUserRPC.Response,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result = profileRepository.changeProfileImage(
                            response.recordId,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleChangeProfileImageResponse(result.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleChangeProfileImageError(result.error)
                        }
                    }
                },
                ::handleException,
            )
        }
    }

    private fun handleChangeProfileImageError(error: ChangeProfileImageRPC.Error) {
        ProfileRPCExceptionHandler.getChangeProfileImageErrorMessage(error)
    }

    private fun handleChangeProfileImageResponse(response: ChangeProfileImageRPC.Response) {
        _currentState.postValue(
            ProfileScreenState.ProfileImageChange(
                response.profileImage.getSpecificResolutionImageURL().toString(),
            ),
        )
    }

    private fun handleGetDocumentIdError(
        context: Context,
        error: GetDocumentIdForSignedInUserRPC.Error,
    ) {
        AuthExceptionHandler.getDocumentIdForSignedInUserRPCExceptions(context, error).apply {
            handleException(
                UIError(
                    ErrorType.DIALOG,
                    errorTitle,
                    errorMessage,
                ),
            )
        }
    }

    private fun handleException(
        uiError: UIError,
    ) {
        _currentState.postValue(
            ProfileScreenState.Error(
                uiError,
            ),
        )
    }

    private fun handleValidatePasswordScreenError(
        uiError: UIError,
    ) {
        _currentValidatePasswordState.postValue(
            ValidatePasswordScreenState.Error(
                uiError,
            ),
        )
    }

    private fun validateProfileImageDimension(uri: Uri, context: Context): Boolean {
        val inputStream = context.contentResolver.openInputStream(uri)
        val bitmap = BitmapFactory.decodeStream(inputStream)
        return bitmap.width == DocumentUploadHelper.MAX_IMAGE_WIDTH && bitmap.height == DocumentUploadHelper.MAX_IMAGE_HEIGHT
    }

    private fun validateProfileImageFileSize(uri: Uri, context: Context): Boolean {
        val assetSize = context.getAssetSize(uri)
        return if (assetSize == null) {
            false
        } else {
            // The file size should be in 10 KBs to 5 MBs
            (assetSize / ONE_KB_IN_BYTES) in DocumentUploadHelper.MIN_DOCUMENT_SIZE_KB..DocumentUploadHelper.MAX_DOCUMENT_SIZE_KB
        }
    }

    fun onCloseMyAccountTapped() {
        _currentState.postValue(ProfileScreenState.AccountCloseConfirmation)
    }

    fun onLogoutAccountTapped() {
        _currentState.postValue(ProfileScreenState.LogoutAccountConfirmation)
    }

    fun onShareAppTapped(activity: Activity, appLink: String) {
        val intent: Intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(
                Intent.EXTRA_TEXT,
                activity.getString(R.string.profileScreenShareAppDescription, appLink),
            )
        }
        activity.startActivity(intent)
    }

    fun onPrivacyPolicyTapped(navController: NavController, context: Context) {
        _currentState.postValue(ProfileScreenState.Waiting)
        val inAppBrowserAction =
            ProfileFragmentDirections.actionProfileFragmentToInAppBrowserFragment(
                destinationUrl = PRIVACY_POLICY_URL,
                toolbarTitle = context.getString(R.string.profileScreenOptionPrivacyPolicy),
                shouldShowBackButton = true,
            )
        navController.navigate(inAppBrowserAction)
    }

    fun onThirdPartySoftwareTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_thirdPartySoftwareFragments)
    }

    fun onCloseMyAccountConfirmed(navController: NavController) {
        _currentState.postValue(ProfileScreenState.Waiting)
        navController.navigateSafe(R.id.action_profileFragment_to_validatePasswordFragment)
    }

    fun onValidatePasswordToDeactivateAccount(
        context: Context,
        password: String,
    ) {
        _currentValidatePasswordState.postValue(ValidatePasswordScreenState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (val result = profileRepository.archiveBCNUser(Password(password))) {
                        is LeoRPCResult.LeoResponse -> handleDeactivateMyAccountRPCResponse()
                        is LeoRPCResult.LeoError -> handleDeactivateMyAccountRPCErrorResponse(
                            context,
                            result.error,
                        )
                    }
                },
                ::handleValidatePasswordScreenError,
            )
        }
    }

    private fun handleDeactivateMyAccountRPCErrorResponse(
        context: Context,
        error: ArchiveBcnUserRPC.Error,
    ) {
        val uiError = ProfileRPCExceptionHandler.getDeactivateBcnUserErrorMessage(context, error)
        _currentValidatePasswordState.postValue(ValidatePasswordScreenState.Error(uiError))
    }

    private fun handleDeactivateMyAccountRPCResponse() {
        vmIoScope.launch {
            clearAllUserData()
            _currentValidatePasswordState.postValue(ValidatePasswordScreenState.AccountDeactivated)
        }
    }

    fun onInlineErrorDismissedOnValidatePassword() {
        _currentValidatePasswordState.postValue(ValidatePasswordScreenState.AcceptInput)
    }

    fun onNewLocaleSelected(context: Context, supportedLocale: SupportedLocale) {
        _currentState.postValue(ProfileScreenState.FullScreenLoading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    val newLocale = when (supportedLocale) {
                        SupportedLocale.EN_US -> Locale(EN_US)
                        SupportedLocale.NYANJA -> Locale(NY)
                    }
                    when (profileRepository.changeLocale(newLocale)) {
                        is LeoRPCResult.LeoResponse -> handleChangeLocaleRPCResponse(supportedLocale)
                        is LeoRPCResult.LeoError -> {
                            _currentState.postValue(
                                ProfileScreenState.Error(
                                    UIError(
                                        ErrorType.DIALOG,
                                        context.getString(R.string.alertTitleLanguageChangeFailure),
                                        context.getString(R.string.alertMessageLanguageChangeFailure),
                                    ),
                                ),
                            )
                        }
                    }
                },
                ::handleException,
            )
        }
    }

    private fun handleChangeLocaleRPCResponse(supportedLocale: SupportedLocale) {
        _currentState.postValue(ProfileScreenState.LanguageChanged(supportedLocale))
    }

    fun onDocumentPickerDismissed() {
        vmIoScope.launch {
            _currentState.postValue(
                ProfileScreenState.Data(
                    Biometric.isBiometricHardwareAvailable(),
                    BiometricPreferenceHelper.getBiometricOption(),
                ),
            )
        }
    }

    private fun initialState() {
        vmIoScope.launch {
            if (_currentState.value != ProfileScreenState.ProfileImagePickerDialog && _currentState.value != ProfileScreenState.EmailVerificationSent && _currentState.value != ProfileScreenState.LogoutAccountConfirmation && _currentState.value != ProfileScreenState.AccountCloseConfirmation) {
                _currentState.postValue(
                    ProfileScreenState.Data(
                        Biometric.isBiometricHardwareAvailable(),
                        BiometricPreferenceHelper.getBiometricOption(),
                    ),
                )
            } else {
                Timber.tag(TAG)
                    .i("VM state should not be changed to Data since we are showing a dialog and user should take a action and close the dialog first.")
            }
        }
    }

    fun requestSendVerification(
        context: Context,
    ) {
        _currentState.postValue(ProfileScreenState.FullScreenLoading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = profileRepository.resendVerificationEmail()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSendVerificationSuccessfulResponse()
                        }
                        is LeoRPCResult.LeoError -> {
                            handleSendVerificationErrorState(context, response.error)
                        }
                    }
                },
                handleException = {
                    _currentState.postValue((ProfileScreenState.Error(it)))
                },
            )
        }
    }

    private fun handleSendVerificationSuccessfulResponse() {
        _currentState.postValue(ProfileScreenState.EmailVerificationSent)
    }

    private fun handleSendVerificationErrorState(
        context: Context,
        error: ResendVerificationEmailRPC.Error,
    ) {
        ProfileRPCExceptionHandler.handleResendVerificationEmailRPCErrors(context, error)
            .showError()
    }

    fun onAddEmailTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_addEmailAddressFragment)
    }

    private fun UIError.showError() {
        _currentState.postValue(ProfileScreenState.Error(this))
    }

    fun onStatementsTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_listOfStatementMonthsFragment)
    }

    fun onManageAccountsTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_manageAccountsFragment)
    }

    fun onChangeAppPinTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_changeAppPinFragment)
    }

    fun onTrustedContactsTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_manageTrustedContactsFragment)
    }

    fun onEditEmailTapped(navController: NavController) {
        navController.navigateSafe(R.id.action_profileFragment_to_addEmailAddressFragment)
    }
}

sealed class ProfileScreenState {
    object Waiting : ProfileScreenState()

    // There is not data passed along with this subclass since we have hardcoded data,
    // it should be updated once we get data from the server
    data class Data(val isBiometricAvailable: Boolean, val isBiometricEnabled: Boolean) :
        ProfileScreenState()

    object FullScreenLoading : ProfileScreenState()
    object InlineLoading : ProfileScreenState()
    data class LanguageChanged(val supportedLocale: SupportedLocale) : ProfileScreenState()
    object ProfileImagePickerDialog : ProfileScreenState()
    object AccountCloseConfirmation : ProfileScreenState()
    object LogoutAccountConfirmation : ProfileScreenState()
    data class Error(val uiError: UIError) : ProfileScreenState()
    object EmailVerificationSent : ProfileScreenState()
    data class ProfileImageChange(val imageURL: String) : ProfileScreenState()
    data class PasswordPolicySuccessful(val passwordPolicy: PasswordPolicy) :
        ProfileScreenState()
}

sealed class ValidatePasswordScreenState {
    object AcceptInput : ValidatePasswordScreenState()
    object Loading : ValidatePasswordScreenState()
    object AccountDeactivated : ValidatePasswordScreenState()
    data class Error(val uiError: UIError) : ValidatePasswordScreenState()
}

private const val TAG = "ProfileScreenVM"
