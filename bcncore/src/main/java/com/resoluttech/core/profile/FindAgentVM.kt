package com.resoluttech.core.profile

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.homeScreen.Agent
import com.resoluttech.bcn.homeScreen.GetAgentListRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.IntentUtils
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.openSystemSettings
import com.suryadigital.leo.libui.location.model.Location
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class FindAgentVM : ViewModel() {
    private var vmIOScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<FindAgentState> =
        MutableLiveData(FindAgentState.Waiting)
    val currentState: LiveData<FindAgentState> = _currentState
    private val repository = ProfileRepository()
    var isAgentSelected = false
    var selectedAgent: Agent? = null

    fun onMarkerTapped(agent: Agent) {
        _currentState.postValue(FindAgentState.AgentSelected(agent))
    }

    fun onMapTapped() {
        _currentState.postValue(FindAgentState.DisplayMap)
    }

    fun getAgentList(context: Context) {
        _currentState.postValue(FindAgentState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = repository.getAgentList()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleResponse(
                                context,
                                result.response,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            // No errors to handle
                        }
                    }
                },
                handleException = {
                    _currentState.postValue(FindAgentState.Error(it))
                },
            )
        }
    }

    fun onLocationPermissionRequested() {
        _currentState.postValue(FindAgentState.LocationPermissionRequest)
    }

    fun onLocationPermissionNeverAskAgainSelected() {
        _currentState.postValue(FindAgentState.LocationPermissionAlert)
    }

    fun onLocationPermissionGranted() {
        _currentState.postValue(FindAgentState.LocationPermissionGranted)
    }

    fun onLocationPermissionDenied() {
        _currentState.postValue(FindAgentState.LocationPermissionDenied)
    }

    fun onNavigateToMapButtonTap(context: Context, location: Location) {
        val uri = "geo:?q=${location.lat},${location.long}"
        val intent = Intent(Intent.ACTION_VIEW, uri.toUri())
        context.startActivity(intent)
    }

    fun onNavigateToDialerTap(context: Context, phoneNumber: LeoPhoneNumber) {
        IntentUtils.callIntent(context, phoneNumber.value)
    }

    fun onGPSAndNetworkProviderNotEnabled(context: Context) {
        _currentState.postValue(
            FindAgentState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleGPSDisabled),
                    context.getString(R.string.alertMessageGPSDisabled),
                ),
            ),
        )
    }

    fun onDialogDismiss(navController: NavController) {
        navController.navigateUp()
    }

    fun onBackArrowTapped(navController: NavController) {
        navController.navigateUp()
    }

    fun onInlineErrorDismiss(navController: NavController) {
        navController.navigateUp()
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
    }

    fun onLocationPermissionAlertDismissed() {
        _currentState.postValue(FindAgentState.LocationPermissionDenied)
    }

    private fun handleResponse(context: Context, response: GetAgentListRPC.Response) {
        if (response.agentList.isNotEmpty()) {
            _currentState.postValue(FindAgentState.Data(response.agentList))
        } else {
            _currentState.postValue(
                FindAgentState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleNoActiveAgent),
                        context.getString(R.string.alertMessageNoActiveAgent),
                    ),
                ),
            )
        }
    }
}

sealed class FindAgentState {

    /* This is the initial state of the screen */
    object Waiting : FindAgentState()

    /* This state occurs when there is no location permission*/
    object LocationPermissionRequest : FindAgentState()

    /* This state occurs when the user denies location permission request*/
    object LocationPermissionDenied : FindAgentState()

    /* This state occurs when the user accepts location permission request*/
    object LocationPermissionGranted : FindAgentState()

    /* This state occurs when the user clicks never ask again*/
    object LocationPermissionAlert : FindAgentState()

    /* This state occurs when user sees map on the screen only. */
    object DisplayMap : FindAgentState()

    /* This state occurs when user taps on a agent on map. */
    data class AgentSelected(val agent: Agent) :
        FindAgentState()

    /* This state occurs when view waits for search results. */
    object Loading : FindAgentState()

    /* This state occurs when the agent list is obtained from the server. */
    data class Data(val agentList: List<Agent>) :
        FindAgentState()

    /* This state occurs when an error is encountered. */
    data class Error(val uiError: UIError) :
        FindAgentState()
}
