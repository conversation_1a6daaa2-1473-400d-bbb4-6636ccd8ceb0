package com.resoluttech.core.profile.manageaccount

import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.PopupMenu
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.getFlagFromCurrency
import com.resoluttech.core.utils.loadImage
import com.suryadigital.leo.libui.listview.ListAdapter
import java.util.UUID

internal class ManageAccountAdapter(
    private val listener: ManageAccountListener,
    listOfAccounts: List<ActiveAccountsItem>,
) :
    ListAdapter<ActiveAccountsItem, RecyclerView.ViewHolder>(listOfAccounts) {

    private var popupMenu: PopupMenu? = null

    interface ManageAccountListener {
        fun onSetAsPrimary(accountId: UUID, accountCurrency: Currency)
        fun onEditAccountDisplayName(accountId: UUID, oldAccountName: String)
        fun onMarkAsInactive(accountId: UUID)
        fun onMarkAsActive(accountId: UUID)
    }

    override fun filter(query: String) {
        // Nothing: We are not performing any filters here.
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            TYPE_HEADER -> bindHeaderViewHolder(
                holder as HeaderViewHolder,
                filterItemList[position] as ActiveAccountsItem.AccountHeader,
            )
            TYPE_ITEM -> bindItemViewHolder(
                holder as ItemViewHolder,
                filterItemList[position] as ActiveAccountsItem.Account,
            )
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_HEADER -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_manage_account_header, parent, false)
                HeaderViewHolder(itemView)
            }
            TYPE_ITEM -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_manage_account_item, parent, false)
                ItemViewHolder(itemView)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (filterItemList[position]) {
            is ActiveAccountsItem.AccountHeader -> TYPE_HEADER
            is ActiveAccountsItem.Account -> TYPE_ITEM
        }
    }

    private fun bindHeaderViewHolder(
        holder: HeaderViewHolder,
        accountHeader: ActiveAccountsItem.AccountHeader,
    ) {
        val country = UserSharedPreference.getUserCountryCurrency()
            .find { it.currencyCode == accountHeader.currency.currencyCode }
        val displayLabel = when (LocaleManager.getCurrentLocale(holder.itemView.context)) {
            SupportedLocale.EN_US -> country!!.displayNameEn
            SupportedLocale.NYANJA ->
                country!!.displayNameNy
                    ?: country.displayNameEn
        }
        holder.headerTV.text = displayLabel
        holder.headerTV.addContentDescriptionString(
            R.string.axManageWalletsCurrencyLabel,
            holder.itemView.context,
            displayLabel,
        )
        holder.currencyCountryFlagIV.loadImage(getFlagFromCurrency(accountHeader.currency.currencyCode))
    }

    private fun bindItemViewHolder(
        holder: ItemViewHolder,
        accountItem: ActiveAccountsItem.Account,
    ) {
        holder.accountNameTV.text = accountItem.accountName
        holder.accountIdTV.text = String.format(
            holder.itemView.context.getString(R.string.selectAccountAccountID),
            accountItem.accountId.takeLast(4),
        )
        if (accountItem.isPrimary) {
            holder.defaultAccountTagTV.visibility = View.VISIBLE
            holder.itemView.addContentDescriptionString(
                R.string.axManageWalletsSelectDefaultWalletLabel,
                holder.itemView.context,
                accountItem.accountName,
                accountItem.accountId.takeLast(4),
            )
        } else {
            holder.defaultAccountTagTV.visibility = View.GONE
            holder.itemView.addContentDescriptionString(
                R.string.axManageWalletsSelectWalletLabel,
                holder.itemView.context,
                accountItem.accountName,
                accountItem.accountId.takeLast(4),
            )
        }
        if (accountItem.isActive) {
            holder.menuIV.visibility = View.VISIBLE
            holder.activateTV.visibility = View.GONE
            holder.menuIV.addContentDescriptionString(
                R.string.axManageWalletsSelectWalletHint,
                holder.menuIV.context,
            )
            holder.menuIV.setOnClickListener {
                val popupMenu = PopupMenu(
                    it.context,
                    it,
                    Gravity.END,
                    0,
                    R.style.popupMenuStyle,
                )
                popupMenu.menuInflater.inflate(R.menu.active_accounts_popup, popupMenu.menu)
                popupMenu.menu.findItem(R.id.action_set_as_primary).isVisible =
                    !accountItem.isPrimary
                popupMenu.menu.findItem(R.id.action_mark_inactive).isVisible =
                    !accountItem.isPrimary
                popupMenu.setOnMenuItemClickListener { item ->
                    when (item.itemId) {
                        R.id.action_set_as_primary -> {
                            listener.onSetAsPrimary(
                                UUID.fromString(accountItem.accountId),
                                accountItem.currency,
                            )
                        }
                        R.id.action_edit_account_name -> {
                            listener.onEditAccountDisplayName(
                                UUID.fromString(accountItem.accountId),
                                accountItem.accountName,
                            )
                        }
                        R.id.action_mark_inactive -> {
                            listener.onMarkAsInactive(UUID.fromString(accountItem.accountId))
                        }
                    }
                    true
                }
                popupMenu.gravity = Gravity.END
                this.popupMenu = popupMenu
                popupMenu.show()
            }
        } else {
            holder.menuIV.visibility = View.GONE
            holder.activateTV.visibility = View.VISIBLE
            holder.activateTV.setOnClickListener {
                listener.onMarkAsActive(UUID.fromString(accountItem.accountId))
            }
        }
    }

    fun closePopupMenu() {
        popupMenu?.dismiss()
    }

    private class ItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val accountNameTV: TextView = itemView.findViewById(R.id.account_name_tv)
        val accountIdTV: TextView = itemView.findViewById(R.id.account_id_tv)
        val defaultAccountTagTV: TextView = itemView.findViewById(R.id.default_account_tag_tv)
        val menuIV: ImageView = itemView.findViewById(R.id.menu_iv)
        val activateTV: TextView = itemView.findViewById(R.id.activate_tv)
    }

    private class HeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val headerTV: TextView = itemView.findViewById(R.id.header_text_tv)
        val currencyCountryFlagIV: ImageView = itemView.findViewById(R.id.flag_iv)
    }
}

private const val TYPE_HEADER = 1
const val TYPE_ITEM: Int = 2
