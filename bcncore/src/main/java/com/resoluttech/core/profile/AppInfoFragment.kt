package com.resoluttech.core.profile

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.FileProvider
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import org.koin.java.KoinJavaComponent
import java.io.File

class AppInfoFragment : BaseFragment() {
    private val userAgentConfig: ApplicationBuildInfo by KoinJavaComponent.inject(
        ApplicationBuildInfo::class.java,
    )

    private lateinit var appVersionTV: TextView
    private lateinit var shareLogs: TextView
    private lateinit var shareReportSwitch: SwitchCompat
    private val crashlytics by lazy(FirebaseCrashlytics::getInstance)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_app_info, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.profileScreenAppInfo))
        initViews()
        setupAppVersion()
        setupShareLogs()
        setupShareReportSwitch()
    }

    private fun initViews() {
        view?.apply {
            appVersionTV = findViewById(R.id.app_version)
            shareLogs = findViewById(R.id.share_logs)
            shareReportSwitch = findViewById(R.id.share_usage_reports_switch)
        }
    }

    private fun setupShareLogs() {
        shareLogs.setOnClickListener {
            shareLogs.disable()
            sendLogs()
        }
    }

    private fun setupAppVersion() {
        appVersionTV.text =
            getString(R.string.appInfoAppVersion, userAgentConfig.packageInfo.appVersion, userAgentConfig.packageInfo.versionCode)
    }

    private fun setupShareReportSwitch() {
        shareReportSwitch.setOnClickListener {
            crashlytics.setCrashlyticsCollectionEnabled(shareReportSwitch.isChecked)
            UserSharedPreference.saveShareReportEnableState(shareReportSwitch.isChecked)
        }

        shareReportSwitch.isChecked = UserSharedPreference.getShareReportEnableState()
    }

    private fun sendLogs() {
        val shareIntent = Intent(Intent.ACTION_SEND)
        shareIntent.apply {
            type = "text/*"
            flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            val fileURI = FileProvider.getUriForFile(
                requireContext(),
                requireContext().packageName + ".provider",
                File("${requireContext().applicationContext.filesDir}/logs.txt"),
            )
            putExtra(Intent.EXTRA_STREAM, fileURI)
        }
        requireActivity().startActivity(
            Intent.createChooser(
                shareIntent,
                requireActivity().getString(R.string.appInfoShareLogs),
            ),
        )
    }

    override fun onResume() {
        super.onResume()
        shareLogs.enable()
    }
}
