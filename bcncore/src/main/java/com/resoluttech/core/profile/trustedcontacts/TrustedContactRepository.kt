package com.resoluttech.core.profile.trustedcontacts

import com.resoluttech.bcn.profile.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.GetTrustedContactsRPC
import com.resoluttech.bcn.profile.RemoveTrustedContactRPC
import com.resoluttech.bcn.profile.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.profile.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.types.Otp
import com.resoluttech.bcn.types.TrustedContact
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

internal class TrustedContactRepository {

    private val requestAddTrustedContactOTPRPC: RequestAddTrustedContactOTPRPC by KoinJavaComponent.inject(
        RequestAddTrustedContactOTPRPC::class.java,
    )

    private val resendAddTrustedContactOTPRPC: ResendAddTrustedContactOTPRPC by KoinJavaComponent.inject(
        ResendAddTrustedContactOTPRPC::class.java,
    )

    private val confirmAddTrustedContactOTPRPC: ConfirmAddTrustedContactOTPRPC by KoinJavaComponent.inject(
        ConfirmAddTrustedContactOTPRPC::class.java,
    )

    private val removeTrustedContactRPC: RemoveTrustedContactRPC by KoinJavaComponent.inject(
        RemoveTrustedContactRPC::class.java,
    )

    private val getTrustedContactsRPC: GetTrustedContactsRPC by KoinJavaComponent.inject(
        GetTrustedContactsRPC::class.java,
    )

    suspend fun requestAddTrustedContactOTP(
        trustedContact: TrustedContact,
    ): LeoRPCResult<RequestAddTrustedContactOTPRPC.Response, RequestAddTrustedContactOTPRPC.Error> {
        return requestAddTrustedContactOTPRPC.execute(
            RequestAddTrustedContactOTPRPC.Request(
                trustedContact,
            ),
        )
    }

    suspend fun resendAddTrustedContactOTP(
        otpId: UUID,
    ): LeoRPCResult<ResendAddTrustedContactOTPRPC.Response, ResendAddTrustedContactOTPRPC.Error> {
        return resendAddTrustedContactOTPRPC.execute(
            ResendAddTrustedContactOTPRPC.Request(
                otpId,
            ),
        )
    }

    suspend fun confirmAddTrustedContactOTP(
        otpId: UUID,
        otp: Otp,
    ): LeoRPCResult<ConfirmAddTrustedContactOTPRPC.Response, ConfirmAddTrustedContactOTPRPC.Error> {
        return confirmAddTrustedContactOTPRPC.execute(
            ConfirmAddTrustedContactOTPRPC.Request(
                otpId,
                otp,
            ),
        )
    }

    suspend fun removeTrustedContactRPC(
        trustedContactId: UUID,
    ): LeoRPCResult<RemoveTrustedContactRPC.Response, RemoveTrustedContactRPC.Error> {
        return removeTrustedContactRPC.execute(
            RemoveTrustedContactRPC.Request(
                trustedContactId,
            ),
        )
    }

    suspend fun getTrustedContactsRPC(): LeoRPCResult<GetTrustedContactsRPC.Response, GetTrustedContactsRPC.Error> {
        return getTrustedContactsRPC.execute(
            GetTrustedContactsRPC.Request,
        )
    }
}
