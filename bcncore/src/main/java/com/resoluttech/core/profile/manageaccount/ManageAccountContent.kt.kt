package com.resoluttech.core.profile.manageaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isEmpty
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.POSITION_KEY
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.DividerItemDecorator
import java.util.UUID

class ManageAccountContent :
    BaseFragment(),
    ManageAccountAdapter.ManageAccountListener,
    EditAccountDisplayNameDialog.EditAccountDisplayNameListener,
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val manageAccountsVM: ManageAccountVM by navGraphViewModels(R.id.manage_accounts_nav)

    private lateinit var accountsListRV: RecyclerView
    private lateinit var loadingPB: ProgressBar
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout
    private lateinit var noInactiveAccountFound: TextView
    private var isActiveWalletFragment: Boolean = true

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.manage_account_fragment_content, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        setSwipeToRefreshLayout()
        networkListenerCallback = this
        isActiveWalletFragment = requireArguments().getInt(POSITION_KEY) == 0
        if (isActiveWalletFragment) {
            manageAccountsVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        } else {
            manageAccountsVM.currentInactiveWalletState.observe(
                viewLifecycleOwner,
                Observer(::reactToState),
            )
        }
        manageAccountsVM.getAllAccounts(requireContext(), isActiveWalletFragment)
    }

    private fun initViews() {
        view?.let {
            accountsListRV = it.findViewById(R.id.accounts_rv)
            loadingPB = it.findViewById(R.id.progress_bar)
            swipeToRefreshLayout = it.findViewById(R.id.swipe_to_refresh)
            noInactiveAccountFound = it.findViewById(R.id.no_inactive_account_found)
        }
    }

    private fun reactToState(state: ManageAccountScreenState) {
        when (state) {
            is ManageAccountScreenState.Data -> {
                handleDataState(state)
            }
            is ManageAccountScreenState.Error -> {
                handleErrorState(state.uiError)
            }
            is ManageAccountScreenState.Loading -> {
                handleLoadingState()
            }
            is ManageAccountScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is ManageAccountScreenState.EditAccountDisplayName -> {
                handleEditAccountDisplayNameState(state)
            }
            is ManageAccountScreenState.InlineLoading -> {
                handleInlineLoadingState(state)
            }
            is ManageAccountScreenState.MarkWalletInactiveConfirmation -> {
                handleMarkWalletInactiveConfirmationState()
            }
        }
    }

    private fun setSwipeToRefreshLayout() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            if (manageAccountsVM.currentState.value is ManageAccountScreenState.Loading) {
                swipeToRefreshLayout.isRefreshing = false
            } else {
                manageAccountsVM.getAllAccounts(requireContext(), isActiveWalletFragment)
            }
        }
    }

    private fun handleMarkWalletInactiveConfirmationState() {
        showAlertDialog(
            requireContext().getString(R.string.alertTitleMarkAccountInactive),
            requireContext().getString(
                R.string.alertMessageMarkAccountInactive,
            ),
            DialogCodes.MARK_WALLET_INACTIVE_CODE,
            getString(R.string.alertActionConfirm),
            getString(R.string.alertActionCancel),
        )
    }

    private fun handleEditAccountDisplayNameState(state: ManageAccountScreenState.EditAccountDisplayName) {
        swipeToRefreshLayout.isRefreshing = false
        showCachedWallets(
            manageAccountsVM.listOfActiveAccounts,
            manageAccountsVM.listOfInActiveAccounts,
        )
        val editAccountDisplayNameDialog =
            EditAccountDisplayNameDialog.newInstance(state.accountId, state.accountName)
        editAccountDisplayNameDialog.setArguments(false)
        editAccountDisplayNameDialog.show(childFragmentManager, EditAccountDisplayNameDialog.TAG)
    }

    private fun handleInlineLoadingState(state: ManageAccountScreenState.InlineLoading) {
        swipeToRefreshLayout.isRefreshing = false
        loadingPB.visibility = View.GONE
        showProgressDialog(childFragmentManager, state.message)
    }

    private fun handleAcceptInputState() {
        swipeToRefreshLayout.isEnabled = true
        swipeToRefreshLayout.isRefreshing = false
        dismissProgressDialog()
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        showCachedWallets(
            manageAccountsVM.listOfActiveAccounts,
            manageAccountsVM.listOfInActiveAccounts,
        )
        swipeToRefreshLayout.isEnabled = true
        swipeToRefreshLayout.isRefreshing = false
        loadingPB.visibility = View.GONE
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                manageAccountsVM::onInlineErrorDismissed,
            )
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.MANAGE_ACCOUNT_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleDataState(data: ManageAccountScreenState.Data) {
        showWalletList(data.listOfActiveAccounts, data.listOfInActiveAccounts)
    }

    private fun handleLoadingState() {
        swipeToRefreshLayout.isEnabled = false
        swipeToRefreshLayout.isRefreshing = false
        handleViewByState(loadingPB)
        dismissProgressDialog()
    }

    private fun handleViewByState(view: View) {
        when (view) {
            accountsListRV -> {
                accountsListRV.visibility = View.VISIBLE
                loadingPB.visibility = View.GONE
            }
            loadingPB -> {
                accountsListRV.visibility = View.GONE
                loadingPB.visibility = View.VISIBLE
                noInactiveAccountFound.visibility = View.GONE
            }
        }
    }

    private fun showCachedWallets(
        listOfActiveAccounts: List<ActiveAccountsItem>,
        listOfInActiveAccounts: List<ActiveAccountsItem>,
    ) {
        if (accountsListRV.isEmpty()) {
            showWalletList(listOfActiveAccounts, listOfInActiveAccounts)
        }
    }

    private fun showWalletList(
        listOfActiveAccounts: List<ActiveAccountsItem>,
        listOfInActiveAccounts: List<ActiveAccountsItem>,
    ) {
        dismissProgressDialog()
        swipeToRefreshLayout.isEnabled = true
        swipeToRefreshLayout.isRefreshing = false
        handleViewByState(accountsListRV)
        val dividerItemDecoration = DividerItemDecorator(
            ContextCompat.getDrawable(
                requireContext(),
                R.drawable.divider,
            ),
            0,
            listOf(TYPE_ITEM),
        )
        val position = requireArguments().getInt(POSITION_KEY)
        val accountList = if (position == 0) {
            accountsListRV.visibility = View.VISIBLE
            noInactiveAccountFound.visibility = View.GONE
            listOfActiveAccounts
        } else {
            if (listOfInActiveAccounts.filterIsInstance<ActiveAccountsItem.Account>().isEmpty()
            ) {
                accountsListRV.visibility = View.GONE
                noInactiveAccountFound.visibility = View.VISIBLE
                return
            }
            accountsListRV.visibility = View.VISIBLE
            noInactiveAccountFound.visibility = View.GONE
            listOfInActiveAccounts
        }
        val adapter = ManageAccountAdapter(this, accountList)
        accountsListRV.adapter = adapter
        accountsListRV.addItemDecoration(dividerItemDecoration)
    }

    override fun onDeviceLocked() {
        super.onDeviceLocked()
        if (accountsListRV.adapter != null) {
            (accountsListRV.adapter as ManageAccountAdapter).closePopupMenu()
        }
    }

    override fun onSetAsPrimary(accountId: UUID, accountCurrency: Currency) {
        manageAccountsVM.onSetAccountAsPrimary(requireContext(), accountId, accountCurrency)
    }

    override fun onEditAccountDisplayName(accountId: UUID, oldAccountName: String) {
        manageAccountsVM.onEditAccountDisplayName(accountId, oldAccountName)
    }

    override fun onMarkAsInactive(accountId: UUID) {
        manageAccountsVM.onMarkAsInActiveTapped(accountId)
    }

    override fun onMarkAsActive(accountId: UUID) {
        manageAccountsVM.onMarkAsActiveTapped(requireContext(), accountId)
    }

    override fun onAccountDisplayNameEntered(
        accountId: UUID,
        oldAccountName: String,
        newAccountName: String,
    ) {
        manageAccountsVM.onAccountDisplayNameChanged(
            requireContext(),
            accountId,
            oldAccountName,
            newAccountName,
        )
    }

    override fun onEditWalletDialogDismissed() {
        manageAccountsVM.onEditWalletNameDialogDismissed()
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                manageAccountsVM.onServerErrorDismissed(findNavController())
            }
            DialogCodes.MARK_WALLET_INACTIVE_CODE -> {
                manageAccountsVM.markWalletInActive(
                    requireContext(),
                    manageAccountsVM.walletId
                        ?: throw IllegalStateException("Wallet Id cannot be null for marking wallet inactive"),
                )
            }
            else -> {
                manageAccountsVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        manageAccountsVM.onInlineErrorDismissed()
    }

    override fun onNetworkAvailable() {
        manageAccountsVM.getAllAccounts(requireContext(), isActiveWalletFragment)
    }
}
