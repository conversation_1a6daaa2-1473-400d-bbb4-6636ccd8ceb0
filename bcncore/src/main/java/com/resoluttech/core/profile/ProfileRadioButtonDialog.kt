package com.resoluttech.core.profile

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.views.BaseDialogFragment

class ProfileRadioButtonDialog : BaseDialogFragment() {

    interface RadioButtonListener {
        fun onCheckChanged(position: Int)
        fun onRadioButtonDialogDismissed()
    }

    private lateinit var dialogTitleTV: TextView
    private lateinit var radioGroup: RadioGroup

    private val radioButtonTitleList: List<String> by lazy {
        arguments?.getStringArrayList(
            KEY_RADIO_BUTTONS_TITLE,
        ) ?: throw IllegalArgumentException("Radio button list should not be null")
    }
    private val dialogTitle: String by lazy { arguments?.getString(KEY_DIALOG_TITLE) ?: throw IllegalArgumentException("Dialog title cannot be null") }
    private val position: Int by lazy { arguments?.getInt(KEY_POSITION) ?: throw IllegalArgumentException("Position cannot be null") }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.profile_radiobox_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(view)
        setupRadioButton()
        return view
    }

    private fun initViews(view: View) {
        dialogTitleTV = view.findViewById(R.id.title)
        dialogTitleTV.text = dialogTitle
        radioGroup = view.findViewById(R.id.radio_group)
    }

    private fun setupRadioButton() {
        radioButtonTitleList.forEachIndexed { index, it ->
            val radioButton = RadioButton(requireContext())
            radioButton.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
            )
            radioButton.text = it
            radioButton.setTextColor(requireContext().getColor(R.color.titleTextColor))
            radioButton.id = index
            radioButton.setPadding(0, 10.getDPPixelValue(), 0, 10.getDPPixelValue())
            radioGroup.addView(radioButton)
        }
        radioGroup.check(position)
        radioGroup.setOnCheckedChangeListener { _, position ->
            if (parentFragment != null && parentFragment is RadioButtonListener) {
                dismiss()
                (parentFragment as RadioButtonListener).onCheckChanged(position)
            } else {
                throw IllegalStateException("$parentFragment must implement ProfileRadioButtonDialog.RadioButtonListener")
            }
        }
    }

    override fun onStop() {
        super.onStop()
        (parentFragment as RadioButtonListener).onRadioButtonDialogDismissed()
    }

    companion object {
        const val TAG: String = "ProfileRadioButtonDialog"

        fun newInstance(dialogTitle: String, radioButtonTitle: ArrayList<String>, position: Int): ProfileRadioButtonDialog {
            val fragment = ProfileRadioButtonDialog()
            val args = Bundle()
            args.putStringArrayList(KEY_RADIO_BUTTONS_TITLE, radioButtonTitle)
            args.putString(KEY_DIALOG_TITLE, dialogTitle)
            args.putInt(KEY_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }
}

private const val KEY_RADIO_BUTTONS_TITLE = "RadioButtonTitle"
private const val KEY_DIALOG_TITLE = "DialogTitle"
private const val KEY_POSITION = "position"
