package com.resoluttech.core.profile.statements

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.listview.ListAdapter
import java.util.UUID

class StatementMonthAdapter(
    private val listener: StatementMonthListener,
    private val listOfStatementMonth: List<StatementMonth>,
) : ListAdapter<StatementMonth, RecyclerView.ViewHolder>(listOfStatementMonth) {

    interface StatementMonthListener {
        fun onMonthSelected(documentId: UUID, statementMonth: String)
    }

    override fun filter(query: String) {
        // Default action is already handled
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val item = listOfStatementMonth[position]
        val viewHolder = (holder as StatementViewHolder)
        viewHolder.apply {
            statementMonthTV.text = item.month
            statementDateTV.text =
                itemView.context.getString(R.string.statementCaption, item.startDate, item.endDate)

            rootViewCL.setOnClickListener {
                listener.onMonthSelected(item.documentId, item.month)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                StatementViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_statement_month, parent, false),
                )
            }
            else -> {
                throw IllegalStateException("Unknown View Type")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return TYPE_ITEM
    }

    private class StatementViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val rootViewCL: ConstraintLayout = itemView.findViewById(R.id.root_view)
        val statementMonthTV: TextView = itemView.findViewById(R.id.statement_month_tv)
        val statementDateTV: TextView = itemView.findViewById(R.id.statement_date_tv)
    }
}

const val TYPE_ITEM: Int = 1
