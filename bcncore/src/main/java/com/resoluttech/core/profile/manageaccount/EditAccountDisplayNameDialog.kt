package com.resoluttech.core.profile.manageaccount

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.EditText
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.views.BaseDialogFragment
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import java.util.UUID

class EditAccountDisplayNameDialog : BaseDialogFragment() {

    interface EditAccountDisplayNameListener {
        fun onAccountDisplayNameEntered(
            accountId: UUID,
            oldAccountName: String,
            newAccountName: String,
        )

        fun onEditWalletDialogDismissed()
    }

    private lateinit var accountNameET: EditText
    private lateinit var accountNameTIL: TextInputLayout
    private lateinit var cancelButton: MaterialButton
    private lateinit var saveButton: MaterialButton
    private val oldAccountName: String by lazy {
        arguments?.getString(KEY_ACCOUNT_NAME) ?: getString(R.string.alertMessageGenericError)
    }
    private val accountId: String by lazy {
        arguments?.getString(KEY_ACCOUNT_ID) ?: getString(R.string.alertMessageGenericError)
    }

    /***
     * 'shouldDismissEditAccountDisplayNameDialog' variable is set as false whenever a new instance of alert dialog is created
     * So when system calls onDestroy() (Theme or Font Config Change or to free up RAM memory) and user comes back to app the shouldDismissEditAccountDisplayNameDialog is reset as true and dialog is dismissed.
     * The fragment calling this alert dialog will create a new instance again by restoring VM state and shouldDismissEditAccountDisplayNameDialog would be set as false.
     */
    private var shouldDismissEditAccountDisplayNameDialog: Boolean = true

    fun setArguments(shouldDismissEditAccountDisplayNameDialog: Boolean) {
        this.shouldDismissEditAccountDisplayNameDialog = shouldDismissEditAccountDisplayNameDialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.dialog_edit_account_display_name, container)
        if (shouldDismissEditAccountDisplayNameDialog) {
            dismissAllowingStateLoss()
        }
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        setupListeners()
    }

    private fun setupViews() {
        accountNameET.setText(oldAccountName)
        accountNameET.setSelection(accountNameET.length())
    }

    private fun setupListeners() {
        cancelButton.setOnClickListener {
            (parentFragment as EditAccountDisplayNameListener).onEditWalletDialogDismissed()
            dismiss()
        }

        saveButton.setOnClickListener {
            (parentFragment as EditAccountDisplayNameListener).onAccountDisplayNameEntered(
                UUID.fromString(
                    accountId,
                ),
                oldAccountName,
                accountNameET.text.toString().trim(),
            )
            dismiss()
        }

        accountNameET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = accountNameTIL,
                maxLength = Config.MAX_WALLET_DISPLAY_NAME_LENGTH,
                onTextChanged = {
                    if (it?.isNotBlank() == true) {
                        saveButton.enable()
                    } else {
                        saveButton.disable()
                    }
                },
            ),
        )
    }

    private fun initViews(rootView: View) {
        rootView.apply {
            accountNameET = findViewById(R.id.account_name_et)
            accountNameTIL = findViewById(R.id.account_name_til)
            cancelButton = findViewById(R.id.cancel_button)
            saveButton = findViewById(R.id.ok_button)

            cancelButton.setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
            saveButton.setTextColor(requireContext().getColor(R.color.colorPrimaryLight))
        }
    }

    companion object {
        const val TAG: String = "EditAccountDisplayNameDialog"
        fun newInstance(accountId: UUID, accountName: String): EditAccountDisplayNameDialog {
            val fragment = EditAccountDisplayNameDialog()
            val args = Bundle()
            args.putString(KEY_ACCOUNT_NAME, accountName)
            args.putString(KEY_ACCOUNT_ID, "$accountId")
            fragment.arguments = args
            return fragment
        }
    }
}

private const val KEY_ACCOUNT_NAME = "Dialog.accountName"
private const val KEY_ACCOUNT_ID = "Dialog.accountId"
