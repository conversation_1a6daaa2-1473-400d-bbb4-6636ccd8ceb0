package com.resoluttech.core.profile.changesessionpin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.SwitchCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.poovam.pinedittextfield.CirclePinField
import com.poovam.pinedittextfield.PinField
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.config.Config.Companion.SESSION_PIN_WARNING_ATTEMPT_LEFT
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes.Companion.BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE
import com.resoluttech.core.utils.HapticConstant
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.performHapticFeedback
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showKeyboard
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import timber.log.Timber

class ChangeAppPinFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    Biometric.AppAuthenticationListener {

    private val changeSessionPinVM by lazy { ViewModelProvider(this)[ChangeSessionPinVM::class.java] }
    private lateinit var enterPinET: CirclePinField
    private lateinit var titleTV: TextView
    private lateinit var errorTV: TextView
    private lateinit var errorBiometricTV: TextView
    private lateinit var biometricSwitch: SwitchCompat
    private var isConfirmPasswordOptionEnabled = false
    private var newAppPin: String? = null
    private var isBiometricsAvailable = false
    private var isCurrentSessionPinConfirmed: Boolean = false
    private lateinit var alertDialog: AlertDialog

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_change_app_pin, container, false)
        initViews(rootView)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.profileScreenOptionChangeSessionPin))
        showKeyboard(enterPinET)
        Biometric.setAuthenticationListener(this)
        setupPinInputListener()
        setupBackPressed(findNavController()::navigateUp)
        changeSessionPinVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onResume() {
        super.onResume()
        if (isConfirmPasswordOptionEnabled) {
            titleTV.text = getString(R.string.confirmNewPinTitle)
            changeSessionPinVM.canUserUseBiometricAuthentication()
            enterPinET.text?.clear()
            handleBiometricAuthenticationLayout(Biometric.isBiometricHardwareAvailable())
        } else if (isCurrentSessionPinConfirmed) {
            titleTV.text = getString(R.string.setupSessionPinEnterNewSessionPinTitle)
        }
        changeSessionPinVM.canUserUseBiometricAuthentication()
    }

    override fun onStop() {
        super.onStop()
        hideKeyboard()
    }

    private fun reactToState(state: ChangeSessionPinScreenState) {
        when (state) {
            is ChangeSessionPinScreenState.AcceptInput -> {
                // Default action is handled already
            }
            is ChangeSessionPinScreenState.AppPinUpdatedSuccessfully -> {
                handleAppPinUpdatedSuccessfully()
            }
            is ChangeSessionPinScreenState.IncorrectPin -> {
                handleErrorState(state)
            }
            is ChangeSessionPinScreenState.AppPinRetriesExhaustedDialog -> {
                handleAppPinRetriesExhaustedDialog(state)
            }
            is ChangeSessionPinScreenState.CurrentAppPinConfirmed -> {
                handleCurrentAppPinConfirmed()
            }
            is ChangeSessionPinScreenState.EnterNewAppPin -> {
                handleEnterNewAppPin(state)
            }
            is ChangeSessionPinScreenState.Data -> {
                handleDataState()
            }
            is ChangeSessionPinScreenState.AuthenticateBiometrics -> {
                handleBiometricAuthentication()
            }
            is ChangeSessionPinScreenState.BiometricAuthentication -> {
                handleBiometricAuthenticationOption(
                    isBiometricPossible = state.isBiometricPossible,
                )
            }
            is ChangeSessionPinScreenState.BiometricRetriesExhausted -> {
                handleBiometricRetriesExhausted()
            }
            ChangeSessionPinScreenState.BiometricFailed -> {
                handleBiometricFailed()
            }
            ChangeSessionPinScreenState.AppPinWarningAttemptLeft -> {
                handleAppPinWarningAttemptLeft()
            }
        }
    }

    private fun handleAppPinWarningAttemptLeft() {
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_REJECT, requireContext(), TAG)
        errorTV.visibility = View.VISIBLE
        errorTV.text = requireContext().resources.getQuantityString(R.plurals.wrongSessionPinEnteredLabelPlural, SESSION_PIN_WARNING_ATTEMPT_LEFT, SESSION_PIN_WARNING_ATTEMPT_LEFT)
    }

    private fun handleBiometricRetriesExhausted() {
        Biometric.cancelAuthentication()
        handleBiometricFailed()
    }

    private fun handleBiometricFailed() {
        if (::alertDialog.isInitialized && alertDialog.isAdded) {
            alertDialog.dismiss()
        }
        alertDialog = AlertDialog.newInstance(
            title = requireContext().getString(R.string.alertTitleBiometryAuthenticationFailure),
            message = getString(R.string.alertMessageBiometricAuthenticationFailure),
            dialogId = BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleBiometricAuthenticationOption(
        isBiometricPossible: Boolean,
    ) {
        isBiometricsAvailable = isBiometricPossible
        showBiometricOption(isConfirmPasswordOptionEnabled)
    }

    private fun showBiometricOption(shouldShowOption: Boolean) {
        if (isBiometricsAvailable && shouldShowOption) {
            biometricSwitch.visibility = View.VISIBLE
            biometricSwitch.isChecked = Biometric.isUserEnrolled()
            biometricSwitch.setOnClickListener {
                if (biometricSwitch.isChecked) {
                    biometricSwitch.isChecked = Biometric.isUserEnrolled()
                }
                if (!Biometric.isUserEnrolled()) {
                    errorBiometricTV.visibility = View.VISIBLE
                    errorBiometricTV.text =
                        getString(R.string.alertMessageDeviceNotEnrolledForBiometric)
                } else {
                    errorBiometricTV.visibility = View.GONE
                }
            }
        } else {
            biometricSwitch.visibility = View.GONE
            biometricSwitch.isChecked = false
        }
    }

    private fun handleBiometricAuthentication() {
        Biometric.authenticate(this, null)
    }

    private fun handleBiometricAuthenticationLayout(isBiometricPossible: Boolean) {
        if (isBiometricPossible && isConfirmPasswordOptionEnabled) {
            biometricSwitch.visibility = View.VISIBLE
            biometricSwitch.isChecked = Biometric.isUserEnrolled()
        } else {
            biometricSwitch.visibility = View.GONE
            biometricSwitch.isChecked = false
        }
    }

    private fun handleDataState() {
        biometricSwitch.isChecked = Biometric.isUserEnrolled()
    }

    private fun handleEnterNewAppPin(state: ChangeSessionPinScreenState.EnterNewAppPin) {
        isConfirmPasswordOptionEnabled = true
        newAppPin = state.newAppPin
        titleTV.text = getString(R.string.confirmNewPinTitle)
        changeSessionPinVM.canUserUseBiometricAuthentication()
        enterPinET.text?.clear()
        handleBiometricAuthenticationLayout(Biometric.isBiometricHardwareAvailable())
    }

    private fun handleCurrentAppPinConfirmed() {
        titleTV.text = getString(R.string.setupSessionPinEnterNewSessionPinTitle)
        errorTV.visibility = View.GONE
        enterPinET.text?.clear()
        isCurrentSessionPinConfirmed = true
        handleBiometricAuthenticationLayout(Biometric.isBiometricHardwareAvailable())
    }

    private fun handleAppPinRetriesExhaustedDialog(state: ChangeSessionPinScreenState.AppPinRetriesExhaustedDialog) {
        showErrorDialog(
            state.uiError.errorTitle,
            state.uiError.errorMessage,
            ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED,
        )
    }

    private fun handleAppPinUpdatedSuccessfully() {
        showAlertDialog(
            requireContext().getString(R.string.alertTitlePinChanged),
            requireContext().getString(R.string.alertMessagePinChanged),
            APP_PIN_CHANGED_SUCCESSFULLY,
            requireContext().getString(R.string.alertActionDismiss),
        )
    }

    private fun handleErrorState(state: ChangeSessionPinScreenState.IncorrectPin) {
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_REJECT, requireContext(), TAG)
        errorTV.visibility = View.VISIBLE
        errorTV.text = state.errorMessage
        enterPinET.text?.clear()
    }

    private fun setupPinInputListener() {
        enterPinET.onTextCompleteListener = object : PinField.OnTextCompleteListener {
            override fun onTextComplete(enteredText: String): Boolean {
                if (isConfirmPasswordOptionEnabled) {
                    changeSessionPinVM.onConfirmPassword(
                        enteredText,
                        newAppPin!!,
                        biometricSwitch.isChecked,
                        requireContext(),
                    )
                } else {
                    changeSessionPinVM.onAppPinEntered(enteredText, requireContext())
                }
                return true
            }
        }
    }

    private fun initViews(rootView: View) {
        rootView.apply {
            errorTV = findViewById(R.id.error_tv)
            errorBiometricTV = findViewById(R.id.error_biometric)
            titleTV = findViewById(R.id.title_tv)
            enterPinET = findViewById(R.id.password_et)
            biometricSwitch = findViewById(R.id.biometric_switch)
            enterPinET.requestFocus()
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("onPositiveAction called for dialogId: $dialogId")
        when (dialogId) {
            ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED -> {
                changeSessionPinVM.onDialogPositiveAction(activity)
            }
            APP_PIN_CHANGED_SUCCESSFULLY -> {
                changeSessionPinVM.onAppPinChangedSuccessfully(findNavController())
            }
            BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE -> {
                changeSessionPinVM.onAppPinChangedSuccessfully(findNavController())
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        Timber.tag(TAG).i("onNegativeAction called for dialogId: $dialogId")
    }

    override fun onDestroy() {
        super.onDestroy()
        showToolbar()
    }

    override fun onBiometricAuthenticationSuccessful() {
        changeSessionPinVM.onBiometricAuthenticationSuccessful()
        Timber.tag(TAG)
            .i("onBiometricAuthenticationSuccessful: Biometric authentication successful while setting up session pin")
    }

    override fun onBiometricAuthenticationFailed() {
        changeSessionPinVM.onAuthenticationFailed()
        Timber.tag(TAG)
            .i("onBiometricAuthenticationFailed: Biometric authentication failed while setting up session pin")
    }

    override fun onBiometricAuthenticationError() {
        changeSessionPinVM.onAuthenticationError()
        Timber.tag(TAG)
            .i("onBiometricAuthenticationError: Biometric authentication error while setting up session pin")
    }
}

private const val TAG = "AppPinAuthFragment"
private const val ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED = 100
private const val APP_PIN_CHANGED_SUCCESSFULLY = 228
