package com.resoluttech.core.profile.manageaccount

import com.resoluttech.bcn.profile.ChangeAccountDisplayNameRPC
import com.resoluttech.bcn.profile.ChangeAccountStateRPC
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPC
import com.resoluttech.bcn.profile.CreateNewAccountRPC
import com.resoluttech.bcn.profile.GetAllAccountsRPC
import com.resoluttech.bcn.types.AccountDisplayName
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcn.types.Currency
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

class ManageAccountRepository {

    private val getAllAccountsRPC: GetAllAccountsRPC by KoinJavaComponent.inject(
        GetAllAccountsRPC::class.java,
    )
    private val changeDefaultAccountRPC: ChangeDefaultAccountRPC by KoinJavaComponent.inject(
        ChangeDefaultAccountRPC::class.java,
    )
    private val changeAccountDisplayNameRPC: ChangeAccountDisplayNameRPC by KoinJavaComponent.inject(
        ChangeAccountDisplayNameRPC::class.java,
    )
    private val createNewAccountRPC: CreateNewAccountRPC by KoinJavaComponent.inject(
        CreateNewAccountRPC::class.java,
    )
    private val changeAccountStateRPC: ChangeAccountStateRPC by KoinJavaComponent.inject(
        ChangeAccountStateRPC::class.java,
    )

    suspend fun getAllAccounts(): LeoRPCResult<GetAllAccountsRPC.Response, GetAllAccountsRPC.Error> {
        return getAllAccountsRPC.execute(GetAllAccountsRPC.Request)
    }

    suspend fun changeDefaultAccount(accountId: UUID, currency: Currency): LeoRPCResult<ChangeDefaultAccountRPC.Response, ChangeDefaultAccountRPC.Error> {
        return changeDefaultAccountRPC.execute(ChangeDefaultAccountRPC.Request(accountId, currency))
    }

    suspend fun changeAccountDisplayName(accountId: UUID, newDisplayName: String): LeoRPCResult<ChangeAccountDisplayNameRPC.Response, ChangeAccountDisplayNameRPC.Error> {
        return changeAccountDisplayNameRPC.execute(ChangeAccountDisplayNameRPC.Request(accountId, AccountDisplayName(newDisplayName)))
    }

    suspend fun createNewAccount(accountName: String, currency: Currency): LeoRPCResult<CreateNewAccountRPC.Response, CreateNewAccountRPC.Error> {
        return createNewAccountRPC.execute(CreateNewAccountRPC.Request(AccountDisplayName(accountName), currency))
    }

    suspend fun changeAccountState(accountId: UUID, accountState: AccountState): LeoRPCResult<ChangeAccountStateRPC.Response, ChangeAccountStateRPC.Error> {
        return changeAccountStateRPC.execute(ChangeAccountStateRPC.Request(accountId, accountState))
    }
}
