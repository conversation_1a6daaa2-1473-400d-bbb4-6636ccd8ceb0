package com.resoluttech.core.profile

import android.app.Activity
import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.core.utils.getAccountQRCodeDeeplink
import com.resoluttech.core.utils.getPhoneNumberQRCodeForCashRequestDeeplink
import com.resoluttech.core.utils.getQRCodeBitmap
import com.resoluttech.core.utils.getUserQRCodeDeeplink
import com.resoluttech.core.utils.shareQRCode
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import java.util.UUID

class QRCodeDialogVM : ViewModel() {

    private var _currentState: MutableLiveData<QRCodeDialogState> =
        MutableLiveData(QRCodeDialogState.Loading)
    val state: LiveData<QRCodeDialogState> = _currentState
    private val vmIoScope = viewModelScope + Dispatchers.IO

    fun generateAccountQRCodeImage(userName: String, accountId: UUID) {
        _currentState.postValue(QRCodeDialogState.Loading)
        vmIoScope.launch {
            val qrCodeBitmap: Bitmap = getQRCodeBitmap(getAccountQRCodeDeeplink(userName, accountId))
            _currentState.postValue(QRCodeDialogState.Data(qrCodeBitmap))
        }
    }

    fun generatePhoneNumberQRCodeForCashRequestImage(phoneNumber: LeoPhoneNumber) {
        _currentState.postValue(QRCodeDialogState.Loading)
        vmIoScope.launch {
            val qrCodeBitmap: Bitmap = getQRCodeBitmap(
                getPhoneNumberQRCodeForCashRequestDeeplink(
                    phoneNumber,
                ),
            )
            _currentState.postValue(QRCodeDialogState.Data(qrCodeBitmap))
        }
    }

    fun generateUserQRCodeImage(userName: String, userId: UUID) {
        _currentState.postValue(QRCodeDialogState.Loading)
        vmIoScope.launch {
            val qrCodeBitmap: Bitmap = getQRCodeBitmap(getUserQRCodeDeeplink(userName, userId))
            _currentState.postValue(QRCodeDialogState.Data(qrCodeBitmap))
        }
    }

    fun onShareClicked(activity: Activity) {
        if (_currentState.value is QRCodeDialogState.Data) {
            shareQRCode(
                activity,
                (_currentState.value as QRCodeDialogState.Data).qrCode,
                QR_CODE_FILE_NAME,
            )
        } else {
            Timber.tag(TAG).i("Share can happen only in the Data state.")
            return
        }
    }
}

sealed class QRCodeDialogState {
    object Loading : QRCodeDialogState()
    data class Data(val qrCode: Bitmap) : QRCodeDialogState()
}

private const val QR_CODE_FILE_NAME = "qrcode"
private const val TAG = "QRCodeDialogVM"
