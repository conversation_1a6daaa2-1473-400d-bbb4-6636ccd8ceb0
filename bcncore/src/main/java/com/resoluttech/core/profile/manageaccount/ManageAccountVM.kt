package com.resoluttech.core.profile.manageaccount

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.ChangeAccountDisplayNameRPC
import com.resoluttech.bcn.profile.ChangeAccountStateRPC
import com.resoluttech.bcn.profile.ChangeDefaultAccountRPC
import com.resoluttech.bcn.profile.GetAllAccountsRPC
import com.resoluttech.bcn.types.Account
import com.resoluttech.bcn.types.AccountDisplayName
import com.resoluttech.bcn.types.AccountState
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcn.types.InvalidAccountDisplayNameTypeException
import com.resoluttech.bcncore.R
import com.resoluttech.core.home.HomeRepository
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.ManageAccountRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ProfileRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getUserFacingValue
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class ManageAccountVM : ViewModel() {

    private var _currentState: MutableLiveData<ManageAccountScreenState> =
        MutableLiveData(ManageAccountScreenState.Loading)
    private var _currentInactiveWalletState: MutableLiveData<ManageAccountScreenState> =
        MutableLiveData(ManageAccountScreenState.Loading)
    val currentInactiveWalletState: MutableLiveData<ManageAccountScreenState> = _currentInactiveWalletState
    val currentState: LiveData<ManageAccountScreenState> = _currentState
    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val manageAccountRepository = ManageAccountRepository()
    private val homeRepository = HomeRepository()
    var walletId: UUID? = null
    val listOfActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()
    val listOfInActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()

    fun getAllAccounts(context: Context, isActiveWalletFragment: Boolean) {
        if (_currentState.value !is ManageAccountScreenState.EditAccountDisplayName && _currentState.value !is ManageAccountScreenState.Error && _currentInactiveWalletState.value !is ManageAccountScreenState.Error) {
            if (isActiveWalletFragment) {
                _currentState.postValue(ManageAccountScreenState.Loading)
            } else {
                _currentInactiveWalletState.postValue(ManageAccountScreenState.Loading)
            }
            vmIoScope.launch {
                executeRPC(
                    context,
                    {
                        when (val result = manageAccountRepository.getAllAccounts()) {
                            is LeoRPCResult.LeoResponse -> handleGetAllAccountsRPCResponse(
                                result.response,
                                context,
                            )
                            is LeoRPCResult.LeoError -> handleGetAllAccountsRPCError(result.error)
                        }
                    },
                    {
                        it.showError(isActiveWalletFragment)
                    },
                )
            }
        }
    }

    private fun handleGetAllAccountsRPCResponse(
        response: GetAllAccountsRPC.Response,
        context: Context,
    ) {
        val listOfActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()
        val listOfInActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()
        vmIoScope.launch {
            val accounts = response.accounts.filter { it.accountState == AccountState.ACTIVE }.map {
                com.resoluttech.core.utils.Account(
                    it.displayName.name,
                    it.accountId.toString(),
                    it.balance.currency.currencyCode,
                    it.isDefault,
                    it.balance.amount,
                )
            }
            homeRepository.refreshSavedAccountList(context, accounts)
            response.accounts.sortedByDescending(Account::isDefault)
                .groupBy { it.balance.currency }.values.forEach { it ->
                    it.forEach { account ->
                        if (listOfActiveAccounts.find { it is ActiveAccountsItem.AccountHeader && it.currency == account.balance.currency } == null) {
                            if (account.accountState == AccountState.ACTIVE) {
                                listOfActiveAccounts.add(
                                    ActiveAccountsItem.AccountHeader(
                                        account.balance.currency,
                                    ),
                                )
                            }
                        }

                        if (listOfInActiveAccounts.find { it is ActiveAccountsItem.AccountHeader && it.currency == account.balance.currency } == null) {
                            if (account.accountState == AccountState.INACTIVE) {
                                listOfInActiveAccounts.add(
                                    ActiveAccountsItem.AccountHeader(
                                        account.balance.currency,
                                    ),
                                )
                            }
                        }

                        if (account.accountState == AccountState.ACTIVE) {
                            listOfActiveAccounts.add(
                                ActiveAccountsItem.Account(
                                    account.displayName.name,
                                    displayAmountWithCurrency(
                                        account.balance.currency.currencyCode,
                                        account.balance.amount.getUserFacingValue(),
                                    ),
                                    account.accountId.toString(),
                                    account.balance.currency,
                                    account.isDefault,
                                    true,
                                ),
                            )
                        } else {
                            listOfInActiveAccounts.add(
                                ActiveAccountsItem.Account(
                                    account.displayName.name,
                                    displayAmountWithCurrency(
                                        account.balance.currency.currencyCode,
                                        account.balance.amount.getUserFacingValue(),
                                    ),
                                    account.accountId.toString(),
                                    account.balance.currency,
                                    account.isDefault,
                                    false,
                                ),
                            )
                        }
                    }
                    _currentState.postValue(
                        ManageAccountScreenState.Data(
                            listOfActiveAccounts,
                            listOfInActiveAccounts,
                        ),
                    )
                    _currentInactiveWalletState.postValue(
                        ManageAccountScreenState.Data(
                            listOfActiveAccounts,
                            listOfInActiveAccounts,
                        ),
                    )
                }
        }
    }

    private fun handleGetAllAccountsRPCError(error: GetAllAccountsRPC.Error) {
        ManageAccountRPCExceptionHandler.getGetAllAccountsRPCErrorMessage(error)
    }

    private fun UIError.showError(isActiveWalletFragment: Boolean = true) {
        if (isActiveWalletFragment) {
            _currentState.postValue(ManageAccountScreenState.Error(this))
        } else {
            _currentInactiveWalletState.postValue(ManageAccountScreenState.Error(this))
        }
    }

    fun onInlineErrorDismissed() {
        walletId = null
        _currentState.postValue(ManageAccountScreenState.AcceptInput)
        _currentInactiveWalletState.postValue(ManageAccountScreenState.AcceptInput)
    }

    fun onSetAccountAsPrimary(context: Context, accountId: UUID, accountCurrency: Currency) {
        _currentState.postValue(ManageAccountScreenState.InlineLoading(context.getString(R.string.alertMessageUpdatingDefaultWallet)))
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result =
                            manageAccountRepository.changeDefaultAccount(accountId, accountCurrency)
                    ) {
                        is LeoRPCResult.LeoResponse -> handleChangeDefaultAccountRPCResponse(context)
                        is LeoRPCResult.LeoError -> handleChangeDefaultAccountRPCError(
                            result.error,
                            context,
                        )
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleChangeDefaultAccountRPCResponse(context: Context) {
        getAllAccounts(context, true)
    }

    private fun handleChangeDefaultAccountRPCError(
        error: ChangeDefaultAccountRPC.Error,
        context: Context,
    ) {
        ProfileRPCExceptionHandler.getChangeDefaultAccountErrorMessage(context, error).showError()
    }

    fun onEditAccountDisplayName(accountId: UUID, accountName: String) {
        _currentState.postValue(
            ManageAccountScreenState.EditAccountDisplayName(
                accountId,
                accountName,
            ),
        )
    }

    private fun validateDisplayName(
        context: Context,
        oldAccountName: String,
        newAccountName: String,
    ): Boolean {
        try {
            AccountDisplayName(newAccountName)
        } catch (e: InvalidAccountDisplayNameTypeException) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleAccountNameTooLong),
                context.getString(R.string.alertMessageAccountNameTooLong),
            ).showError()
            return false
        } catch (e: InvalidAccountDisplayNameTypeException) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleEmptyAccountName),
                context.getString(R.string.alertMessageEmptyAccountName),
            ).showError()
            return false
        }

        if (oldAccountName == newAccountName) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleAccountNameExists),
                context.getString(R.string.alertMessageAccountNameExists),
            ).showError()
            return false
        }

        return true
    }

    fun onEditWalletNameDialogDismissed() {
        _currentState.postValue(ManageAccountScreenState.AcceptInput)
    }

    fun onAccountDisplayNameChanged(
        context: Context,
        accountId: UUID,
        oldAccountName: String,
        newAccountName: String,
    ) {
        if (!validateDisplayName(context, oldAccountName, newAccountName)) {
            return
        }

        vmIoScope.launch {
            _currentState.postValue(ManageAccountScreenState.InlineLoading(context.getString(R.string.alertMessageUpdatingWalletName)))
            executeRPC(
                context,
                {
                    when (
                        val result = manageAccountRepository.changeAccountDisplayName(
                            accountId,
                            newAccountName,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> handleChangeAccountDisplayNameRPCResponse(
                            context,
                        )
                        is LeoRPCResult.LeoError -> handleChangeAccountDisplayNameRPCError(
                            context,
                            result.error,
                        )
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleChangeAccountDisplayNameRPCResponse(context: Context) {
        getAllAccounts(context, true)
        _currentState.postValue(ManageAccountScreenState.AcceptInput)
    }

    private fun handleChangeAccountDisplayNameRPCError(
        context: Context,
        error: ChangeAccountDisplayNameRPC.Error,
    ) {
        ManageAccountRPCExceptionHandler.getChangeAccountDisplayNameRPCErrorMessage(context, error)
            .showError()
    }

    private fun handleChangeAccountStateRPCError(
        context: Context,
        error: ChangeAccountStateRPC.Error,
    ) {
        ManageAccountRPCExceptionHandler.getChangeAccountStateRPCErrorMessage(context, error)
            .showError()
    }

    fun onMarkAsInActiveTapped(walletId: UUID) {
        this.walletId = walletId
        _currentState.postValue(ManageAccountScreenState.MarkWalletInactiveConfirmation)
    }

    fun markWalletInActive(context: Context, walletId: UUID) {
        changeAccountState(accountState = AccountState.INACTIVE, walletId, context, true)
        _currentState.postValue(ManageAccountScreenState.AcceptInput)
    }

    fun onMarkAsActiveTapped(context: Context, accountId: UUID) {
        changeAccountState(accountState = AccountState.ACTIVE, accountId, context, false)
        _currentInactiveWalletState.postValue(ManageAccountScreenState.AcceptInput)
    }

    private fun changeAccountState(
        accountState: AccountState,
        accountId: UUID,
        context: Context,
        isActiveWalletFragment: Boolean,
    ) {
        val message = when (accountState) {
            AccountState.ACTIVE -> context.getString(R.string.alertMessageMarkingWalletActive)
            AccountState.INACTIVE -> context.getString(R.string.alertMessageMarkingWalletInactive)
        }
        vmIoScope.launch {
            if (isActiveWalletFragment) {
                _currentState.postValue(ManageAccountScreenState.InlineLoading(message))
            } else {
                _currentInactiveWalletState.postValue(ManageAccountScreenState.InlineLoading(message))
            }
            executeRPC(
                context,
                {
                    when (
                        val result = manageAccountRepository.changeAccountState(
                            accountId,
                            accountState,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> handleAccountStateChangeResponse(
                            context,
                            accountId,
                            isActiveWalletFragment,
                        )
                        is LeoRPCResult.LeoError -> handleChangeAccountStateRPCError(
                            context,
                            result.error,
                        )
                    }
                },
                {
                    it.showError(isActiveWalletFragment)
                },
            )
        }
    }

    private fun handleAccountStateChangeResponse(
        context: Context,
        accountId: UUID,
        isActiveWalletFragment: Boolean,
    ) {
        if ("$accountId" == SelectedAccountHelper.getSelectedAccount()?.id || SelectedAccountHelper.getSelectedAccount() == null) {
            SelectedAccountHelper.setSelectedAccountId(
                context,
                UUID.fromString(SelectedAccountHelper.getPersistedAccounts()[0].id),
            )
        }
        getAllAccounts(context, isActiveWalletFragment)
    }

    fun onServerErrorDismissed(navController: NavController) {
        navController.navigateUp()
    }
}

sealed class ManageAccountScreenState {
    object AcceptInput : ManageAccountScreenState()
    object Loading : ManageAccountScreenState()
    data class InlineLoading(val message: String) : ManageAccountScreenState()
    data class Data(
        val listOfActiveAccounts: List<ActiveAccountsItem>,
        val listOfInActiveAccounts: MutableList<ActiveAccountsItem>,
    ) : ManageAccountScreenState()

    data class EditAccountDisplayName(val accountId: UUID, val accountName: String) :
        ManageAccountScreenState()

    data class Error(val uiError: UIError) : ManageAccountScreenState()
    object MarkWalletInactiveConfirmation : ManageAccountScreenState()
}
