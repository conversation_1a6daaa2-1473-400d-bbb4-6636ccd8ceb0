package com.resoluttech.core.profile.statements

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.createBitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.pdf.PdfRenderer
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.FileProvider
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.github.chrisbanes.photoview.PhotoView
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.PDFRendererPreviousPath
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.views.BaseFragment
import timber.log.Timber
import java.io.File
import java.io.FileDescriptor
import java.io.IOException

/**
 * This fragment has a big [ImageView] that shows PDF pages, and 2 [ImageView]s to move between pages.
 * We use a [PdfRenderer] to render PDF pages as [Bitmap]s.
 */
class PDFRendererFragment : BaseFragment() {

    private val statementsVM by lazy { ViewModelProvider(this)[StatementsVM::class.java] }
    val args: PDFRendererFragmentArgs by navArgs()
    private val file by lazy {
        File(
            requireContext().applicationContext.filesDir.path + File.separator + "${
                args.title.replace(
                    " ",
                    "_",
                )
            }.pdf",
        )
    }

    private lateinit var pdfRenderer: PdfRenderer
    private lateinit var currentPage: PdfRenderer.Page
    private var currentPageNumber: Int = INITIAL_PAGE_INDEX

    private lateinit var pdfPageView: PhotoView
    private lateinit var previousButton: ImageView
    private lateinit var nextButton: ImageView
    private lateinit var backIV: ImageView
    private lateinit var toolbarTitleTV: TextView
    private lateinit var shareIV: ImageView
    private lateinit var pageCountTV: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_pdf_renderer, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        hideToolbar()
        setupViews()

        // If there is a savedInstanceState (screen orientations, etc.), we restore the page index.
        currentPageNumber = savedInstanceState?.getInt(CURRENT_PAGE_INDEX_KEY, INITIAL_PAGE_INDEX)
            ?: INITIAL_PAGE_INDEX
    }

    private fun setupViews() {
        view?.apply {
            backIV = findViewById<ImageView>(R.id.back_button).apply {
                setOnClickListener {
                    findNavController().navigateUp()
                }
            }
            toolbarTitleTV = findViewById<TextView>(R.id.toolbar_text).apply {
                text = args.title
            }
            shareIV = findViewById(R.id.share_button)
            if (PDFRendererPreviousPath.STATEMENT.name == args.previousPath) {
                shareIV.apply {
                    setOnClickListener {
                        shareIV.disable()
                        statementsVM.onShareStatement(requireContext(), file)
                    }
                }
            } else {
                shareIV.visibility = View.GONE
            }

            pdfPageView = findViewById(R.id.image)
            previousButton = findViewById<ImageView>(R.id.previous_iv).apply {
                setOnClickListener {
                    showPage(currentPage.index - 1)
                }
            }
            nextButton = findViewById<ImageView>(R.id.next_iv).apply {
                setOnClickListener {
                    showPage(currentPage.index + 1)
                }
            }
            pageCountTV = findViewById(R.id.page_count_tv)
        }
    }

    override fun onStart() {
        super.onStart()

        val documentUri = FileProvider.getUriForFile(
            requireContext(),
            requireContext().packageName + ".provider",
            file,
        )
        try {
            openRenderer(activity, documentUri)
            showPage(currentPageNumber)
        } catch (ioException: IOException) {
            Timber.tag(TAG).d(t = ioException, message = "Exception opening document")
        }
    }

    override fun onStop() {
        super.onStop()
        try {
            closeRenderer()
        } catch (ioException: IOException) {
            Timber.tag(TAG).d(t = ioException, message = "Exception closing document")
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(CURRENT_PAGE_INDEX_KEY, currentPage.index)
        super.onSaveInstanceState(outState)
    }

    /**
     * Sets up a [PdfRenderer] and related resources.
     */
    @Throws(IOException::class)
    private fun openRenderer(context: Context?, documentUri: Uri) {
        if (context == null) return

        /**
         * It may be tempting to use `use` here, but [PdfRenderer] expects to take ownership
         * of the [FileDescriptor], and, if we did use `use`, it would be auto-closed at the
         * end of the block, preventing us from rendering additional pages.
         */
        val fileDescriptor = context.contentResolver.openFileDescriptor(documentUri, "r") ?: return

        // This is the PdfRenderer we use to render the PDF.
        pdfRenderer = PdfRenderer(fileDescriptor)
        currentPage = pdfRenderer.openPage(currentPageNumber)
    }

    /**
     * Closes the [PdfRenderer] and related resources.
     *
     * @throws IOException When the PDF file cannot be closed.
     */
    @Throws(IOException::class)
    private fun closeRenderer() {
        currentPage.close()
        pdfRenderer.close()
    }

    /**
     * Shows the specified page of PDF to the screen.
     *
     * The way [PdfRenderer] works is that it allows for "opening" a page with the method
     * [PdfRenderer.openPage], which takes a (0 based) page number to open. This returns
     * a [PdfRenderer.Page] object, which represents the content of this page.
     *
     * There are two ways to render the content of a [PdfRenderer.Page].
     * [PdfRenderer.Page.RENDER_MODE_FOR_PRINT] and [PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY].
     * Since we're displaying the data on the screen of the device, we'll use the later.
     *
     * @param index The page index.
     */
    private fun showPage(index: Int) {
        if (index < 0 || index >= pdfRenderer.pageCount) return

        currentPage.close()
        currentPage = pdfRenderer.openPage(index)

        // Important: the destination bitmap must be ARGB (not RGB).
        val bitmap = createBitmap(currentPage.width, currentPage.height, Bitmap.Config.ARGB_8888)

        // Handling if PDF contains blank pages which turns into transparent images and we have white background.
        val canvas = Canvas(bitmap)
        canvas.drawColor(Color.WHITE)
        canvas.drawBitmap(bitmap, 0f, 0f, null)

        currentPage.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
        pdfPageView.setImageBitmap(bitmap)

        val pageCount = pdfRenderer.pageCount
        if (0 == index) {
            previousButton.disable()
            previousButton.setImageResource(R.drawable.ic_chevron_left_disabled)
        } else {
            previousButton.enable()
            previousButton.setImageResource(R.drawable.ic_chevron_left_enabled)
        }
        if (index + 1 < pageCount) {
            nextButton.enable()
            nextButton.setImageResource(R.drawable.ic_chevron_right_enabled)
        } else {
            nextButton.disable()
            nextButton.setImageResource(R.drawable.ic_chevron_right_grey)
        }
        pageCountTV.text = getString(R.string.pdfPageNumber, (index + 1).toString(), pageCount.toString())
    }

    override fun onResume() {
        super.onResume()
        shareIV.enable()
    }
}

private const val TAG = "PDFRenderFragment"
private const val INITIAL_PAGE_INDEX = 0

/**
 * Key string for saving the state of current page index.
 */
private const val CURRENT_PAGE_INDEX_KEY = "CURRENT_PAGE_INDEX_KEY"
