package com.resoluttech.core.profile.manageaccount

import com.resoluttech.bcn.types.Currency

sealed class ActiveAccountsItem {
    data class AccountHeader(
        val currency: Currency,
    ) : ActiveAccountsItem()

    data class Account(
        val accountName: String,
        val balance: String,
        val accountId: String,
        val currency: Currency,
        val isPrimary: Boolean,
        val isActive: Boolean,
        val isSelected: Boolean = false,
    ) : ActiveAccountsItem()
}
