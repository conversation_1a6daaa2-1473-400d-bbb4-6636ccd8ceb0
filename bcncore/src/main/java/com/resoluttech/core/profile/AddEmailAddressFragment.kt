package com.resoluttech.core.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.User
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.isEmailValid
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.utils.trim
import com.resoluttech.core.views.BaseFragment

class AddEmailAddressFragment :
    BaseFragment(),
    AlertDialog.ActionListener {
    private lateinit var emailAddressET: EditText
    private lateinit var descriptionTV: TextView
    private lateinit var sendVerificationLink: MaterialButton
    private lateinit var emailAddress: String
    private val addEmailAddressVM: AddEmailAddressVM by navGraphViewModels(R.id.home_nav)
    private var user = UserSharedPreference.getUser()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_add_email, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        initViews()
        setupSendVerification()
        setupTextWatcher()
        addEmailAddressVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        UserSharedPreference.getUser()!!.apply {
            if (!emailId.isNullOrEmpty()) {
                setDefaultToolbar(getString(R.string.emailVerificationUpdateEmailTitle))
                descriptionTV.text = getString(R.string.emailVerificationUpdateEmailMessage)
            } else {
                setDefaultToolbar(getString(R.string.emailVerificationAddEmailTitle))
                descriptionTV.text = getString(R.string.emailVerificationAddEmailMessage)
            }
        }
    }

    private fun setupTextWatcher() {
        emailAddressET.addTextChangedListener {
            validInput()
        }
    }

    private fun validInput() {
        if (emailAddressET.text.toString().isEmailValid()) {
            sendVerificationLink.enable()
        } else {
            sendVerificationLink.disable()
        }
    }

    private fun initViews() {
        view?.apply {
            emailAddressET = findViewById(R.id.email_et)
            sendVerificationLink = findViewById(R.id.send_verification_button)
            descriptionTV = findViewById(R.id.description)
            sendVerificationLink.disable()
        }
    }

    private fun setupSendVerification() {
        sendVerificationLink.setOnClickListener {
            emailAddress = emailAddressET.trim()
            addEmailAddressVM.onSendEmailVerificationClicked(
                requireContext(),
                emailAddress,
            )
        }
    }

    private fun reactToState(state: AddEmailAddressState) {
        when (state) {
            is AddEmailAddressState.AcceptInput -> {
                handleAcceptInputState()
            }
            is AddEmailAddressState.Error -> {
                handleErrorState(state.error)
            }
            is AddEmailAddressState.Loading -> {
                handleLoadingState()
            }
            is AddEmailAddressState.EmailVerified -> {
                handleEmailVerifiedState()
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleErrorState(error: UIError) {
        dismissProgressDialog()
        when (error.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    error.errorMessage,
                    requireView(),
                    addEmailAddressVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    error.errorCode ?: EMAIL_VERIFICATION_SENT_ERROR_DIALOG_ID,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleEmailVerifiedState() {
        dismissProgressDialog()
        val alertDialog = AlertDialog.newInstance(
            requireContext().getString(R.string.alertTitleEmailSent),
            requireContext().getString(
                R.string.alertMessageEmailSent,
                addEmailAddressVM.emailAddress.trim(),
            ),
            EMAIL_VERIFICATION_SENT_DIALOG_ID,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
        user?.apply {
            val updatedUser = User(
                userId = userId,
                name = name,
                emailId = addEmailAddressVM.emailAddress,
                isEmailVerified = false,
                imageUrl = imageUrl,
                phoneNumber = phoneNumber,
                primaryCurrency = primaryCurrency,
                country = country,
                countryCurrencies = countryCurrencies,
                supportedCountries = supportedCountries,
                district = district,
            )
            UserSharedPreference.saveUser(updatedUser)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> addEmailAddressVM.onInlineErrorDismissed()
            EMAIL_VERIFICATION_SENT_ERROR_DIALOG_ID -> addEmailAddressVM.onInlineErrorDismissed()
            else -> {
                addEmailAddressVM.onSendVerificationSuccessful(findNavController())
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        // Default action is already handled
    }
}

private const val EMAIL_VERIFICATION_SENT_ERROR_DIALOG_ID = 223
private const val EMAIL_VERIFICATION_SENT_DIALOG_ID = 224
