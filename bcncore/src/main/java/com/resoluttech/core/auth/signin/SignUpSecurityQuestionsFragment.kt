package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment

class SignUpSecurityQuestionsFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    SignUpSecurityQuestionsAdapter.ItemClickListener {

    private lateinit var nextButton: MaterialButton
    private lateinit var securityQuestionAnswerRV: RecyclerView
    private lateinit var adapter: SignUpSecurityQuestionsAdapter

    private val signUpSecurityQuestionsVM: SignUpSecurityQuestionsVM by navGraphViewModels(R.id.sign_up_nav)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_signup_security_questions, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.setSecurityQuestionNavigationTitle), false)
        initViews(view)
        setupSecurityQuestionsStateListener()
        setupNextButtonAction()
    }

    override fun onResume() {
        super.onResume()
        setupQuestionListRV()
    }

    private fun setupQuestionListRV() {
        adapter = SignUpSecurityQuestionsAdapter(requireContext(), this)
        securityQuestionAnswerRV.adapter = adapter
        adapter.updateItems(
            signUpSecurityQuestionsVM.getSavedQuestionAnswer(),
        )
    }

    private fun setupNextButtonAction() {
        nextButton.setOnClickListener {
            signUpSecurityQuestionsVM.onNextButtonTapped(
                requireContext(),
                findNavController(),
            )
        }
    }

    private fun setupSecurityQuestionsStateListener() {
        signUpSecurityQuestionsVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun reactToState(state: SignUpSecurityQuestionsScreenState) {
        when (state) {
            is SignUpSecurityQuestionsScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is SignUpSecurityQuestionsScreenState.Error -> {
                handleErrorState(state.uiError)
            }
        }
    }

    private fun handleAcceptInputState() {
        // Nothing to handle
    }

    private fun handleErrorState(uiError: UIError) {
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    signUpSecurityQuestionsVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    DialogCodes.SECURITY_QUESTIONS_DIALOG_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun initViews(view: View) {
        view.apply {
            nextButton = findViewById(R.id.next_button)
            securityQuestionAnswerRV = findViewById(R.id.sc_rv)
        }
    }

    private fun setupNextButton() {
        var isAllQuestionsAnswered = true
        signUpSecurityQuestionsVM.getSavedQuestionAnswer().forEach { selectedQuestionWithAnswer ->
            if (selectedQuestionWithAnswer.answer == null || (
                    selectedQuestionWithAnswer.answer
                        ?: throw IllegalStateException("Answer cannot be null")
                    ).isBlank() || selectedQuestionWithAnswer.selectedQuestion == null
            ) {
                isAllQuestionsAnswered = false
            }
        }
        nextButton.enable(isAllQuestionsAnswered)
    }

    override fun onPositiveAction(dialogId: Int) {
        signUpSecurityQuestionsVM.onInlineErrorDismissed()
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onQuestionSelected(position: Int) {
        signUpSecurityQuestionsVM.onQuestionSelected(position, findNavController())
    }

    override fun onAnswerChanged(position: Int, answer: String) {
        signUpSecurityQuestionsVM.saveAnswer(position, answer)
    }
}
