package com.resoluttech.core.auth.app

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.poovam.pinedittextfield.CirclePinField
import com.poovam.pinedittextfield.PinField
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.config.Config
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.utils.HapticConstant
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.getPreviousPath
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.performHapticFeedback
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showKeyboard
import timber.log.Timber

class AppPinAuthFragment :
    Fragment(),
    Biometric.AppAuthenticationListener,
    AlertDialog.ActionListener {

    private val appPinAuthVM by lazy { ViewModelProvider(this)[AppPinAuthVM::class.java] }
    private val previousPath: String? by lazy { getPreviousPath(arguments) }
    private lateinit var dataView: ScrollView
    private lateinit var enterPinET: CirclePinField
    private lateinit var wrongBiometricTV: TextView
    private lateinit var errorTV: TextView
    private lateinit var forgotSessionPinTV: TextView
    private lateinit var appPinAuthView: ConstraintLayout

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_app_pin, container, false)
        initViews(rootView)
        setupBackPressed(appPinAuthVM::onArrowBackClicked)
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        hideToolbar()
        setStatusBarColor()
        setupListeners()
        Biometric.setAuthenticationListener(this)
        appPinAuthVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onStop() {
        super.onStop()
        hideKeyboard()
    }

    private fun reactToState(state: AppPinScreenState) {
        when (state) {
            is AppPinScreenState.AcceptInput -> {
                // Default action is handled already
            }

            is AppPinScreenState.BiometricAuthentication -> {
                handleBiometricAuthentication()
            }

            is AppPinScreenState.AppPinAuthentication -> {
                handleAppPinAuthentication()
            }

            is AppPinScreenState.AppPinConfirmed -> {
                handleAppPinConfirmedState()
            }

            is AppPinScreenState.IncorrectPin -> {
                handleErrorState(state)
            }

            is AppPinScreenState.AppPinRetriesExhausted -> {
                handleRetriesExhaustedState()
            }

            is AppPinScreenState.BiometricRetriesExhausted -> {
                handleBiometricRetiresExhausted()
            }

            is AppPinScreenState.AppPinNotSetup -> {
                handleAppPinNotSetupState()
            }

            is AppPinScreenState.AppPinRetriesExhaustedDialog -> {
                handleAppPinRetriesExhaustedDialog(state)
            }

            is AppPinScreenState.AppPinCancelled -> {
                handleAppPinCancelledState()
            }
            AppPinScreenState.BiometricFailed -> {
                handleBiometricFailedState()
            }
            AppPinScreenState.AppPinWarningAttemptLeft -> {
                handleAppPinWarningAttemptLeft()
            }
        }
    }

    private fun handleAppPinWarningAttemptLeft() {
        appPinAuthView.visibility = View.VISIBLE
        errorTV.visibility = View.VISIBLE
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_REJECT, requireContext(), TAG)
        enterPinET.text?.clear()
        errorTV.text = requireContext().resources.getQuantityString(
            R.plurals.wrongSessionPinEnteredLabelPlural,
            Config.SESSION_PIN_WARNING_ATTEMPT_LEFT,
            Config.SESSION_PIN_WARNING_ATTEMPT_LEFT,
        )
        appPinAuthVM.onAppPinWarningAttemptLeft()
    }

    private fun handleBiometricFailedState() {
        wrongBiometricTV.visibility = View.VISIBLE
        wrongBiometricTV.text = getString(R.string.alertMessageDeviceNotConfiguredForBiometric)
        wrongBiometricTV.setTextColor(requireContext().getColor(R.color.negativeInfoTextColor))
    }

    private fun handleAppPinRetriesExhaustedDialog(state: AppPinScreenState.AppPinRetriesExhaustedDialog) {
        appPinAuthView.visibility = View.VISIBLE
        showErrorDialog(
            state.uiError.errorTitle,
            state.uiError.errorMessage,
            ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED,
        )
    }

    private fun handleAppPinNotSetupState() {
        findNavController().navigate(R.id.action_appPinAuthFragment_to_appCodeHintScreen)
    }

    private fun handleBiometricRetiresExhausted() {
        appPinAuthView.visibility = View.VISIBLE
        errorTV.visibility = View.VISIBLE
        errorTV.text = getString(R.string.alertMessageBiometricAttemptsExhaust)
        appPinAuthVM.onBiometricRetriesExhausted()
    }

    private fun handleBiometricAuthentication() {
        Biometric.authenticate(this, previousPath)
        appPinAuthView.visibility = View.VISIBLE
    }

    private fun handleAppPinAuthentication() {
        Biometric.cancelAuthentication()
        appPinAuthView.visibility = View.VISIBLE
        showKeyboard(enterPinET)
    }

    private fun handleRetriesExhaustedState() {
        appPinAuthView.visibility = View.VISIBLE
        enterPinET.disable()
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_REJECT, requireContext(), TAG)
        appPinAuthVM.onAppPinRetriesExhausted()
    }

    private fun handleAppPinConfirmedState() {
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_CONFIRM, requireContext(), TAG)
        appPinAuthVM.onAppPinConfirmed(findNavController(), statusListener, previousPath)
    }

    private fun handleErrorState(state: AppPinScreenState.IncorrectPin) {
        appPinAuthView.visibility = View.VISIBLE
        errorTV.visibility = View.VISIBLE
        enterPinET.performHapticFeedback(HapticConstant.HAPTIC_CODE_REJECT, requireContext(), TAG)
        enterPinET.text?.clear()
        errorTV.text = requireContext().resources.getQuantityString(
            R.plurals.wrongSessionPinEnteredLabelPlural,
            state.triesLeft,
            state.triesLeft,
        )
    }

    private fun handleAppPinCancelledState() {
        appPinAuthView.visibility = View.VISIBLE
        appPinAuthVM.onAppPinCancelled(
            findNavController(),
            statusListener,
            previousPath,
            requireActivity(),
        )
    }

    private fun setupListeners() {
        enterPinET.onTextCompleteListener = object : PinField.OnTextCompleteListener {
            override fun onTextComplete(enteredText: String): Boolean {
                appPinAuthVM.onAppPinEntered(enteredText)
                return true
            }
        }

        enterPinET.addTextChangedListener {
            if (it != null) {
                if (it.isNotEmpty()) {
                    Biometric.cancelAuthentication()
                }
            }
        }

        enterPinET.setOnClickListener {
            Biometric.cancelAuthentication()
        }

        forgotSessionPinTV.setOnClickListener {
            showAlertDialog(
                getString(R.string.alertTitleSessionPinSignedOut),
                getString(R.string.alertMessageSessionPinSignedOut),
                FORGOT_SESSION_PIN_DIALOG_CODE,
                positiveActionLabel = getString(R.string.alertActionSignOut),
                negativeActionLabel = getString(R.string.alertActionCancel),
            )
        }
    }

    private fun initViews(rootView: View) {
        rootView.apply {
            dataView = findViewById(R.id.data_layout)
            wrongBiometricTV = findViewById(R.id.error_biometric)
            forgotSessionPinTV = findViewById(R.id.forgot_session_pin)
            enterPinET = findViewById(R.id.password_et)
            enterPinET.requestFocus()
            appPinAuthView = findViewById(R.id.app_pin_container)
            errorTV = findViewById(R.id.error_tv)
        }
    }

    private fun showAlertDialog(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    override fun onBiometricAuthenticationSuccessful() {
        appPinAuthVM.onBiometricConfirmed(findNavController(), statusListener, previousPath)
    }

    override fun onBiometricAuthenticationFailed() {
        appPinAuthVM.onAuthenticationFailed()
    }

    override fun onBiometricAuthenticationError() {
        appPinAuthVM.onAuthenticationError()
    }

    companion object {
        private var statusListener: AppAuthenticationStatus? = null
        fun setAuthenticationStatusListener(statusListener: AppAuthenticationStatus) {
            this.statusListener = statusListener
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("onPositiveAction called for dialogId: $dialogId")
        when (dialogId) {
            ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED -> {
                appPinAuthVM.onDialogPositiveAction(activity)
            }

            FORGOT_SESSION_PIN_DIALOG_CODE -> {
                appPinAuthVM.onForgotSessionPin(requireActivity())
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        Timber.tag(TAG).i("onNegativeAction called for dialogId: $dialogId")
        appPinAuthVM.onErrorDismissed()
    }
}

private const val TAG = "AppPinAuthFragment"
private const val ERROR_CODE_APP_PIN_RETRIES_EXHAUSTED = 100
private const val FORGOT_SESSION_PIN_DIALOG_CODE = 200
