package com.resoluttech.core.auth.signup

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.views.UnAuthenticatedBaseFragment

internal class SignUpRequirementsFragment : UnAuthenticatedBaseFragment() {

    private lateinit var continueButton: MaterialButton
    private lateinit var signUpLater: MaterialButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_sign_up_requirements, container, false)
        initViews(view)
        setupOnClickListener()
        setupBackPressed(requireActivity()::finish)
        return view
    }

    private fun initViews(view: View) {
        continueButton = view.findViewById(R.id.done_button)
        signUpLater = view.findViewById(R.id.sign_up_later_button)
    }

    private fun setupOnClickListener() {
        continueButton.setOnClickListener {
            findNavController().navigate(R.id.action_signUpRequirementsFragment_to_signupFragment)
        }
        signUpLater.setOnClickListener {
            findNavController().navigateUp()
        }
    }

    override fun onResume() {
        super.onResume()
        hideToolbar()
    }
}
