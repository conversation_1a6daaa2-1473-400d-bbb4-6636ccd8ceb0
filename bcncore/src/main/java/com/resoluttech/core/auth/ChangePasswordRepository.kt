package com.resoluttech.core.auth

import com.resoluttech.bcn.profile.ChangePasswordRPC
import com.resoluttech.bcn.profile.RequestChangePasswordOTPRPC
import com.resoluttech.bcn.profile.ResendChangePasswordOTPRPC
import com.resoluttech.bcn.types.DeviceInformation
import com.resoluttech.bcn.types.Otp
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent

class ChangePasswordRepository {
    private val requestChangePasswordOTPRPC: RequestChangePasswordOTPRPC by KoinJavaComponent.inject(
        RequestChangePasswordOTPRPC::class.java,
    )

    private val resendChangePasswordOTPRPC: ResendChangePasswordOTPRPC by KoinJavaComponent.inject(
        ResendChangePasswordOTPRPC::class.java,
    )

    private val changePasswordRPC: ChangePasswordRPC by KoinJavaComponent.inject(ChangePasswordRPC::class.java)

    suspend fun getChangePasswordRequestOTP(oldPassword: String, newPassword: String): LeoRPCResult<RequestChangePasswordOTPRPC.Response, RequestChangePasswordOTPRPC.Error> {
        return requestChangePasswordOTPRPC.execute(RequestChangePasswordOTPRPC.Request(oldPassword, newPassword))
    }

    suspend fun resendChangePasswordOTP(): LeoRPCResult<ResendChangePasswordOTPRPC.Response, ResendChangePasswordOTPRPC.Error> {
        return resendChangePasswordOTPRPC.execute(ResendChangePasswordOTPRPC.Request)
    }

    suspend fun changePassword(
        otp: String,
        deviceInformation: DeviceInformation,
    ): LeoRPCResult<ChangePasswordRPC.Response, ChangePasswordRPC.Error> {
        return changePasswordRPC.execute(
            ChangePasswordRPC.Request(
                Otp(otp),
                deviceInformation,
            ),
        )
    }
}
