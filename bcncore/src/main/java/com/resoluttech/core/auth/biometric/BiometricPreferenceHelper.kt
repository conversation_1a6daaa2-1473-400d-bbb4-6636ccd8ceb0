package com.resoluttech.core.auth.biometric

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import org.koin.java.KoinJavaComponent
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

object BiometricPreferenceHelper {

    private const val HARDCODED_BIOMETRIC_PREF = true
    private val lock = ReentrantLock()
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val biometricPref = context.getSharedPreferences(BIOMETRIC_PREFERENCES, Context.MODE_PRIVATE)

    @Suppress("RedundantSuspendModifier")
    suspend fun getBiometricOption(): Boolean {
        lock.withLock {
            return biometricPref.getBoolean(KEY_BIOMETRIC_OPTION, HARDCODED_BIOMETRIC_PREF)
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setBiometricOption(enable: Boolean) {
        lock.withLock {
            with(biometricPref.edit()) {
                putBoolean(KEY_BIOMETRIC_OPTION, enable)
                commit()
            }
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun clearBiometricPreferences() {
        lock.withLock {
            biometricPref.edit(action = SharedPreferences.Editor::clear)
        }
    }
}

private const val BIOMETRIC_PREFERENCES = "biometric_preferences"
private const val KEY_BIOMETRIC_OPTION = "biometric_option"
