package com.resoluttech.core.auth

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.profile.ChangePasswordRPC
import com.resoluttech.bcn.profile.RequestChangePasswordOTPRPC
import com.resoluttech.bcn.profile.ResendChangePasswordOTPRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getDeviceInformation
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class ChangePasswordVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<ChangePasswordScreenState> = MutableLiveData()
    val state: LiveData<ChangePasswordScreenState> = _currentState
    private val changePasswordRepository = ChangePasswordRepository()

    fun onConfirmedTapped(
        context: Context,
        currentPassword: String,
        newPassword: String,
        confirmNewPassword: String,
    ) {
        if (!validateEnteredInput(context, currentPassword, newPassword, confirmNewPassword)) return
        _currentState.postValue(ChangePasswordScreenState.InlineLoading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response = changePasswordRepository.getChangePasswordRequestOTP(
                            currentPassword,
                            newPassword,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSuccessfulResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleErrorState(context, response.error)
                        }
                    }
                },
                handleException = {
                    _currentState.postValue(ChangePasswordScreenState.Error(it))
                },
            )
        }
    }

    private fun validateEnteredInput(
        context: Context,
        currentPassword: String,
        newPassword: String,
        confirmNewPassword: String,
    ): Boolean {
        if (currentPassword.isEmpty()) {
            _currentState.postValue(
                ChangePasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(
                            R.string.alertTitleMissingPasswordField,
                        ),
                        context.getString(
                            R.string.alertMessageMissingCurrentPasswordField,
                        ),
                    ),
                ),
            )
            return false
        }

        if (newPassword.isEmpty()) {
            _currentState.postValue(
                ChangePasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(
                            R.string.alertTitleMissingPasswordField,
                        ),
                        context.getString(
                            R.string.alertMessageMissingNewPasswordField,
                        ),
                    ),
                ),
            )
            return false
        }

        if (confirmNewPassword.isEmpty()) {
            _currentState.postValue(
                ChangePasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(
                            R.string.alertTitleMissingPasswordField,
                        ),
                        context.getString(
                            R.string.alertMessageMissingConfirmPasswordField,
                        ),
                    ),
                ),
            )
            return false
        }

        if (confirmNewPassword != newPassword) {
            _currentState.postValue(
                ChangePasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(
                            R.string.alertTitlePasswordsDoNotMatch,
                        ),
                        context.getString(
                            R.string.alertMessagePasswordsDoNotMatch,
                        ),
                    ),
                ),
            )
            return false
        }
        return true
    }

    private fun handleSuccessfulResponse(
        response: RequestChangePasswordOTPRPC.Response,
    ) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            otpId = null,
            nextResendAt = otpDetails.nextResendAt,
            expiresAt = otpDetails.expiresAt,
        )
        _currentState.postValue(
            ChangePasswordScreenState.OTPReading(
                otpResponse,
            ),
        )
    }

    private fun handleErrorState(context: Context, error: RequestChangePasswordOTPRPC.Error) {
        _currentState.postValue(
            ChangePasswordScreenState.Error(
                AuthExceptionHandler.getChangePasswordRPCErrorMessage(
                    context,
                    error,
                ),
            ),
        )
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(ChangePasswordScreenState.AcceptInput)
    }

    fun onOTPReadCancelled() {
        _currentState.postValue(ChangePasswordScreenState.OTPReadingCancelled)
    }

    fun onResendOTPRequest(context: Context) {
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = changePasswordRepository.resendChangePasswordOTP()) {
                        is LeoRPCResult.LeoResponse -> {
                            handleResendSuccessfulResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> handleResendRCPError(context, response.error)
                    }
                },
                handleException = {
                    when (it.errorCode) {
                        DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> _currentState.postValue(
                            ChangePasswordScreenState.Error(it),
                        )
                        else -> _currentState.postValue(ChangePasswordScreenState.ResendOTPFailed(it.errorMessage))
                    }
                },
            )
        }
    }

    private fun handleResendRCPError(context: Context, error: ResendChangePasswordOTPRPC.Error) {
        AuthExceptionHandler.getResendOTPChangePasswordRPCErrorMessage(context, error).apply {
            _currentState.postValue(ChangePasswordScreenState.ResendOTPFailed(errorMessage))
        }
    }

    private fun handleResendSuccessfulResponse(
        response: ResendChangePasswordOTPRPC.Response,
    ) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            otpId = null,
            nextResendAt = otpDetails.validityDetails.nextResendAt,
            expiresAt = otpDetails.validityDetails.expiresAt,
            otpLeft = otpDetails.numberOfResendsLeft,
        )
        _currentState.postValue(ChangePasswordScreenState.ResendOTPSuccess(otpResponse))
    }

    fun changePassword(
        context: Context,
        otp: String,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response = changePasswordRepository.changePassword(
                            otp,
                            getDeviceInformation(
                                pushToken = PushTokenHelper.getPushToken(context),
                                context = context,
                            ),
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSuccessfulChangePassword(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handlePasswordChangeError(context, response.error)
                        }
                    }
                },
                handleException = {
                    _currentState.postValue(ChangePasswordScreenState.Error(it))
                },
            )
        }
    }

    private fun handlePasswordChangeError(context: Context, error: ChangePasswordRPC.Error) {
        AuthExceptionHandler.getChangePasswordRPCErrorMessage(context, error).apply {
            _currentState.postValue(ChangePasswordScreenState.Error(this))
        }
    }

    private suspend fun handleSuccessfulChangePassword(response: ChangePasswordRPC.Response) {
        KeyStoreHelper.storeSLT(response.slt)
        KeyStoreHelper.storeLLT(response.llt)
        _currentState.postValue(ChangePasswordScreenState.PasswordChangeSuccessful)
    }

    fun onPasswordChangeSuccessful(
        navController: NavController,
    ) {
        navController.navigateUp()
    }
}

sealed class ChangePasswordScreenState {
    data class Error(val error: UIError) : ChangePasswordScreenState()
    object AcceptInput : ChangePasswordScreenState()
    object InlineLoading : ChangePasswordScreenState()
    data class OTPReading(
        val response: OTPResponse,
    ) : ChangePasswordScreenState()

    object OTPReadingCancelled : ChangePasswordScreenState()
    data class ResendOTPSuccess(val response: OTPResponse) : ChangePasswordScreenState()
    data class ResendOTPFailed(val error: String) : ChangePasswordScreenState()
    object PasswordChangeSuccessful : ChangePasswordScreenState()
}
