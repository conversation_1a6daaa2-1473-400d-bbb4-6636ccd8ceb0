package com.resoluttech.core.auth.signin

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPC
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcn.types.PasswordPolicy
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.suryadigital.leo.types.LeoPhoneNumber
import java.util.UUID
import kotlin.properties.Delegates

class SignUpSecurityQuestionsVM : ViewModel() {

    private val _currentState: MutableLiveData<SignUpSecurityQuestionsScreenState> =
        MutableLiveData(SignUpSecurityQuestionsScreenState.AcceptInput)
    val currentState: LiveData<SignUpSecurityQuestionsScreenState> = _currentState
    private lateinit var phoneNumber: LeoPhoneNumber
    private lateinit var trustedContactValidatedToken: UUID
    private lateinit var securityQuestions: MutableList<SecurityQuestion>
    private var requireNumberOfQuestions: Int by Delegates.notNull()
    lateinit var passwordPolicy: PasswordPolicy
    private lateinit var tempSecurityQuestionsList: SelectedQuestionsList
    private lateinit var savedQuestionAnswer: MutableList<SelectedQuestionWithAnswer>

    fun setSecurityQuestionsData(response: GetTrustedContactValidatedTokenRPC.Response) {
        _currentState.postValue(SignUpSecurityQuestionsScreenState.AcceptInput)
        requireNumberOfQuestions = response.numberOfRequiredAnswers
        trustedContactValidatedToken = response.trustedContactValidatedToken
        passwordPolicy = response.passwordPolicy
        securityQuestions = response.securityQuestions.toMutableList()
        savedQuestionAnswer =
            MutableList(requireNumberOfQuestions) { SelectedQuestionWithAnswer(null, null) }
    }

    fun updateValidationToken(phoneNumber: LeoPhoneNumber) {
        this.phoneNumber = phoneNumber
    }

    fun resetSavedQuestions() {
        savedQuestionAnswer.clear()
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(SignUpSecurityQuestionsScreenState.AcceptInput)
    }

    private fun getSecurityQuestionList(): MutableList<SecurityQuestion> {
        val tempQuestionList: MutableList<SecurityQuestion> = mutableListOf()
        tempQuestionList.addAll(securityQuestions)
        return tempQuestionList
    }

    fun onNextButtonTapped(
        context: Context,
        controller: NavController,
    ) {
        _currentState.postValue(SignUpSecurityQuestionsScreenState.AcceptInput)
        var isAllQuestionsAnswered = true
        getSavedQuestionAnswer().forEach { selectedQuestionWithAnswer ->
            if (selectedQuestionWithAnswer.answer == null || (
                    selectedQuestionWithAnswer.answer
                        ?: throw IllegalStateException("Answer cannot be null")
                    ).isBlank() || selectedQuestionWithAnswer.selectedQuestion == null
            ) {
                isAllQuestionsAnswered = false
            }
        }
        if (isAllQuestionsAnswered) {
            controller.navigate(R.id.action_securityQuestionsFragment_to_setPasswordFragment)
        } else {
            _currentState.postValue(
                SignUpSecurityQuestionsScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleMissingQuestions),
                        context.getString(R.string.alertMessageMissingQuestions),
                    ),
                ),
            )
        }
    }

    fun getValidatedToken(): String {
        return "$trustedContactValidatedToken"
    }

    fun getPhoneNumber(): LeoPhoneNumber {
        return phoneNumber
    }

    fun onQuestionSelected(position: Int, navController: NavController) {
        tempSecurityQuestionsList = SelectedQuestionsList(
            getSecurityQuestionList(),
            position,
            null,
        )
        savedQuestionAnswer.forEachIndexed { index, selectedQuestionWithAnswer ->
            if (position != index) {
                if (selectedQuestionWithAnswer.selectedQuestion != null && tempSecurityQuestionsList.securityQuestionList.contains(
                        selectedQuestionWithAnswer.selectedQuestion,
                    )
                ) {
                    tempSecurityQuestionsList.securityQuestionList.remove(
                        selectedQuestionWithAnswer.selectedQuestion,
                    )
                }
            } else {
                if (selectedQuestionWithAnswer.selectedQuestion != null && tempSecurityQuestionsList.securityQuestionList.contains(
                        selectedQuestionWithAnswer.selectedQuestion,
                    )
                ) {
                    tempSecurityQuestionsList.selectedQuestion =
                        selectedQuestionWithAnswer.selectedQuestion
                }
            }
        }
        navController.navigate(R.id.action_securityQuestionFragment_to_selectQuestionFragment)
    }

    fun getTempSecurityQuestionsList(): SelectedQuestionsList {
        return tempSecurityQuestionsList
    }

    fun getSavedQuestionAnswer(): MutableList<SelectedQuestionWithAnswer> {
        return savedQuestionAnswer
    }

    fun addSavedQuestion(position: Int, selectedQuestion: SecurityQuestion) {
        savedQuestionAnswer[position].selectedQuestion = selectedQuestion
    }

    fun saveAnswer(position: Int, answer: String) {
        savedQuestionAnswer[position].answer = answer
    }
}

sealed class SignUpSecurityQuestionsScreenState {
    object AcceptInput : SignUpSecurityQuestionsScreenState()
    data class Error(val uiError: UIError) : SignUpSecurityQuestionsScreenState()
}

data class SelectedQuestionWithAnswer(
    var selectedQuestion: SecurityQuestion?,
    var answer: String?,
)
