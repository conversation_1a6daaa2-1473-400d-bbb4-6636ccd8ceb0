package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import androidx.core.view.isEmpty
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signup.SetupPasswordVM
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.uicomponents.ResendOTPListener
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.DividerItemDecorator
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import timber.log.Timber
import java.util.UUID

class SignInTrustedContactFragment :
    UnAuthenticatedBaseFragment(),
    ResetPasswordTrustedContactAdapter.TrustedContactsListListener,
    OTPReaderDialog.OTPListener,
    AlertDialog.ActionListener,
    UnAuthenticatedBaseFragment.NetworkListener {

    private val signInTrustedContactVM: SignInTrustedContactVM by navGraphViewModels(R.id.sign_in_nav)
    private val args: SignInTrustedContactFragmentArgs by navArgs()

    private lateinit var progressBar: ProgressBar
    private lateinit var trustedContactRV: RecyclerView
    private lateinit var otpReaderDialog: OTPReaderDialog
    private var selectedTrustedContactId: UUID? = null
    private var selectedTrustedContactName: String? = null
    private var selectedTrustedPhoneNumber: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_sign_in_trusted_contact, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(title = getString(R.string.chooseTrustedContactsTitle), isBackArrowShown = true)
        initViews()
        networkListenerCallback = this
        signInTrustedContactVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        signInTrustedContactVM.getTrustedContacts(requireContext(), args.phoneNumberValidatedToken)
    }

    private fun reactToState(state: SignInTrustedContactScreenState) {
        when (state) {
            is SignInTrustedContactScreenState.AcceptInput -> {
                // Default action is already handled
            }
            is SignInTrustedContactScreenState.Data -> {
                handleDataState(state)
            }
            is SignInTrustedContactScreenState.Error -> {
                handleErrorState(state)
            }
            is SignInTrustedContactScreenState.FullScreenLoading -> {
                handleLoadingState()
            }
            is SignInTrustedContactScreenState.OTPConfirmed -> {
                handleOTPConfirmedState(state)
            }
            is SignInTrustedContactScreenState.OTPGenerated -> {
                handleOTPGeneratedState(state.response)
            }
            is SignInTrustedContactScreenState.ResendOTPSuccess -> {
                handleOTPResendSuccessState(state.response)
            }
            is SignInTrustedContactScreenState.ConfirmTrustedContact -> {
                handleConfirmTrustedContact(state)
            }
            SignInTrustedContactScreenState.LoadingDialog -> {
                handleLoadingDialogState()
            }
        }
    }

    private fun handleLoadingDialogState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleOTPConfirmedState(state: SignInTrustedContactScreenState.OTPConfirmed) {
        dismissProgressDialog()
        signInTrustedContactVM.trustedContactOTPConfirmed(
            findNavController(),
            args.phoneNumber,
            state.resetForgottenPasswordToken,
            requireContext(),
        )
    }

    private fun handleOTPResendSuccessState(response: OTPResponse) {
        dismissProgressDialog()
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onSuccessfulResendOTP(response)
        } else {
            handleOTPGeneratedState(response)
        }
    }

    private fun handleOTPGeneratedState(response: OTPResponse) {
        dismissProgressDialog()
        showCachedTrustedContact()
        otpReaderDialog = OTPReaderDialog.newInstance(response)
        otpReaderDialog.setArguments(false)
        otpReaderDialog.show(childFragmentManager, OTPReaderDialog.TAG)
    }

    private fun handleErrorState(error: SignInTrustedContactScreenState.Error) {
        dismissProgressDialog()
        onOTPReadCancelled()
        showCachedTrustedContact()
        val uiError = error.error
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    signInTrustedContactVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.TRUSTED_CONTACTS_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleLoadingState() {
        showViewForState(progressBar)
    }

    private fun handleDataState(data: SignInTrustedContactScreenState.Data) {
        dismissProgressDialog()
        showViewForState(trustedContactRV)
        trustedContactRV.adapter = ResetPasswordTrustedContactAdapter(this, data.listOfTrustedContacts)
    }

    private fun initViews() {
        view?.apply {
            progressBar = findViewById(R.id.progressBar)
            trustedContactRV = findViewById(R.id.trusted_contacts_rv)
        }
    }

    private fun showCachedTrustedContact() {
        if (trustedContactRV.isEmpty()) {
            val listOfTrustedContact: List<TrustedContact>? =
                signInTrustedContactVM.listOfTrustedContact
            if (listOfTrustedContact != null) {
                dismissProgressDialog()
                showViewForState(trustedContactRV)
                trustedContactRV.adapter = ResetPasswordTrustedContactAdapter(
                    this,
                    listOfTrustedContact,
                )
                val dividerItemDecoration = DividerItemDecorator(
                    ContextCompat.getDrawable(requireContext(), R.drawable.divider),
                    DividerItemDecorator.LIST_MARGIN_72,
                    listOf(TYPE_ITEM),
                )
                trustedContactRV.addItemDecoration(dividerItemDecoration)
            }
        }
    }

    private fun showViewForState(view: View) {
        when (view) {
            trustedContactRV -> {
                progressBar.visibility = View.INVISIBLE
                trustedContactRV.visibility = View.VISIBLE
            }
            progressBar -> {
                trustedContactRV.visibility = View.INVISIBLE
                progressBar.visibility = View.VISIBLE
            }
        }
    }

    private fun handleConfirmTrustedContact(state: SignInTrustedContactScreenState.ConfirmTrustedContact) {
        selectedTrustedContactId = state.trustedContactId
        selectedTrustedContactName = state.name
        selectedTrustedPhoneNumber = state.phoneNumber
        showCachedTrustedContact()
        showErrorDialog(
            requireContext().getString(R.string.alertTitleChooseTrustedContact),
            requireContext().getString(
                R.string.alertMessageChooseTrustedContactScreen,
                state.name,
                state.phoneNumber,
            ),
            DialogCodes.REQUEST_OTP_FOR_RESET_PASSWORD_BY_TRUSTED_CONTACTS_CODE,
            getString(R.string.alertActionYes),
            getString(R.string.alertActionNo),
        )
    }

    override fun onTrustedContactTapped(trustedContactId: UUID, name: String, phoneNumber: String) {
        signInTrustedContactVM.confirmTrustedContact(trustedContactId, name, phoneNumber)
    }

    override fun onResendTapped(otpId: UUID?) {
        signInTrustedContactVM.onResendOTPTapped(
            requireContext(),
            args.phoneNumberValidatedToken,
            otpId,
        )
    }

    override fun onOTPReadCancelled() {
        if (::otpReaderDialog.isInitialized) {
            signInTrustedContactVM.onOTPReadCancelled(otpReaderDialog)
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onOTPReadFailed(e: OTPFailureException) {
        // OTP failure happens due to timeout so we are just logging it.
        Timber.tag(TAG).e(message = e.stackTraceToString())
    }

    override fun onSuccessfulOTPRead(otp: String, otpId: UUID?) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
            signInTrustedContactVM.onConfirmOTPTapped(
                requireContext(),
                args.phoneNumberValidatedToken,
                otpId,
                otp,
            )
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.REQUEST_OTP_FOR_RESET_PASSWORD_BY_TRUSTED_CONTACTS_CODE -> {
                dismissProgressDialog()
                showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
                signInTrustedContactVM.onTrustedContactTapped(
                    requireContext(),
                    args.phoneNumberValidatedToken,
                    selectedTrustedContactId
                        ?: throw IllegalStateException("trustedContactId cannot be null for requesting OTP"),
                    selectedTrustedContactName
                        ?: throw IllegalStateException("selectedTrustedContactName cannot be null for requesting OTP"),
                    selectedTrustedPhoneNumber
                        ?: throw IllegalStateException("selectedTrustedPhoneNumber cannot be null for requesting OTP"),
                )
                selectedTrustedContactId = null
                selectedTrustedContactName = null
                selectedTrustedPhoneNumber = null
            }
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                signInTrustedContactVM.handleLeoServerException(findNavController())
            }
            SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN or DialogCodes.SIGN_UP_IN_SESSION_EXPIRED -> {
                restartActivity()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        selectedTrustedContactId = null
        selectedTrustedContactName = null
        selectedTrustedPhoneNumber = null
        signInTrustedContactVM.onInlineErrorDismissed()
    }

    override fun onNetworkAvailable() {
        signInTrustedContactVM.getTrustedContacts(requireContext(), args.phoneNumberValidatedToken)
    }
}

private const val TAG = "SignInTrustedContactFragment"
