package com.resoluttech.core.auth.app

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import org.koin.java.KoinJavaComponent
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

object AppPinHelper {

    private val lock = ReentrantLock()
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC)
    private val sharedPreferences = EncryptedSharedPreferences.create(
        FILE_NAME,
        masterKeyAlias,
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
    )

    @Suppress("RedundantSuspendModifier")
    suspend fun storeAppPin(appPin: String) {
        lock.withLock {
            if (appPin.isNotEmpty()) {
                with(sharedPreferences.edit()) {
                    putString(KEY_APP_PIN, appPin)
                    if (!commit()) {
                        throw IllegalStateException("Unable to write app pin to disk")
                    }
                }
            } else {
                throw IllegalArgumentException("App Pin shouldn't be empty")
            }
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getAppPin(): String? {
        lock.withLock {
            return sharedPreferences.getString(KEY_APP_PIN, null)
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun clearAppPin() {
        lock.withLock {
            sharedPreferences.edit(action = SharedPreferences.Editor::clear)
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setAppPinAttemptLeft(appPinAttemptNumber: Int) {
        with(sharedPreferences.edit()) {
            putInt(KEY_APP_PIN_ATTEMPT_LEFT, appPinAttemptNumber)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getAppPinAttemptLeft(): Int {
        return sharedPreferences.getInt(KEY_APP_PIN_ATTEMPT_LEFT, APP_PIN_ATTEMPT_ALLOWED)
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun resetAppPinAttemptLeft() {
        with(sharedPreferences.edit()) {
            putInt(KEY_APP_PIN_ATTEMPT_LEFT, APP_PIN_ATTEMPT_ALLOWED)
            commit()
        }
    }
}

private const val KEY_APP_PIN_ATTEMPT_LEFT = "app_pin_attempt_left"
private const val FILE_NAME = "app_pin_shared_prefs"
private const val KEY_APP_PIN = "com.resoluttech.apppin"
private const val APP_PIN_ATTEMPT_ALLOWED = 3
