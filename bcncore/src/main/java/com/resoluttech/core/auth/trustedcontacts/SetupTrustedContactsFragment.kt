package com.resoluttech.core.auth.trustedcontacts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signin.SignUpSecurityQuestionsVM
import com.resoluttech.core.auth.signup.SetupPasswordVM
import com.resoluttech.core.profile.trustedcontacts.AddTrustedContactCCPVM
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.TrustedContactAdapter
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.suryadigital.leo.types.LeoPhoneNumber
import java.util.UUID

class SetupTrustedContactsFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    TrustedContactAdapter.TrustedContactsListListener,
    UnAuthenticatedBaseFragment.NetworkListener {

    private val trustedContactInfoVM: TrustedContactInfoVM by navGraphViewModels(R.id.sign_up_nav)
    private val signUpSecurityQuestionsVM: SignUpSecurityQuestionsVM by navGraphViewModels(R.id.sign_up_nav)
    private val args: SetupTrustedContactsFragmentArgs by navArgs()
    private val addTrustedContactCCPVM: AddTrustedContactCCPVM by activityViewModels()

    private lateinit var addContactButton: MaterialButton
    private lateinit var finishButton: MaterialButton
    private lateinit var noDataFoundTV: TextView
    private lateinit var dataRV: RecyclerView
    private lateinit var progressBar: ProgressBar
    private var trustedContactId: UUID? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_setup_trusted_contacts, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(title = getString(R.string.trustedContactsOnboardingTitle))
        setupBackPressed {
            trustedContactInfoVM.trustedContacts = null
            trustedContactInfoVM.minNumberOfTrustedContact = null
        }
        initViews()
        resetAddTrustedContactCCPVM()
        setupListeners()
        setupBackPressed { trustedContactInfoVM.onBackPressed(findNavController()) }
        networkListenerCallback = this
        trustedContactInfoVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        trustedContactInfoVM.getTrustedContacts(requireContext(), args.phoneNumberValidatedToken)
    }

    private fun resetAddTrustedContactCCPVM() {
        addTrustedContactCCPVM.profileAddContactSelectedCountryCode = null
        addTrustedContactCCPVM.singUpAddTrustedContactSelectedCountryCode = null
    }

    private fun reactToState(state: TrustedContactScreenState) {
        when (state) {
            is TrustedContactScreenState.Data -> {
                handleDataState(state)
            }
            is TrustedContactScreenState.Error -> {
                handleErrorState(state)
            }
            is TrustedContactScreenState.Loading -> {
                handleLoadingState()
            }
            is TrustedContactScreenState.NoData -> {
                handleNoDataState()
            }
            is TrustedContactScreenState.AcceptInput -> {
                // Default action is already handled
            }
            is TrustedContactScreenState.TrustedContactToken -> {
                handleTrustedContactTokenState(state)
            }
        }
    }

    private fun handleTrustedContactTokenState(state: TrustedContactScreenState.TrustedContactToken) {
        trustedContactInfoVM.setupSecurityQuestionsAndPassword(
            findNavController(),
            signUpSecurityQuestionsVM,
            state.response,
            LeoPhoneNumber(args.phoneNumber),
        )
    }

    private fun handleDataState(data: TrustedContactScreenState.Data) {
        showTrustedContactList(
            data.response.trustedContacts,
            data.response.minNumberOfTrustedContact,
        )
    }

    private fun handleErrorState(error: TrustedContactScreenState.Error) {
        progressBar.visibility = View.GONE
        val uiError = error.error
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    trustedContactInfoVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showCachedTrustedContact()
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.SIGN_IN_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showCachedTrustedContact() {
        val trustedContacts = trustedContactInfoVM.trustedContacts
        val minNumberOfTrustedContact = trustedContactInfoVM.minNumberOfTrustedContact
        if (trustedContacts != null && minNumberOfTrustedContact != null) {
            if (trustedContacts.isEmpty()) {
                showViewForState(noDataFoundTV)
                finishButton.disable()
                addContactButton.enable()
                addContactButton.visibility = View.VISIBLE
            } else {
                showTrustedContactList(trustedContacts, minNumberOfTrustedContact)
            }
        }
    }

    private fun showTrustedContactList(
        trustedContacts: List<TrustedContact>,
        minNumberOfTrustedContact: Int,
    ) {
        showViewForState(dataRV)
        if (trustedContacts.size >= minNumberOfTrustedContact) {
            finishButton.enable()
            addContactButton.disable()
            addContactButton.visibility = View.GONE
        } else {
            finishButton.disable()
            addContactButton.enable()
            addContactButton.visibility = View.VISIBLE
        }
        dataRV.adapter =
            TrustedContactAdapter(this, trustedContacts)
    }

    private fun handleLoadingState() {
        showViewForState(progressBar)
        finishButton.disable()
        addContactButton.disable()
    }

    private fun handleNoDataState() {
        showViewForState(noDataFoundTV)
        finishButton.disable()
        addContactButton.enable()
        addContactButton.visibility = View.VISIBLE
    }

    private fun showViewForState(view: View) {
        when (view) {
            noDataFoundTV -> {
                dataRV.visibility = View.INVISIBLE
                progressBar.visibility = View.INVISIBLE
                noDataFoundTV.visibility = View.VISIBLE
            }
            dataRV -> {
                noDataFoundTV.visibility = View.INVISIBLE
                progressBar.visibility = View.INVISIBLE
                dataRV.visibility = View.VISIBLE
            }
            progressBar -> {
                dataRV.visibility = View.INVISIBLE
                noDataFoundTV.visibility = View.INVISIBLE
                progressBar.visibility = View.VISIBLE
            }
        }
    }

    private fun setupListeners() {
        addContactButton.setOnClickListener {
            val action =
                SetupTrustedContactsFragmentDirections.actionSetupTrustedContactsFragmentToAddTrustedContactFragment(
                    args.phoneNumberValidatedToken,
                )
            findNavController().navigate(action)
            addContactButton.disable()
        }
        finishButton.setOnClickListener {
            trustedContactInfoVM.onFinishTapped(requireContext(), args.phoneNumberValidatedToken)
        }
    }

    private fun initViews() {
        view?.apply {
            addContactButton = findViewById(R.id.add_contact_button)
            finishButton = findViewById(R.id.next_button)
            noDataFoundTV = findViewById(R.id.no_trusted_contact_found_tv)
            dataRV = findViewById(R.id.trusted_contacts_rv)
            progressBar = findViewById(R.id.progressBar)

            finishButton.disable()
            addContactButton.disable()
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        trustedContactInfoVM.onInlineErrorDismissed()
        when (dialogId) {
            DialogCodes.REMOVE_TRUSTED_CONTACTS_CODE -> {
                trustedContactInfoVM.onRemoveTrustedContactTapped(
                    requireContext(),
                    args.phoneNumberValidatedToken,
                    trustedContactId
                        ?: throw IllegalStateException("Trusted Contact ID which user wants remove cannot be null"),
                )
                trustedContactId = null
            }
            SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN -> {
                restartActivity()
            }
            else -> trustedContactInfoVM.onInlineErrorDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        trustedContactId = null
        trustedContactInfoVM.onInlineErrorDismissed()
    }

    override fun onTrustedContactRemoved(trustedContactId: UUID, name: String) {
        this.trustedContactId = trustedContactId
        showErrorDialog(
            title = getString(R.string.alertTitleRemoveTrustedContact),
            message = getString(R.string.alertMessageRemoveTrustedContact, name),
            dialogId = DialogCodes.REMOVE_TRUSTED_CONTACTS_CODE,
            positiveActionLabel = getString(R.string.alertActionRemove),
            negativeActionLabel = getString(R.string.alertActionCancel),
        )
    }

    override fun onNetworkAvailable() {
        trustedContactInfoVM.getTrustedContacts(requireContext(), args.phoneNumberValidatedToken)
    }
}
