package com.resoluttech.core.auth.signin

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPC
import com.resoluttech.bcn.signUpIn.ResendSignInOTPRPC
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getCurrentLocale
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class SignInVM : ViewModel() {

    private val _currentState: MutableLiveData<SignInScreenState> =
        MutableLiveData()
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val signInRepository = SignInRepository()

    val currentState: LiveData<SignInScreenState> = _currentState

    fun getSupportedCountries(context: Context, supportedCountries: MutableList<Country>) {
        if (!(_currentState.value is SignInScreenState.OTPGenerated || _currentState.value is SignInScreenState.ResendOTPSuccess || _currentState.value is SignInScreenState.ConfirmPhoneNumber || _currentState.value is SignInScreenState.Error)) {
            _currentState.postValue(SignInScreenState.FullScreenLoading)
            if (supportedCountries.isEmpty()) {
                vmIOScope.launch {
                    executeRPC(
                        context,
                        rpcBlock = {
                            when (val result = signInRepository.getSupportedCountries()) {
                                is LeoRPCResult.LeoResponse -> {
                                    result.response.countries.forEach(supportedCountries::add)
                                    _currentState.postValue(
                                        SignInScreenState.SupportedCountriesAvailable(
                                            supportedCountries,
                                        ),
                                    )
                                }
                                is LeoRPCResult.LeoError -> {
                                    // No Error Codes for this RPC
                                }
                            }
                        },
                        handleException = {
                            _currentState.postValue(SignInScreenState.FullScreenError(it))
                        },
                    )
                }
            } else {
                _currentState.postValue(
                    SignInScreenState.SupportedCountriesAvailable(
                        supportedCountries,
                    ),
                )
            }
        }
    }

    fun onPhoneNumberConfirmed(context: Context, phoneNumber: LeoPhoneNumber) {
        _currentState.postValue(SignInScreenState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response = signInRepository.requestSignInOTP(
                            phoneNumber,
                            context.getCurrentLocale(),
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> handleSuccessSignInOTPResponse(response.response)
                        is LeoRPCResult.LeoError -> handleSignInOTPError(context, response.error)
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    fun onAlertDialogDismissed() {
        _currentState.postValue(SignInScreenState.AcceptInput)
    }

    fun onSignInTapped(phoneNumber: LeoPhoneNumber) {
        _currentState.postValue(SignInScreenState.ConfirmPhoneNumber(phoneNumber))
    }

    private fun handleSuccessSignInOTPResponse(response: RequestSignInOtpRPC.Response) {
        val otpDetails = response.otpDetails
        _currentState.postValue(
            SignInScreenState.OTPGenerated(
                OTPResponse(
                    otpId = otpDetails.otpId,
                    nextResendAt = otpDetails.validityDetails.nextResendAt,
                    expiresAt = otpDetails.validityDetails.expiresAt,
                ),
            ),
        )
    }

    private fun handleSignInOTPError(context: Context, error: RequestSignInOtpRPC.Error) {
        AuthExceptionHandler.requestSignInOTPRPCExceptions(context, error).apply {
            showError()
        }
    }

    fun onInvalidPhoneNumberInput(
        title: String,
        error: String,
    ) {
        _currentState.postValue(
            SignInScreenState.Error(
                UIError(ErrorType.DIALOG, title, error),
            ),
        )
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(SignInScreenState.AcceptInput)
    }

    fun onSuccessfulOTPRead(
        context: Context,
        otp: String,
        otpId: UUID?,
        phoneNumber: LeoPhoneNumber,
    ) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        _currentState.postValue(SignInScreenState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response =
                            signInRepository.confirmSignInOTP(otpId, otp, phoneNumber)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleOTPConfirmSuccessResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleOTPConfirmRPCError(context, response.error)
                        }
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleOTPConfirmSuccessResponse(response: ConfirmSignInOTPRPC.Response) {
        _currentState.postValue(SignInScreenState.OTPConfirmed(response))
    }

    private fun handleOTPConfirmRPCError(context: Context, error: ConfirmSignInOTPRPC.Error) {
        AuthExceptionHandler.confirmSignInOTPRPCExceptions(context, error)?.apply {
            showError()
        }
    }

    private fun UIError.showError() {
        _currentState.postValue(SignInScreenState.Error(this))
    }

    fun onOTPResendTapped(context: Context, otpId: UUID?) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = signInRepository.resendSignInOTP(otpId)) {
                        is LeoRPCResult.LeoResponse -> handleResendOTPSuccessResponse(
                            otpId,
                            response.response,
                        )
                        is LeoRPCResult.LeoError -> handleResendOTPErrorResponse(
                            context,
                            response.error,
                        )
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleResendOTPSuccessResponse(otpId: UUID, response: ResendSignInOTPRPC.Response) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            otpId = otpId,
            nextResendAt = otpDetails.validityDetails.nextResendAt,
            expiresAt = otpDetails.validityDetails.expiresAt,
            otpLeft = otpDetails.numberOfResendsLeft,
        )
        _currentState.postValue(SignInScreenState.ResendOTPSuccess(otpResponse))
    }

    private fun handleResendOTPErrorResponse(context: Context, error: ResendSignInOTPRPC.Error) {
        AuthExceptionHandler.resendSignInOTPRPCExceptions(context, error).apply {
            showError()
        }
    }

    fun nextStepEnterPassword(
        controller: NavController,
        phoneNumber: LeoPhoneNumber,
        phoneNumberValidatedToken: UUID,
    ) {
        _currentState.postValue(SignInScreenState.AcceptInput)
        val action =
            SignInFragmentDirections.actionSignInFragmentToEnterPasswordFragment(
                "$phoneNumberValidatedToken",
                phoneNumber.value,
            )
        controller.navigate(action)
    }

    fun onRegisterTapped(controller: NavController) {
        controller.navigate(R.id.action_signInFragment_to_sign_up_nav)
    }

    fun onOTPReadCancelled(otpReaderDialog: OTPReaderDialog) {
        otpReaderDialog.dismiss()
        _currentState.postValue(SignInScreenState.AcceptInput)
    }
}

sealed class SignInScreenState {
    object AcceptInput : SignInScreenState()
    object Loading : SignInScreenState()
    object FullScreenLoading : SignInScreenState()
    data class Error(val error: UIError) : SignInScreenState()
    data class SupportedCountriesAvailable(val supportedCountries: List<Country>) :
        SignInScreenState()

    data class ConfirmPhoneNumber(val phoneNumber: LeoPhoneNumber) : SignInScreenState()
    data class OTPGenerated(val response: OTPResponse) : SignInScreenState()
    data class OTPConfirmed(val response: ConfirmSignInOTPRPC.Response) : SignInScreenState()
    data class ResendOTPSuccess(val response: OTPResponse) : SignInScreenState()
    data class FullScreenError(val error: UIError) : SignInScreenState()
}
