package com.resoluttech.core.auth

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.uicomponents.ResendOTPListener
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.selectionActionModeCallback
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_DESCRIPTION
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_TITLE
import com.resoluttech.core.views.PasswordPolicyAdapter
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import timber.log.Timber
import java.util.UUID
import com.hbb20.R as CountryCodeFieldResource

class ChangePasswordFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    OTPReaderDialog.OTPListener,
    BaseFragment.NetworkListener {

    private lateinit var confirmButton: MaterialButton
    private lateinit var currentPasswordTIL: TextInputLayout
    private lateinit var newPasswordTIL: TextInputLayout
    private lateinit var confirmNewPasswordTIL: TextInputLayout
    private lateinit var currentPasswordET: TextInputEditText
    private lateinit var newPasswordET: TextInputEditText
    private lateinit var confirmNewPasswordET: TextInputEditText
    private lateinit var otpReaderDialog: OTPReaderDialog
    private lateinit var errorSnackBar: Snackbar
    private val changePasswordVM: ChangePasswordVM by navGraphViewModels(R.id.change_password_nav)
    private lateinit var passwordPolicyRV: RecyclerView
    private lateinit var passwordPolicyHeadingTV: TextView
    private val passwordPolicyHeading: String? by lazy { getPrivacyPolicyHeadText(arguments) }
    private val passwordPolicyDescription: List<String> by lazy {
        arguments?.getStringArrayList(
            KEY_PRIVACY_POLICY_DESCRIPTION,
        ) ?: throw IllegalArgumentException("Password policy description should not be null")
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_change_password, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.changePasswordTitle))
        initViews(view)
        setupConfirmWithOTP()
        setupPasswordEditText()
        setupPasswordPolicy()
        networkListenerCallback = this
        changePasswordVM.state.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun getPrivacyPolicyHeadText(args: Bundle?): String? {
        return args?.getString(KEY_PRIVACY_POLICY_TITLE)
    }

    private fun setupPasswordEditText() {
        newPasswordET.selectionActionModeCallback()
        confirmNewPasswordET.selectionActionModeCallback()
        currentPasswordET.selectionActionModeCallback()
    }

    private fun reactToState(state: ChangePasswordScreenState) {
        when (state) {
            is ChangePasswordScreenState.Error -> {
                handleErrorState(state.error)
            }
            is ChangePasswordScreenState.AcceptInput -> {
                handleAcceptInput()
            }
            is ChangePasswordScreenState.InlineLoading -> {
                handleInlineLoading()
            }
            is ChangePasswordScreenState.OTPReading -> {
                handleOTPReading(state.response)
            }
            is ChangePasswordScreenState.OTPReadingCancelled -> {
                handleOTPReadingCancelled()
            }
            is ChangePasswordScreenState.ResendOTPSuccess -> {
                handleResendOTPSuccess(state)
            }
            is ChangePasswordScreenState.ResendOTPFailed -> {
                handleResendOTPFailure(state)
            }
            is ChangePasswordScreenState.PasswordChangeSuccessful -> {
                handleSuccessfulPasswordChange()
            }
        }
    }

    private fun handleOTPReadingCancelled() {
        dismissProgressDialog()
        if (::otpReaderDialog.isInitialized) {
            otpReaderDialog.dismiss()
        } else {
            Timber.tag(TAG).d("Dialog is already hidden")
        }
    }

    private fun handleSuccessfulPasswordChange() {
        dismissProgressDialog()
        showErrorDialog(
            getString(R.string.alertTitlePasswordChanged),
            getString(R.string.alertMessagePasswordChanged),
            DialogCodes.PASSWORD_CHANGED_CODE,
            requireContext().getString(CountryCodeFieldResource.string.dismiss_button_content_description),
        )
    }

    private fun handleResendOTPFailure(state: ChangePasswordScreenState.ResendOTPFailed) {
        if (otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onResendOTPFailed(state.error)
        }
    }

    private fun handleResendOTPSuccess(state: ChangePasswordScreenState.ResendOTPSuccess) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onSuccessfulResendOTP(state.response)
        } else {
            handleOTPReading(state.response)
        }
    }

    private fun handleOTPReading(response: OTPResponse) {
        otpReaderDialog = OTPReaderDialog.newInstance(response)
        otpReaderDialog.setArguments(false)
        otpReaderDialog.show(childFragmentManager, OTPReaderDialog.TAG)
        dismissProgressDialog()
    }

    private fun handleAcceptInput() {
        dismissProgressDialog()
        if (::otpReaderDialog.isInitialized) {
            otpReaderDialog.dismiss()
        } else {
            Timber.tag(TAG).d("Dialog is already hidden")
        }
    }

    private fun handleInlineLoading() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(error: UIError) {
        dismissProgressDialog()
        onOTPReadCancelled()
        when (error.type) {
            ErrorType.SNACKBAR -> {
                errorSnackBar =
                    Snackbar.make(requireView(), error.errorMessage, Snackbar.LENGTH_INDEFINITE)
                errorSnackBar.let {
                    it.setAction(CountryCodeFieldResource.string.dismiss_button_content_description) {
                        changePasswordVM.onInlineErrorDismissed()
                    }
                    it.show()
                }
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    title = error.errorTitle,
                    message = error.errorMessage,
                    dialogId = error.errorCode ?: DialogCodes.CHANGE_PASSWORD_ERROR_CODE,
                    forceOutUserDestination = ForceOutUserDestination.PROFILE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun setupConfirmWithOTP() {
        confirmButton.setOnClickListener {
            changePasswordVM.onConfirmedTapped(
                requireContext(),
                currentPasswordET.text.toString().trim(),
                newPasswordET.text.toString().trim(),
                confirmNewPasswordET.text.toString().trim(),
            )
        }
    }

    private fun initViews(view: View) {
        view.apply {
            confirmButton = findViewById(R.id.confirm_with_otp_button)

            currentPasswordET = findViewById(R.id.current_password_et)
            currentPasswordTIL = findViewById(R.id.current_password_til)
            currentPasswordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = currentPasswordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                ),
            )

            newPasswordET = findViewById(R.id.new_password_et)
            newPasswordTIL = findViewById(R.id.new_password_til)
            newPasswordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = newPasswordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                ),
            )

            confirmNewPasswordET = findViewById(R.id.confirm_password_et)
            confirmNewPasswordTIL = findViewById(R.id.confirm_password_til)
            confirmNewPasswordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = confirmNewPasswordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                ),
            )
            passwordPolicyRV = findViewById(R.id.password_policy_rv)
            passwordPolicyHeadingTV = findViewById(R.id.privacy_policy_heading_tv)
        }
    }

    private fun setupPasswordPolicy() {
        passwordPolicyHeadingTV.text = passwordPolicyHeading
        passwordPolicyRV.adapter = PasswordPolicyAdapter(passwordPolicyDescription)
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.PASSWORD_CHANGED_CODE -> {
                changePasswordVM.onPasswordChangeSuccessful(
                    findNavController(),
                )
            }
            else -> {
                changePasswordVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onResendTapped(otpId: UUID?) {
        changePasswordVM.onResendOTPRequest(requireContext())
    }

    override fun onOTPReadCancelled() {
        changePasswordVM.onOTPReadCancelled()
    }

    override fun onOTPReadFailed(e: OTPFailureException) {
        // OTP failure happens due to timeout so we are just logging it.
        Timber.tag(TAG).e(message = e.stackTraceToString())
    }

    override fun onSuccessfulOTPRead(otp: String, otpId: UUID?) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
            dismissProgressDialog()
            showProgressDialog(
                childFragmentManager,
                getString(R.string.alertLoading),
            )
            changePasswordVM.changePassword(
                requireContext(),
                otp,
            )
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        showToolbar()
    }

    override fun onNetworkAvailable() {
        changePasswordVM.onInlineErrorDismissed()
    }
}

private const val TAG: String = "ChangePasswordFragment"
