package com.resoluttech.core.auth.signin

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.addContentDescriptionString
import com.suryadigital.leo.libui.listview.ListAdapter
import java.util.Locale
import java.util.UUID

class ResetPasswordTrustedContactAdapter(
    private val listener: TrustedContactsListListener,
    private val listOfTrustedContacts: List<TrustedContact>,
) : ListAdapter<TrustedContact, RecyclerView.ViewHolder>(listOfTrustedContacts) {

    interface TrustedContactsListListener {
        fun onTrustedContactTapped(trustedContactId: UUID, name: String, phoneNumber: String)
    }

    override fun filter(query: String) {
        // Default action is already handled
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val item = listOfTrustedContacts[position]
        val viewHolder = (holder as TrustedContactViewHolder)
        viewHolder.apply {
            numbersTV.text = String.format(Locale.getDefault(), "%d", position.inc())

            val name = if (item.lastName != null) {
                viewHolder.itemView.context.getString(
                    R.string.profileUserDisplayName,
                    item.firstName,
                    item.lastName,
                )
            } else {
                item.firstName
            }
            val paddedName = padTrustedContactName(name)
            nameTV.text = paddedName

            val phoneNumber = item.phoneNumber.getFormattedPhoneNumber()
            val paddedPhoneNumber = phoneNumber
                .take(phoneNumber.length - 7)
                .padEnd(item.phoneNumber.value.length, '*')
                .plus(
                    item.phoneNumber.value.substring(
                        item.phoneNumber.value.length - 2,
                        item.phoneNumber.value.length,
                    ),
                )
            phoneNumberTV.text = paddedPhoneNumber

            itemTrustedContact.setOnClickListener {
                listener.onTrustedContactTapped(item.id, paddedName, paddedPhoneNumber)
            }

            itemTrustedContact.addContentDescriptionString(
                R.string.axSignInTrustedContactLabel,
                itemTrustedContact.context,
                getTrustedContactNameForContentDescription(name = name),
                item.phoneNumber.value.substring(
                    item.phoneNumber.value.length - 4,
                    item.phoneNumber.value.length,
                ),
            )
        }
    }

    private fun padTrustedContactName(name: String): String {
        return when (name.length) {
            1 -> name
            2 -> name.padEnd(1)
            in 3..5 -> {
                name.take(1).padEnd(name.length, '*').plus(name.last())
            }

            in 6..8 -> {
                name.take(2).padEnd(name.length, '*')
                    .plus(name.substring(name.length - 2, name.length))
            }

            else -> {
                name.take(3).padEnd(name.length / 2, '*')
                    .plus(name.substring(name.length - 3, name.length))
            }
        }
    }

    private fun getTrustedContactNameForContentDescription(name: String): String {
        return when (name.length) {
            1,
            2,
            3,
            -> name

            else -> {
                name.take(3)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_ITEM -> {
                TrustedContactViewHolder(
                    LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_trusted_contact_sign_in, parent, false),
                )
            }

            else -> {
                throw IllegalStateException("Unknown View Type")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return TYPE_ITEM
    }

    private class TrustedContactViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val itemTrustedContact: ConstraintLayout = itemView.findViewById(R.id.item_trusted_contact)
        val numbersTV: TextView = itemView.findViewById(R.id.serial_number_tv)
        val nameTV: TextView = itemView.findViewById(R.id.name_tv)
        val phoneNumberTV: TextView = itemView.findViewById(R.id.phone_number_tv)
    }
}

const val TYPE_ITEM: Int = 1
