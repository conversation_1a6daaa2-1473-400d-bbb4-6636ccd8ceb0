package com.resoluttech.core.auth

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.SignInUserRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signin.EnterPasswordFragmentDirections
import com.resoluttech.core.auth.signin.SignInRepository
import com.resoluttech.core.auth.signin.SignInSecurityQuestionsVM
import com.resoluttech.core.landingscreen.LandingScreenSharedPreference
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.ERROR_CODE_SIGN_IN_AGAIN
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getDeviceInformation
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class EnterPasswordVM : ViewModel() {

    private val _currentState: MutableLiveData<EnterPasswordScreenState> =
        MutableLiveData(EnterPasswordScreenState.AcceptInput)
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val signInRepository = SignInRepository()
    val currentState: LiveData<EnterPasswordScreenState> = _currentState

    init {
        vmIOScope.launch {
            LandingScreenSharedPreference.setLandingScreenAlreadyShown(true)
        }
    }

    fun onSignInTapped(
        context: Context,
        temporaryToken: UUID,
        password: String,
        controller: NavController,
        phoneNumber: LeoPhoneNumber,
    ) {
        _currentState.postValue(EnterPasswordScreenState.Loading)
        getPushNotificationToken(context, temporaryToken, password, controller, phoneNumber)
    }

    private fun attemptSignIn(
        context: Context,
        temporaryToken: UUID,
        password: String,
        controller: NavController,
        phoneNumber: LeoPhoneNumber,
        pushToken: String?,
    ) {
        if (password.isEmpty()) {
            _currentState.postValue(
                EnterPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleIncorrectPassword),
                        context.getString(R.string.alertMessageIncorrectPassword),
                    ),
                ),
            )
            return
        }
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response =
                            signInRepository.signInUser(
                                temporaryToken,
                                password,
                                phoneNumber,
                                pushToken,
                                getDeviceInformation(pushToken, context),
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleSuccessfulSignInUserResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            if (response.error == SignInUserRPC.Error.PhonenumberValidatedTokenExpired()) {
                                showError(
                                    UIError(
                                        ErrorType.DIALOG,
                                        context.getString(R.string.alertTitleSessionExpired),
                                        context.getString(R.string.alertMessageSessionExpired),
                                    ),
                                    ERROR_CODE_SIGN_IN_AGAIN,
                                )
                            } else {
                                handleErrorSignInUserResponse(context, response.error)
                            }
                        }
                    }
                },
                handleException = ::showError,
            )
        }
    }

    private fun showError(error: UIError, errorCode: Int? = null) {
        _currentState.postValue(EnterPasswordScreenState.Error(error, errorCode))
    }

    private fun handleErrorSignInUserResponse(context: Context, error: SignInUserRPC.Error) {
        when (error) {
            is SignInUserRPC.Error.TooManyRequests -> {
                showError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTooManyRequests),
                        context.getString(R.string.alertMessageTooManyRequests),
                    ),
                    ERROR_CODE_SIGN_IN_AGAIN,
                )
            }
            else -> {
                AuthExceptionHandler.signInUserRPCException(context, error)?.apply {
                    showError(this)
                }
            }
        }
    }

    private fun handleSuccessfulSignInUserResponse(
        response: SignInUserRPC.Response,
    ) {
        vmIOScope.launch {
            when (val result = response.result) {
                is SignInUserRPC.Response.Result.SignedIn -> {
                    KeyStoreHelper.storeSLT(result.slt)
                    KeyStoreHelper.storeLLT(result.llt)
                    _currentState.postValue(EnterPasswordScreenState.SignInSuccessful)
                }
                is SignInUserRPC.Response.Result.KycEntryNeeded -> {
                    _currentState.postValue(EnterPasswordScreenState.KYCRequired(result.passwordValidatedToken))
                }
            }
        }
    }

    fun onSuccessfulSignIn(controller: NavController) {
        _currentState.postValue(EnterPasswordScreenState.AcceptInput)
        controller.popBackStack()
        controller.navigate(R.id.home_nav)
    }

    fun onInlineErrorDismissed(dialogId: Int?, restartActivity: () -> Unit) {
        when (dialogId) {
            ERROR_CODE_SIGN_IN_AGAIN or DialogCodes.SIGN_UP_IN_SESSION_EXPIRED -> {
                restartActivity()
            }
            else -> {
                _currentState.postValue(EnterPasswordScreenState.AcceptInput)
            }
        }
    }

    fun onErrorDismissed() {
        _currentState.postValue(EnterPasswordScreenState.AcceptInput)
    }

    fun onForgetPasswordTapped(
        controller: NavController,
        phoneNumberValidationToken: String,
        phoneNumber: String,
        signInSecurityQuestionsVM: SignInSecurityQuestionsVM,
    ) {
        _currentState.postValue(EnterPasswordScreenState.AcceptInput)
        signInSecurityQuestionsVM.updateValidationToken(
            LeoPhoneNumber(phoneNumber),
            phoneNumberValidationToken,
        )
        val action =
            EnterPasswordFragmentDirections.actionEnterPasswordFragmentToSecurityQuestionsFragment(
                phoneNumberValidationToken,
                phoneNumber,
            )
        controller.navigate(action)
    }

    fun onNextStepKYCRequired(
        controller: NavController,
    ) {
        _currentState.postValue(EnterPasswordScreenState.AcceptInput)
        controller.navigate(R.id.kyc_nav)
    }

    private fun getPushNotificationToken(
        context: Context,
        temporaryToken: UUID,
        password: String,
        controller: NavController,
        phoneNumber: LeoPhoneNumber,
    ) {
        PushTokenHelper.init(context) {
            attemptSignIn(
                context = context,
                temporaryToken = temporaryToken,
                password = password,
                controller = controller,
                phoneNumber = phoneNumber,
                pushToken = it,
            )
        }
    }
}

sealed class EnterPasswordScreenState {
    object Loading : EnterPasswordScreenState()
    object AcceptInput : EnterPasswordScreenState()
    data class Error(val uiError: UIError, val errorCode: Int? = null) : EnterPasswordScreenState()
    object SignInSuccessful : EnterPasswordScreenState()
    data class KYCRequired(val passwordValidatedToken: UUID) : EnterPasswordScreenState()
}
