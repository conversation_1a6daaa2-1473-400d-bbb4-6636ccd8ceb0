package com.resoluttech.core.auth

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import org.koin.java.KoinJavaComponent
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

object KeyStoreHelper {

    private val lock = ReentrantLock()
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC)
    private val sharedPreferences = EncryptedSharedPreferences.create(
        FILE_NAME,
        masterKeyAlias,
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM,
    )
    private var cachedSLT: String? = null

    /*
     * We are suppressing the lint warning as it didn't find the definition blocking and hence keep
     * giving warning of redundant modifier.
     * Caller should call this method in a coroutine, as we don't want to do IO operation on Main
     * thread and hence the method is marked as suspending.
     */
    @Suppress("RedundantSuspendModifier")
    suspend fun storeSLT(token: String) {
        lock.withLock {
            with(sharedPreferences.edit()) {
                putString(KEY_SLT, token)
                // Storing SLT in cache only when storing it in disk is successful
                if (commit()) {
                    cachedSLT = token
                } else {
                    throw IllegalStateException("Unable to write short lived token to disk")
                }
            }
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun storeLLT(token: String) {
        lock.withLock {
            with(sharedPreferences.edit()) {
                putString(KEY_LLT, token)
                if (!commit()) {
                    throw IllegalStateException("Unable to write long lived token to disk")
                }
            }
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getSLT(): String? {
        lock.withLock {
            if (cachedSLT == null) {
                cachedSLT = sharedPreferences.getString(KEY_SLT, null)
            }
            return cachedSLT
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getLLT(): String? {
        lock.withLock {
            return sharedPreferences.getString(KEY_LLT, null)
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun clearAllTokens() {
        lock.withLock {
            sharedPreferences.edit(action = SharedPreferences.Editor::clear)
        }
    }

    suspend fun isUserLoggedIn(): Boolean {
        return getLLT() != null
    }
}

private const val FILE_NAME = "secret_shared_prefs"
private const val KEY_SLT = "com.resoluttech.slt"
private const val KEY_LLT = "com.resoluttech.llt"
