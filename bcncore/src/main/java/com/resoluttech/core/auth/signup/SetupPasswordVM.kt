package com.resoluttech.core.auth.signup

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.SecurityQuestionAnswer
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signin.SelectedQuestionWithAnswer
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class SetupPasswordVM : ViewModel() {

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<SetupPasswordScreenState> =
        MutableLiveData(SetupPasswordScreenState.AcceptInput)
    private val signUpRepository = SignUpRepository()

    val currentState: LiveData<SetupPasswordScreenState> = _currentState

    fun onInlineErrorDismissed() {
        _currentState.postValue(SetupPasswordScreenState.AcceptInput)
    }

    fun onSuccessfulPasswordSetup(
        controller: NavController,
    ) {
        _currentState.postValue(SetupPasswordScreenState.AcceptInput)
        controller.navigate(R.id.kyc_nav)
    }

    fun onContinueTapped(
        context: Context,
        temporaryToken: String,
        newPassword: String,
        confirmPassword: String,
        controller: NavController,
        phoneNumber: LeoPhoneNumber,
        savedQuestionAnswer: MutableList<SelectedQuestionWithAnswer>,
    ) {
        if (!validateEnteredPassword(context, newPassword, confirmPassword)) return
        _currentState.postValue(SetupPasswordScreenState.Loading)
        val securityQuestionAnswers = getQuestionAnswer(savedQuestionAnswer)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response = signUpRepository.submitPassword(
                            UUID.fromString(temporaryToken),
                            newPassword,
                            phoneNumber,
                            securityQuestionAnswers,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> handleSetupPasswordSuccessResponse(response.response)
                        is LeoRPCResult.LeoError -> {
                            handleSetupPasswordErrorResponse(context, response.error, controller)
                        }
                    }
                },
                handleException = ::showError,
            )
        }
    }

    private fun getQuestionAnswer(savedQuestionAnswer: List<SelectedQuestionWithAnswer>): List<SecurityQuestionAnswer> {
        return mutableListOf<SecurityQuestionAnswer>().apply {
            savedQuestionAnswer.forEach { ques ->
                run {
                    this.add(
                        SecurityQuestionAnswer(
                            (
                                ques.selectedQuestion
                                    ?: throw IllegalStateException("Security Questions cannot be null in SetupPassword Screen")
                                ).id,
                            ques.answer
                                ?: throw IllegalStateException("Security Questions Answers cannot be null in SetupPassword Screen"),
                        ),
                    )
                }
            }
        }
    }

    private fun handleSetupPasswordErrorResponse(
        context: Context,
        error: SubmitSecurityQuestionAnswersAndPasswordRPC.Error,
        controller: NavController,
    ) {
        // See more: https://github.com/Resolut-Tech/Android/pull/538#discussion_r662146524
        when (error) {
            is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.InactiveState -> {
                throw IllegalStateException("Received INACTIVE_STATE on SubmitSecurityQuestionAnswersAndPasswordRPC")
            }
            is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.UserAlreadyExists -> {
                showError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleUserAlreadyExists),
                        context.getString(R.string.alertMessageUserAlreadyExists),
                    ),
                    ERROR_CODE_USER_ALREADY_EXISTS,
                )
            }
            is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.TooManyRequests -> {
                showError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleSignUpBlocked),
                        context.getString(R.string.alertMessageSignUpBlocked),
                    ),
                    ERROR_CODE_SIGN_UP_AGAIN,
                )
            }
            is SubmitSecurityQuestionAnswersAndPasswordRPC.Error.TrustedContactsValidatedTokenExpired -> {
                controller.popBackStack(R.id.sign_in_nav, false)
            }
            else -> {
                AuthExceptionHandler.getSetupPasswordRPCException(context, error).apply {
                    showError(this)
                }
            }
        }
    }

    private fun handleSetupPasswordSuccessResponse(response: SubmitSecurityQuestionAnswersAndPasswordRPC.Response) {
        _currentState.postValue(SetupPasswordScreenState.SetupSuccessful(response))
    }

    private fun validateEnteredPassword(
        context: Context,
        newPassword: String,
        confirmPassword: String,
    ): Boolean {
        if (newPassword.isEmpty() or confirmPassword.isEmpty()) {
            _currentState.postValue(
                SetupPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleMissingPasswordField),
                        context.getString(R.string.alertMessageMissingNewPasswordField),
                    ),
                ),
            )
            return false
        }

        if (newPassword != confirmPassword) {
            _currentState.postValue(
                SetupPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePasswordsDoNotMatch),
                        context.getString(R.string.alertMessagePasswordsDoNotMatch),
                    ),
                ),
            )
            return false
        }

        if (newPassword.length < Config.MIN_PASSWORD_LENGTH) {
            _currentState.postValue(
                SetupPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInsufficientPasswordLength),
                        context.getString(R.string.alertMessageInsufficientNewPasswordLength),
                    ),
                ),
            )
            return false
        }

        return true
    }

    private fun showError(uiError: UIError, errorCode: Int? = null) {
        _currentState.postValue(SetupPasswordScreenState.Error(uiError, errorCode))
    }

    companion object {
        const val ERROR_CODE_INACTIVE_STATE: Int = 1001
        const val ERROR_CODE_USER_ALREADY_EXISTS: Int = 1002
        const val ERROR_CODE_SIGN_UP_AGAIN: Int = 1003
    }
}

sealed class SetupPasswordScreenState {
    object AcceptInput : SetupPasswordScreenState()
    object Loading : SetupPasswordScreenState()
    data class Error(val uiError: UIError, val errorCode: Int? = null) : SetupPasswordScreenState()
    data class SetupSuccessful(val response: SubmitSecurityQuestionAnswersAndPasswordRPC.Response) :
        SetupPasswordScreenState()
}
