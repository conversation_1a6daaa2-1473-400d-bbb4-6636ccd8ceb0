package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_DESCRIPTION
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_TITLE
import com.resoluttech.core.views.PasswordPolicyAdapter
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import java.util.UUID

class ResetPasswordFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    UnAuthenticatedBaseFragment.NetworkListener {

    private lateinit var resetButton: MaterialButton
    private lateinit var newPasswordET: TextInputEditText
    private lateinit var newPasswordTIL: TextInputLayout
    private lateinit var confirmPasswordET: TextInputEditText
    private lateinit var confirmPasswordTIL: TextInputLayout
    private lateinit var passwordPolicyRV: RecyclerView
    private val passwordPolicyHeading: String? by lazy { getPrivacyPolicyHeadText(arguments) }
    private val phoneNumber: String? by lazy { getphoneNumber(arguments) }
    private val resetToken: String? by lazy { getResetToken(arguments) }
    private val passwordPolicyDescription: List<String> by lazy {
        arguments?.getStringArrayList(
            KEY_PRIVACY_POLICY_DESCRIPTION,
        ) ?: throw IllegalArgumentException("Password policy description should not be null")
    }
    private lateinit var passwordPolicyHeadingTV: TextView

    private val resetPasswordVM: ResetPasswordVM by navGraphViewModels(R.id.sign_in_nav)
    private var phoneNumberValidatedToken: UUID? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_set_password, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(
            title = getString(R.string.alertTitleResetPasswordDeeplink),
            isBackArrowShown = false,
        )
        initViews(view)
        setupStateObserver()
        setupResetAction()
        setupPasswordPolicy()
        networkListenerCallback = this
    }

    private fun setupResetAction() {
        resetButton.setOnClickListener {
            resetPasswordVM.onResetPasswordTapped(
                requireContext(),
                (phoneNumber ?: throw IllegalArgumentException("Phone Number cannot be null")),
                (resetToken ?: throw IllegalArgumentException("Reset token cannot be null")),
                newPasswordET.text.toString().trim(),
                confirmPasswordET.text.toString().trim(),
            )
        }
    }

    private fun getPrivacyPolicyHeadText(args: Bundle?): String? {
        return args?.getString(KEY_PRIVACY_POLICY_TITLE)
    }

    private fun getphoneNumber(args: Bundle?): String? {
        return args?.getString(KEY_USER_PHONE_NUMBER)
    }

    private fun getResetToken(args: Bundle?): String? {
        return args?.getString(KEY_RESET_FORGOTTEN_PASSWORD_TOKEN)
    }

    private fun setupStateObserver() {
        resetPasswordVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun reactToState(state: ResetPasswordScreenState) {
        when (state) {
            is ResetPasswordScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            is ResetPasswordScreenState.Error -> {
                handleErrorState(state.uiError)
            }

            is ResetPasswordScreenState.Loading -> {
                handleLoadingState()
            }

            is ResetPasswordScreenState.ResetSuccessful -> {
                handleSuccessfulReset(state.response.phoneNumberValidatedToken)
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                resetPasswordVM::onInlineErrorDismissed,
            )

            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.ENTER_PASSWORD_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleSuccessfulReset(phoneNumberValidatedToken: UUID) {
        dismissProgressDialog()
        this.phoneNumberValidatedToken = phoneNumberValidatedToken
        showErrorDialog(
            title = getString(R.string.alertTitleResetSuccessful),
            message = getString(R.string.alertMessageResetSuccessful),
            dialogId = DialogCodes.RESET_SUCCESS_DIALOG,
            requireContext().getString(R.string.signUpInSignIn),
        )
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun initViews(view: View) {
        view.apply {
            resetButton = findViewById(R.id.reset_button)
            newPasswordET = findViewById(R.id.enter_password_et)
            newPasswordTIL = findViewById(R.id.enter_password_til)
            newPasswordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = newPasswordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                ),
            )
            confirmPasswordET = findViewById(R.id.confirm_password_tiet)
            confirmPasswordTIL = findViewById(R.id.confirm_password_til)
            confirmPasswordET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = confirmPasswordTIL,
                    maxLength = Config.MAX_PASSWORD_LENGTH,
                ),
            )
            passwordPolicyRV = findViewById(R.id.password_policy_rv)
            passwordPolicyHeadingTV = findViewById(R.id.privacy_policy_heading_tv)
        }
    }

    private fun setupPasswordPolicy() {
        passwordPolicyHeadingTV.text = passwordPolicyHeading
        passwordPolicyRV.adapter = PasswordPolicyAdapter(passwordPolicyDescription)
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.RESET_SUCCESS_DIALOG -> {
                resetPasswordVM.onSuccessfulPasswordReset(
                    findNavController(),
                    (phoneNumber ?: throw IllegalArgumentException("Phone Number cannot be null")),
                    (phoneNumberValidatedToken.toString()),
                )
            }

            DialogCodes.SIGN_UP_IN_SESSION_EXPIRED -> {
                resetPasswordVM.onSignUpSessionExpired(this::restartActivity)
            }

            else -> {
                resetPasswordVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        resetPasswordVM.onInlineErrorDismissed()
    }
}

const val KEY_USER_PHONE_NUMBER: String = "phoneNumber"
const val KEY_RESET_FORGOTTEN_PASSWORD_TOKEN: String = "resetForgottenPasswordToken"
