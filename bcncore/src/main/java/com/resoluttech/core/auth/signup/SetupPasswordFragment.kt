package com.resoluttech.core.auth.signup

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signin.SignUpSecurityQuestionsVM
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.localiseTextArray
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.selectionActionModeCallback
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.PasswordPolicyAdapter
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.resoluttech.core.views.UnAuthenticatedBaseFragment.NetworkListener

class SetupPasswordFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    NetworkListener {

    private lateinit var resetButton: MaterialButton
    private lateinit var newPasswordTIL: TextInputLayout
    private lateinit var newPasswordET: TextInputEditText
    private lateinit var confirmPasswordET: TextInputEditText
    private lateinit var passwordPolicyRV: RecyclerView
    private lateinit var passwordPolicyHeadingTV: TextView

    private val setupPasswordVM: SetupPasswordVM by navGraphViewModels(R.id.sign_up_nav)
    private val signUpSecurityQuestionsVM: SignUpSecurityQuestionsVM by navGraphViewModels(R.id.sign_up_nav)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_set_password, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(
            title = getString(R.string.setPasswordTitle),
            isBackArrowShown = false,
        )
        initViews(view)
        setupViews()
        setupStateObserver()
        setupResetAction()
        setupPasswordEditText()
        setupPasswordPolicy()
        networkListenerCallback = this
    }

    private fun setupPasswordEditText() {
        newPasswordET.selectionActionModeCallback()
        confirmPasswordET.selectionActionModeCallback()
    }

    private fun initViews(view: View) {
        view.apply {
            resetButton = findViewById(R.id.reset_button)
            newPasswordET = findViewById(R.id.enter_password_et)
            newPasswordTIL = findViewById(R.id.enter_password_til)
            confirmPasswordET = findViewById(R.id.confirm_password_tiet)
            passwordPolicyRV = findViewById(R.id.password_policy_rv)
            passwordPolicyHeadingTV = findViewById(R.id.privacy_policy_heading_tv)
        }
    }

    private fun setupPasswordPolicy() {
        passwordPolicyHeadingTV.text = signUpSecurityQuestionsVM.passwordPolicy.title.localisedText(
            LocaleManager.getCurrentLocale(requireContext()),
        )
        passwordPolicyRV.adapter = PasswordPolicyAdapter(
            signUpSecurityQuestionsVM.passwordPolicy.description.localiseTextArray(
                LocaleManager.getCurrentLocale(
                    requireContext(),
                ),
            ),
        )
    }

    private fun setupViews() {
        newPasswordTIL.hint = requireContext().getString(R.string.setPasswordEnterPassword)
        resetButton.text = requireContext().getString(R.string.setPasswordButtonTitle)
    }

    private fun setupStateObserver() {
        setupPasswordVM.currentState.observe(viewLifecycleOwner, ::reactToState)
        setupBackPressed {
            signUpSecurityQuestionsVM.resetSavedQuestions()
            findNavController().navigate(R.id.action_setPasswordFragment_to_signUpFragment)
        }
    }

    private fun setupResetAction() {
        resetButton.setOnClickListener {
            setupPasswordVM.onContinueTapped(
                requireContext(),
                signUpSecurityQuestionsVM.getValidatedToken(),
                newPasswordET.text.toString().trim(),
                confirmPasswordET.text.toString().trim(),
                findNavController(),
                signUpSecurityQuestionsVM.getPhoneNumber(),
                signUpSecurityQuestionsVM.getSavedQuestionAnswer(),
            )
        }
    }

    private fun reactToState(state: SetupPasswordScreenState) {
        when (state) {
            is SetupPasswordScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is SetupPasswordScreenState.Error -> {
                handleErrorState(state.uiError, state.errorCode)
            }
            is SetupPasswordScreenState.Loading -> {
                handleLoadingState()
            }
            is SetupPasswordScreenState.SetupSuccessful -> {
                handleSuccessfulSetup(state.response)
            }
        }
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleErrorState(uiError: UIError, errorCode: Int?) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                setupPasswordVM::onInlineErrorDismissed,
            )
            ErrorType.DIALOG -> {
                if (errorCode != null) {
                    showErrorDialog(uiError.errorTitle, uiError.errorMessage, errorCode)
                } else {
                    showErrorDialog(
                        uiError.errorTitle,
                        uiError.errorMessage,
                        DialogCodes.ENTER_PASSWORD_CODE,
                    )
                }
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleSuccessfulSetup(response: SubmitSecurityQuestionAnswersAndPasswordRPC.Response) {
        dismissProgressDialog()
        KYCSharedPreference.saveKYCDetails(
            KYCDetails(
                "${response.passwordValidatedToken}",
                signUpSecurityQuestionsVM.getPhoneNumber().value,
                KYCPreviousPath.SIGN_UP,
            ),
        )
        setupPasswordVM.onSuccessfulPasswordSetup(
            findNavController(),
        )
    }

    override fun onPositiveAction(dialogId: Int) {
        setupPasswordVM.onInlineErrorDismissed()
        // See more: https://github.com/Resolut-Tech/Android/pull/538#discussion_r662146524
        if (dialogId == SetupPasswordVM.ERROR_CODE_INACTIVE_STATE ||
            dialogId == SetupPasswordVM.ERROR_CODE_USER_ALREADY_EXISTS ||
            dialogId == SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN
        ) {
            restartActivity()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        setupPasswordVM.onInlineErrorDismissed()
    }
}
