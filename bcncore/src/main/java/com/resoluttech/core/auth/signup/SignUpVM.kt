package com.resoluttech.core.auth.signup

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.RequestSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignUpOTPRPC
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getCurrentLocale
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class SignUpVM : ViewModel() {

    private val _currentState: MutableLiveData<SignUpScreenState> = MutableLiveData()
    val currentState: LiveData<SignUpScreenState> = _currentState

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val repository = SignUpRepository()
    var ageCBIsChecked: Boolean = false

    fun init(context: Context, supportedCountries: MutableList<Country>) {
        if (!(
                _currentState.value is SignUpScreenState.ConfirmPhoneNumber ||
                    _currentState.value is SignUpScreenState.OTPGenerated ||
                    _currentState.value is SignUpScreenState.ResendOTPSuccess ||
                    _currentState.value is SignUpScreenState.Error
                )
        ) {
            _currentState.postValue(SignUpScreenState.Loading)
            if (supportedCountries.isEmpty()) {
                vmIOScope.launch {
                    executeRPC(
                        context,
                        rpcBlock = {
                            when (val result = repository.getSupportedCountries()) {
                                is LeoRPCResult.LeoResponse -> {
                                    result.response.countries.forEach(supportedCountries::add)
                                    _currentState.postValue(
                                        SignUpScreenState.SupportedCountriesAvailable(
                                            supportedCountries,
                                        ),
                                    )
                                }
                                is LeoRPCResult.LeoError -> {
                                    // Nothing to handle.
                                }
                            }
                        },
                        handleException = {
                            _currentState.postValue(SignUpScreenState.FullScreenError(it))
                        },
                    )
                }
            } else {
                _currentState.postValue(
                    SignUpScreenState.SupportedCountriesAvailable(
                        supportedCountries,
                    ),
                )
            }
        }
    }

    fun onPhoneNumberConfirmed(
        fullNumber: LeoPhoneNumber,
        context: Context,
    ) {
        _currentState.postValue(SignUpScreenState.InlineLoading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            repository.requestSignUpOTP(fullNumber, context.getCurrentLocale())
                    ) {
                        is LeoRPCResult.LeoResponse -> handleSuccessSignUpOTPResponse(result.response)
                        is LeoRPCResult.LeoError -> handleSignUpOTPError(context, result.error)
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    fun onVerifyOTPTapped(
        fullNumber: LeoPhoneNumber,
    ) {
        _currentState.postValue(SignUpScreenState.ConfirmPhoneNumber(fullNumber))
    }

    private fun UIError.showError() {
        _currentState.postValue(SignUpScreenState.Error(this))
    }

    private fun handleSuccessSignUpOTPResponse(response: RequestSignUpOTPRPC.Response) {
        val otpDetails = response.otpDetails
        _currentState.postValue(
            SignUpScreenState.OTPGenerated(
                OTPResponse(
                    otpId = otpDetails.otpId,
                    nextResendAt = otpDetails.validityDetails.nextResendAt,
                    expiresAt = otpDetails.validityDetails.expiresAt,
                ),
            ),
        )
    }

    private fun handleSignUpOTPError(context: Context, error: RequestSignUpOTPRPC.Error) {
        AuthExceptionHandler.requestSignUpOTPRPCExceptions(context, error).apply {
            showError()
        }
    }

    fun onSuccessfulOTPRead(
        context: Context,
        otp: String,
        otpId: UUID?,
        phoneNumber: LeoPhoneNumber,
    ) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = repository.confirmSignUpOTP(otpId, otp, phoneNumber)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleOTPConfirmSuccessResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleOTPConfirmRPCError(context, response.error)
                        }
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleOTPConfirmSuccessResponse(response: ConfirmSignUpOTPRPC.Response) {
        _currentState.postValue(SignUpScreenState.OTPConfirmed(response))
    }

    private fun handleOTPConfirmRPCError(context: Context, error: ConfirmSignUpOTPRPC.Error) {
        AuthExceptionHandler.confirmSignUpOTPRPCExceptions(context, error).apply {
            showError()
        }
    }

    fun onSignInTapped(controller: NavController) {
        controller.navigate(R.id.sign_in_nav)
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(SignUpScreenState.AcceptInput)
    }

    fun onOTPReadCancelled() {
        _currentState.postValue(SignUpScreenState.AcceptInput)
    }

    fun setupTrustedContact(
        controller: NavController,
        response: ConfirmSignUpOTPRPC.Response,
        phoneNumber: LeoPhoneNumber,
    ) {
        _currentState.postValue(SignUpScreenState.AcceptInput)
        val action = SignUpFragmentDirections.actionSignUpFragmentToTrustedContactInfoFragment(
            response.phoneNumberValidatedToken,
            response.phoneNumberValidatedTokenValidityTimeInSeconds,
            phoneNumber.value,
        )
        ageCBIsChecked = false
        controller.navigate(action)
    }

    fun onOTPResendTapped(context: Context, otpId: UUID?) {
        if (otpId == null) {
            throw IllegalArgumentException("OTP ID is null")
        }
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = repository.resendSignUpOTP(otpId)) {
                        is LeoRPCResult.LeoResponse -> handleResendOTPSuccessResponse(
                            otpId,
                            response.response,
                        )
                        is LeoRPCResult.LeoError -> handleResendOTPErrorResponse(
                            context,
                            response.error,
                        )
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleResendOTPSuccessResponse(otpId: UUID, response: ResendSignUpOTPRPC.Response) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            otpId = otpId,
            nextResendAt = otpDetails.validityDetails.nextResendAt,
            expiresAt = otpDetails.validityDetails.expiresAt,
            otpLeft = otpDetails.numberOfResendsLeft,
        )
        _currentState.postValue(SignUpScreenState.ResendOTPSuccess(otpResponse))
    }

    private fun handleResendOTPErrorResponse(context: Context, error: ResendSignUpOTPRPC.Error) {
        AuthExceptionHandler.resendSignUpOTPRPCExceptions(context, error).apply {
            showError()
        }
    }

    fun onAlertDialogDismissed() {
        _currentState.postValue(SignUpScreenState.AcceptInput)
    }
}

sealed class SignUpScreenState {
    object AcceptInput : SignUpScreenState()
    object InlineLoading : SignUpScreenState()
    object Loading : SignUpScreenState()
    data class SupportedCountriesAvailable(val supportedCountries: List<Country>) :
        SignUpScreenState()

    data class ConfirmPhoneNumber(val phoneNumber: LeoPhoneNumber) : SignUpScreenState()
    data class OTPGenerated(val response: OTPResponse) : SignUpScreenState()
    data class OTPConfirmed(val response: ConfirmSignUpOTPRPC.Response) : SignUpScreenState()
    data class ResendOTPSuccess(val response: OTPResponse) : SignUpScreenState()
    data class Error(val error: UIError) : SignUpScreenState()
    data class FullScreenError(val error: UIError) : SignUpScreenState()
}
