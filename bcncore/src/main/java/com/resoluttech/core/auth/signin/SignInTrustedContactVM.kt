package com.resoluttech.core.auth.signin

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.ConfirmResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.RequestResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.types.PasswordPolicy
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.OTPReadMode
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.localiseTextArray
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_DESCRIPTION
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_TITLE
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class SignInTrustedContactVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<SignInTrustedContactScreenState> = MutableLiveData(
        SignInTrustedContactScreenState.AcceptInput,
    )
    private lateinit var passwordPolicy: PasswordPolicy
    val currentState: LiveData<SignInTrustedContactScreenState> = _currentState
    private val repository: SignInRepository = SignInRepository()
    var listOfTrustedContact: List<TrustedContact>? = null

    fun getTrustedContacts(
        context: Context,
        phoneNumberValidatedToken: UUID,
    ) {
        if (!(_currentState.value is SignInTrustedContactScreenState.Error || _currentState.value is SignInTrustedContactScreenState.OTPGenerated || _currentState.value is SignInTrustedContactScreenState.ResendOTPSuccess || _currentState.value is SignInTrustedContactScreenState.ConfirmTrustedContact)) {
            vmIoScope.launch {
                executeRPC(
                    context,
                    {
                        when (
                            val result =
                                repository.getTrustedContactsRPC(phoneNumberValidatedToken)
                        ) {
                            is LeoRPCResult.LeoResponse -> {
                                handleGetTrustedContactRPCResponse(result.response)
                            }

                            is LeoRPCResult.LeoError -> {
                                handleGetTrustedContactRPCError(context, result.error)
                            }
                        }
                    },
                    {
                        it.showError()
                    },
                )
            }
        }
    }

    private fun handleGetTrustedContactRPCResponse(
        response: GetTrustedContactsRPC.Response,
    ) {
        listOfTrustedContact = response.trustedContacts
        _currentState.postValue(SignInTrustedContactScreenState.Data(response.trustedContacts))
    }

    private fun handleGetTrustedContactRPCError(
        context: Context,
        error: GetTrustedContactsRPC.Error,
    ) {
        AuthExceptionHandler.handleGetTrustedContactsRPCErrors(context, error).showError()
    }

    fun onTrustedContactTapped(
        context: Context,
        phoneNumberValidatedToken: UUID,
        trustedContactId: UUID,
        trustedContactName: String,
        trustedPhoneNumber: String,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result = repository.requestResetPasswordByTrustedContactOTP(
                            phoneNumberValidatedToken,
                            trustedContactId,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleRequestResetPasswordByTrustedContactOTPRPCResponse(
                                context = context,
                                response = result.response,
                                trustedContactName = trustedContactName,
                                trustedPhoneNumber = trustedPhoneNumber,
                            )
                        }

                        is LeoRPCResult.LeoError -> {
                            handleRequestResetPasswordByTrustedContactOTPRPCError(
                                context,
                                result.error,
                            )
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleRequestResetPasswordByTrustedContactOTPRPCResponse(
        context: Context,
        response: RequestResetPasswordByTrustedContactOTPRPC.Response,
        trustedContactName: String,
        trustedPhoneNumber: String,
    ) {
        val otpDetails = response.otpDetails
        _currentState.postValue(
            SignInTrustedContactScreenState.OTPGenerated(
                OTPResponse(
                    title = context.getString(
                        R.string.otpTrustedContact,
                        trustedContactName,
                        trustedPhoneNumber,
                    ),
                    otpId = otpDetails.otpId,
                    nextResendAt = otpDetails.validityDetails.nextResendAt,
                    expiresAt = otpDetails.validityDetails.expiresAt,
                    otpReadMode = OTPReadMode.MANUAL,
                ),
            ),
        )
    }

    private fun handleRequestResetPasswordByTrustedContactOTPRPCError(
        context: Context,
        error: RequestResetPasswordByTrustedContactOTPRPC.Error,
    ) {
        AuthExceptionHandler.handleRequestResetPasswordByTrustedContactOTPRPCExceptions(
            context,
            error,
        ).showError()
    }

    fun onResendOTPTapped(
        context: Context,
        phoneNumberValidatedToken: UUID,
        otpId: UUID?,
    ) {
        if (otpId == null) {
            throw IllegalStateException("OTP Id cannot be null")
        }
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result = repository.resendResetPasswordByTrustedContactOTP(
                            phoneNumberValidatedToken,
                            otpId,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleRequestResendResetPasswordByTrustedContactOTPRPCResponse(
                                otpId,
                                result.response,
                            )
                        }

                        is LeoRPCResult.LeoError -> {
                            handleRequestResendResetPasswordByTrustedContactOTPRPCError(
                                context,
                                result.error,
                            )
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleRequestResendResetPasswordByTrustedContactOTPRPCResponse(
        otpId: UUID,
        response: ResendResetPasswordByTrustedContactOTPRPC.Response,
    ) {
        val otpDetails = response.otpDetails
        val otpResponse = OTPResponse(
            title = null,
            otpId = otpId,
            nextResendAt = otpDetails.validityDetails.nextResendAt,
            expiresAt = otpDetails.validityDetails.expiresAt,
            otpLeft = otpDetails.numberOfResendsLeft,
            otpReadMode = OTPReadMode.MANUAL,
        )
        _currentState.postValue(SignInTrustedContactScreenState.ResendOTPSuccess(otpResponse))
    }

    private fun handleRequestResendResetPasswordByTrustedContactOTPRPCError(
        context: Context,
        error: ResendResetPasswordByTrustedContactOTPRPC.Error,
    ) {
        AuthExceptionHandler.handleResendResetPasswordByTrustedContactOTPRPCExceptions(
            context,
            error,
        ).showError()
    }

    fun onConfirmOTPTapped(
        context: Context,
        phoneNumberValidatedToken: UUID,
        otpId: UUID?,
        otp: String,
    ) {
        if (otpId == null) {
            throw IllegalStateException("OTP Id cannot be null")
        }
        _currentState.postValue(SignInTrustedContactScreenState.LoadingDialog)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result = repository.confirmResetPasswordByTrustedContactOTP(
                            phoneNumberValidatedToken,
                            otpId,
                            otp,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleConfirmResetPasswordByTrustedContactOTPRPCResponse(result.response)
                        }

                        is LeoRPCResult.LeoError -> {
                            handleConfirmResetPasswordByTrustedContactOTPRPCError(
                                context,
                                result.error,
                            )
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    fun trustedContactOTPConfirmed(
        navController: NavController,
        phoneNumber: String,
        resetForgottenPasswordToken: UUID,
        context: Context,
    ) {
        _currentState.postValue(SignInTrustedContactScreenState.AcceptInput)
        val action =
            SignInTrustedContactFragmentDirections.actionSignInTrustedContactFragmentToResetPasswordFragment()
        val args = Bundle()
        args.putString(
            KEY_PRIVACY_POLICY_TITLE,
            passwordPolicy.title.localisedText(LocaleManager.getCurrentLocale(context)),
        )
        args.putString(KEY_USER_PHONE_NUMBER, phoneNumber)
        args.putString(KEY_RESET_FORGOTTEN_PASSWORD_TOKEN, "$resetForgottenPasswordToken")
        args.putStringArrayList(
            KEY_PRIVACY_POLICY_DESCRIPTION,
            passwordPolicy.description.localiseTextArray(LocaleManager.getCurrentLocale(context)),
        )
        navController.navigate(action.actionId, args)
    }

    private fun handleConfirmResetPasswordByTrustedContactOTPRPCResponse(
        response: ConfirmResetPasswordByTrustedContactOTPRPC.Response,
    ) {
        passwordPolicy = response.passwordPolicy
        _currentState.postValue(SignInTrustedContactScreenState.OTPConfirmed(response.resetForgottenPasswordToken))
    }

    private fun handleConfirmResetPasswordByTrustedContactOTPRPCError(
        context: Context,
        error: ConfirmResetPasswordByTrustedContactOTPRPC.Error,
    ) {
        AuthExceptionHandler.handleConfirmResetPasswordByTrustedContactOTPRPCExceptions(
            context,
            error,
        ).showError()
    }

    fun onOTPReadCancelled(otpReaderDialog: OTPReaderDialog) {
        otpReaderDialog.dismiss()
        _currentState.postValue(SignInTrustedContactScreenState.AcceptInput)
    }

    private fun UIError.showError() {
        _currentState.postValue(SignInTrustedContactScreenState.Error(this))
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(SignInTrustedContactScreenState.AcceptInput)
    }

    fun handleLeoServerException(navController: NavController) {
        navController.popBackStack(R.id.enterPasswordFragment, false)
    }

    fun confirmTrustedContact(trustedContactId: UUID, name: String, phoneNumber: String) {
        _currentState.postValue(
            SignInTrustedContactScreenState.ConfirmTrustedContact(
                trustedContactId,
                name,
                phoneNumber,
            ),
        )
    }
}

sealed class SignInTrustedContactScreenState {
    data class Error(val error: UIError) : SignInTrustedContactScreenState()
    object AcceptInput : SignInTrustedContactScreenState()
    object FullScreenLoading : SignInTrustedContactScreenState()
    object LoadingDialog : SignInTrustedContactScreenState()
    data class Data(val listOfTrustedContacts: List<TrustedContact>) :
        SignInTrustedContactScreenState()

    data class OTPGenerated(val response: OTPResponse) : SignInTrustedContactScreenState()
    data class OTPConfirmed(val resetForgottenPasswordToken: UUID) :
        SignInTrustedContactScreenState()

    data class ResendOTPSuccess(val response: OTPResponse) : SignInTrustedContactScreenState()
    data class ConfirmTrustedContact(
        val trustedContactId: UUID,
        val name: String,
        val phoneNumber: String,
    ) : SignInTrustedContactScreenState()
}
