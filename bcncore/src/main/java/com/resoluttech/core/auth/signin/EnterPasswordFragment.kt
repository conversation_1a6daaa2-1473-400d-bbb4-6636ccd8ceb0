package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.EnterPasswordScreenState
import com.resoluttech.core.auth.EnterPasswordVM
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.resoluttech.core.views.UnAuthenticatedBaseFragment.NetworkListener
import com.suryadigital.leo.types.LeoPhoneNumber
import java.util.UUID

class EnterPasswordFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    NetworkListener {

    private lateinit var signInButton: MaterialButton
    private lateinit var forgetPasswordTV: TextView
    private lateinit var passwordET: TextInputEditText
    private lateinit var passwordTIL: TextInputLayout

    private val enterPasswordVM: EnterPasswordVM by navGraphViewModels(R.id.sign_in_nav)
    private val signInSecurityQuestionsVM: SignInSecurityQuestionsVM by navGraphViewModels(R.id.sign_in_nav)
    private val args: EnterPasswordFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_enter_password, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(title = getString(R.string.enterPasswordTitle), isBackArrowShown = false)
        initViews(view)
        setupPasswordET(view)
        setupStateObserver()
        setupSignInAction()
        setupForgetPasswordAction()
        networkListenerCallback = this
    }

    private fun setupForgetPasswordAction() {
        forgetPasswordTV.setOnClickListener {
            enterPasswordVM.onForgetPasswordTapped(
                findNavController(),
                args.phoneNumberValidatedToken,
                args.phoneNumber,
                signInSecurityQuestionsVM,
            )
        }
    }

    private fun setupSignInAction() {
        signInButton.setOnClickListener {
            enterPasswordVM.onSignInTapped(
                requireContext(),
                UUID.fromString(args.phoneNumberValidatedToken),
                passwordET.text.toString().trim(),
                findNavController(),
                LeoPhoneNumber(args.phoneNumber),
            )
        }
    }

    private fun setupStateObserver() {
        enterPasswordVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        setupBackPressed(findNavController()::navigateUp)
    }

    private fun reactToState(state: EnterPasswordScreenState) {
        when (state) {
            is EnterPasswordScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is EnterPasswordScreenState.SignInSuccessful -> {
                handleSuccessfulLogin()
            }
            is EnterPasswordScreenState.Error -> {
                handleErrorState(state.uiError, state.errorCode)
            }
            is EnterPasswordScreenState.Loading -> {
                handleLoadingState()
            }
            is EnterPasswordScreenState.KYCRequired -> {
                handleKYCRequiredState(state.passwordValidatedToken)
            }
        }
    }

    private fun handleKYCRequiredState(passwordValidatedToken: UUID) {
        KYCSharedPreference.saveKYCDetails(
            KYCDetails(
                "$passwordValidatedToken",
                args.phoneNumber,
                KYCPreviousPath.SIGN_IN,
            ),
        )
        enterPasswordVM.onNextStepKYCRequired(
            findNavController(),
        )
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(uiError: UIError, errorCode: Int?) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
            ) {
                enterPasswordVM.onInlineErrorDismissed(errorCode, this::restartActivity)
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.ENTER_PASSWORD_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleSuccessfulLogin() {
        dismissProgressDialog()
        enterPasswordVM.onSuccessfulSignIn(findNavController())
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun initViews(view: View) {
        view.apply {
            signInButton = findViewById(R.id.sign_in_bt)
            signInButton.disable()
            forgetPasswordTV = findViewById(R.id.forget_password_tv)
        }
    }

    private fun setupPasswordET(view: View) {
        passwordET = view.findViewById(R.id.enter_password_tiet)
        passwordTIL = view.findViewById(R.id.enter_password_til)

        passwordET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = passwordTIL,
                maxLength = Config.MAX_PASSWORD_LENGTH,
                onTextChanged = {
                    validateInput()
                },
            ),
        )
    }

    private fun validateInput() {
        if (passwordET.text?.isNotBlank() == true && passwordET.text.toString().length >= Config.MIN_PASSWORD_LENGTH
        ) {
            signInButton.enable()
        } else {
            signInButton.disable()
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        enterPasswordVM.onInlineErrorDismissed(dialogId, this::restartActivity)
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        enterPasswordVM.onErrorDismissed()
    }
}
