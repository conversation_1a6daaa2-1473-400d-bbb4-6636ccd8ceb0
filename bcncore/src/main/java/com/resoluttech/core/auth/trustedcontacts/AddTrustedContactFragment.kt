package com.resoluttech.core.auth.trustedcontacts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageButton
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputLayout
import com.hbb20.CountryCodePicker
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signup.SetupPasswordVM
import com.resoluttech.core.auth.signup.SignUpInVM
import com.resoluttech.core.config.Config
import com.resoluttech.core.profile.trustedcontacts.AddTrustedContactCCPVM
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.uicomponents.ResendOTPListener
import com.resoluttech.core.utils.CONTACT_KEY
import com.resoluttech.core.utils.CONTACT_NAME_KEY
import com.resoluttech.core.utils.CONTACT_NUMBER_KEY
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getPhoneNumberWithoutCountryCode
import com.resoluttech.core.utils.isCountryCodeAttached
import com.resoluttech.core.utils.isEmailValid
import com.resoluttech.core.utils.isPhoneNumberValid
import com.resoluttech.core.utils.parsePhoneCode
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.utils.trim
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.resoluttech.core.views.setDialogSize
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber
import java.util.UUID

class AddTrustedContactFragment :
    UnAuthenticatedBaseFragment(),
    OTPReaderDialog.OTPListener,
    AlertDialog.ActionListener,
    UnAuthenticatedBaseFragment.NetworkListener,
    CountryCodePicker.OnCountryChangeListener {

    private val signUpInVM: SignUpInVM by activityViewModels()
    private val addTrustedContactVM: AddTrustedContactVM by navGraphViewModels(R.id.sign_up_nav)
    private val addTrustedContactCCPVM: AddTrustedContactCCPVM by activityViewModels()
    private val args: AddTrustedContactFragmentArgs by navArgs()

    private lateinit var countryCodePicker: CountryCodePicker
    private lateinit var phoneNumberET: EditText
    private lateinit var firstNameET: EditText
    private lateinit var firstNameTIL: TextInputLayout
    private lateinit var lastNameET: EditText
    private lateinit var lastNameTIL: TextInputLayout
    private lateinit var emailIdET: EditText
    private lateinit var addTrustedContactButton: MaterialButton
    private lateinit var otpReaderDialog: OTPReaderDialog
    private lateinit var contactPickerIV: ImageButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_add_trusted_contact, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(title = getString(R.string.addTrustedContactTitle))
        initViews()
        setupFragmentResultListener()
        setupCountryCodePicker()
        setupListeners()
        setupContactPicker()
        addTrustedContactVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        networkListenerCallback = this
    }

    private fun setupCountryCodePicker() {
        countryCodePicker.registerCarrierNumberEditText(phoneNumberET)
        var masterCountry = requireActivity().getEmptyString()
        signUpInVM.supportedCountry.forEach {
            masterCountry += "${it.code.code},"
        }
        countryCodePicker.setCustomMasterCountries(masterCountry)
        countryCodePicker.setCountryForNameCode(
            addTrustedContactCCPVM.singUpAddTrustedContactSelectedCountryCode
                ?: signUpInVM.supportedCountry.first().code.code,
        )
        changeDialogTitle()
        countryCodePicker.setOnCountryChangeListener(this)
        countryCodePicker.setDialogSize(requireContext())
    }

    private fun changeDialogTitle() {
        countryCodePicker.setCustomDialogTextProvider(object :
            CountryCodePicker.CustomDialogTextProvider {
            override fun getCCPDialogTitle(
                language: CountryCodePicker.Language,
                defaultTitle: String,
            ): String {
                return getString(R.string.addTrustedContactSelectCountry)
            }

            override fun getCCPDialogSearchHintText(
                language: CountryCodePicker.Language,
                defaultSearchHintText: String,
            ): String {
                return defaultSearchHintText
            }

            override fun getCCPDialogNoResultACK(
                language: CountryCodePicker.Language,
                defaultNoResultACK: String,
            ): String {
                return defaultNoResultACK
            }
        })
    }

    private fun setupFragmentResultListener() {
        setFragmentResultListener(CONTACT_KEY) { _, bundle ->
            val contact = bundle.getString(CONTACT_NUMBER_KEY)
            val name = bundle.getString(CONTACT_NAME_KEY)

            val supportedCountries = signUpInVM.supportedCountry
            firstNameET.setText(requireContext().getEmptyString())
            lastNameET.setText(requireContext().getEmptyString())
            phoneNumberET.setText(requireContext().getEmptyString())
            countryCodePicker.setCountryForNameCode(supportedCountries.first().code.code)

            contact?.let {
                if (isCountryCodeAttached(it)) {
                    getPhoneNumberWithoutCountryCode(contact)?.let(phoneNumberET::setText)
                } else {
                    phoneNumberET.setText(it)
                }
                val phoneCode = parsePhoneCode(contact)
                if (phoneCode != null && supportedCountries.map(Country::phoneCode)
                        .contains("+$phoneCode")
                ) {
                    countryCodePicker.setCountryForPhoneCode(phoneCode)
                } else {
                    countryCodePicker.setCountryForNameCode(supportedCountries.first().code.code)
                }
            }

            name?.let {
                val userNames: MutableList<String> =
                    it.split(" ", limit = 2).toMutableList()
                if (userNames.size == 1) {
                    userNames.add(requireContext().getEmptyString())
                }
                firstNameET.setText(userNames[0])
                lastNameET.setText(userNames[1])
            }
        }
    }

    private fun reactToState(state: AddTrustedContactState) {
        when (state) {
            is AddTrustedContactState.AcceptInput -> {
                handleAcceptInputState()
            }

            is AddTrustedContactState.Error -> {
                handleErrorState(state.error)
            }

            is AddTrustedContactState.Loading -> {
                handleLoadingState()
            }

            is AddTrustedContactState.OTPConfirmed -> {
                handleOTPConfirmedState()
            }

            is AddTrustedContactState.OTPGenerated -> {
                handleOTPGeneratedState(state.response)
            }

            is AddTrustedContactState.ResendOTPSuccess -> {
                handleOTPResendSuccessState(state.response)
            }

            is AddTrustedContactState.ConfirmPhoneNumber -> {
                handleConfirmPhoneNumberState(state.phoneNumber)
            }
        }
    }

    private fun handleOTPResendSuccessState(response: OTPResponse) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onSuccessfulResendOTP(response)
        } else {
            handleOTPGeneratedState(response)
        }
        dismissProgressDialog()
    }

    private fun handleOTPGeneratedState(response: OTPResponse) {
        otpReaderDialog = OTPReaderDialog.newInstance(response)
        otpReaderDialog.setArguments(false)
        otpReaderDialog.show(childFragmentManager, OTPReaderDialog.TAG)
        dismissProgressDialog()
    }

    private fun handleOTPConfirmedState() {
        findNavController().navigateUp()
        addTrustedContactVM.onInlineErrorDismissed()
    }

    private fun handleErrorState(error: UIError) {
        dismissProgressDialog()
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
        }
        when (error.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    error.errorMessage,
                    requireView(),
                    addTrustedContactVM::onInlineErrorDismissed,
                )
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    error.errorCode ?: DialogCodes.SIGN_IN_ERROR_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleConfirmPhoneNumberState(phoneNumber: LeoPhoneNumber) {
        showAlertDialog(
            title = getString(R.string.alertTitleAddTrustedContactConfirmation),
            message = getString(
                R.string.alertMessageAddTrustedContactConfirmation,
                phoneNumber.getFormattedPhoneNumber(),
            ),
            dialogId = DialogCodes.CONFIRM_TRUSTED_CONTACT_PHONE_NUMBER,
            positiveActionLabel = getString(R.string.alertActionYes),
            negativeActionLabel = getString(R.string.alertActionNo),
        )
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun setupListeners() {
        addTrustedContactButton.setOnClickListener {
            if (phoneNumberET.text.isNullOrEmpty() || phoneNumberET.text.isBlank()) {
                addTrustedContactVM.onInvalidPhoneNumberInput(
                    requireContext().getString(R.string.alertTitleTrustedContactsMissingDetails),
                    getString(R.string.alertMessageTrustedContactMissingMobileNumber),
                )
            }
            if (!countryCodePicker.fullNumberWithPlus.isPhoneNumberValid()) {
                addTrustedContactVM.onInvalidPhoneNumberInput(
                    title = getString(R.string.alertTitleInvalidMobileNumber),
                    message = getString(R.string.alertMessageInvalidMobileNumber),
                )
                return@setOnClickListener
            } else if (firstNameET.text.isNullOrEmpty() || firstNameET.text.isBlank()) {
                addTrustedContactVM.onInvalidPhoneNumberInput(
                    title = getString(R.string.alertTitleTrustedContactsMissingDetails),
                    message = getString(R.string.alertMessageTrustedContactMissingFirstName),
                )
            } else if (emailIdET.text.isNotBlank() && !emailIdET.text.toString().isEmailValid()) {
                addTrustedContactVM.onInvalidPhoneNumberInput(
                    title = getString(R.string.alertTitleInvalidEmailId),
                    getString(R.string.alertMessageInvalidEmailId),
                )
            } else {
                addTrustedContactVM.onAddTrustedContactTapped(LeoPhoneNumber(countryCodePicker.fullNumberWithPlus))
            }
        }
    }

    private fun initViews() {
        view?.apply {
            countryCodePicker = findViewById(R.id.country_code_picker)
            phoneNumberET = findViewById(R.id.phone_number_et)
            firstNameET = findViewById(R.id.first_name_et)
            firstNameTIL = findViewById(R.id.first_name_til)
            firstNameET.addTextChangedListener(SingleLineCharacterLimitTextWatcher(firstNameTIL, Config.MAX_FIRST_NAME_LENGTH))
            lastNameET = findViewById(R.id.last_name_et)
            lastNameTIL = findViewById(R.id.last_name_til)
            lastNameET.addTextChangedListener(SingleLineCharacterLimitTextWatcher(lastNameTIL, Config.MAX_LAST_NAME_LENGTH))
            emailIdET = findViewById(R.id.email_id_et)
            addTrustedContactButton = findViewById(R.id.finish_button)
            contactPickerIV = findViewById(R.id.contact_picker)
        }
    }

    private fun setupContactPicker() {
        contactPickerIV.setOnClickListener {
            addTrustedContactVM.onPickContactClicked(findNavController())
        }
    }

    override fun onResendTapped(otpId: UUID?) {
        addTrustedContactVM.resendAddTrustedContactOTP(
            requireContext(),
            args.phoneNumberValidatedToken,
            otpId,
        )
    }

    override fun onOTPReadCancelled() {
        if (::otpReaderDialog.isInitialized) {
            addTrustedContactVM.onOTPReadCancelled(otpReaderDialog)
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onOTPReadFailed(e: OTPFailureException) {
        // OTP failure happens due to timeout so we are just logging it.
        Timber.tag(TAG).e(message = e.stackTraceToString())
    }

    override fun onSuccessfulOTPRead(otp: String, otpId: UUID?) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
            dismissProgressDialog()
            showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
            addTrustedContactVM.confirmAddTrustedContactOTP(
                requireContext(),
                args.phoneNumberValidatedToken,
                otpId,
                otp,
            )
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        addTrustedContactVM.onInlineErrorDismissed()
        // See more: https://github.com/Resolut-Tech/Android/pull/538#discussion_r662146524
        when (dialogId) {
            SetupPasswordVM.ERROR_CODE_SIGN_UP_AGAIN -> restartActivity()
            DialogCodes.CONFIRM_TRUSTED_CONTACT_PHONE_NUMBER -> addTrustedContactVM.requestAddTrustedContactOTP(
                requireContext(),
                args.phoneNumberValidatedToken,
                firstNameET.trim(),
                lastNameET.trim().ifEmpty { null },
                countryCodePicker.fullNumberWithPlus,
                emailIdET.trim().ifEmpty { null },
            )
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        if (dialogId == DialogCodes.CONFIRM_TRUSTED_CONTACT_PHONE_NUMBER) {
            addTrustedContactVM.onTrustedContactConfirmationDismissed()
        }
        // Default action is already handled
    }

    override fun onNetworkAvailable() {
        addTrustedContactVM.onInlineErrorDismissed()
    }

    override fun onCountrySelected() {
        addTrustedContactCCPVM.singUpAddTrustedContactSelectedCountryCode =
            countryCodePicker.selectedCountryNameCode
    }
}

private const val TAG = "AddTrustedContactFragment"
