package com.resoluttech.core.auth.signin

import android.app.PendingIntent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.common.api.ApiException
import com.google.android.material.button.MaterialButton
import com.google.gson.Gson
import com.hbb20.CountryCodePicker
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.signup.SignUpInVM
import com.resoluttech.core.db.KEY_SUPPORTED_COUNTRIES
import com.resoluttech.core.db.TinyDB
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.uicomponents.ResendOTPListener
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getHighlightedText
import com.resoluttech.core.utils.getPhoneNumberWithoutCountryCode
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.isPhoneNumberValid
import com.resoluttech.core.utils.parsePhoneCode
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.resoluttech.core.views.setDialogSize
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber
import java.util.UUID

class SignInFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    OTPReaderDialog.OTPListener,
    UnAuthenticatedBaseFragment.NetworkListener,
    CountryCodePicker.OnCountryChangeListener {

    private lateinit var appLogoIV: ImageView
    private lateinit var registerLabelTV: TextView
    private lateinit var registerContainer: View
    private lateinit var countryCodePicker: CountryCodePicker
    private lateinit var phoneNumberET: EditText
    private lateinit var verifyByOTPButton: MaterialButton
    private lateinit var otpReaderDialog: OTPReaderDialog
    private lateinit var phoneNumber: LeoPhoneNumber
    private lateinit var loadingContainer: View
    private lateinit var errorContainer: View
    private lateinit var retryButton: MaterialButton
    private lateinit var errorTV: TextView
    private lateinit var enterPhoneNumberContainer: View
    private lateinit var changeLanguageLL: LinearLayout
    private lateinit var languageName: TextView
    private lateinit var languageFlag: ImageView

    private val signInVM: SignInVM by navGraphViewModels(R.id.sign_in_nav)
    private val signUpInVM: SignUpInVM by activityViewModels()
    private val phoneNumberHintIntentResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            try {
                val phoneNumber = Identity.getSignInClient(requireActivity())
                    .getPhoneNumberFromIntent(result.data)
                getPhoneNumberWithoutCountryCode(phoneNumber)?.let(phoneNumberET::setText)
                val phoneCode = parsePhoneCode(phoneNumber)
                val supportedCountries = signUpInVM.supportedCountry
                if (phoneCode != null && supportedCountries.map(Country::phoneCode)
                        .contains("+$phoneCode")
                ) {
                    countryCodePicker.setCountryForPhoneCode(phoneCode)
                } else {
                    countryCodePicker.setCountryForNameCode(supportedCountries.first().code.code)
                }
            } catch (e: ApiException) {
                Timber.tag(TAG).e("Phone Number Hint failed")
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_sign_in, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupTextWatcher()
        signInVM.getSupportedCountries(requireContext(), signUpInVM.supportedCountry)
        setupSupportedCountries(signUpInVM.supportedCountry)
        handleStates()
        setupCountryCodePicker()
        setupRegistration()
        setupSignIn()
        setupBackPressed(requireActivity()::finish)
        setupRetryButton()
        networkListenerCallback = this
    }

    private fun setupSupportedCountries(supportedCountries: List<Country>) {
        if (supportedCountries.isNotEmpty()) {
            storeSupportedCountries(supportedCountries)
            signUpInVM.supportedCountry = supportedCountries.toMutableList()
            registerContainer.visibility = View.VISIBLE
            enterPhoneNumberContainer.visibility = View.VISIBLE
            appLogoIV.visibility = View.VISIBLE
            loadingContainer.visibility = View.GONE
            errorContainer.visibility = View.GONE
            var masterCountry = requireContext().getEmptyString()
            supportedCountries.forEach {
                masterCountry += "${it.code.code},"
            }
            countryCodePicker.setCustomMasterCountries(masterCountry)
            countryCodePicker.setCountryForNameCode(
                signUpInVM.signUpSelectedCountryCode ?: supportedCountries.first().code.code,
            )
        }
        countryCodePicker.setDialogSize(requireContext())
    }

    private fun setupTextWatcher() {
        phoneNumberET.addTextChangedListener {
            validInput()
        }
    }

    private fun validInput() {
        if (countryCodePicker.fullNumberWithPlus.isPhoneNumberValid()) {
            verifyByOTPButton.enable()
            verifyByOTPButton.addContentDescriptionString(
                R.string.axSignInButtonLabel,
                requireContext(),
            )
        } else {
            verifyByOTPButton.disable()
            verifyByOTPButton.addContentDescriptionString(
                R.string.axSignUpButtonDisabledMobileNumber,
                requireContext(),
            )
        }
    }

    private fun setupPhoneNumberHint() {
        val request: GetPhoneNumberHintIntentRequest =
            GetPhoneNumberHintIntentRequest.builder().build()

        Identity.getSignInClient(requireActivity())
            .getPhoneNumberHintIntent(request)
            .addOnSuccessListener { result: PendingIntent ->
                try {
                    phoneNumberHintIntentResultLauncher.launch(
                        IntentSenderRequest.Builder(result).build(),
                    )
                } catch (e: ApiException) {
                    Timber.tag(TAG)
                        .e("Launching the PendingIntent failed")
                }
            }
            .addOnFailureListener {
                Timber.tag(TAG).e("Phone Number Hint failed")
            }
    }

    override fun onResume() {
        super.onResume()
        hideToolbar()
    }

    private fun setupSignIn() {
        verifyByOTPButton.setOnClickListener {
            if (!countryCodePicker.fullNumberWithPlus.isPhoneNumberValid()) {
                signInVM.onInvalidPhoneNumberInput(
                    getString(R.string.alertTitleInvalidPhoneNumber),
                    getString(R.string.alertMessageInvalidPhoneNumber),
                )
                return@setOnClickListener
            } else {
                phoneNumber = LeoPhoneNumber(countryCodePicker.fullNumberWithPlus)
                signInVM.onSignInTapped(
                    phoneNumber,
                )
            }
        }
    }

    private fun handleStates() {
        signInVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(state: SignInScreenState) {
        when (state) {
            is SignInScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is SignInScreenState.Error -> {
                handleErrorState(state.error)
            }
            is SignInScreenState.Loading -> {
                handleLoadingState()
            }
            is SignInScreenState.FullScreenLoading -> {
                handleFullScreenLoading()
            }
            is SignInScreenState.OTPConfirmed -> {
                handleOTPConfirmedState(state.response)
            }
            is SignInScreenState.OTPGenerated -> {
                handleOTPGeneratedState(state.response)
            }
            is SignInScreenState.ResendOTPSuccess -> {
                handleOTPResendSuccessState(state.response)
            }
            is SignInScreenState.ConfirmPhoneNumber -> {
                handleConfirmPhoneNumberState(state.phoneNumber)
            }
            is SignInScreenState.FullScreenError -> {
                handleFullScreenErrorState(state.error)
            }
            is SignInScreenState.SupportedCountriesAvailable -> {
                handleSupportedCurrenciesAvailableState(state.supportedCountries)
            }
        }
    }

    private fun handleSupportedCurrenciesAvailableState(supportedCountries: List<Country>) {
        storeSupportedCountries(supportedCountries)
        signUpInVM.supportedCountry = supportedCountries.toMutableList()
        showViewForState(enterPhoneNumberContainer)
        registerLabelTV.enable()
        var masterCountry = requireContext().getEmptyString()
        supportedCountries.forEach {
            masterCountry += "${it.code.code},"
        }
        countryCodePicker.setCustomMasterCountries(masterCountry)
        countryCodePicker.setCountryForNameCode(
            signUpInVM.signInSelectedCountryCode ?: supportedCountries.first().code.code,
        )
        if (phoneNumberET.text.isBlank()) {
            setupPhoneNumberHint()
        }
        signInVM.onAlertDialogDismissed()
    }

    private fun storeSupportedCountries(supportedCountries: List<Country>) {
        val jsonString = Gson().toJson(supportedCountries)
        TinyDB.storeWithKey(KEY_SUPPORTED_COUNTRIES, jsonString)
    }

    private fun handleFullScreenErrorState(error: UIError) {
        showViewForState(errorContainer)
        errorTV.text = error.errorMessage
    }

    private fun handleConfirmPhoneNumberState(phoneNumber: LeoPhoneNumber) {
        registerLabelTV.disable()
        showAlertDialog(
            title = getString(R.string.alertTitleConfirmMobileNumber),
            message = getString(R.string.alertMessageConfirmMobileNumberAndroid, phoneNumber.getFormattedPhoneNumber()),
            dialogId = DialogCodes.CONFIRM_PHONE_NUMBER_DIALOG_ID,
            positiveActionLabel = getString(R.string.alertActionYes),
            negativeActionLabel = getString(R.string.alertActionNo),
        )
    }

    private fun handleOTPResendSuccessState(response: OTPResponse) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onSuccessfulResendOTP(response)
        } else {
            handleOTPGeneratedState(response)
        }
    }

    private fun handleOTPConfirmedState(response: ConfirmSignInOTPRPC.Response) {
        signInVM.nextStepEnterPassword(
            findNavController(),
            phoneNumber,
            response.phoneNumberValidatedToken,
        )
    }

    private fun handleOTPGeneratedState(response: OTPResponse) {
        registerLabelTV.disable()
        otpReaderDialog = OTPReaderDialog.newInstance(response)
        otpReaderDialog.setArguments(false)
        otpReaderDialog.show(childFragmentManager, OTPReaderDialog.TAG)
        dismissProgressDialog()
    }

    private fun handleFullScreenLoading() {
        showViewForState(loadingContainer)
    }

    private fun handleLoadingState() {
        registerLabelTV.disable()
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(error: UIError) {
        dismissProgressDialog()
        when (error.type) {
            ErrorType.SNACKBAR -> {
                registerLabelTV.enable()
                showInlineErrorSnackBar(
                    error.errorMessage,
                    requireView(),
                    signInVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                registerLabelTV.disable()
                if (::otpReaderDialog.isInitialized) {
                    otpReaderDialog.dismiss()
                    registerLabelTV.enable()
                }
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    DialogCodes.SIGN_IN_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleAcceptInputState() {
        registerLabelTV.enable()
        dismissProgressDialog()
        showViewForState(enterPhoneNumberContainer)
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
        } else {
            Timber.tag(TAG).i("The OTP Reader dialog is hidden.")
        }
    }

    private fun showViewForState(stateView: View) {
        when (stateView) {
            enterPhoneNumberContainer -> {
                errorContainer.visibility = View.GONE
                loadingContainer.visibility = View.GONE
                registerContainer.visibility = View.VISIBLE
                enterPhoneNumberContainer.visibility = View.VISIBLE
                appLogoIV.visibility = View.VISIBLE
            }
            errorContainer -> {
                registerContainer.visibility = View.GONE
                loadingContainer.visibility = View.GONE
                errorContainer.visibility = View.VISIBLE
                enterPhoneNumberContainer.visibility = View.GONE
                appLogoIV.visibility = View.GONE
                changeLanguageLL.visibility = View.GONE
            }
            loadingContainer -> {
                registerContainer.visibility = View.GONE
                enterPhoneNumberContainer.visibility = View.GONE
                errorContainer.visibility = View.GONE
                appLogoIV.visibility = View.GONE
                changeLanguageLL.visibility = View.GONE
                loadingContainer.visibility = View.VISIBLE
            }
        }
    }

    private fun setupCountryCodePicker() {
        countryCodePicker.registerCarrierNumberEditText(phoneNumberET)
        countryCodePicker.setOnCountryChangeListener(this)
        changeDialogTitle()
    }

    private fun changeDialogTitle() {
        countryCodePicker.setCustomDialogTextProvider(object :
            CountryCodePicker.CustomDialogTextProvider {
            override fun getCCPDialogTitle(
                language: CountryCodePicker.Language,
                defaultTitle: String,
            ): String {
                return getString(R.string.countryCodePickerTitle)
            }

            override fun getCCPDialogSearchHintText(
                language: CountryCodePicker.Language,
                defaultSearchHintText: String,
            ): String {
                return defaultSearchHintText
            }

            override fun getCCPDialogNoResultACK(
                language: CountryCodePicker.Language,
                defaultNoResultACK: String,
            ): String {
                return defaultNoResultACK
            }
        })
    }

    private fun setupRegistration() {
        registerLabelTV.text = requireContext().getHighlightedText(
            startingString = getString(R.string.signUpInDontHaveAccountLabel),
            highlightedString = getString(R.string.signUpInSignUp),
            startStringColor = R.color.descriptionTextColor,
            highlightedStringColor = R.color.highLightedTextColor,
        )

        registerLabelTV.setOnClickListener {
            signInVM.onRegisterTapped(findNavController())
        }
    }

    private fun initViews(view: View) {
        view.apply {
            appLogoIV = findViewById(R.id.signin_logo_iv)
            registerLabelTV = findViewById(R.id.register_tv)
            registerContainer = findViewById(R.id.register_container)
            countryCodePicker = findViewById(R.id.country_code_picker)
            phoneNumberET = findViewById(R.id.phone_number_et)
            verifyByOTPButton = findViewById(R.id.sign_in_bt)
            loadingContainer = findViewById(R.id.loading_container)
            errorContainer = findViewById(R.id.error_container)
            retryButton = findViewById(R.id.retry_button)
            errorTV = findViewById(R.id.error_tv)
            enterPhoneNumberContainer = findViewById(R.id.enter_number_container)
            changeLanguageLL = findViewById(R.id.select_language_ll)
            languageFlag = findViewById(R.id.language_flag)
            languageName = findViewById(R.id.language_name)
            verifyByOTPButton.disable()
            verifyByOTPButton.addContentDescriptionString(
                R.string.axSignUpButtonDisabledMobileNumber,
                requireContext(),
            )
        }
    }

    private fun setupLanguageSelector() {
        changeLanguageLL.setOnClickListener {
            findNavController().navigate(R.id.action_signInFragment_to_selectLanguage)
        }
        if (LocaleManager.getCurrentLocale(requireContext()).name == SupportedLocale.EN_US.name) {
            languageName.text = getString(R.string.languageSelectorOptionEnglish)
            languageFlag.setImageResource(R.drawable.ic_flag_uk)
        } else {
            languageName.text = getString(R.string.languageSelectorOptionNyanja)
            languageFlag.setImageResource(R.drawable.ic_flag_malawi)
        }
    }

    private fun setupRetryButton() {
        retryButton.setOnClickListener {
            signInVM.getSupportedCountries(requireContext(), signUpInVM.supportedCountry)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.CONFIRM_PHONE_NUMBER_DIALOG_ID -> {
                if (!::phoneNumber.isInitialized) {
                    phoneNumber = LeoPhoneNumber(countryCodePicker.fullNumberWithPlus)
                }
                signInVM.onPhoneNumberConfirmed(requireContext(), phoneNumber)
            }
            else -> {
                signInVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        signInVM.onAlertDialogDismissed()
    }

    override fun onResendTapped(otpId: UUID?) {
        signInVM.onOTPResendTapped(requireContext(), otpId)
    }

    override fun onOTPReadCancelled() {
        if (::otpReaderDialog.isInitialized) {
            signInVM.onOTPReadCancelled(otpReaderDialog)
            registerLabelTV.enable()
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onOTPReadFailed(e: OTPFailureException) {
        // OTP failure happens due to timeout so we are just logging it.
        registerLabelTV.enable()
        Timber.tag(TAG).e(message = e.stackTraceToString())
    }

    override fun onSuccessfulOTPRead(otp: String, otpId: UUID?) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
            dismissProgressDialog()
            signInVM.onSuccessfulOTPRead(
                requireContext(),
                otp,
                otpId,
                LeoPhoneNumber(countryCodePicker.fullNumberWithPlus),
            )
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onNetworkAvailable() {
        signInVM.getSupportedCountries(requireContext(), signUpInVM.supportedCountry)
        setupSupportedCountries(signUpInVM.supportedCountry)
    }

    override fun onCountrySelected() {
        signUpInVM.signInSelectedCountryCode = countryCodePicker.selectedCountryNameCode
    }
}

private const val TAG = "SignInFragment"
