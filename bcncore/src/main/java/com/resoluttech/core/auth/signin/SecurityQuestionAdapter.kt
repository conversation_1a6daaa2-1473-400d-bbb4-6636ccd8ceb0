package com.resoluttech.core.auth.signin

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class SecurityQuestionAdapter(private val items: List<QuestionListItem>, private val context: Context) :
    AbstractTextDropDownAdapter<SecurityQuestionAdapter.QuestionListItem>(items) {

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: QuestionViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_dropdown_item, parent, false)
            viewHolder =
                QuestionViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as QuestionViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is QuestionListItem.Question -> {
                    textView.visibility = View.VISIBLE
                    val gender = item.question
                    textView.text = gender
                }
                is QuestionListItem.Header -> textView.visibility = View.GONE
            }
        }
        return view
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: QuestionViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.question_spinner_item_view, parent, false)
            viewHolder =
                QuestionViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as QuestionViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is QuestionListItem.Question -> {
                    textView.text = item.question
                    textView.setTextColor(context.getColor(R.color.titleTextColor))
                    textView.typeface = Typeface.create(textView.typeface, Typeface.NORMAL)
                }
                is QuestionListItem.Header -> {
                    textView.setTextColor(context.getColor(R.color.titleTextColor))
                    textView.text = item.displayLabel
                    textView.typeface = Typeface.create(textView.typeface, Typeface.BOLD)
                }
            }
        }
        return view
    }

    private class QuestionViewHolder(view: View) {
        var textView: TextView = view.findViewById(
            R.id.dropdown_item_text_view,
        )
    }

    sealed class QuestionListItem {
        data class Question(val question: String) : QuestionListItem()
        data class Header(val displayLabel: String) : QuestionListItem()
    }
}
