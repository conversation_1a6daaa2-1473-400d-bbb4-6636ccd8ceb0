package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.getCurrentLocale
import com.resoluttech.core.utils.getHighlightedText
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import java.util.UUID

class SignInSecurityQuestionsFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    UnAuthenticatedBaseFragment.NetworkListener {

    private lateinit var questionsRV: RecyclerView
    private lateinit var submitButton: MaterialButton
    private lateinit var adapter: SecurityQuestionsRVAdapter
    private lateinit var trustedContactTV: TextView

    private val signInSecurityQuestionsVM: SignInSecurityQuestionsVM by navGraphViewModels(R.id.sign_in_nav)
    private val args: SignInSecurityQuestionsFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_security_questions, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.forgotPasswordAnswerSecurityTitle))
        initViews(view)
        setupSecurityQuestionsStateListener()
        setupSubmitAction()
        networkListenerCallback = this
        signInSecurityQuestionsVM.getSecurityQuestions(requireContext())
    }

    private fun setupSubmitAction() {
        submitButton.setOnClickListener {
            val answeredQuestions = adapter.getAllQuestionAnswer()
            val locale = requireContext().getCurrentLocale()
            signInSecurityQuestionsVM.onSubmitTapped(
                requireContext(),
                answeredQuestions,
                findNavController(),
                locale,
            )
        }
    }

    private fun setupSecurityQuestionsStateListener() {
        signInSecurityQuestionsVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun reactToState(state: SignInSecurityQuestionsScreenState) {
        when (state) {
            is SignInSecurityQuestionsScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
            is SignInSecurityQuestionsScreenState.Data -> {
                handleDataState(state.questions)
            }
            is SignInSecurityQuestionsScreenState.Error -> {
                handleErrorState(state.uiError)
            }
            is SignInSecurityQuestionsScreenState.Loading -> {
                handleLoadingState()
            }
            is SignInSecurityQuestionsScreenState.QuestionsAnsweredSignIn -> {
                handleQuestionAnsweredState(state.resetToken)
            }
        }
    }

    private fun handleQuestionAnsweredState(resetToken: UUID) {
        dismissProgressDialog()
        signInSecurityQuestionsVM.onQuestionsAnswered(
            findNavController(),
            resetToken,
            requireContext(),
        )
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    uiError.errorMessage,
                    requireView(),
                    signInSecurityQuestionsVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.SECURITY_QUESTIONS_DIALOG_CODE,
                    forceOutUserDestination = ForceOutUserDestination.SIGN_UP_IN,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleDataState(questions: List<SecurityQuestion>) {
        setQuestionsAdapter(questions)
    }

    private fun setQuestionsAdapter(questions: List<SecurityQuestion>) {
        dismissProgressDialog()
        adapter = SecurityQuestionsRVAdapter(requireContext(), questions)
        questionsRV.adapter = adapter
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun initViews(view: View) {
        view.apply {
            submitButton = findViewById(R.id.nextButton)
            questionsRV = findViewById(R.id.security_question_rv)
            trustedContactTV = findViewById(R.id.trusted_contacts_tv)
            trustedContactTV.text = requireContext().getHighlightedText(
                startingString = requireContext().getString(R.string.forgotPasswordChooseTrustedContactMessage),
                highlightedString = requireContext().getString(R.string.forgotPasswordChooseTrustedContactTitle),
                startStringColor = R.color.subtitleTextColor,
                highlightedStringColor = R.color.colorPrimaryLight,
            )
            trustedContactTV.setOnClickListener {
                val action =
                    SignInSecurityQuestionsFragmentDirections.actionSecurityQuestionsFragmentToSignInTrustedContactFragment(
                        args.phoneNumber,
                        UUID.fromString(args.phoneNumberValidatedToken),
                    )
                findNavController().navigate(action)
            }
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        signInSecurityQuestionsVM.onErrorDismissed(
            dialogId,
            findNavController(),
            this::restartActivity,
        )
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        signInSecurityQuestionsVM.getSecurityQuestions(requireContext())
    }
}
