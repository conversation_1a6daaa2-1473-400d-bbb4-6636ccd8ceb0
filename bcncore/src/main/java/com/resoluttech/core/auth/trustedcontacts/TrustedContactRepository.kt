package com.resoluttech.core.auth.trustedcontacts

import com.resoluttech.bcn.signUpIn.ConfirmAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.RemoveTrustedContactRPC
import com.resoluttech.bcn.signUpIn.RequestAddTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendAddTrustedContactOTPRPC
import com.resoluttech.bcn.types.Otp
import com.resoluttech.bcn.types.TrustedContact
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

internal class TrustedContactRepository {

    private val requestAddTrustedContactOTPRPC: RequestAddTrustedContactOTPRPC by <PERSON>inJavaComponent.inject(
        RequestAddTrustedContactOTPRPC::class.java,
    )

    private val resendAddTrustedContactOTPRPC: ResendAddTrustedContactOTPRPC by KoinJavaComponent.inject(
        ResendAddTrustedContactOTPRPC::class.java,
    )

    private val confirmAddTrustedContactOTPRPC: ConfirmAddTrustedContactOTPRPC by KoinJavaComponent.inject(
        ConfirmAddTrustedContactOTPRPC::class.java,
    )

    private val removeTrustedContactRPC: RemoveTrustedContactRPC by KoinJavaComponent.inject(
        RemoveTrustedContactRPC::class.java,
    )

    private val getTrustedContactsRPC: GetTrustedContactsRPC by KoinJavaComponent.inject(
        GetTrustedContactsRPC::class.java,
    )

    private val getTrustedContactValidatedTokenRPC: GetTrustedContactValidatedTokenRPC by KoinJavaComponent.inject(
        GetTrustedContactValidatedTokenRPC::class.java,
    )

    suspend fun requestAddTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        trustedContact: TrustedContact,
    ): LeoRPCResult<RequestAddTrustedContactOTPRPC.Response, RequestAddTrustedContactOTPRPC.Error> {
        return requestAddTrustedContactOTPRPC.execute(
            RequestAddTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                trustedContact,
            ),
        )
    }

    suspend fun resendAddTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        otpId: UUID,
    ): LeoRPCResult<ResendAddTrustedContactOTPRPC.Response, ResendAddTrustedContactOTPRPC.Error> {
        return resendAddTrustedContactOTPRPC.execute(
            ResendAddTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                otpId,
            ),
        )
    }

    suspend fun confirmAddTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        otpId: UUID,
        otp: Otp,
    ): LeoRPCResult<ConfirmAddTrustedContactOTPRPC.Response, ConfirmAddTrustedContactOTPRPC.Error> {
        return confirmAddTrustedContactOTPRPC.execute(
            ConfirmAddTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                otpId,
                otp,
            ),
        )
    }

    suspend fun removeTrustedContactRPC(
        phoneNumberValidatedToken: UUID,
        trustedContactId: UUID,
    ): LeoRPCResult<RemoveTrustedContactRPC.Response, RemoveTrustedContactRPC.Error> {
        return removeTrustedContactRPC.execute(
            RemoveTrustedContactRPC.Request(
                phoneNumberValidatedToken,
                trustedContactId,
            ),
        )
    }

    suspend fun getTrustedContactsRPC(
        phoneNumberValidatedToken: UUID,
    ): LeoRPCResult<GetTrustedContactsRPC.Response, GetTrustedContactsRPC.Error> {
        return getTrustedContactsRPC.execute(
            GetTrustedContactsRPC.Request(
                phoneNumberValidatedToken,
            ),
        )
    }

    suspend fun getTrustedContactValidatedTokenRPC(
        phoneNumberValidatedToken: UUID,
    ): LeoRPCResult<GetTrustedContactValidatedTokenRPC.Response, GetTrustedContactValidatedTokenRPC.Error> {
        return getTrustedContactValidatedTokenRPC.execute(
            GetTrustedContactValidatedTokenRPC.Request(
                phoneNumberValidatedToken,
            ),
        )
    }
}
