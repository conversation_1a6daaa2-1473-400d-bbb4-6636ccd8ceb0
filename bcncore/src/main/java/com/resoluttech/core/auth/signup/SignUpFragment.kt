package com.resoluttech.core.auth.signup

import android.app.PendingIntent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.common.api.ApiException
import com.google.android.material.button.MaterialButton
import com.google.gson.Gson
import com.hbb20.CountryCodePicker
import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPC
import com.resoluttech.bcn.types.Country
import com.resoluttech.bcncore.R
import com.resoluttech.core.db.KEY_SUPPORTED_COUNTRIES
import com.resoluttech.core.db.TinyDB
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.OTPReaderDialog
import com.resoluttech.core.uicomponents.OTPResponse
import com.resoluttech.core.uicomponents.ResendOTPListener
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.CONFIRM_PHONE_NUMBER_DIALOG_ID
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getHighlightedText
import com.resoluttech.core.utils.getPhoneNumberWithoutCountryCode
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.isPhoneNumberValid
import com.resoluttech.core.utils.parsePhoneCode
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment
import com.resoluttech.core.views.setDialogSize
import com.suryadigital.leo.libui.otptextfield.OTPFailureException
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber
import java.util.UUID

class SignUpFragment :
    UnAuthenticatedBaseFragment(),
    AlertDialog.ActionListener,
    OTPReaderDialog.OTPListener,
    UnAuthenticatedBaseFragment.NetworkListener,
    CountryCodePicker.OnCountryChangeListener {

    private lateinit var signInLabelTV: TextView
    private lateinit var countryCodePicker: CountryCodePicker
    private lateinit var phoneNumberET: EditText
    private lateinit var phoneNumber: LeoPhoneNumber
    private lateinit var verifyButton: MaterialButton
    private lateinit var ageCB: CheckBox
    private lateinit var otpReaderDialog: OTPReaderDialog
    private lateinit var enterPhoneNumberContainer: LinearLayout
    private lateinit var signInContainer: LinearLayout
    private lateinit var loadingContainer: LinearLayout
    private lateinit var errorContainer: LinearLayout
    private lateinit var retryButton: MaterialButton
    private lateinit var errorTV: TextView
    private lateinit var appLogoIV: ImageView
    private lateinit var changeLanguageLL: LinearLayout
    private lateinit var languageName: TextView
    private lateinit var languageFlag: ImageView

    private val signUpVM: SignUpVM by navGraphViewModels(R.id.sign_up_nav)
    private val signUpInVM: SignUpInVM by activityViewModels()
    private val phoneNumberHintIntentResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            try {
                val phoneNumber = Identity.getSignInClient(requireActivity())
                    .getPhoneNumberFromIntent(result.data)
                getPhoneNumberWithoutCountryCode(phoneNumber)?.let(phoneNumberET::setText)
                val phoneCode = parsePhoneCode(phoneNumber)
                val supportedCountries = signUpInVM.supportedCountry
                if (phoneCode != null && supportedCountries.map(Country::phoneCode)
                        .contains("+$phoneCode")
                ) {
                    countryCodePicker.setCountryForPhoneCode(phoneCode)
                } else {
                    countryCodePicker.setCountryForNameCode(supportedCountries.first().code.code)
                }
            } catch (e: ApiException) {
                Timber.tag(TAG).e("Phone Number Hint failed")
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_sign_up, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        setupTextWatcher()
        setupAgeCB()
        signUpVM.init(requireContext(), signUpInVM.supportedCountry)
        setupSupportedCountries(signUpInVM.supportedCountry)
        handleStates()
        setupCountryCodePicker()
        setupRegistration()
        setupVerifyButton()
        setupBackPressed(requireActivity()::finish)
        setupRetryButton()
        networkListenerCallback = this
    }

    private fun setupSupportedCountries(supportedCountries: List<Country>) {
        if (supportedCountries.isNotEmpty()) {
            storeSupportedCountries(supportedCountries)
            signUpInVM.supportedCountry = supportedCountries.toMutableList()
            signInContainer.visibility = View.VISIBLE
            enterPhoneNumberContainer.visibility = View.VISIBLE
            appLogoIV.visibility = View.VISIBLE
            loadingContainer.visibility = View.GONE
            errorContainer.visibility = View.GONE
            var masterCountry = requireContext().getEmptyString()
            supportedCountries.forEach {
                masterCountry += "${it.code.code},"
            }
            countryCodePicker.setCustomMasterCountries(masterCountry)
            countryCodePicker.setCountryForNameCode(
                signUpInVM.signUpSelectedCountryCode ?: supportedCountries.first().code.code,
            )
        }
        countryCodePicker.setDialogSize(requireContext())
    }

    private fun setupPhoneNumberHint() {
        val request: GetPhoneNumberHintIntentRequest =
            GetPhoneNumberHintIntentRequest.builder().build()

        Identity.getSignInClient(requireActivity())
            .getPhoneNumberHintIntent(request)
            .addOnSuccessListener { result: PendingIntent ->
                try {
                    phoneNumberHintIntentResultLauncher.launch(
                        IntentSenderRequest.Builder(result).build(),
                    )
                } catch (e: ApiException) {
                    Timber.tag(TAG).e("Launching the PendingIntent failed")
                }
            }
            .addOnFailureListener {
                Timber.tag(TAG).e("Phone Number Hint failed")
            }
    }

    override fun onResume() {
        super.onResume()
        hideToolbar()
    }

    private fun setupTextWatcher() {
        phoneNumberET.addTextChangedListener {
            validInput()
        }
    }

    private fun validInput() {
        if (ageCB.isChecked && countryCodePicker.fullNumberWithPlus.isPhoneNumberValid()) {
            verifyButton.enable()
            verifyButton.addContentDescriptionString(
                R.string.signUpInSignUp,
                requireContext(),
            )
        } else {
            verifyButton.disable()
            if (!countryCodePicker.fullNumberWithPlus.isPhoneNumberValid()) {
                verifyButton.addContentDescriptionString(
                    R.string.axSignUpButtonDisabledMobileNumber,
                    requireContext(),
                )
            } else {
                verifyButton.addContentDescriptionString(
                    R.string.axSignUpButtonDisabledAgeAgreement,
                    requireContext(),
                )
            }
        }
    }

    private fun setupAgeCB() {
        if (signUpVM.ageCBIsChecked) {
            ageCB.isChecked = true
        }
        ageCB.setOnClickListener {
            signUpVM.ageCBIsChecked = ageCB.isChecked
            validInput()
        }
    }

    private fun setupVerifyButton() {
        verifyButton.setOnClickListener {
            phoneNumber = LeoPhoneNumber(countryCodePicker.fullNumberWithPlus)
            signUpVM.onVerifyOTPTapped(
                phoneNumber,
            )
        }
    }

    private fun handleStates() {
        signUpVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun reactToState(state: SignUpScreenState) {
        when (state) {
            is SignUpScreenState.AcceptInput -> {
                handleAcceptInput()
            }
            is SignUpScreenState.Error -> {
                handleErrorState(state.error)
            }
            is SignUpScreenState.InlineLoading -> {
                handleInlineLoading()
            }
            is SignUpScreenState.OTPConfirmed -> {
                handleOTPConfirmedState(state.response)
            }
            is SignUpScreenState.OTPGenerated -> {
                handleOTPGeneratedState(state.response)
            }
            is SignUpScreenState.ResendOTPSuccess -> {
                handleOTPResendSuccessState(state.response)
            }
            is SignUpScreenState.Loading -> {
                handleLoadingState()
            }
            is SignUpScreenState.SupportedCountriesAvailable -> {
                handleSupportedCurrenciesAvailableState(state.supportedCountries)
            }
            is SignUpScreenState.FullScreenError -> {
                handleFullScreenErrorState(state.error)
            }
            is SignUpScreenState.ConfirmPhoneNumber -> {
                handleConfirmPhoneNumberState(state.phoneNumber)
            }
        }
    }

    private fun handleOTPConfirmedState(response: ConfirmSignUpOTPRPC.Response) {
        dismissProgressDialog()
        signUpVM.setupTrustedContact(
            findNavController(),
            response,
            phoneNumber,
        )
    }

    private fun handleConfirmPhoneNumberState(phoneNumber: LeoPhoneNumber) {
        signInLabelTV.disable()
        showAlertDialog(
            title = getString(R.string.alertTitleConfirmMobileNumber),
            message = getString(R.string.alertMessageConfirmMobileNumberAndroid, phoneNumber.getFormattedPhoneNumber()),
            dialogId = CONFIRM_PHONE_NUMBER_DIALOG_ID,
            positiveActionLabel = getString(R.string.alertActionYes),
            negativeActionLabel = getString(R.string.alertActionNo),
        )
    }

    private fun handleFullScreenErrorState(error: UIError) {
        appLogoIV.visibility = View.GONE
        changeLanguageLL.visibility = View.GONE
        loadingContainer.visibility = View.GONE
        errorContainer.visibility = View.VISIBLE
        signInContainer.visibility = View.GONE
        enterPhoneNumberContainer.visibility = View.GONE
        errorTV.text = error.errorMessage
    }

    private fun handleLoadingState() {
        appLogoIV.visibility = View.GONE
        changeLanguageLL.visibility = View.GONE
        signInContainer.visibility = View.GONE
        enterPhoneNumberContainer.visibility = View.GONE
        loadingContainer.visibility = View.VISIBLE
        errorContainer.visibility = View.GONE
    }

    private fun setupRetryButton() {
        retryButton.setOnClickListener {
            signUpVM.init(requireContext(), signUpInVM.supportedCountry)
        }
    }

    private fun handleSupportedCurrenciesAvailableState(supportedCountries: List<Country>) {
        storeSupportedCountries(supportedCountries)
        signUpInVM.supportedCountry = supportedCountries.toMutableList()
        signInContainer.visibility = View.VISIBLE
        enterPhoneNumberContainer.visibility = View.VISIBLE
        appLogoIV.visibility = View.VISIBLE
        loadingContainer.visibility = View.GONE
        errorContainer.visibility = View.GONE
        var masterCountry = requireContext().getEmptyString()
        supportedCountries.forEach {
            masterCountry += "${it.code.code},"
        }
        countryCodePicker.setCustomMasterCountries(masterCountry)
        countryCodePicker.setCountryForNameCode(
            signUpInVM.signUpSelectedCountryCode ?: supportedCountries.first().code.code,
        )
        if (phoneNumberET.text.isBlank()) {
            setupPhoneNumberHint()
        }
    }

    private fun storeSupportedCountries(supportedCountries: List<Country>) {
        val jsonString = Gson().toJson(supportedCountries)
        TinyDB.storeWithKey(KEY_SUPPORTED_COUNTRIES, jsonString)
    }

    private fun handleOTPGeneratedState(response: OTPResponse) {
        signInLabelTV.disable()
        otpReaderDialog = OTPReaderDialog.newInstance(response)
        otpReaderDialog.setArguments(false)
        otpReaderDialog.show(childFragmentManager, OTPReaderDialog.TAG)
        dismissProgressDialog()
    }

    private fun handleOTPResendSuccessState(response: OTPResponse) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isAdded) {
            (otpReaderDialog as ResendOTPListener).onSuccessfulResendOTP(response)
        } else {
            handleOTPGeneratedState(response)
        }
    }

    private fun handleInlineLoading() {
        signInLabelTV.disable()
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleAcceptInput() {
        signInLabelTV.enable()
        dismissProgressDialog()
    }

    private fun handleErrorState(error: UIError) {
        dismissProgressDialog()
        signInLabelTV.enable()
        when (error.type) {
            ErrorType.SNACKBAR -> {
                signInLabelTV.enable()
                showInlineErrorSnackBar(
                    error.errorMessage,
                    requireView(),
                    signUpVM::onInlineErrorDismissed,
                )
            }
            ErrorType.DIALOG -> {
                signInLabelTV.disable()
                if (::otpReaderDialog.isInitialized) {
                    otpReaderDialog.dismiss()
                    signInLabelTV.enable()
                }
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    DialogCodes.SIGN_UP_ERROR_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun setupCountryCodePicker() {
        countryCodePicker.registerCarrierNumberEditText(phoneNumberET)
        changeDialogTitle()
        countryCodePicker.setOnCountryChangeListener(this)
    }

    private fun changeDialogTitle() {
        countryCodePicker.setCustomDialogTextProvider(object :
            CountryCodePicker.CustomDialogTextProvider {
            override fun getCCPDialogTitle(
                language: CountryCodePicker.Language,
                defaultTitle: String,
            ): String {
                return getString(R.string.countryCodePickerTitle)
            }

            override fun getCCPDialogSearchHintText(
                language: CountryCodePicker.Language,
                defaultSearchHintText: String,
            ): String {
                return defaultSearchHintText
            }

            override fun getCCPDialogNoResultACK(
                language: CountryCodePicker.Language,
                defaultNoResultACK: String,
            ): String {
                return defaultNoResultACK
            }
        })
    }

    private fun setupRegistration() {
        signInLabelTV.text = requireContext().getHighlightedText(
            getString(R.string.signUpInAlreadyHaveAccountLabel),
            getString(R.string.signUpInSignIn),
            startStringColor = R.color.descriptionTextColor,
            highlightedStringColor = R.color.highLightedTextColor,
        )
        signInLabelTV.setOnClickListener {
            signUpVM.onSignInTapped(findNavController())
        }
    }

    private fun initViews(view: View) {
        view.apply {
            appLogoIV = findViewById(R.id.logo)
            signInLabelTV = findViewById(R.id.sign_in_label_tv)
            countryCodePicker = findViewById(R.id.country_code_picker)
            phoneNumberET = findViewById(R.id.phone_number_et)
            verifyButton = findViewById(R.id.verify_bt)
            ageCB = findViewById(R.id.age_cb)
            enterPhoneNumberContainer = findViewById(R.id.enter_phonenumber_contaier)
            signInContainer = findViewById(R.id.sign_in_container)
            loadingContainer = findViewById(R.id.loading_container)
            errorContainer = findViewById(R.id.error_container)
            retryButton = findViewById(R.id.retry_button)
            errorTV = findViewById(R.id.error_tv)
            changeLanguageLL = findViewById(R.id.select_language_ll)
            languageFlag = findViewById(R.id.language_flag)
            languageName = findViewById(R.id.language_name)
            verifyButton.disable()
        }
    }

    private fun setupLanguageSelector() {
        changeLanguageLL.setOnClickListener {
            findNavController().navigate(R.id.action_signUpFragment_to_selectLanguage)
        }
        if (LocaleManager.getCurrentLocale(requireContext()).name == SupportedLocale.EN_US.name) {
            languageName.text = getString(R.string.languageSelectorOptionEnglish)
            languageFlag.setImageResource(R.drawable.ic_flag_uk)
        } else {
            languageName.text = getString(R.string.languageSelectorOptionNyanja)
            languageFlag.setImageResource(R.drawable.ic_flag_malawi)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CONFIRM_PHONE_NUMBER_DIALOG_ID -> {
                if (!::phoneNumber.isInitialized) {
                    phoneNumber = LeoPhoneNumber(countryCodePicker.fullNumberWithPlus)
                }
                signUpVM.onPhoneNumberConfirmed(phoneNumber, requireContext())
            }
            else -> {
                signUpVM.onInlineErrorDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        signUpVM.onAlertDialogDismissed()
    }

    override fun onResendTapped(otpId: UUID?) {
        signUpVM.onOTPResendTapped(requireContext(), otpId)
    }

    override fun onOTPReadCancelled() {
        if (::otpReaderDialog.isInitialized) {
            otpReaderDialog.dismiss()
            signUpVM.onOTPReadCancelled()
            signInLabelTV.enable()
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onOTPReadFailed(e: OTPFailureException) {
        // OTP failure happens due to timeout so we are just logging it.
        signInLabelTV.enable()
        Timber.tag(TAG).e(message = e.stackTraceToString())
    }

    override fun onSuccessfulOTPRead(otp: String, otpId: UUID?) {
        if (::otpReaderDialog.isInitialized && otpReaderDialog.isVisible) {
            otpReaderDialog.dismiss()
            dismissProgressDialog()
            showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
            signUpVM.onSuccessfulOTPRead(
                requireContext(),
                otp,
                otpId,
                LeoPhoneNumber(countryCodePicker.fullNumberWithPlus),
            )
        } else {
            Timber.tag(TAG).d("Dialog is already hidden.")
        }
    }

    override fun onNetworkAvailable() {
        signUpVM.init(requireContext(), signUpInVM.supportedCountry)
        setupSupportedCountries(signUpInVM.supportedCountry)
    }

    override fun onCountrySelected() {
        signUpInVM.signUpSelectedCountryCode = countryCodePicker.selectedCountryNameCode
    }
}

private const val TAG = "SignUpFragment"
