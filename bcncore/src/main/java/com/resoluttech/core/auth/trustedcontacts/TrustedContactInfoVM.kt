package com.resoluttech.core.auth.trustedcontacts

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.GetTrustedContactValidatedTokenRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.RemoveTrustedContactRPC
import com.resoluttech.bcn.types.TrustedContact
import com.resoluttech.core.auth.signin.SignUpSecurityQuestionsVM
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class TrustedContactInfoVM : ViewModel() {

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<TrustedContactScreenState> =
        MutableLiveData(TrustedContactScreenState.AcceptInput)
    val currentState: LiveData<TrustedContactScreenState> = _currentState
    private val repository = TrustedContactRepository()
    var trustedContacts: List<TrustedContact>? = null
    var minNumberOfTrustedContact: Int? = null

    fun getTrustedContacts(context: Context, phoneNumberValidatedToken: UUID) {
        _currentState.postValue(TrustedContactScreenState.Loading)
        getTrustedContactsRPC(context, phoneNumberValidatedToken)
    }

    private fun getTrustedContactsRPC(context: Context, phoneNumberValidatedToken: UUID) {
        if (_currentState.value !is TrustedContactScreenState.Error) {
            vmIoScope.launch {
                executeRPC(
                    context,
                    {
                        when (
                            val result =
                                repository.getTrustedContactsRPC(phoneNumberValidatedToken)
                        ) {
                            is LeoRPCResult.LeoResponse -> {
                                handleGetTrustedContactsRPCResponse(result.response)
                            }
                            is LeoRPCResult.LeoError -> {
                                handleGetTrustedContactsRPCError(context, result.error)
                            }
                        }
                    },
                    {
                        it.showError()
                    },
                )
            }
        }
    }

    private fun handleGetTrustedContactsRPCResponse(
        response: GetTrustedContactsRPC.Response,
    ) {
        trustedContacts = response.trustedContacts
        minNumberOfTrustedContact = response.minNumberOfTrustedContact
        if (response.trustedContacts.isEmpty()) {
            _currentState.postValue(TrustedContactScreenState.NoData)
        } else {
            _currentState.postValue(TrustedContactScreenState.Data(response))
        }
    }

    private fun handleGetTrustedContactsRPCError(
        context: Context,
        error: GetTrustedContactsRPC.Error,
    ) {
        AuthExceptionHandler.handleGetTrustedContactsRPCErrors(context, error).showError()
    }

    fun onRemoveTrustedContactTapped(
        context: Context,
        phoneNumberValidatedToken: UUID,
        trustedContactId: UUID,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result = repository.removeTrustedContactRPC(
                            phoneNumberValidatedToken,
                            trustedContactId,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleRemoveTrustedContactRPCResponse(
                                context,
                                phoneNumberValidatedToken,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleRemoveTrustedContactRPCError(result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleRemoveTrustedContactRPCResponse(
        context: Context,
        phoneNumberValidatedToken: UUID,
    ) {
        _currentState.postValue(TrustedContactScreenState.Loading)
        getTrustedContactsRPC(context, phoneNumberValidatedToken)
    }

    private fun handleRemoveTrustedContactRPCError(
        error: RemoveTrustedContactRPC.Error,
    ) {
        AuthExceptionHandler.handleRemoveTrustedContactRPCErrors(error).showError()
    }

    fun onFinishTapped(context: Context, phoneNumberValidatedToken: UUID) {
        _currentState.postValue(TrustedContactScreenState.Loading)
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result =
                            repository.getTrustedContactValidatedTokenRPC(phoneNumberValidatedToken)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetTrustedContactValidatedTokenRPCResponse(result.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetTrustedContactValidatedTokenRPCError(context, result.error)
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleGetTrustedContactValidatedTokenRPCResponse(
        response: GetTrustedContactValidatedTokenRPC.Response,
    ) {
        _currentState.postValue(TrustedContactScreenState.TrustedContactToken(response))
    }

    private fun handleGetTrustedContactValidatedTokenRPCError(
        context: Context,
        error: GetTrustedContactValidatedTokenRPC.Error,
    ) {
        AuthExceptionHandler.handleGetTrustedContactValidatedTokenRPCErrors(context, error)
            .showError()
    }

    fun setupSecurityQuestionsAndPassword(
        controller: NavController,
        signUpSecurityQuestionsVM: SignUpSecurityQuestionsVM,
        response: GetTrustedContactValidatedTokenRPC.Response,
        phoneNumber: LeoPhoneNumber,
    ) {
        _currentState.postValue(TrustedContactScreenState.AcceptInput)
        signUpSecurityQuestionsVM.setSecurityQuestionsData(response)
        signUpSecurityQuestionsVM.updateValidationToken(phoneNumber)
        trustedContacts = null
        minNumberOfTrustedContact = null
        val action =
            SetupTrustedContactsFragmentDirections.actionSetupTrustedContactsFragmentToSecurityQuestions()
        controller.navigate(action)
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(TrustedContactScreenState.AcceptInput)
    }

    fun onBackPressed(navController: NavController) {
        _currentState.postValue(TrustedContactScreenState.AcceptInput)
        trustedContacts = null
        minNumberOfTrustedContact = null
        navController.navigateUp()
    }
    private fun UIError.showError() {
        _currentState.postValue(TrustedContactScreenState.Error(this))
    }
}

sealed class TrustedContactScreenState {
    data class Error(val error: UIError) : TrustedContactScreenState()
    object AcceptInput : TrustedContactScreenState()
    object Loading : TrustedContactScreenState()
    data class Data(val response: GetTrustedContactsRPC.Response) : TrustedContactScreenState()
    object NoData : TrustedContactScreenState()
    data class TrustedContactToken(
        val response: GetTrustedContactValidatedTokenRPC.Response,
    ) : TrustedContactScreenState()
}
