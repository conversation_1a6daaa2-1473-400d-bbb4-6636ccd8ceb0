package com.resoluttech.core.auth.app

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AppPinSetupFragment
import com.resoluttech.core.utils.hideToolbar

class AppCodeHintScreen : Fragment(R.layout.app_code_hint_fragment) {

    private val appPinAuthVM by lazy { ViewModelProvider(this)[AppPinAuthVM::class.java] }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        hideToolbar()
        setupContinueButton(view)
        setResultListener()
    }

    private fun setupContinueButton(view: View) {
        view.findViewById<MaterialButton>(R.id.continue_button).setOnClickListener {
            findNavController().navigate(R.id.action_appCodeHintScreen_to_appPinSetupFragment)
        }
    }

    private fun setResultListener() {
        parentFragmentManager.setFragmentResultListener(
            AppPinSetupFragment.PIN_KEY,
            viewLifecycleOwner,
        ) { _, result ->
            appPinAuthVM.onNewAppPinEntered(
                findNavController(),
                result.getString(
                    AppPinSetupFragment.PIN_KEY,
                )!!,
            )
        }
    }
}
