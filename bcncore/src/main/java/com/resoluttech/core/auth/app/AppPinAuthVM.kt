package com.resoluttech.core.auth.app

import android.app.Activity
import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.auth.biometric.Biometric
import com.resoluttech.core.auth.biometric.BiometricPreferenceHelper
import com.resoluttech.core.config.Config
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.peertopeer.SendMoneyVM
import com.resoluttech.core.utils.SignOutRepository
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.PREVIOUS_PATH_SEND_MONEY_OR_BILLERS
import com.resoluttech.core.utils.clearAllUserData
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.logout
import com.resoluttech.core.views.PREVIOUS_PATH_APP_INACTIVE
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber

class AppPinAuthVM : ViewModel() {

    private var _currentState: MutableLiveData<AppPinScreenState> =
        MutableLiveData(AppPinScreenState.AcceptInput)
    val currentState: LiveData<AppPinScreenState> = _currentState

    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val context: Context by KoinJavaComponent.inject(Context::class.java)

    private var appPinAuthenticationTriesLeft: Int? = null
    private var biometricAuthenticationTriesLeft = 3

    init {
        vmIoScope.launch {
            if (AppPinHelper.getAppPin().isNullOrEmpty()) {
                _currentState.postValue(AppPinScreenState.AppPinNotSetup)
            } else {
                appPinAuthenticationTriesLeft = AppPinHelper.getAppPinAttemptLeft()
                if ((
                        appPinAuthenticationTriesLeft
                            ?: throw IllegalStateException("App pin authentication tries left is null")
                        ) <= 0
                ) {
                    _currentState.postValue(AppPinScreenState.AppPinRetriesExhausted)
                } else {
                    if (_currentState.value == AppPinScreenState.AcceptInput) {
                        if (BiometricPreferenceHelper.getBiometricOption() and Biometric.isBiometricHardwareAvailable()) {
                            _currentState.postValue(AppPinScreenState.BiometricAuthentication)
                        } else if (BiometricPreferenceHelper.getBiometricOption()) {
                            _currentState.postValue(AppPinScreenState.BiometricFailed)
                            if ((appPinAuthenticationTriesLeft ?: throw IllegalStateException("appPinAuthenticationTriesLeft cannot be null")) == Config.SESSION_PIN_WARNING_ATTEMPT_LEFT) {
                                _currentState.postValue(AppPinScreenState.AppPinWarningAttemptLeft)
                            }
                        } else {
                            _currentState.postValue(AppPinScreenState.AppPinAuthentication)
                            if ((appPinAuthenticationTriesLeft ?: throw IllegalStateException("appPinAuthenticationTriesLeft cannot be null")) == Config.SESSION_PIN_WARNING_ATTEMPT_LEFT) {
                                _currentState.postValue(AppPinScreenState.AppPinWarningAttemptLeft)
                            }
                        }
                    } else {
                        Timber.tag(TAG)
                            .w("User has already tried biometric and now can login with App Pin only")
                    }
                }
            }
        }
    }

    fun onArrowBackClicked() {
        _currentState.postValue(AppPinScreenState.AppPinCancelled)
    }

    /**
     * We are supressing ConvertLambdaToReference for Coroutine because resetAppPinAttemptLeft retuns
     * Unit but the coroutine launch accepts an extension function of CoroutineScope.
     */
    @Suppress("ConvertLambdaToReference")
    fun onBiometricConfirmed(
        navController: NavController,
        statusListener: AppAuthenticationStatus?,
        previousPath: String?,
    ) {
        vmIoScope.launch {
            AppPinHelper.resetAppPinAttemptLeft()
        }
        when (previousPath) {
            PREVIOUS_PATH_SEND_MONEY_OR_BILLERS -> {
                onAppAuthenticationConfirmed(navController, statusListener)
            }
            PREVIOUS_PATH_APP_INACTIVE -> {
                onAppAuthenticationConfirmed(navController, statusListener)
            }
            else -> {
                navController.navigate(R.id.action_appPinAuthFragment_to_moneyScreenFragment)
            }
        }
    }

    /**
     * We are supressing ConvertLambdaToReference for Coroutine because resetAppPinAttemptLeft retuns
     * Unit but the coroutine launch accepts an extension function of CoroutineScope.
     */
    @Suppress("ConvertLambdaToReference")
    fun onAppPinConfirmed(
        navController: NavController,
        statusListener: AppAuthenticationStatus?,
        previousPath: String?,
    ) {
        vmIoScope.launch {
            AppPinHelper.resetAppPinAttemptLeft()
        }
        when (previousPath) {
            PREVIOUS_PATH_SEND_MONEY_OR_BILLERS -> {
                onAppAuthenticationConfirmed(navController, statusListener)
            }
            PREVIOUS_PATH_APP_INACTIVE -> {
                onAppAuthenticationConfirmed(navController, statusListener)
            }
            else -> {
                navController.navigate(R.id.action_appPinAuthFragment_to_moneyScreenFragment)
            }
        }
    }

    fun onAppPinCancelled(
        navController: NavController,
        statusListener: AppAuthenticationStatus?,
        previousPath: String?,
        activity: Activity,
    ) {
        when (previousPath) {
            PREVIOUS_PATH_SEND_MONEY_OR_BILLERS -> {
                onAppAuthenticationCancelled(navController, statusListener)
            }
            else -> activity.finish()
        }
    }

    fun onAppPinRetriesExhausted() {
        vmIoScope.launch {
            _currentState.postValue(
                AppPinScreenState.AppPinRetriesExhaustedDialog(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePinAttemptsExhausted),
                        context.getString(R.string.alertMessagePinAttemptsExhausted),
                        SendMoneyVM.ERROR_CODE_APP_AUTHENTICATION_FAILED,
                    ),
                ),
            )
            val llt: String? = KeyStoreHelper.getLLT()
            val pushToken = PushTokenHelper.getPushToken(context)
            executeRPC(
                context = context,
                rpcBlock = {
                    when (
                        val response =
                            SignOutRepository.signOut(pushToken, llt)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            Timber.tag(TAG)
                                .i("signOutUserRPC Called Successfully ${Json.encodeToString(response.response)}")
                        }
                        is LeoRPCResult.LeoError -> {
                            Timber.tag(TAG)
                                .w("signOutUserRPC thrown and exception ${Json.encodeToString(response.error)}")
                        }
                    }
                },
                handleException = {
                    Timber.tag(TAG).e("Server error happened on signOutUserRPC ${it.errorMessage}")
                },
            )
            clearAllUserData()
        }
    }

    fun onAppPinEntered(appPin: String?) {
        vmIoScope.launch {
            if (appPin == AppPinHelper.getAppPin()) {
                _currentState.postValue(AppPinScreenState.AppPinConfirmed)
            } else {
                appPinAuthenticationTriesLeft = appPinAuthenticationTriesLeft!! - 1
                AppPinHelper.setAppPinAttemptLeft(appPinAuthenticationTriesLeft!!)
                if (appPinAuthenticationTriesLeft!! > 0) {
                    _currentState.postValue(
                        AppPinScreenState.IncorrectPin(
                            appPinAuthenticationTriesLeft!!,
                        ),
                    )
                } else {
                    _currentState.postValue(AppPinScreenState.AppPinRetriesExhausted)
                }
            }
        }
    }

    fun onErrorDismissed() {
        _currentState.postValue(AppPinScreenState.AcceptInput)
    }

    fun onAuthenticationFailed() {
        biometricAuthenticationTriesLeft--
        if (biometricAuthenticationTriesLeft == 0) {
            _currentState.postValue(AppPinScreenState.BiometricRetriesExhausted)
        }
    }

    fun onAuthenticationError() {
        if ((appPinAuthenticationTriesLeft ?: throw IllegalStateException("appPinAuthenticationTriesLeft cannot be null")) == Config.SESSION_PIN_WARNING_ATTEMPT_LEFT) {
            _currentState.postValue(AppPinScreenState.AppPinWarningAttemptLeft)
        } else {
            _currentState.postValue(AppPinScreenState.AppPinAuthentication)
        }
    }

    fun onBiometricRetriesExhausted() {
        if ((appPinAuthenticationTriesLeft ?: throw IllegalStateException("appPinAuthenticationTriesLeft cannot be null")) == Config.SESSION_PIN_WARNING_ATTEMPT_LEFT) {
            _currentState.postValue(AppPinScreenState.AppPinWarningAttemptLeft)
        } else {
            _currentState.postValue(AppPinScreenState.AppPinAuthentication)
        }
    }

    fun onAppPinWarningAttemptLeft() {
        _currentState.postValue(AppPinScreenState.AppPinAuthentication)
    }

    fun onNewAppPinEntered(navController: NavController, newAppPin: String) {
        vmIoScope.launch {
            AppPinHelper.storeAppPin(newAppPin)
        }
        _currentState.postValue(AppPinScreenState.AcceptInput)
        navController.navigate(R.id.action_appCodeHintScreen_to_moneyScreenFragment)
    }

    fun onDialogPositiveAction(activity: FragmentActivity?) {
        vmIoScope.launch {
            logout(activity)
        }
    }

    private fun onAppAuthenticationConfirmed(
        navController: NavController,
        statusListener: AppAuthenticationStatus?,
    ) {
        navController.navigateUp()
        statusListener?.onAppAuthenticationSuccessful(navController)
    }

    private fun onAppAuthenticationCancelled(
        navController: NavController,
        statusListener: AppAuthenticationStatus?,
    ) {
        navController.navigateUp()
        statusListener?.onAppAuthenticationCancelled(navController)
    }

    fun onForgotSessionPin(activity: FragmentActivity) {
        vmIoScope.launch {
            logout(activity)
        }
    }
}

sealed class AppPinScreenState {
    object AppPinNotSetup : AppPinScreenState()
    object AcceptInput : AppPinScreenState()
    object BiometricAuthentication : AppPinScreenState()
    object AppPinAuthentication : AppPinScreenState()
    object AppPinConfirmed : AppPinScreenState()
    object AppPinCancelled : AppPinScreenState()
    data class IncorrectPin(val triesLeft: Int) : AppPinScreenState()
    object AppPinRetriesExhausted : AppPinScreenState()
    data class AppPinRetriesExhaustedDialog(val uiError: UIError) : AppPinScreenState()
    object BiometricRetriesExhausted : AppPinScreenState()
    object BiometricFailed : AppPinScreenState()
    object AppPinWarningAttemptLeft : AppPinScreenState()
}

private const val TAG = "AppPinAuthVM"
