package com.resoluttech.core.auth.signin

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.UnAuthenticatedBaseFragment

class SelectSecurityQuestionFragment :
    UnAuthenticatedBaseFragment(),
    SignUpSecurityQuestionSelectorAdapter.ItemClickListener {

    private lateinit var securityQuestionRV: RecyclerView
    private lateinit var selectQuestionButton: MaterialButton
    private lateinit var selectedQuestion: SecurityQuestion
    private lateinit var securityQuestionAdapter: SignUpSecurityQuestionSelectorAdapter
    private val signUpSecurityQuestionsVM: SignUpSecurityQuestionsVM by navGraphViewModels(R.id.sign_up_nav)

    private lateinit var tempSecurityQuestionList: SelectedQuestionsList

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_select_security_question, container, false)
        initView(view)
        setupSelectQuestionButton()
        tempSecurityQuestionList = signUpSecurityQuestionsVM.getTempSecurityQuestionsList()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showToolbar()
        setDefaultToolbar(getString(R.string.setSecurityQuestionDetailViewTitle))
        setupSecurityQuestionsRV()
    }

    private fun initView(view: View) {
        securityQuestionRV = view.findViewById(R.id.select_question_rv)
        selectQuestionButton = view.findViewById(R.id.select_question)
    }

    private fun setupSelectQuestionButton() {
        selectQuestionButton.disable()
        selectQuestionButton.setOnClickListener {
            signUpSecurityQuestionsVM.addSavedQuestion(
                tempSecurityQuestionList.questionSelectedIndex,
                selectedQuestion,
            )
            findNavController().navigateUp()
        }
    }

    private fun setupSecurityQuestionsRV() {
        securityQuestionAdapter = SignUpSecurityQuestionSelectorAdapter(requireContext(), this)
        securityQuestionRV.adapter = securityQuestionAdapter
        if (tempSecurityQuestionList.selectedQuestion != null) {
            securityQuestionAdapter.updateItems(
                tempSecurityQuestionList.securityQuestionList,
                tempSecurityQuestionList.selectedQuestion?.id,
            )
        } else {
            securityQuestionAdapter.updateItems(
                tempSecurityQuestionList.securityQuestionList,
                null,
            )
        }
    }

    override fun onSecurityQuestionSelected(item: SecurityQuestion) {
        selectedQuestion = item
        securityQuestionAdapter.updateItems(
            tempSecurityQuestionList.securityQuestionList,
            item.id,
        )
        selectQuestionButton.enable()
    }

    override fun onSecurityQuestionCached(item: SecurityQuestion) {
        selectedQuestion = item
        selectQuestionButton.enable()
    }
}

data class SelectedQuestionsList(
    val securityQuestionList: MutableList<SecurityQuestion>,
    val questionSelectedIndex: Int,
    var selectedQuestion: SecurityQuestion?,
)
