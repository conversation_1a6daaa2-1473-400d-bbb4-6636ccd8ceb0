package com.resoluttech.core.auth.signin

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.localisedText
import java.util.UUID

class SignUpSecurityQuestionSelectorAdapter(
    private val context: Context,
    private val listener: ItemClickListener,
) : RecyclerView.Adapter<SignUpSecurityQuestionSelectorViewHolder>() {

    private val items: MutableList<SecurityQuestion> = mutableListOf()
    private var selectedQuestionCode: UUID? = null

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): SignUpSecurityQuestionSelectorViewHolder {
        val view =
            LayoutInflater.from(context).inflate(R.layout.item_security_question, parent, false)
        return SignUpSecurityQuestionSelectorViewHolder(view)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: SignUpSecurityQuestionSelectorViewHolder, position: Int) {
        holder.bind(items[position], listener, selectedQuestionCode, context)
    }

    interface ItemClickListener {
        fun onSecurityQuestionSelected(item: SecurityQuestion)
        fun onSecurityQuestionCached(item: SecurityQuestion)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateItems(items: List<SecurityQuestion>, selectedQuestionID: UUID?) {
        this.items.clear()
        this.items.addAll(items)
        this.selectedQuestionCode = selectedQuestionID
        notifyDataSetChanged()
    }
}

class SignUpSecurityQuestionSelectorViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    private val selectorIV: ImageView = view.findViewById(R.id.question_selector_iv)
    private val questionTV: TextView = view.findViewById(R.id.security_question_tv)

    fun bind(
        item: SecurityQuestion,
        listener: SignUpSecurityQuestionSelectorAdapter.ItemClickListener,
        selectedQuestionID: UUID?,
        context: Context,
    ) {
        questionTV.text = item.question.localisedText(
            LocaleManager.getCurrentLocale(context),
        )
        if (item.id == selectedQuestionID) {
            selectorIV.load(R.drawable.ic_radiobox_marked)
            listener.onSecurityQuestionCached(item)
        } else {
            selectorIV.load(R.drawable.ic_radiobox_blank)
        }
        itemView.setOnClickListener {
            listener.onSecurityQuestionSelected(item)
        }
    }
}
