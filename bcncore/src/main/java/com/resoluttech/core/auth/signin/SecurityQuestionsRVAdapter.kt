package com.resoluttech.core.auth.signin

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcn.signUpIn.SecurityQuestionAnswer
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher

class SecurityQuestionViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    val questionTV: TextView = view.findViewById(R.id.question_tv)
    val answerET: TextInputEditText = view.findViewById(R.id.answer_et)
    val answerTIL: TextInputLayout = view.findViewById(R.id.answer_til)
    val questionNumberTV: TextView = view.findViewById(R.id.question_number_tv)
}

class SecurityQuestionsRVAdapter(
    private val context: Context,
    private val questions: List<SecurityQuestion>,
) :
    RecyclerView.Adapter<SecurityQuestionViewHolder>() {

    private val answers: MutableList<String?> = MutableList(questions.size) { null }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SecurityQuestionViewHolder {
        return SecurityQuestionViewHolder(
            LayoutInflater.from(context)
                .inflate(R.layout.item_security_question_answer, parent, false),
        )
    }

    override fun onBindViewHolder(holder: SecurityQuestionViewHolder, position: Int) {
        holder.apply {
            questionNumberTV.text =
                context.getString(R.string.questionNumber, holder.bindingAdapterPosition + 1)
            questionTV.text =
                questions[holder.bindingAdapterPosition].question.localisedText(
                    LocaleManager.getCurrentLocale(context),
                )
            holder.answerET.addTextChangedListener(
                SingleLineCharacterLimitTextWatcher(
                    textInputLayout = holder.answerTIL,
                    maxLength = Config.MAX_SECURITY_ANSWER_LENGTH,
                    afterTextChange = {
                        answers[holder.bindingAdapterPosition] = answerET.text.toString()
                    },
                ),
            )
        }
    }

    override fun getItemCount(): Int = questions.size

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    fun getAllQuestionAnswer(): MutableList<SecurityQuestionAnswer?> {
        val answeredQuestions: MutableList<SecurityQuestionAnswer?> =
            MutableList(questions.size) { null }
        answers.forEachIndexed { index, answer ->
            val question = SecurityQuestionAnswer(
                questions[index].id,
                answer.toString(),
            )
            answeredQuestions[index] = question
        }
        return answeredQuestions
    }
}
