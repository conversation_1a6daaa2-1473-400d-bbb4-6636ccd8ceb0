package com.resoluttech.core.auth.signin

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.ResetForgottenPasswordRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class ResetPasswordVM : ViewModel() {

    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<ResetPasswordScreenState> =
        MutableLiveData(ResetPasswordScreenState.AcceptInput)
    private val signInRepository = SignInRepository()

    val currentState: LiveData<ResetPasswordScreenState> = _currentState

    fun onResetPasswordTapped(
        context: Context,
        phoneNumber: String,
        resetPasswordToken: String,
        newPassword: String,
        confirmPassword: String,
    ) {
        if (!validateEnteredPassword(context, newPassword, confirmPassword)) return
        _currentState.postValue(ResetPasswordScreenState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response = signInRepository.resetPassword(
                            LeoPhoneNumber(phoneNumber),
                            UUID.fromString(resetPasswordToken),
                            newPassword,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> handleResetPasswordSuccessResponse(response.response)
                        is LeoRPCResult.LeoError -> handleResetPasswordErrorResponse(
                            context,
                            response.error,
                        )
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun validateEnteredPassword(
        context: Context,
        newPassword: String,
        confirmPassword: String,
    ): Boolean {
        if (newPassword.isEmpty()) {
            _currentState.postValue(
                ResetPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleMissingPasswordField),
                        context.getString(R.string.alertMessageMissingNewPasswordField),
                    ),
                ),
            )
            return false
        }

        if (confirmPassword.isEmpty()) {
            _currentState.postValue(
                ResetPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleMissingPasswordField),
                        context.getString(R.string.alertMessageMissingConfirmPasswordField),
                    ),
                ),
            )
            return false
        }

        if (newPassword.length < Config.MIN_PASSWORD_LENGTH) {
            _currentState.postValue(
                ResetPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInsufficientPasswordLength),
                        context.getString(R.string.alertMessageInsufficientNewPasswordLength),
                    ),
                ),
            )
            return false
        }

        if (confirmPassword.length < Config.MIN_PASSWORD_LENGTH) {
            _currentState.postValue(
                ResetPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInsufficientPasswordLength),
                        context.getString(R.string.alertMessageInsufficientConfirmPasswordLength),
                    ),
                ),
            )
            return false
        }

        if (newPassword != confirmPassword) {
            _currentState.postValue(
                ResetPasswordScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitlePasswordsDoNotMatch),
                        context.getString(R.string.alertMessagePasswordsDoNotMatch),
                    ),
                ),
            )
            return false
        }

        return true
    }

    private fun UIError.showError() {
        _currentState.postValue(ResetPasswordScreenState.Error(this))
    }

    private fun handleResetPasswordSuccessResponse(response: ResetForgottenPasswordRPC.Response) {
        _currentState.postValue(ResetPasswordScreenState.ResetSuccessful(response))
    }

    private fun handleResetPasswordErrorResponse(
        context: Context,
        error: ResetForgottenPasswordRPC.Error,
    ) {
        AuthExceptionHandler.getResetPasswordRPCException(context, error)?.apply {
            showError()
        }
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(ResetPasswordScreenState.AcceptInput)
    }

    fun onSignUpSessionExpired(restartActivity: () -> Unit) {
        restartActivity()
    }

    fun onBackPressed(navController: NavController) {
        navController.navigateUp()
    }

    fun onSuccessfulPasswordReset(controller: NavController, phoneNumber: String, phoneNumberValidatedToken: String) {
        _currentState.postValue(ResetPasswordScreenState.AcceptInput)
        val action = ResetPasswordFragmentDirections.actionResetPasswordFragmentToEnterPassword(
            phoneNumberValidatedToken,
            phoneNumber,
        )
        controller.navigate(action)
    }
}

sealed class ResetPasswordScreenState {
    object AcceptInput : ResetPasswordScreenState()
    object Loading : ResetPasswordScreenState()
    data class Error(val uiError: UIError) : ResetPasswordScreenState()
    data class ResetSuccessful(val response: ResetForgottenPasswordRPC.Response) :
        ResetPasswordScreenState()
}
