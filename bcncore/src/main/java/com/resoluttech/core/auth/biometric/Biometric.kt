package com.resoluttech.core.auth.biometric

import android.content.Context
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricManager.Authenticators.BIOMETRIC_STRONG
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.apppin.PREVIOUS_PATH_SEND_MONEY_OR_BILLERS
import org.koin.java.KoinJavaComponent
import timber.log.Timber

object Biometric {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)

    interface AppAuthenticationListener {
        fun onBiometricAuthenticationSuccessful()
        fun onBiometricAuthenticationFailed()
        fun onBiometricAuthenticationError()
    }

    private var listener: AppAuthenticationListener? = null
    private val biometricManager: BiometricManager by lazy { BiometricManager.from(context) }
    private var prompt: BiometricPrompt? = null

    fun setAuthenticationListener(listener: AppAuthenticationListener) {
        this.listener = listener
    }

    fun isBiometricHardwareAvailable(): Boolean {
        return isBiometricHardwareAvailable(BIOMETRIC_STRONG)
    }

    private fun isBiometricHardwareAvailable(authClass: Int): Boolean {
        return when (biometricManager.canAuthenticate(authClass)) {
            BiometricManager.BIOMETRIC_SUCCESS -> {
                showMessage("App can authenticate using biometrics.")
                true
            }
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                showMessage("App can authenticate using biometrics but user has not enrolled.")
                true
            }
            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED -> {
                true
            }
            BiometricManager.BIOMETRIC_STATUS_UNKNOWN -> {
                true
            }
            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
                showMessage("No biometric features available on this device.")
                false
            }
            BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED -> {
                showMessage("The user can't authenticate because the specified options are incompatible with the current Android")
                return false
            }
            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
                showMessage("Biometric features are currently unavailable.")
                false
            }
            else -> {
                return false
            }
        }
    }

    private fun instanceOfBiometricPrompt(fragment: Fragment): BiometricPrompt {
        if (prompt == null) {
            val executor = ContextCompat.getMainExecutor(fragment.requireContext())

            val callback = object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    showMessage("$errorCode :: $errString")
                    listener?.onBiometricAuthenticationError()
                }

                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    showMessage("Authentication failed for an unknown reason")
                    listener?.onBiometricAuthenticationFailed()
                }

                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    showMessage("Authentication was successful")
                    listener?.onBiometricAuthenticationSuccessful()
                }
            }

            prompt = BiometricPrompt(fragment, executor, callback)
        }
        return prompt!!
    }

    private fun showMessage(message: String) {
        Timber.tag(TAG).i(message)
    }

    fun isUserEnrolled(): Boolean {
        return isUserEnrolledForBiometric(BIOMETRIC_STRONG)
    }

    private fun isUserEnrolledForBiometric(authClass: Int): Boolean {
        return when (biometricManager.canAuthenticate(authClass)) {
            BiometricManager.BIOMETRIC_SUCCESS -> {
                showMessage("App can authenticate using biometrics.")
                true
            }
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                showMessage("App can authenticate using biometrics but user has not enrolled.")
                false
            }
            else -> {
                false
            }
        }
    }

    fun authenticate(fragment: Fragment, previousPath: String?) {
        val title: String = when (previousPath) {
            PREVIOUS_PATH_SEND_MONEY_OR_BILLERS -> {
                fragment.requireContext()
                    .getString(R.string.alertTitleSessionPinTouchIDAuthRequiredTitle)
            }
            else -> {
                fragment.requireContext().getString(R.string.enterSessionPinBiometricTitle)
            }
        }
        val subtitle: String = when (previousPath) {
            PREVIOUS_PATH_SEND_MONEY_OR_BILLERS -> {
                fragment.requireContext()
                    .getString(R.string.alertTitleSessionPinTouchIDAuthRequiredMessage)
            }
            else -> {
                fragment.requireContext().getString(R.string.alertBiometricAuthenticationRequired)
            }
        }

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setNegativeButtonText(fragment.requireContext().getString(R.string.alertActionCancel))
            .setAllowedAuthenticators(BIOMETRIC_STRONG)
            .build()
        prompt = null
        instanceOfBiometricPrompt(fragment).authenticate(promptInfo)
    }

    fun cancelAuthentication() {
        prompt?.cancelAuthentication()
    }
}

private const val TAG = "Biometric"
