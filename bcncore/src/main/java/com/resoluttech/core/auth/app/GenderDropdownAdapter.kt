package com.resoluttech.core.auth.app

import android.content.Context
import android.graphics.Typeface
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class GenderDropdownAdapter(private val items: List<GenderListItem>, private val context: Context) :
    AbstractTextDropDownAdapter<GenderDropdownAdapter.GenderListItem>(items) {

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: GenderViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_dropdown_item, parent, false)
            viewHolder =
                GenderViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as GenderViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is GenderListItem.Gender -> {
                    textView.visibility = View.VISIBLE
                    val gender = getGender(item)
                    textView.text = gender
                }
                is GenderListItem.Header -> textView.visibility = View.GONE
            }
        }
        return view
    }

    private fun getGender(item: GenderListItem.Gender): String {
        return item.name
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: GenderViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.spinner_item_view, parent, false)
            viewHolder =
                GenderViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as GenderViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is GenderListItem.Gender -> {
                    textView.text = getGender(item)
                    textView.setTextColor(context.getColor(R.color.titleTextColor))
                    textView.typeface = Typeface.create(textView.typeface, Typeface.NORMAL)
                }
                is GenderListItem.Header -> {
                    textView.setTextColor(context.getColor(R.color.editTextHintColor))
                    textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, DROPDOWN_TEXT_SIZE)
                    textView.text = item.displayLabel
                    textView.typeface = Typeface.create(textView.typeface, Typeface.NORMAL)
                }
            }
        }
        return view
    }

    private class GenderViewHolder(view: View) {
        var textView: TextView = view.findViewById(
            R.id.dropdown_item_text_view,
        )
    }

    sealed class GenderListItem {
        data class Gender(val name: String) : GenderListItem()
        data class Header(val displayLabel: String) : GenderListItem()
    }
}

private const val DROPDOWN_TEXT_SIZE = 16F
