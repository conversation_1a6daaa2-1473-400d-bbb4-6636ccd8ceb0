package com.resoluttech.core.auth.signin

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.signUpIn.ConfirmForgotPasswordAnswersRPC
import com.resoluttech.bcn.signUpIn.GetForgotPasswordQuestionsRPC
import com.resoluttech.bcn.signUpIn.SecurityQuestion
import com.resoluttech.bcn.signUpIn.SecurityQuestionAnswer
import com.resoluttech.bcn.types.Locale
import com.resoluttech.bcn.types.PasswordPolicy
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.ERROR_CODE_SIGN_IN_AGAIN
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.localiseTextArray
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_DESCRIPTION
import com.resoluttech.core.views.KEY_PRIVACY_POLICY_TITLE
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class SignInSecurityQuestionsVM : ViewModel() {

    private val _currentState: MutableLiveData<SignInSecurityQuestionsScreenState> =
        MutableLiveData(SignInSecurityQuestionsScreenState.AcceptInput)
    val currentState: LiveData<SignInSecurityQuestionsScreenState> = _currentState
    private val phoneNumber: MutableLiveData<LeoPhoneNumber> = MutableLiveData()
    private val phoneNumberValidationToken: MutableLiveData<String> = MutableLiveData()
    private lateinit var passwordPolicy: PasswordPolicy
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val signInRepository = SignInRepository()

    fun updateValidationToken(phoneNumber: LeoPhoneNumber, phoneNumberValidatedToken: String) {
        this.phoneNumber.postValue(phoneNumber)
        phoneNumberValidationToken.postValue(phoneNumberValidatedToken)
    }

    private fun getPhoneNumberValidatedToken(): UUID {
        return UUID.fromString(phoneNumberValidationToken.value!!)
    }

    private fun getPhoneNumber(): LeoPhoneNumber {
        return LeoPhoneNumber(phoneNumber.value!!.value)
    }

    private fun UIError.showError() {
        _currentState.postValue(SignInSecurityQuestionsScreenState.Error(this))
    }

    private fun handleForgetPasswordQuestionErrorResponse(
        error: GetForgotPasswordQuestionsRPC.Error,
        context: Context,
    ) {
        AuthExceptionHandler.getForgetPasswordQuestionRPCException(error, context)?.apply {
            showError()
        }
    }

    private fun handleForgetPasswordSecuritySuccessResponse(response: GetForgotPasswordQuestionsRPC.Response) {
        _currentState.postValue(SignInSecurityQuestionsScreenState.Data(response.securityQuestions))
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(SignInSecurityQuestionsScreenState.AcceptInput)
    }

    fun onErrorDismissed(dialogId: Int, navController: NavController, restartActivity: () -> Unit) {
        when (dialogId) {
            ERROR_CODE_SIGN_IN_AGAIN -> {
                restartActivity()
            }
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                navController.navigateUp()
                _currentState.postValue(SignInSecurityQuestionsScreenState.AcceptInput)
            }
            DialogCodes.SIGN_UP_IN_SESSION_EXPIRED -> {
                restartActivity()
            }
            else -> {
                _currentState.postValue(SignInSecurityQuestionsScreenState.AcceptInput)
            }
        }
    }

    fun onSubmitTapped(
        context: Context,
        answeredQuestions: MutableList<SecurityQuestionAnswer?>,
        controller: NavController,
        supportedLocales: Locale,
    ) {
        if (!validateAnsweredQuestions(answeredQuestions)) {
            _currentState.postValue(
                SignInSecurityQuestionsScreenState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleIncorrectAnswers),
                        context.getString(R.string.alertMessageIncorrectAnswers),
                    ),
                ),
            )
            return
        }
        _currentState.postValue(SignInSecurityQuestionsScreenState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val answers = answeredQuestions.map { it!! }
                    val response = signInRepository.confirmForgetPasswordSecurityAnswer(
                        getPhoneNumber(),
                        getPhoneNumberValidatedToken(),
                        answers,
                        supportedLocales,
                    )
                    when (response) {
                        is LeoRPCResult.LeoResponse -> {
                            passwordPolicy = response.response.passwordPolicy
                            handleSubmitAnswerSuccessResponse(response.response.resetForgottenPasswordToken)
                        }
                        is LeoRPCResult.LeoError -> handleSubmitAnswerErrorResponse(
                            context,
                            response.error,
                            controller,
                        )
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleSubmitAnswerErrorResponse(
        context: Context,
        error: ConfirmForgotPasswordAnswersRPC.Error,
        controller: NavController,
    ) {
        when (error) {
            is ConfirmForgotPasswordAnswersRPC.Error.PhonenumberValidatedTokenExpired -> {
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleSessionExpired),
                    context.getString(R.string.alertMessageSessionExpired),
                    ERROR_CODE_SIGN_IN_AGAIN,
                ).showError()
            }
            is ConfirmForgotPasswordAnswersRPC.Error.InvalidPhonenumberValidatedToken -> {
                throw IllegalStateException("Received INVALID_PHONENUMBER_VALIDATED_TOKEN on ConfirmForgotPasswordAnswersRPC")
            }
            else -> {
                AuthExceptionHandler.confirmForgotPasswordAnswers(context, error)?.apply {
                    showError()
                }
            }
        }
    }

    private fun handleSubmitAnswerSuccessResponse(resetForgottenPasswordToken: UUID) {
        _currentState.postValue(
            SignInSecurityQuestionsScreenState.QuestionsAnsweredSignIn(
                resetForgottenPasswordToken,
            ),
        )
    }

    fun onBackPressed(navController: NavController) {
        navController.navigateUp()
    }

    private fun validateAnsweredQuestions(answeredQuestions: MutableList<SecurityQuestionAnswer?>): Boolean {
        answeredQuestions.forEach {
            if (it == null) {
                return false
            }
            if (it.answer.isEmpty()) {
                return false
            }
        }
        return true
    }

    fun onQuestionsAnswered(
        controller: NavController,
        resetToken: UUID,
        context: Context,
    ) {
        _currentState.postValue(SignInSecurityQuestionsScreenState.AcceptInput)
        val action =
            SignInSecurityQuestionsFragmentDirections.actionSecurityQuestionsFragmentToResetPasswordFragment()
        val args = Bundle()
        args.putString(
            KEY_PRIVACY_POLICY_TITLE,
            passwordPolicy.title.localisedText(LocaleManager.getCurrentLocale(context)),
        )
        args.putString(
            KEY_USER_PHONE_NUMBER,
            (
                phoneNumber.value
                    ?: throw IllegalStateException("Phone Number Cannot be Null")
                ).value,
        )
        args.putString(KEY_RESET_FORGOTTEN_PASSWORD_TOKEN, "$resetToken")
        args.putStringArrayList(
            KEY_PRIVACY_POLICY_DESCRIPTION,
            passwordPolicy.description.localiseTextArray(LocaleManager.getCurrentLocale(context)),
        )
        controller.navigate(action.actionId, args)
    }

    fun getSecurityQuestions(
        context: Context,
    ) {
        _currentState.postValue(SignInSecurityQuestionsScreenState.Loading)
        handleSecurityQuestionFromSignIn(
            context,
            getPhoneNumber(),
            "${getPhoneNumberValidatedToken()}",
        )
    }

    private fun handleSecurityQuestionFromSignIn(
        context: Context,
        phoneNumber: LeoPhoneNumber,
        phoneNumberValidatedToken: String,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response =
                            signInRepository.getForgetPasswordSecurityQuestions(
                                phoneNumber,
                                UUID.fromString(phoneNumberValidatedToken),
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleForgetPasswordSecuritySuccessResponse(response.response)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleForgetPasswordQuestionErrorResponse(
                                response.error,
                                context,
                            )
                        }
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }
}

sealed class SignInSecurityQuestionsScreenState {
    object Loading : SignInSecurityQuestionsScreenState()
    object AcceptInput : SignInSecurityQuestionsScreenState()
    data class Error(val uiError: UIError) : SignInSecurityQuestionsScreenState()
    data class Data(val questions: List<SecurityQuestion>) : SignInSecurityQuestionsScreenState()
    data class QuestionsAnsweredSignIn(val resetToken: UUID) :
        SignInSecurityQuestionsScreenState()
}
