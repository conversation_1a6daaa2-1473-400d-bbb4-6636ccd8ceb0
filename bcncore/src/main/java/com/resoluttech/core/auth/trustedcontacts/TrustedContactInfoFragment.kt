package com.resoluttech.core.auth.trustedcontacts

import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.hideToolbar
import java.lang.IllegalArgumentException

class TrustedContactInfoFragment : Fragment(R.layout.fragment_trusted_contact_info) {

    private val args: TrustedContactInfoFragmentArgs by navArgs()
    private lateinit var setupTimeTV: TextView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        view.findViewById<MaterialButton>(R.id.setup_trusted_contact_button).setOnClickListener {
            val action =
                TrustedContactInfoFragmentDirections.actionTrustedContactInfoFragmentToSetupTrustedContactsFragment(
                    args.phoneNumberValidatedToken,
                    args.phoneNumber,
                )
            findNavController().navigate(action)
        }

        setupTimeText(args.phoneNumberValidatedTokenValidityTimeInSeconds)
    }

    override fun onResume() {
        super.onResume()
        hideToolbar()
    }

    private fun initViews(rootView: View) {
        setupTimeTV = rootView.findViewById(R.id.description_4_tv)
    }

    private fun setupTimeText(setupTime: Int) {
        val hours: Int = setupTime / 3600
        val minutes: Int = (setupTime % 3600) / 60
        val seconds: Int = setupTime % 60

        var setupTimeString = ""
        if (hours > 0) {
            setupTimeString =
                requireContext().resources.getQuantityString(
                    R.plurals.trustedContactsOnboardingLabelHours,
                    hours,
                    hours,
                )
        }
        if (minutes > 0) {
            setupTimeString += requireContext().resources.getQuantityString(
                R.plurals.trustedContactsOnboardingLabelMinutes,
                minutes,
                minutes,
            )
        }
        if (seconds > 0) {
            setupTimeString += requireContext().resources.getQuantityString(
                R.plurals.trustedContactsOnboardingLabelSeconds,
                seconds,
                seconds,
            )
        }

        if (setupTimeString.isNotBlank()) {
            setupTimeTV.text = getString(
                R.string.trustedContactsOnboardingPointFour,
                setupTimeString,
            )
        } else {
            throw IllegalArgumentException("phoneNumberValidatedTokenValidityTimeInSeconds cannot be 0 in Sign Up Trusted Contact Flow")
        }
    }
}
