package com.resoluttech.core.auth.signup

import com.resoluttech.bcn.signUpIn.ConfirmSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.GetSupportedCountriesRPC
import com.resoluttech.bcn.signUpIn.RequestSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignUpOTPRPC
import com.resoluttech.bcn.signUpIn.SecurityQuestionAnswer
import com.resoluttech.bcn.signUpIn.SubmitSecurityQuestionAnswersAndPasswordRPC
import com.resoluttech.bcn.types.Locale
import com.resoluttech.bcn.types.Otp
import com.resoluttech.bcn.types.Password
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import java.util.UUID

class SignUpRepository {

    private val requestSignUpOTPRPC: RequestSignUpOTPRPC by KoinJavaComponent.inject(
        RequestSignUpOTPRPC::class.java,
    )

    private val confirmSignUpOTPRPC: ConfirmSignUpOTPRPC by KoinJavaComponent.inject(
        ConfirmSignUpOTPRPC::class.java,
    )

    private val resendSignUpOTPRPC: ResendSignUpOTPRPC by KoinJavaComponent.inject(
        ResendSignUpOTPRPC::class.java,
    )

    private val submitPasswordRPC: SubmitSecurityQuestionAnswersAndPasswordRPC by KoinJavaComponent.inject(
        SubmitSecurityQuestionAnswersAndPasswordRPC::class.java,
    )

    private val getSupportedCountriesRPC: GetSupportedCountriesRPC by KoinJavaComponent.inject(
        GetSupportedCountriesRPC::class.java,
    )

    suspend fun requestSignUpOTP(
        phoneNumber: LeoPhoneNumber,
        locales: Locale,
    ): LeoRPCResult<RequestSignUpOTPRPC.Response, RequestSignUpOTPRPC.Error> {
        return requestSignUpOTPRPC.execute(
            RequestSignUpOTPRPC.Request(
                phoneNumber,
                locales,
            ),
        )
    }

    suspend fun confirmSignUpOTP(
        otpId: UUID,
        otp: String,
        phoneNumber: LeoPhoneNumber,
    ): LeoRPCResult<ConfirmSignUpOTPRPC.Response, ConfirmSignUpOTPRPC.Error> {
        return confirmSignUpOTPRPC.execute(
            ConfirmSignUpOTPRPC.Request(
                otpId = otpId,
                otp = Otp(otp),
                phoneNumber,
            ),
        )
    }

    suspend fun resendSignUpOTP(otpId: UUID): LeoRPCResult<ResendSignUpOTPRPC.Response, ResendSignUpOTPRPC.Error> {
        return resendSignUpOTPRPC.execute(ResendSignUpOTPRPC.Request(otpId))
    }

    suspend fun getSupportedCountries(): LeoRPCResult<GetSupportedCountriesRPC.Response, GetSupportedCountriesRPC.Error> {
        return getSupportedCountriesRPC.execute(GetSupportedCountriesRPC.Request)
    }

    suspend fun submitPassword(
        temporaryToken: UUID,
        password: String,
        phoneNumber: LeoPhoneNumber,
        securityQuestion: List<SecurityQuestionAnswer>,
    ): LeoRPCResult<SubmitSecurityQuestionAnswersAndPasswordRPC.Response, SubmitSecurityQuestionAnswersAndPasswordRPC.Error> {
        return submitPasswordRPC.execute(
            SubmitSecurityQuestionAnswersAndPasswordRPC.Request(
                phoneNumber,
                temporaryToken,
                securityQuestion,
                Password(password),
            ),
        )
    }
}
