package com.resoluttech.core.auth.signin

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.UnitConverter.Companion.getDPPixelValue
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher

class SignUpSecurityQuestionsAdapter(
    private val context: Context,
    private val listener: ItemClickListener,
) : RecyclerView.Adapter<SignUpSecurityQuestionsViewHolder>() {

    private val items: MutableList<SelectedQuestionWithAnswer> = mutableListOf()

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): SignUpSecurityQuestionsViewHolder {
        val view =
            LayoutInflater.from(context)
                .inflate(R.layout.item_select_security_question, parent, false)
        return SignUpSecurityQuestionsViewHolder(view)
    }

    override fun getItemCount(): Int = items.size

    override fun onBindViewHolder(holder: SignUpSecurityQuestionsViewHolder, position: Int) {
        if (items[position].selectedQuestion != null) {
            holder.bind(
                items[position].selectedQuestion?.question?.localisedText(
                    LocaleManager.getCurrentLocale(context),
                ),
                items[position].answer,
                listener,
                position,
                context,
            )
        } else {
            holder.bind(
                holder.itemView.context.getEmptyString(),
                holder.itemView.context.getEmptyString(),
                listener,
                position,
                context,
            )
        }
    }

    interface ItemClickListener {
        fun onQuestionSelected(position: Int)
        fun onAnswerChanged(position: Int, answer: String)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateItems(items: MutableList<SelectedQuestionWithAnswer>) {
        this.items.clear()
        this.items.addAll(items)
        notifyDataSetChanged()
    }
}

class SignUpSecurityQuestionsViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    private val questionTV: TextView = view.findViewById(R.id.question_tv)
    private val answerTV: TextView = view.findViewById(R.id.answer_et)
    private val answerTIL: TextInputLayout = view.findViewById(R.id.answer_til)
    private val questionHint: TextView = view.findViewById(R.id.question_hint_tv)
    private val securityQuestionLL: LinearLayout = view.findViewById(R.id.security_question_ll)

    fun bind(
        question: String?,
        answer: String?,
        listener: SignUpSecurityQuestionsAdapter.ItemClickListener,
        position: Int,
        context: Context,
    ) {
        if (question != null && question.isNotBlank()) {
            questionHint.visibility = View.VISIBLE
            questionHint.text =
                context.getString(R.string.setSecurityQuestionChooseQuestion, (position + 1))
            questionTV.text = question
            answerTV.enable()
            questionTV.setPadding(0, 10.getDPPixelValue(), 4, 0)
            if (answer != null && answer.isNotBlank()) {
                answerTV.text = answer
            }
        } else {
            questionHint.visibility = View.GONE
            questionTV.text = context.getString(R.string.setSecurityQuestionChooseQuestion, (position + 1))
            answerTV.disable()
            questionTV.setTextColor(context.getColor(R.color.subtitleTextColorSecondary))
            questionTV.setPadding(0, 0, 0, 0)
        }
        securityQuestionLL.setOnClickListener {
            listener.onQuestionSelected(position)
        }
        answerTV.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = answerTIL,
                maxLength = Config.MAX_SECURITY_ANSWER_LENGTH,
                onTextChanged = {
                    listener.onAnswerChanged(position, answerTV.text.toString())
                },
            ),
        )
    }
}
