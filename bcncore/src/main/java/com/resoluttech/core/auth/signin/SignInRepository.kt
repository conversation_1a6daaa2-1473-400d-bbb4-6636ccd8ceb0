package com.resoluttech.core.auth.signin

import com.resoluttech.bcn.signUpIn.ConfirmForgotPasswordAnswersRPC
import com.resoluttech.bcn.signUpIn.ConfirmResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ConfirmSignInOTPRPC
import com.resoluttech.bcn.signUpIn.GetForgotPasswordQuestionsRPC
import com.resoluttech.bcn.signUpIn.GetSupportedCountriesRPC
import com.resoluttech.bcn.signUpIn.GetTrustedContactsRPC
import com.resoluttech.bcn.signUpIn.RequestResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.RequestSignInOtpRPC
import com.resoluttech.bcn.signUpIn.ResendResetPasswordByTrustedContactOTPRPC
import com.resoluttech.bcn.signUpIn.ResendSignInOTPRPC
import com.resoluttech.bcn.signUpIn.ResetForgottenPasswordRPC
import com.resoluttech.bcn.signUpIn.SecurityQuestionAnswer
import com.resoluttech.bcn.signUpIn.SignInUserRPC
import com.resoluttech.bcn.types.App
import com.resoluttech.bcn.types.DeviceInformation
import com.resoluttech.bcn.types.FrontEndPlatform
import com.resoluttech.bcn.types.Locale
import com.resoluttech.bcn.types.Otp
import com.resoluttech.bcn.types.Password
import com.resoluttech.bcn.types.PushToken
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import java.util.UUID

class SignInRepository {

    private val requestSignInOTPRPC: RequestSignInOtpRPC by KoinJavaComponent.inject(
        RequestSignInOtpRPC::class.java,
    )

    private val confirmSignInOTPRPC: ConfirmSignInOTPRPC by KoinJavaComponent.inject(
        ConfirmSignInOTPRPC::class.java,
    )

    private val signInUserRPC: SignInUserRPC by KoinJavaComponent.inject(
        SignInUserRPC::class.java,
    )

    private val resendSignInOTPRPC: ResendSignInOTPRPC by KoinJavaComponent.inject(
        ResendSignInOTPRPC::class.java,
    )

    private val forgetPasswordSecurityQuestions: GetForgotPasswordQuestionsRPC by KoinJavaComponent.inject(
        GetForgotPasswordQuestionsRPC::class.java,
    )

    private val forgetPasswordQuestionAnswer: ConfirmForgotPasswordAnswersRPC by KoinJavaComponent.inject(
        ConfirmForgotPasswordAnswersRPC::class.java,
    )

    private val resetForgottenPasswordRPC: ResetForgottenPasswordRPC by KoinJavaComponent.inject(
        ResetForgottenPasswordRPC::class.java,
    )

    private val requestResetPasswordByTrustedContactOTPRPC: RequestResetPasswordByTrustedContactOTPRPC by KoinJavaComponent.inject(
        RequestResetPasswordByTrustedContactOTPRPC::class.java,
    )

    private val resendResetPasswordByTrustedContactOTPRPC: ResendResetPasswordByTrustedContactOTPRPC by KoinJavaComponent.inject(
        ResendResetPasswordByTrustedContactOTPRPC::class.java,
    )

    private val confirmResetPasswordByTrustedContactOTPRPC: ConfirmResetPasswordByTrustedContactOTPRPC by KoinJavaComponent.inject(
        ConfirmResetPasswordByTrustedContactOTPRPC::class.java,
    )

    private val getTrustedContactsRPC: GetTrustedContactsRPC by KoinJavaComponent.inject(
        GetTrustedContactsRPC::class.java,
    )

    private val getSupportedCountriesRPC: GetSupportedCountriesRPC by KoinJavaComponent.inject(
        GetSupportedCountriesRPC::class.java,
    )

    suspend fun getSupportedCountries(): LeoRPCResult<GetSupportedCountriesRPC.Response, GetSupportedCountriesRPC.Error> {
        return getSupportedCountriesRPC.execute(GetSupportedCountriesRPC.Request)
    }

    suspend fun requestSignInOTP(phoneNumber: LeoPhoneNumber, locale: Locale): LeoRPCResult<RequestSignInOtpRPC.Response, RequestSignInOtpRPC.Error> {
        return requestSignInOTPRPC.execute(
            RequestSignInOtpRPC.Request(
                phoneNumber,
                locale,
            ),
        )
    }

    suspend fun confirmSignInOTP(
        otpId: UUID,
        otp: String,
        phoneNumber: LeoPhoneNumber,
    ): LeoRPCResult<ConfirmSignInOTPRPC.Response, ConfirmSignInOTPRPC.Error> {
        return confirmSignInOTPRPC.execute(
            ConfirmSignInOTPRPC.Request(
                otpId = otpId,
                otp = Otp(otp),
                phoneNumber,
            ),
        )
    }

    suspend fun signInUser(
        phoneNumberValidatedToken: UUID,
        password: String,
        phoneNumber: LeoPhoneNumber,
        pushToken: String?,
        deviceInformation: DeviceInformation,
    ): LeoRPCResult<SignInUserRPC.Response, SignInUserRPC.Error> {
        return signInUserRPC.execute(
            SignInUserRPC.Request(
                phoneNumberValidatedToken,
                Password(password),
                phoneNumber,
                pushToken?.let {
                    PushToken(it, App.BCN, FrontEndPlatform.ANDROID)
                },
                deviceInformation,
            ),
        )
    }

    suspend fun resendSignInOTP(otpId: UUID): LeoRPCResult<ResendSignInOTPRPC.Response, ResendSignInOTPRPC.Error> {
        return resendSignInOTPRPC.execute(ResendSignInOTPRPC.Request(otpId))
    }

    suspend fun getForgetPasswordSecurityQuestions(
        phoneNumber: LeoPhoneNumber,
        phoneNumberValidatedToken: UUID,
    ): LeoRPCResult<GetForgotPasswordQuestionsRPC.Response, GetForgotPasswordQuestionsRPC.Error> {
        return forgetPasswordSecurityQuestions.execute(
            GetForgotPasswordQuestionsRPC.Request(
                phoneNumber,
                phoneNumberValidatedToken,
            ),
        )
    }

    suspend fun resetPassword(
        phoneNumber: LeoPhoneNumber,
        phoneNumberValidatedToken: UUID,
        newPassword: String,
    ): LeoRPCResult<ResetForgottenPasswordRPC.Response, ResetForgottenPasswordRPC.Error> {
        return resetForgottenPasswordRPC.execute(
            ResetForgottenPasswordRPC.Request(
                phoneNumber = phoneNumber,
                resetForgottenPasswordToken = phoneNumberValidatedToken,
                newPassword = Password(newPassword),
            ),
        )
    }

    suspend fun confirmForgetPasswordSecurityAnswer(
        phoneNumber: LeoPhoneNumber,
        phoneNumberValidatedToken: UUID,
        answers: List<SecurityQuestionAnswer>,
        locale: Locale,
    ): LeoRPCResult<ConfirmForgotPasswordAnswersRPC.Response, ConfirmForgotPasswordAnswersRPC.Error> {
        return forgetPasswordQuestionAnswer.execute(
            ConfirmForgotPasswordAnswersRPC.Request(
                phoneNumber,
                phoneNumberValidatedToken,
                answers,
                locale,
            ),
        )
    }

    suspend fun getTrustedContactsRPC(
        phoneNumberValidatedToken: UUID,
    ): LeoRPCResult<GetTrustedContactsRPC.Response, GetTrustedContactsRPC.Error> {
        return getTrustedContactsRPC.execute(
            GetTrustedContactsRPC.Request(
                phoneNumberValidatedToken,
            ),
        )
    }

    suspend fun requestResetPasswordByTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        trustedContactId: UUID,
    ): LeoRPCResult<RequestResetPasswordByTrustedContactOTPRPC.Response, RequestResetPasswordByTrustedContactOTPRPC.Error> {
        return requestResetPasswordByTrustedContactOTPRPC.execute(
            RequestResetPasswordByTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                trustedContactId,
            ),
        )
    }

    suspend fun resendResetPasswordByTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        otpId: UUID,
    ): LeoRPCResult<ResendResetPasswordByTrustedContactOTPRPC.Response, ResendResetPasswordByTrustedContactOTPRPC.Error> {
        return resendResetPasswordByTrustedContactOTPRPC.execute(
            ResendResetPasswordByTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                otpId,
            ),
        )
    }

    suspend fun confirmResetPasswordByTrustedContactOTP(
        phoneNumberValidatedToken: UUID,
        otpId: UUID,
        otp: String,
    ): LeoRPCResult<ConfirmResetPasswordByTrustedContactOTPRPC.Response, ConfirmResetPasswordByTrustedContactOTPRPC.Error> {
        return confirmResetPasswordByTrustedContactOTPRPC.execute(
            ConfirmResetPasswordByTrustedContactOTPRPC.Request(
                phoneNumberValidatedToken,
                otpId,
                Otp((otp)),
            ),
        )
    }
}
