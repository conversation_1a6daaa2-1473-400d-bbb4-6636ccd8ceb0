package com.resoluttech.core.kyc

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import coil.load
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.HapticConstant
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.chooseQrCodeFromGallery
import com.resoluttech.core.utils.handleQRCodeImagePickedFromGallery
import com.resoluttech.core.utils.performHapticFeedback
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseFragment
import com.suryadigital.leo.libui.qrcode.BarcodeException
import com.suryadigital.leo.libui.qrcode.BarcodeFragment
import com.suryadigital.leo.libui.qrcode.BarcodeListener
import com.suryadigital.leo.libui.qrcode.ScanFormat
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.libui.qrcode.setScanFormat
import timber.log.Timber
import `in`.aabhasjindal.otptextview.R as OTPTextViewResource

class KYCQRScannerFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private var readyToScanQR = true
    private val kycqrScannerVM: KYCQRScannerVM by navGraphViewModels(R.id.kyc_nav)
    private lateinit var barcodeCameraMask: ImageView
    private lateinit var messageTV: TextView
    private lateinit var requestCameraPermissionButton: MaterialButton
    private lateinit var errorLayout: LinearLayout
    private lateinit var headerLayout: LinearLayout
    private lateinit var footerLayout: View
    private lateinit var flashIconButton: ImageButton
    private lateinit var galleryIconButton: ImageButton
    private val kycDetails: KYCDetails by lazy(KYCSharedPreference::getKYCDetails)
    private var isFlashOn = false

    private var imagePickerResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            handleQRCodeImagePickedFromGallery(
                requireActivity(),
                result,
                requireContext(),
                ::onBarcodeValueAvailable,
            ) { kycqrScannerVM.onInvalidImageSelectedFromGallery(requireContext()) }
        }

    private val startForCameraPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                kycqrScannerVM.onCameraPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                kycqrScannerVM.onCameraPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Camera permission denied, unable to proceed.")
                kycqrScannerVM.onCameraPermissionDenied()
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_kyc_qr_scanner, container, false)
        handleBarcodeFragmentSetup()
        initViews(view)
        setupRequestPermissionButton()
        setToolBar()
        shouldAuthenticate = kycDetails.previousPath == KYCPreviousPath.PROFILE
        kycqrScannerVM.onCameraPermissionRequested()
        return view
    }

    private fun setToolBar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.qr_scanner_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            setArrowBackIcon(toolbarView)
            setTitle(toolbarView)
            setFlashIcon(toolbarView)
            setGalleryIcon(toolbarView)
        }
    }

    private fun setTitle(toolbar: View) {
        val title: TextView = toolbar.findViewById(R.id.title)
        title.text = getString(R.string.kycScanQRCodeTitle)
    }

    private fun setGalleryIcon(toolbar: View) {
        galleryIconButton = toolbar.findViewById(R.id.pick_qr_code_from_gallery)
        galleryIconButton.setOnClickListener {
            chooseQrCodeFromGallery(imagePickerResultLauncher)
        }
    }

    private fun setArrowBackIcon(toolbar: View) {
        val arrowBackButton = toolbar.findViewById<ImageButton>(R.id.arrow_back_icon)
        arrowBackButton.setOnClickListener {
            kycqrScannerVM.onArrowBackClicked(findNavController())
        }
    }

    override fun onPause() {
        super.onPause()
        stopBarcodeDetection()
        if (isFlashOn && flashIconButton.isVisible) {
            isFlashOn = false
            flashIconButton.load(R.drawable.ic_flashlight_off)
        }
    }

    private fun disableFlashIcon() {
        isFlashOn = false
        flashIconButton.visibility = View.GONE
    }

    private fun enableFlashIcon() {
        isFlashOn = false
        flashIconButton.visibility = View.VISIBLE
    }

    private fun setupRequestPermissionButton() {
        requestCameraPermissionButton.setOnClickListener {
            kycqrScannerVM.onCameraPermissionRequested()
        }
    }

    private fun initViews(view: View) {
        barcodeCameraMask = view.findViewById(R.id.barcode_camera_mask)
        messageTV = view.findViewById(R.id.message)
        requestCameraPermissionButton = view.findViewById(R.id.request_camera_permission)
        errorLayout = view.findViewById(R.id.camera_permission_error)
        headerLayout = view.findViewById(R.id.heading_layout)
        footerLayout = view.findViewById(R.id.footer_layout)
    }

    private fun setFlashIcon(toolbar: View) {
        flashIconButton = toolbar.findViewById(R.id.flash_icon)
        flashIconButton.setOnClickListener {
            isFlashOn = !isFlashOn
            getBarcodeFragment()?.toggleFlashLight(isFlashOn)
            if (isFlashOn) {
                flashIconButton.load(R.drawable.ic_flashlight_on)
            } else {
                flashIconButton.load(R.drawable.ic_flashlight_off)
            }
        }
    }

    private fun onBarcodeValueAvailable(rawValue: String) {
        if (readyToScanQR) {
            kycqrScannerVM.onReceiveQRValue(
                requireContext(),
                rawValue,
                findNavController(),
            ) {
                barcodeCameraMask.performHapticFeedback(
                    HapticConstant.HAPTIC_CODE_CONFIRM,
                    requireContext(),
                    TAG,
                )
            }
            readyToScanQR = false
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        networkListenerCallback = this
        kycqrScannerVM.cameraPermissionState.observe(
            viewLifecycleOwner,
            Observer(::reactToCameraState),
        )
        kycqrScannerVM.qrScannerState.observe(viewLifecycleOwner, Observer(::reactToScannerState))
    }

    private fun stopBarcodeDetection() {
        (
            getBarcodeFragment()
                ?: throw IllegalStateException("Barcode Fragment cannot be null")
            ).stopBarcodeDetection()
    }

    private fun reactToCameraState(cameraState: KYCScannerCameraState) {
        when (cameraState) {
            is KYCScannerCameraState.CameraPermissionRequested -> {
                showCameraPermissionDeniedError()
                disableFlashIcon()
                startForCameraPermissionResult.launch(Manifest.permission.CAMERA)
            }
            is KYCScannerCameraState.CameraPermissionGranted -> {
                dismissCameraPermissionDeniedError()
                startBarcodeDetection()
            }
            is KYCScannerCameraState.CameraPermissionDenied -> {
                disableFlashIcon()
                showCameraPermissionDeniedError()
            }
            is KYCScannerCameraState.CameraPermissionAlert -> {
                disableFlashIcon()
                handleCameraPermissionAlertState()
            }
            is KYCScannerCameraState.Waiting -> {
                disableFlashIcon()
                handleWaitingOnCameraState()
            }
        }
    }

    private fun reactToScannerState(state: KYCQRCodeScannerState) {
        when (state) {
            is KYCQRCodeScannerState.AcceptInput -> {
                readyToScanQR = true
                barcodeCameraMask.load(R.drawable.bg_scanner_background)
            }
            is KYCQRCodeScannerState.InlineError -> {
                handleQRCodeInlineError(state.errorMessage)
            }
            is KYCQRCodeScannerState.EnterManually -> {
                handleEnterManuallyState()
            }
            KYCQRCodeScannerState.NoInternet -> {
                handleNoInternetState()
            }
        }
    }

    private fun handleNoInternetState() {
        stopBarcodeDetection()
        if (isFlashOn && flashIconButton.isVisible) {
            isFlashOn = false
            flashIconButton.load(R.drawable.ic_flashlight_off)
        }
        showErrorDialog(
            requireContext().getString(R.string.alertTitleNoInternet),
            getString(R.string.alertMessageNoInternet),
            DialogCodes.QR_CODE_SCANNER_NO_INTERNET,
        )
    }

    private fun handleEnterManuallyState() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.kycEnterDetailsManuallyTitle),
            getString(R.string.kycEnterDetailsManuallySubtitle),
            ENTER_MANUALLY_DIALOG_CODE,
            positiveActionLabel = getString(R.string.kycEnterDetailsManuallyTitle),
            negativeActionLabel = getString(R.string.alertActionCancel),
            alertDialogButtonColor = null,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleQRCodeInlineError(errorMessage: String) {
        barcodeCameraMask.performHapticFeedback(
            HapticConstant.HAPTIC_CODE_REJECT,
            requireContext(),
            TAG,
        )
        barcodeCameraMask.load(R.drawable.bg_scanner_background_error)
        AnimationUtils.loadAnimation(requireContext(), R.anim.bounce).apply {
            reset()
            messageTV.clearAnimation()
            duration = 1000
            messageTV.startAnimation(this)
            messageTV.text = errorMessage
            messageTV.setTextColor(requireContext().getColor(R.color.failed_transaction_theme_color))
            messageTV.animation.setAnimationListener(
                object : Animation.AnimationListener {
                    override fun onAnimationStart(animation: Animation?) {
                        // Do nothing
                    }

                    override fun onAnimationEnd(animation: Animation?) {
                        kycqrScannerVM.onInlineErrorDismiss {
                            messageTV.setTextColor(requireContext().getColor(OTPTextViewResource.color.white))
                            messageTV.text = getString(R.string.kycScanQRCodeVerifyNationalIdSubtitle)
                        }
                    }

                    override fun onAnimationRepeat(animation: Animation?) {
                        // Do nothing
                    }
                },
            )
        }
    }

    private fun showCameraPermissionDeniedError() {
        errorLayout.visibility = View.VISIBLE
        barcodeCameraMask.visibility = View.GONE
        headerLayout.visibility = View.GONE
        footerLayout.visibility = View.GONE
    }

    private fun dismissCameraPermissionDeniedError() {
        errorLayout.visibility = View.GONE
        barcodeCameraMask.visibility = View.VISIBLE
        headerLayout.visibility = View.VISIBLE
        footerLayout.visibility = View.VISIBLE
    }

    private fun handleWaitingOnCameraState() {
        dismissCameraPermissionDeniedError()
        if (!checkCameraPermissionGranted(requireContext())) {
            kycqrScannerVM.onCameraPermissionRequested()
        }
    }

    private fun handleCameraPermissionAlertState() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.alertTitleCameraAccessNotPermitted),
            getString(R.string.kycCameraPermissionDeniedSubtitle),
            CAMERA_PERMISSION_DIALOG_CODE,
            positiveActionLabel = getString(R.string.alertActionOpenSettings),
            negativeActionLabel = getString(R.string.alertActionCancel),
            alertDialogButtonColor = null,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleBarcodeFragmentSetup() {
        val barcodeFragment = BarcodeFragment()
        val metrics = requireContext().resources.displayMetrics
        barcodeFragment.setPreviewSize(metrics.heightPixels, metrics.widthPixels)
        barcodeFragment.setScanFormat(ScanFormat.QR_CODE)
        barcodeFragment.setBarcodeListener(object : BarcodeListener {
            override fun onBarcodeValueReceived(rawValue: String) {
                onBarcodeValueAvailable(rawValue)
            }

            override fun onFailure(e: BarcodeException) {
                Timber.tag(TAG).d(e.message!!)
            }
        })
        presentBarcodeFragment(barcodeFragment)
    }

    private fun presentBarcodeFragment(barcodeFragment: BarcodeFragment) {
        if (!barcodeFragment.isAdded) {
            childFragmentManager.beginTransaction()
                .add(R.id.barcode_fragment_container, barcodeFragment, BARCODE_FRAGMENT_TAG)
                .commit()
        } else {
            Timber.tag(TAG).e("Trying to add barcode fragment when it is already added.")
        }
    }

    private fun startBarcodeDetection() {
        if (!checkCameraPermissionGranted(requireContext())) {
            kycqrScannerVM.onCameraPermissionRequested()
            disableFlashIcon()
        } else {
            enableFlashIcon()
            val barcodeView: View = requireView().findViewById(R.id.barcode_fragment_container)
            barcodeView.visibility = View.VISIBLE
            getBarcodeFragment()?.startBarcodeDetection()
        }
    }

    private fun getBarcodeFragment(): BarcodeFragment? {
        val fragment = childFragmentManager.findFragmentByTag(BARCODE_FRAGMENT_TAG)
        return if (fragment is BarcodeFragment) {
            val metrics = requireContext().resources.displayMetrics
            fragment.setPreviewSize(metrics.heightPixels, metrics.widthPixels)
            fragment
        } else {
            null
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycqrScannerVM.onOpenSettingsClicked(requireContext())
            ENTER_MANUALLY_DIALOG_CODE -> kycqrScannerVM.onEnterManuallyTapped(
                findNavController(),
            )
            DialogCodes.QR_CODE_SCANNER_NO_INTERNET -> {
                kycqrScannerVM.onArrowBackClicked(findNavController())
            }
            else -> Timber.tag(TAG)
                .i("Positive action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycqrScannerVM.onCameraPermissionAlertDismissed()
            ENTER_MANUALLY_DIALOG_CODE -> kycqrScannerVM.onAlertDialogDismissed()
            else -> Timber.tag(TAG)
                .i("Negative action occurred on alert dialog having id $dialogId.")
        }
    }

    override fun onResume() {
        super.onResume()
        if (checkCameraPermissionGranted(requireContext())) {
            kycqrScannerVM.onCameraPermissionGranted()
        }
    }

    override fun onNetworkAvailable() {
        kycqrScannerVM.onInlineErrorDismiss {
            messageTV.setTextColor(requireContext().getColor(OTPTextViewResource.color.white))
            messageTV.text = getString(R.string.kycScanQRCodeVerifyNationalIdSubtitle)
        }
    }
}

private const val TAG = "KYCQRScanner"
private const val BARCODE_FRAGMENT_TAG = "BARCODE_FRAGMENT_TAG"
private const val ENTER_MANUALLY_DIALOG_CODE: Int = 116
