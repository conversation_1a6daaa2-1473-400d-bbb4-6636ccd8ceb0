package com.resoluttech.core.kyc

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.DatePickerDialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.DatePicker
import android.widget.EditText
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.DateValidatorPointForward
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.datepicker.MaterialPickerOnPositiveButtonClickListener
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.types.Gender
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.app.GenderDropdownAdapter
import com.resoluttech.core.auth.app.GenderDropdownAdapter.GenderListItem
import com.resoluttech.core.config.Config
import com.resoluttech.core.config.GenderConfig
import com.resoluttech.core.kyc.KYCDataVM.ErrorField
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.selectionActionModeCallback
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.utils.trim
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.DocumentPickerDialog
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.imagecrop.ImagePicker
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.util.UUID

class EnterKYCDataFragment :
    BaseFragment(),
    DatePickerDialog.OnDateSetListener,
    AlertDialog.ActionListener,
    DocumentPickerDialog.DocumentPickerListener,
    ImagePicker.ImagePickerFailureListener,
    MaterialPickerOnPositiveButtonClickListener<Long>,
    View.OnClickListener,
    BaseFragment.NetworkListener {

    private val kycDataVM: KYCDataVM by navGraphViewModels(R.id.kyc_nav)

    //region Profile Image
    private lateinit var profileImageTV: TextView
    private lateinit var uploadProfileImageIV: ImageView
    private lateinit var loadingProfileImagePB: ProgressBar
    private lateinit var cameraProfileImageIV: ImageView
    private lateinit var profileImageCIV: ContactIconView
    //endregion

    //region KYC Data
    private lateinit var firstNameET: EditText
    private lateinit var firstNameTIL: TextInputLayout
    private lateinit var lastNameET: EditText
    private lateinit var lastNameTIL: TextInputLayout
    private lateinit var dateOfBirthET: EditText
    private lateinit var otherNameET: EditText
    private lateinit var otherNameTIL: TextInputLayout
    private lateinit var nrbNumberET: EditText
    private lateinit var nrbNumberTIL: TextInputLayout
    private lateinit var emailAddressET: EditText
    private lateinit var placeOfBirthET: EditText
    private lateinit var placeOfBirthTIL: TextInputLayout
    private lateinit var nationalIdIssueDateET: EditText
    private lateinit var nationalIdExpiryDateET: EditText
    private lateinit var residentAddressET: EditText
    private lateinit var residentAddressTIL: TextInputLayout
    private lateinit var postalAddressET: EditText
    private lateinit var postalAddressTIL: TextInputLayout
    private lateinit var genderDropdown: TextDropdown
    private lateinit var genderDropDownHint: TextView
    private lateinit var genderDropDownIV: ImageView
    //endregion

    private lateinit var nextButton: MaterialButton
    private lateinit var progressBar: ProgressBar
    private lateinit var contentSection: ConstraintLayout
    private val kycDetails: KYCDetails by lazy(KYCSharedPreference::getKYCDetails)

    private lateinit var datePickedFor: DatePickedFor

    private var documentUploadTarget: DocumentUploadTarget? = null

    private val genderConfig: GenderConfig by KoinJavaComponent.inject(
        GenderConfig::class.java,
    )

    private val args: EnterKYCDataFragmentArgs by navArgs()

    //region Document Picker Intent
    private val startForDocumentPickerResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let {
                    kycDataVM.onDocumentSelected(
                        it,
                        documentUploadTarget!!,
                        FileExtension.PDF,
                        kycDetails.phoneNumber?.let(::LeoPhoneNumber),
                        kycDetails.passwordValidatedToken?.let(UUID::fromString),
                        requireContext(),
                        kycDetails.previousPath,
                    )
                }
            }
        }
    //endregion

    //region Camera Permission Intent
    private val startForCameraPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                kycDataVM.onCameraPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                kycDataVM.onCameraPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Camera permission denied, unable to proceed with image capture.")
                kycDataVM.onCameraPermissionDenied()
            }
        }
    //endregion

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_enter_kyc_data, container, false)
        initViews(view)
        setupListeners()
        setupDateOfBirthPicker()
        setupNationalIdIssuePicker()
        setupNationalIdExpiryPicker()
        setupDateEditText()
        setupNextButton()
        setupGenderDropdown()
        shouldAuthenticate = kycDetails.previousPath == KYCPreviousPath.PROFILE
        kycDataVM.getKYCData(args.kycData, kycDetails.previousPath, requireContext())
        if (kycDataVM.profileImageUri != null) {
            setImage(kycDataVM.profileImageUri!!, DocumentUploadTarget.PROFILE_IMAGE)
        }
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setStatusBarColor()
        setupToolbar()
        networkListenerCallback = this
        kycDataVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        kycDataVM.currentCameraState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onPause() {
        super.onPause()
        genderDropdown.dismiss()
    }

    private fun setupDateEditText() {
        nationalIdIssueDateET.selectionActionModeCallback()
        nationalIdExpiryDateET.selectionActionModeCallback()
        dateOfBirthET.selectionActionModeCallback()
    }

    private fun setupToolbar() {
        if (kycDetails.previousPath == KYCPreviousPath.PROFILE) {
            showToolbar()
            setDefaultToolbar(getString(R.string.profileScreenOptionUpdateKYCDetails))
            val params = profileImageCIV.layoutParams as ConstraintLayout.LayoutParams
            params.setMargins(
                resources.getDimensionPixelSize(R.dimen.dimen_0dp),
                resources.getDimensionPixelSize(R.dimen.dimen_0dp),
                resources.getDimensionPixelSize(R.dimen.dimen_0dp),
                resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            )
            profileImageCIV.layoutParams = params
        } else {
            showToolbar()
            setDefaultToolbar(getString(R.string.kycDetailsTitle))
        }
        setupBackPressed {
            kycDataVM.onBackPressed(findNavController())
        }
    }

    private fun reactToState(state: KYCDataState) {
        when (state) {
            is KYCDataState.AcceptInput -> {
                handleAcceptInput()
            }

            is KYCDataState.Error -> {
                handleErrorState(state)
            }

            is KYCDataState.Loading -> {
                handleLoadingState()
            }

            is KYCDataState.SuccessfulSubmit -> {
                handleSuccessfulSubmission()
            }

            is KYCDataState.Data -> {
                handleDocumentState(state)
            }

            is KYCDataState.PrefillData -> {
                handlePrefillDataState(state.kycData)
            }

            KYCDataState.FullScreenLoading -> {
                handleFullScreenLoading()
            }
        }
    }

    private fun handleFullScreenLoading() {
        progressBar.visibility = View.VISIBLE
        contentSection.visibility = View.GONE
        nextButton.visibility = View.GONE
    }

    private fun handlePrefillDataState(kycData: KYCDataUtils?) {
        progressBar.visibility = View.GONE
        contentSection.visibility = View.VISIBLE
        nextButton.visibility = View.VISIBLE
        kycData?.apply {
            firstNameET.setText(firstName)
            lastname?.let(lastNameET::setText)
            otherNames?.let(otherNameET::setText)
            nrbNumberET.setText(nationalId)
            val gender = if (gender.first().uppercase() == "M") {
                Gender.MALE
            } else {
                Gender.FEMALE
            }
            genderDropdown.setSelection(genderConfig.gender.indexOf(gender) + 1)
            val formattedDateOfBirth = dateOfBirth.getFormattedDateTime(
                requireContext(),
                dateTimeType = DateTimeType.DATE,
            )
            dateOfBirthET.setText(formattedDateOfBirth)
            val formattedNationalIdIssueDate = dateOfIssue.getFormattedDateTime(
                requireContext(),
                dateTimeType = DateTimeType.DATE,
            )
            nationalIdIssueDateET.setText(formattedNationalIdIssueDate)

            dateOfExpiry?.apply {
                val formattedNationalIdExpiryDate = getFormattedDateTime(
                    requireContext(),
                    dateTimeType = DateTimeType.DATE,
                )
                nationalIdExpiryDateET.setText(formattedNationalIdExpiryDate)
            }

            emailId?.let(emailAddressET::setText)

            placeOfBirth?.let(placeOfBirthET::setText)

            residentAddress?.let(residentAddressET::setText)

            postalAddress?.let(postalAddressET::setText)

            if (kycDetails.previousPath == KYCPreviousPath.PROFILE) {
                if (kycDataVM.profileImageUri != null) {
                    setImage(kycDataVM.profileImageUri ?: throw IllegalStateException("Uploaded Profile Image URI cannot be null"), DocumentUploadTarget.PROFILE_IMAGE)
                } else {
                    setImage(
                        (
                            UserSharedPreference.getUser()
                                ?: throw IllegalStateException("User cannot be null")
                            ).imageUrl.toUri(),
                        DocumentUploadTarget.PROFILE_IMAGE,
                    )
                }
            }
        }
    }

    private fun handleDocumentState(state: KYCDataState.Data) {
        when (val imageState = state.documentViewState) {
            is ProfileDocumentViewState.DocumentUploaded -> {
                handleDocumentUploadedState(imageState)
            }

            is ProfileDocumentViewState.InitializeDocumentSelection -> {
                handleDocumentInitializationState(imageState)
            }

            is ProfileDocumentViewState.SelectDocumentAndUpload -> {
                handleDocumentSelectionAndUpload(imageState.target, imageState.showRemoveOption)
            }
        }
    }

    private fun reactToState(state: KYCDataCameraState) {
        when (state) {
            is KYCDataCameraState.CameraPermissionAlert -> {
                handleCameraPermissionAlertState()
            }

            is KYCDataCameraState.CameraPermissionDenied -> {
                showCameraPermissionDeniedError()
            }

            is KYCDataCameraState.CameraPermissionGranted -> {
                startCamera()
            }

            is KYCDataCameraState.CameraPermissionRequested -> {
                requestCameraPermission()
            }

            is KYCDataCameraState.Waiting -> {
                handleWaitingOnCameraState()
            }
        }
    }

    private fun showCameraPermissionDeniedError() {
        showCameraPermissionAlert(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            message = getString(R.string.kycCameraPermissionDeniedSubtitle),
            dialogId = CAMERA_PERMISSION_DENIED_DIALOG_CODE,
            positiveActionLabel = getString(R.string.alertActionDismiss),
            negativeActionLabel = requireContext().getEmptyString(),
        )
    }

    private fun requestCameraPermission() {
        startForCameraPermissionResult.launch(Manifest.permission.CAMERA)
    }

    private fun handleWaitingOnCameraState() {
        // Nothing to do. Waiting for user to tap on `Capture an Image` from dialog.
    }

    private fun startCamera() {
        ImagePicker.openCamera(this)
        kycDataVM.onCameraPermissionDialogDismissed()
    }

    private fun handleCameraPermissionAlertState() {
        showCameraPermissionAlert(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            getString(R.string.kycCameraPermissionDeniedSubtitle),
            CAMERA_PERMISSION_DIALOG_CODE,
            getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun showCameraPermissionAlert(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleDocumentUploadedState(state: ProfileDocumentViewState.DocumentUploaded) {
        when (state.target) {
            DocumentUploadTarget.PROFILE_IMAGE -> kycDataVM.documentIdMap[KEY_PROFILE_IMAGE_ID] =
                state.documentId

            else -> {
                throw IllegalStateException("Only profile image is supported in this screen.")
            }
        }
        when (state.fileExtension) {
            FileExtension.JPG -> setImage(state.documentUri, state.target)
            FileExtension.PNG -> setImage(state.documentUri, state.target)
            FileExtension.PDF -> setImage(state.documentUri, state.target)
        }
        kycDataVM.onDocumentUploaded()
    }

    private fun handleDocumentInitializationState(documentSelectionState: ProfileDocumentViewState.InitializeDocumentSelection) {
        setLoadingForTarget(documentSelectionState.target)
    }

    private fun setLoadingForTarget(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                loadingProfileImagePB.visibility = View.VISIBLE
                uploadProfileImageIV.visibility = View.INVISIBLE
            }

            else -> {
                throw IllegalStateException("Only profile image is supported in this screen.")
            }
        }
    }

    private fun handleDocumentSelectionAndUpload(
        target: DocumentUploadTarget,
        showRemoveOption: Boolean,
    ) {
        val showDocumentOption = when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> false
            else -> true
        }
        val documentPickerDialog = DocumentPickerDialog()
        documentPickerDialog.setArguments(
            showDocumentOption,
            showRemoveOption,
            target,
        )
        documentPickerDialog.show(childFragmentManager, DocumentPickerDialog.TAG)
    }

    private fun handleAcceptInput() {
        dismissProgressDialog()
        resetAllLoading()
    }

    private fun handleSuccessfulSubmission() {
        dismissProgressDialog()
        kycDataVM.onKYCDataSubmitted(findNavController())
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(state: KYCDataState.Error) {
        hideKeyboard()
        if (state.documentUploadTarget != null) {
            resetImageLoading(state.documentUploadTarget)
            documentUploadTarget = null
        }
        dismissProgressDialog()
        progressBar.visibility = View.GONE
        contentSection.visibility = View.VISIBLE
        nextButton.visibility = View.VISIBLE
        when (state.error.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    state.error.errorMessage,
                    requireView(),
                    kycDataVM::onInlineErrorDismissed,
                )
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    state.error.errorTitle,
                    state.error.errorMessage,
                    state.errorCode ?: DialogCodes.KYC_DATA_ERROR_CODE,
                    forceOutUserDestination = if (kycDetails.previousPath == KYCPreviousPath.PROFILE) ForceOutUserDestination.PROFILE else ForceOutUserDestination.SIGN_UP_IN,
                )
                requestViewFocus(state)
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun requestViewFocus(state: KYCDataState.Error) {
        when (state.errorField) {
            ErrorField.FIRST_NAME -> firstNameET.requestFocus()
            ErrorField.LAST_NAME -> lastNameET.requestFocus()
            ErrorField.OTHER_NAME -> otherNameET.requestFocus()
            ErrorField.GENDER -> genderDropdown.requestFocus()
            ErrorField.NRB_NUMBER -> nrbNumberET.requestFocus()
            ErrorField.DATE_OF_BIRTH -> dateOfBirthET.requestFocus()
            ErrorField.EMAIL -> emailAddressET.requestFocus()
            ErrorField.PLACE_OF_BIRTH -> placeOfBirthET.requestFocus()
            ErrorField.NATIONAL_ID_ISSUE_DATE -> nationalIdIssueDateET.requestFocus()
            ErrorField.NATIONAL_ID_EXPIRY_DATE -> nationalIdExpiryDateET.requestFocus()
            null -> {
                // Do nothing
            }
        }
    }

    private fun initViews(view: View) {
        //region Profile Image
        profileImageTV = view.findViewById(R.id.add_profile)
        uploadProfileImageIV = view.findViewById(R.id.upload_profile_image)
        loadingProfileImagePB = view.findViewById(R.id.profile_loading)
        cameraProfileImageIV = view.findViewById(R.id.profile_camera_iv)
        profileImageCIV = view.findViewById(R.id.profile_iv)
        //endregion

        //region KYC Data
        firstNameET = view.findViewById(R.id.first_name_et)
        firstNameTIL = view.findViewById(R.id.first_name_til)
        firstNameET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = firstNameTIL,
                maxLength = Config.MAX_FIRST_NAME_LENGTH,
            ),
        )

        lastNameET = view.findViewById(R.id.last_name_et)
        lastNameTIL = view.findViewById(R.id.last_name_til)
        lastNameET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = lastNameTIL,
                maxLength = Config.MAX_LAST_NAME_LENGTH,
            ),
        )

        dateOfBirthET = view.findViewById(R.id.date_of_birth_et)

        otherNameET = view.findViewById(R.id.other_name_et)
        otherNameTIL = view.findViewById(R.id.other_name_til)
        otherNameET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = otherNameTIL,
                maxLength = Config.MAX_OTHER_NAME_LENGTH,
            ),
        )

        nrbNumberET = view.findViewById(R.id.nrb_number_et)
        nrbNumberTIL = view.findViewById(R.id.nrb_number_til)
        nrbNumberET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = nrbNumberTIL,
                maxLength = Config.MAX_NATIONAL_ID_LENGTH,
            ),
        )

        emailAddressET = view.findViewById(R.id.email_et)

        placeOfBirthET = view.findViewById(R.id.place_of_birth_et)
        placeOfBirthTIL = view.findViewById(R.id.place_of_birth_til)
        placeOfBirthET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = placeOfBirthTIL,
                maxLength = Config.MAX_PLACE_OF_BIRTH_LENGTH,
            ),
        )

        nationalIdIssueDateET = view.findViewById(R.id.national_id_issue_date_et)
        nationalIdExpiryDateET = view.findViewById(R.id.national_id_expiry_date_et)

        residentAddressET = view.findViewById(R.id.resident_address_et)
        residentAddressTIL = view.findViewById(R.id.resident_address_til)
        residentAddressET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = residentAddressTIL,
                maxLength = Config.MAX_RESIDENT_ADDRESS_LENGTH,
            ),
        )

        postalAddressET = view.findViewById(R.id.postal_address_et)
        postalAddressTIL = view.findViewById(R.id.postal_address_til)
        postalAddressET.addTextChangedListener(
            SingleLineCharacterLimitTextWatcher(
                textInputLayout = postalAddressTIL,
                maxLength = Config.MAX_POSTAL_ADDRESS_LENGTH,
            ),
        )

        genderDropdown = view.findViewById(R.id.gender_dropdown)
        genderDropDownHint = view.findViewById(R.id.gender_dropdown_hint_tv)
        nextButton = view.findViewById(R.id.next_button)
        progressBar = view.findViewById(R.id.loading_progress_bar)
        contentSection = view.findViewById(R.id.content_section)
        genderDropDownIV = view.findViewById(R.id.gender_iv)
        //endregion
    }

    private fun setupListeners() {
        //region Profile Image
        uploadProfileImageIV.setOnClickListener {
            kycDataVM.onDocumentUploadTapped(DocumentUploadTarget.PROFILE_IMAGE, false)
        }
        cameraProfileImageIV.setOnClickListener {
            kycDataVM.onDocumentUploadTapped(DocumentUploadTarget.PROFILE_IMAGE, false)
        }
        profileImageCIV.setOnClickListener {
            kycDataVM.onDocumentUploadTapped(DocumentUploadTarget.PROFILE_IMAGE, false)
        }
        //endregion

        ImagePicker.setImagePickerFailureListener(this)
    }

    private fun deleteImageOffline(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                // Cannot remove image for mandatory field, it can only be changed
            }

            else -> {
                throw IllegalStateException("Only profile image is supported in this screen.")
            }
        }
    }

    private fun setupGenderDropdown() {
        val genderListItem = mutableListOf<GenderListItem>()
        genderListItem.add(
            GenderListItem.Header(getString(R.string.kycGenderPlaceholder)),
        )
        genderConfig.gender.forEach {
            if (it.value == Gender.MALE.value) {
                genderListItem.add(
                    GenderListItem.Gender(getString(R.string.genderMale)),
                )
            } else if (it.value == Gender.FEMALE.value) {
                genderListItem.add(
                    GenderListItem.Gender(getString(R.string.genderFemale)),
                )
            }
        }
        genderDropdown.dropDownVerticalOffset =
            (resources.getDimensionPixelSize(R.dimen.dimen_48dp))
        genderDropdown.adapter = GenderDropdownAdapter(
            genderListItem,
            requireContext(),
        )
        genderDropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(p0: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (genderListItem[position] is GenderListItem.Gender) {
                    val gender =
                        if ((genderListItem[position] as GenderListItem.Gender).name == getString(R.string.genderMale)) {
                            Gender.MALE
                        } else {
                            Gender.FEMALE
                        }
                    genderDropDownHint.visibility = View.VISIBLE
                    genderDropDownIV.setPadding(resources.getDimensionPixelOffset(R.dimen.dimen_0dp))
                    genderDropdown.setPadding(
                        resources.getDimensionPixelSize(R.dimen.dimen_4dp),
                        resources.getDimensionPixelSize(R.dimen.dimen_0dp),
                        resources.getDimensionPixelSize(R.dimen.dimen_4dp),
                        resources.getDimensionPixelSize(R.dimen.dimen_4dp),
                    )
                    kycDataVM.onGenderDropdownUpdated(gender)
                }
            }

            override fun onNothingSelected(p0: AdapterView<*>?) {}
        }
        genderDropdown.addContentDescriptionString(
            R.string.axKycDetailsGenderLabel,
            requireContext(),
        )
    }

    private fun setupDateOfBirthPicker() {
        val calendarConstraints = CalendarConstraints.Builder()
            .setEnd(getMinimumAllowedDateMillis())
            .setStart(getMaximumAllowDateMillis())
            .setOpenAt(getMinimumAllowedDateMillis())
            .setValidator(DateValidatorPointBackward.before(getMinimumAllowedDateMillis()))
            .build()

        val datePicker = MaterialDatePicker.Builder
            .datePicker()
            .setCalendarConstraints(calendarConstraints)
            .build()

        dateOfBirthET.setOnClickListener {
            if (!datePicker.isAdded) {
                datePickedFor = DatePickedFor.DATE_OF_BIRTH
                datePicker.show(childFragmentManager, DATE_PICKER_TAG)
            }
        }

        datePicker.addOnPositiveButtonClickListener(this)
        datePicker.addOnNegativeButtonClickListener(this)
    }

    private fun setupNationalIdIssuePicker() {
        val maximumAllowedDateMillis =
            LocalDateTime.of(LocalDate.now(), LocalTime.now()).atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli()

        val calendarConstraints = CalendarConstraints.Builder()
            .setStart(getMaximumAllowDateMillis())
            .setEnd(maximumAllowedDateMillis)
            .setValidator(DateValidatorPointBackward.before(maximumAllowedDateMillis))
            .build()

        val datePicker = MaterialDatePicker.Builder
            .datePicker()
            .setCalendarConstraints(calendarConstraints)
            .build()

        nationalIdIssueDateET.setOnClickListener {
            if (!datePicker.isAdded) {
                datePickedFor = DatePickedFor.NATIONAL_ID_ISSUE_DATE
                datePicker.show(childFragmentManager, DATE_PICKER_TAG)
            }
        }

        datePicker.addOnPositiveButtonClickListener(this)
        datePicker.addOnNegativeButtonClickListener(this)
    }

    private fun setupNationalIdExpiryPicker() {
        val minimumAllowedDateMillis =
            LocalDateTime.of(LocalDate.now(), LocalTime.now()).atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli()

        val calendarConstraints = CalendarConstraints.Builder()
            .setStart(minimumAllowedDateMillis)
            .setValidator(DateValidatorPointForward.from(minimumAllowedDateMillis))
            .build()

        val datePicker = MaterialDatePicker.Builder
            .datePicker()
            .setCalendarConstraints(calendarConstraints)
            .build()

        nationalIdExpiryDateET.setOnClickListener {
            if (!datePicker.isAdded) {
                datePickedFor = DatePickedFor.NATIONAL_ID_EXPIRY_DATE
                datePicker.show(childFragmentManager, DATE_PICKER_TAG)
            }
        }

        datePicker.addOnPositiveButtonClickListener(this)
        datePicker.addOnNegativeButtonClickListener(this)
    }

    private fun getMaximumAllowDateMillis(): Long {
        val date = LocalDateTime.of(LocalDate.now(), LocalTime.now())
        return date.atZone(ZoneId.systemDefault())
            .minusYears(MAXIMUM_ALLOWED_AGE)
            .toInstant()
            .toEpochMilli()
    }

    private fun getMinimumAllowedDateMillis(): Long {
        val date = LocalDateTime.of(LocalDate.now(), LocalTime.now())
        return date.atZone(ZoneId.systemDefault())
            .minusYears(MINIMUM_ALLOWED_AGE)
            .toInstant()
            .toEpochMilli()
    }

    override fun onDateSet(p0: DatePicker?, year: Int, month: Int, day: Int) {
        val date = LocalDate.of(year, month + 1, day)
        val formattedEndDate = date.getFormattedDateTime(
            requireContext(),
            dateTimeType = DateTimeType.DATE,
        )
        when (datePickedFor) {
            DatePickedFor.DATE_OF_BIRTH -> dateOfBirthET.setText(formattedEndDate)
            DatePickedFor.NATIONAL_ID_ISSUE_DATE -> nationalIdIssueDateET.setText(formattedEndDate)
            DatePickedFor.NATIONAL_ID_EXPIRY_DATE -> nationalIdExpiryDateET.setText(formattedEndDate)
        }
    }

    @SuppressLint("HardwareIds")
    private fun setupNextButton() {
        nextButton.setOnClickListener {
            if (isProfilePictureBeingUploaded()) {
                showErrorDialog(
                    getString(R.string.alertTitleWaitForPictureUpload),
                    getString(R.string.alertMessageWaitForPictureUpload),
                    DialogCodes.PROFILE_PICTURE_UPLOAD,
                )
            } else {
                val otherName = otherNameET.trim().ifBlank {
                    null
                }
                val lastName = lastNameET.trim().ifBlank {
                    null
                }
                kycDataVM.onNextClicked(
                    requireContext(),
                    kycDetails.passwordValidatedToken?.let(UUID::fromString),
                    firstNameET.trim(),
                    lastName,
                    dateOfBirthET.trim(),
                    otherName,
                    nrbNumberET.trim(),
                    emailAddressET.trim(),
                    kycDetails.phoneNumber,
                    placeOfBirthET.trim(),
                    nationalIdIssueDateET.trim(),
                    nationalIdExpiryDateET.trim(),
                    residentAddressET.trim(),
                    postalAddressET.trim(),
                    kycDataVM.documentIdMap[KEY_PROFILE_IMAGE_ID],
                    kycDetails.previousPath,
                )
            }
        }
    }

    private fun isProfilePictureBeingUploaded(): Boolean {
        return loadingProfileImagePB.isVisible
    }

    /**
     * Not allowing user to crop free style when selecting an image for profile.
     */
    private fun isFreeStyleCropEnabled() =
        documentUploadTarget != DocumentUploadTarget.PROFILE_IMAGE

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == AppCompatActivity.RESULT_OK) {
            when (requestCode) {
                ImagePicker.IMAGE_PICKER_GALLERY_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        data,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = isFreeStyleCropEnabled(),
                    )
                }

                ImagePicker.IMAGE_PICKER_CAMERA_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        null,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = isFreeStyleCropEnabled(),
                    )
                }

                ImagePicker.IMAGE_CROP_REQUEST_CODE -> {
                    val resultUri = ImagePicker.getOutput(data!!)!!
                    kycDataVM.onDocumentSelected(
                        resultUri,
                        documentUploadTarget!!,
                        FileExtension.JPG,
                        kycDetails.phoneNumber?.let(::LeoPhoneNumber),
                        kycDetails.passwordValidatedToken?.let(UUID::fromString),
                        requireContext(),
                        kycDetails.previousPath,
                    )
                }

                ImagePicker.IMAGE_CROP_RESULT_ERROR_CODE -> {
                    resetImageLoading(documentUploadTarget!!)
                    val cropError = ImagePicker.getError(data!!)
                    cropError?.message?.let {
                        Timber.tag(TAG).d("ImagePicker: $it")
                    }
                }
            }
        }
    }

    private fun setImage(
        resultUri: Uri,
        target: DocumentUploadTarget,
    ) {
        kycDataVM.profileImageUri = resultUri
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                uploadProfileImageIV.visibility = View.INVISIBLE
                cameraProfileImageIV.visibility = View.VISIBLE
                profileImageCIV.visibility = View.VISIBLE
                loadingProfileImagePB.visibility = View.GONE
                profileImageCIV.imageView.loadImage(resultUri)
                updateUploadedImageTextViews(
                    profileImageTV,
                    requireContext().getString(R.string.kycProfilePhoto),
                )
            }

            else -> {
                throw IllegalStateException("Only profile image is supported in this screen.")
            }
        }
    }

    private fun updateUploadedImageTextViews(textView: TextView, text: String) {
        textView.text = text
        textView.setTextColor(requireContext().getColor(R.color.titleTextColor))
    }

    private fun showImagePickingInstructionToast(documentUploadTarget: DocumentUploadTarget) {
        if (documentUploadTarget == DocumentUploadTarget.PROFILE_IMAGE) {
            Toast.makeText(
                requireContext(),
                requireContext().getString(R.string.toastMessageImageDimensions),
                Toast.LENGTH_LONG,
            ).show()
        }
    }

    private fun pickDocument() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/pdf"
        }

        startForDocumentPickerResult.launch(intent)
    }

    private fun resetImageLoading(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                loadingProfileImagePB.visibility = View.INVISIBLE
                uploadProfileImageIV.visibility = View.VISIBLE
            }

            else -> {
                throw IllegalStateException("Only profile image is supported in this screen.")
            }
        }
    }

    private fun resetAllLoading() {
        loadingProfileImagePB.visibility = View.INVISIBLE

        /***
         * Once we have reset loading progress bar and made it invisible we should check if
         * a image was uploaded or not, according to that we need to make other images for profile
         * image on this screen visible or invisible or else a blank space might get shown on this
         * screen.
         */
        if (kycDataVM.profileImageUri != null) {
            uploadProfileImageIV.visibility = View.INVISIBLE
            profileImageCIV.visibility = View.VISIBLE
            cameraProfileImageIV.visibility = View.VISIBLE
        } else {
            uploadProfileImageIV.visibility = View.VISIBLE
            profileImageCIV.visibility = View.INVISIBLE
            cameraProfileImageIV.visibility = View.INVISIBLE
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycDataVM.onOpenSettingsClicked(
                requireContext(),
            )

            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                kycDataVM.onErrorDialogDismissed()
            }

            CAMERA_PERMISSION_DENIED_DIALOG_CODE -> {
                kycDataVM.onCameraPermissionDialogDismissed()
                kycDataVM.onErrorDialogDismissed()
            }

            KYCDataVM.ERROR_CODE_TOKEN_EXPIRED -> {
                when (kycDetails.previousPath) {
                    KYCPreviousPath.SIGN_IN -> {
                        findNavController().popBackStack(R.id.sign_in_nav, true)
                    }

                    KYCPreviousPath.SIGN_UP -> {
                        restartActivity()
                    }

                    KYCPreviousPath.PROFILE -> throw IllegalStateException("No token involved in update KYC flow.")
                }
            }

            DialogCodes.SIGN_UP_IN_SESSION_EXPIRED -> {
                when (kycDetails.previousPath) {
                    KYCPreviousPath.SIGN_IN -> {
                        findNavController().popBackStack(R.id.sign_in_nav, true)
                    }

                    KYCPreviousPath.SIGN_UP -> {
                        findNavController().popBackStack(R.id.sign_up_nav, true)
                    }

                    KYCPreviousPath.PROFILE -> {
                        throw IllegalStateException("SIGNUP_SESSION_EXPIRED error code cannot be encountered from Update KYC Flow")
                    }
                }
            }

            DialogCodes.PROFILE_PICTURE_UPLOAD -> {
                // Nothing to handle as we are waiting for profile picture to get uploaded
            }

            else -> {
                kycDataVM.onErrorDialogDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycDataVM.onCameraPermissionAlertDismissed()
        }
    }

    override fun captureImageFromCamera(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        if (!checkCameraPermissionGranted(requireContext())) {
            kycDataVM.onCameraPermissionRequested()
        } else {
            showImagePickingInstructionToast(documentUploadTarget)
            startCamera()
            kycDataVM.onOptionSelectedDocumentPickerDialog()
        }
    }

    override fun pickImageFromGallery(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        showImagePickingInstructionToast(documentUploadTarget)
        ImagePicker.openImageGallery(this)
        kycDataVM.onOptionSelectedDocumentPickerDialog()
    }

    override fun pickDocumentFromStorage(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        pickDocument()
    }

    override fun removeDocument(documentUploadTarget: DocumentUploadTarget) {
        deleteImageOffline(DocumentUploadTarget.PROFILE_IMAGE)
    }

    override fun dismissed() {
        kycDataVM.onDocumentPickerDismissed()
    }

    override fun imagePickerFailure(error: Exception) {
        kycDataVM.onDocumentPickerFailure(requireContext(), documentUploadTarget!!)
        Timber.tag(TAG).e(error)
    }

    override fun onPositiveButtonClick(selection: Long) {
        val instant = Instant.ofEpochMilli(selection)
        val formattedEndDate = instant.getFormattedDateTime(
            requireContext(),
            dateTimeType = DateTimeType.DATE,
        )
        when (datePickedFor) {
            DatePickedFor.DATE_OF_BIRTH -> dateOfBirthET.setText(formattedEndDate)
            DatePickedFor.NATIONAL_ID_ISSUE_DATE -> nationalIdIssueDateET.setText(formattedEndDate)
            DatePickedFor.NATIONAL_ID_EXPIRY_DATE -> nationalIdExpiryDateET.setText(formattedEndDate)
        }
    }

    override fun onClick(p0: View) {
        // Do nothing
    }

    private enum class DatePickedFor {
        DATE_OF_BIRTH, NATIONAL_ID_ISSUE_DATE, NATIONAL_ID_EXPIRY_DATE
    }

    override fun onNetworkAvailable() {
        kycDataVM.getKYCData(args.kycData, kycDetails.previousPath, requireContext())
    }
}

private const val TAG = "KYCDataFragment"
const val IMAGE_COMPRESSION_QUALITY: Int = 100
private const val MAXIMUM_ALLOWED_AGE = 200L
private const val MINIMUM_ALLOWED_AGE = 18L
const val CAMERA_PERMISSION_DIALOG_CODE: Int = 115
const val CAMERA_PERMISSION_DENIED_DIALOG_CODE: Int = 116
const val KEY_PROFILE_IMAGE_ID: String = "profile_image_id"
private const val DATE_PICKER_TAG = "profile_image_id"
