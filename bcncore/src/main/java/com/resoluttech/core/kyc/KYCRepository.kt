package com.resoluttech.core.kyc

import com.resoluttech.bcn.document.FileAttributes
import com.resoluttech.bcn.document.GetDocumentIdRPC
import com.resoluttech.bcn.document.GetUrlRPC
import com.resoluttech.bcn.profile.GetKYCDataRPC
import com.resoluttech.bcn.profile.UpdateKYCDataRPC
import com.resoluttech.bcn.signUpIn.SubmitKYCDataRPC
import com.resoluttech.bcn.types.DeviceInformation
import com.resoluttech.bcn.types.Gender
import com.resoluttech.bcn.types.KYCData
import com.resoluttech.bcn.types.PushToken
import com.resoluttech.bcn.types.UserPersonalDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import java.time.LocalDate
import java.util.UUID
import com.resoluttech.bcn.profile.VerifyKYCDataRPC as ProfileVerifyKYCDataRPC
import com.resoluttech.bcn.signUpIn.VerifyKYCDataRPC as SignUpInVerifyKYCDataRPC

class KYCRepository {

    private val getUrlRPC: GetUrlRPC by KoinJavaComponent.inject(
        GetUrlRPC::class.java,
    )

    private val getDocumentIdRPC: GetDocumentIdRPC by KoinJavaComponent.inject(
        GetDocumentIdRPC::class.java,
    )

    private val submitKYCDataRPC: SubmitKYCDataRPC by KoinJavaComponent.inject(
        SubmitKYCDataRPC::class.java,
    )

    private val signUpInVerifyKYCDataRPC: SignUpInVerifyKYCDataRPC by KoinJavaComponent.inject(
        SignUpInVerifyKYCDataRPC::class.java,
    )

    private val profileKYCDataRPC: ProfileVerifyKYCDataRPC by KoinJavaComponent.inject(
        ProfileVerifyKYCDataRPC::class.java,
    )

    private val getKYCDataRPC: GetKYCDataRPC by KoinJavaComponent.inject(
        GetKYCDataRPC::class.java,
    )

    private val updateKYCDataRPC: UpdateKYCDataRPC by KoinJavaComponent.inject(
        UpdateKYCDataRPC::class.java,
    )

    suspend fun getDocumentUploadUrl(
        phoneNumber: LeoPhoneNumber,
        passwordValidatedToken: UUID,
        fileAttributes: FileAttributes,
    ): LeoRPCResult<GetUrlRPC.Response, GetUrlRPC.Error> {
        return getUrlRPC.execute(
            GetUrlRPC.Request(
                phoneNumber,
                passwordValidatedToken,
                fileAttributes,
            ),
        )
    }

    suspend fun getDocumentId(
        phoneNumber: LeoPhoneNumber,
        passwordValidatedToken: UUID,
        sha256: String,
    ): LeoRPCResult<GetDocumentIdRPC.Response, GetDocumentIdRPC.Error> {
        return getDocumentIdRPC.execute(
            GetDocumentIdRPC.Request(
                phoneNumber,
                passwordValidatedToken,
                sha256,
            ),
        )
    }

    suspend fun submitKYCData(
        passwordValidatedToken: UUID,
        signatureImageId: UUID,
        nationalIdFrontId: UUID,
        nationalIdBackId: UUID,
        passportFrontPhotoId: UUID?,
        passportBackPhotoId: UUID?,
        proofOfResidencePhotoId: UUID,
        pushToken: PushToken?,
        deviceInformation: DeviceInformation,
    ): LeoRPCResult<SubmitKYCDataRPC.Response, SubmitKYCDataRPC.Error> {
        return submitKYCDataRPC.execute(
            SubmitKYCDataRPC.Request(
                passwordValidatedToken,
                signatureImageId,
                nationalIdFrontId,
                nationalIdBackId,
                proofOfResidencePhotoId,
                passportFrontPhotoId,
                passportBackPhotoId,
                pushToken,
                deviceInformation,
            ),
        )
    }

    suspend fun getKYCData(): LeoRPCResult<GetKYCDataRPC.Response, GetKYCDataRPC.Error> {
        return getKYCDataRPC.execute(
            GetKYCDataRPC.Request,
        )
    }

    suspend fun signUpInVerifyKYCData(
        phoneNumber: LeoPhoneNumber,
        passwordValidatedToken: UUID,
        emailId: LeoEmailId?,
        profileImageId: UUID,
        firstName: String,
        placeOfBirth: String,
        lastName: String?,
        otherName: String?,
        gender: Gender,
        dob: LocalDate,
        nationalIdIssueDate: LocalDate,
        nationalIdExpiryDate: LocalDate,
        identificationNumber: String,
        residentAddress: String,
        postalAddress: String,
    ): LeoRPCResult<SignUpInVerifyKYCDataRPC.Response, SignUpInVerifyKYCDataRPC.Error> {
        val kycData = KYCData(
            UserPersonalDetails(
                firstName = firstName,
                lastName = lastName,
                otherName = otherName,
                gender = gender,
                dateOfBirth = dob,
                nationalId = identificationNumber,
            ),
            emailId,
            placeOfBirth,
            nationalIdIssueDate,
            nationalIdExpiryDate,
            residentAddress,
            postalAddress,
        )

        return signUpInVerifyKYCDataRPC.execute(
            SignUpInVerifyKYCDataRPC.Request(
                phoneNumber,
                passwordValidatedToken,
                kycData,
                profileImageId,
            ),
        )
    }

    suspend fun profileVerifyKYCData(
        emailId: LeoEmailId?,
        profileImageId: UUID?,
        firstName: String,
        placeOfBirth: String,
        lastName: String?,
        otherName: String?,
        gender: Gender,
        dob: LocalDate,
        nationalIdIssueDate: LocalDate,
        nationalIdExpiryDate: LocalDate,
        identificationNumber: String,
        residentAddress: String,
        postalAddress: String,
    ): LeoRPCResult<ProfileVerifyKYCDataRPC.Response, ProfileVerifyKYCDataRPC.Error> {
        val kycData = KYCData(
            UserPersonalDetails(
                firstName = firstName,
                lastName = lastName,
                otherName = otherName,
                gender = gender,
                dateOfBirth = dob,
                nationalId = identificationNumber,
            ),
            emailId,
            placeOfBirth,
            nationalIdIssueDate,
            nationalIdExpiryDate,
            residentAddress,
            postalAddress,
        )

        return profileKYCDataRPC.execute(
            ProfileVerifyKYCDataRPC.Request(
                kycData,
                profileImageId,
            ),
        )
    }

    suspend fun updatedKYCRPC(
        signatureImageId: UUID,
        nationalIdFrontImageId: UUID,
        nationalIdBackImageId: UUID,
        proofOfResidenceImageId: UUID,
        passportFrontImageId: UUID?,
        passportBackImageId: UUID?,
    ): LeoRPCResult<UpdateKYCDataRPC.Response, UpdateKYCDataRPC.Error> {
        return updateKYCDataRPC.execute(
            UpdateKYCDataRPC.Request(
                signatureImageId,
                nationalIdFrontImageId,
                nationalIdBackImageId,
                proofOfResidenceImageId,
                passportFrontImageId,
                passportBackImageId,
            ),
        )
    }
}
