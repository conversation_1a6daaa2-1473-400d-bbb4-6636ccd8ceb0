package com.resoluttech.core.kyc

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.document.FileAttributes
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetDocumentIdRPC
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPC
import com.resoluttech.bcn.document.GetUrlRPC
import com.resoluttech.bcn.profile.UpdateKYCDataRPC
import com.resoluttech.bcn.signUpIn.SubmitKYCDataRPC
import com.resoluttech.bcn.types.App
import com.resoluttech.bcn.types.FrontEndPlatform
import com.resoluttech.bcn.types.PushToken
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.landingscreen.LandingScreenSharedPreference
import com.resoluttech.core.profile.ProfileRepository
import com.resoluttech.core.profile.PushTokenHelper
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getAssetSize
import com.resoluttech.core.utils.getByteArray
import com.resoluttech.core.utils.getDeviceInformation
import com.resoluttech.core.utils.getSHA256
import com.resoluttech.core.utils.openSystemSettings
import com.resoluttech.core.utils.validateDocument
import com.suryadigital.leo.kedwig.Response
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class KYCDocumentsVM : ViewModel() {
    private val _currentState = MutableLiveData<KYCDocumentState>(KYCDocumentState.AcceptInput)
    val currentState: LiveData<KYCDocumentState> = _currentState
    private val _currentCameraState: MutableLiveData<KYCDocumentCameraState> =
        MutableLiveData(KYCDocumentCameraState.Waiting)
    val currentCameraState: LiveData<KYCDocumentCameraState> = _currentCameraState
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val kycRepository = KYCRepository()
    private val profileRepository = ProfileRepository()
    val documentIdMap: java.util.HashMap<String, UUID?> = HashMap()
    val targetMutableList: MutableList<DocumentUploadTarget> = mutableListOf()

    /***
     * The variable [canUpdateDocumentIdMap] is created to check if we can update [documentIdMap].
     * Example of when this is helpful : When user uploads a document we create a thread from
     * Coroutine to call RPC to upload document, while the RPC is getting called user can hit back
     * button and main thread will take user to Enter KYC Screen. The thread which was calling RPC
     * to upload document will get executed, at this time we won't be updating [documentIdMap].
     */
    private var canUpdateDocumentIdMap: Boolean = false
    fun init() {
        canUpdateDocumentIdMap = true
    }
    fun onDocumentSelected(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        phoneNumber: LeoPhoneNumber?,
        temporaryToken: UUID?,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        _currentState.postValue(
            KYCDocumentState.Data(
                DocumentViewState.InitializeDocumentSelection(target),
            ),
        )

        if (!validateDocument(documentUri, context)) {
            _currentState.postValue(
                KYCDocumentState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidFileSize),
                        context.getString(
                            R.string.alertMessageInvalidFileSize,
                            DocumentUploadHelper.MIN_DOCUMENT_SIZE,
                            DocumentUploadHelper.MAX_DOCUMENT_SIZE,
                        ),
                    ),
                    null,
                    target,
                ),
            )
            return
        }

        vmIOScope.launch {
            val extension = when (fileExtension) {
                FileExtension.JPG -> FileAttributes.FileExtension.JPG
                FileExtension.PNG -> FileAttributes.FileExtension.PNG
                FileExtension.PDF -> FileAttributes.FileExtension.PDF
            }
            val documentSHA256 = getSHA256(getByteArray(context, documentUri))
            val fileAttributes = FileAttributes(
                documentSHA256,
                documentUri.lastPathSegment,
                extension,
                context.getAssetSize(documentUri)!!.toInt(),
            )

            executeRPC(
                context,
                rpcBlock = {
                    if (previousPath == KYCPreviousPath.PROFILE) {
                        getDocumentUploadURLForSignedUser(
                            documentUri,
                            target,
                            fileExtension,
                            context,
                            fileAttributes,
                            documentSHA256,
                            previousPath,
                        )
                    } else {
                        getDocumentUploadURL(
                            documentUri,
                            target,
                            fileExtension,
                            phoneNumber!!,
                            temporaryToken!!,
                            context,
                            fileAttributes,
                            documentSHA256,
                            previousPath,
                        )
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        it.errorCode,
                        target,
                        it.type,
                    )
                },
            )
        }
    }

    private suspend fun getDocumentUploadURL(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        context: Context,
        fileAttributes: FileAttributes,
        documentSHA256: String,
        previousPath: KYCPreviousPath,
    ) {
        when (
            val result = kycRepository.getDocumentUploadUrl(
                phoneNumber,
                temporaryToken,
                fileAttributes,
            )
        ) {
            is LeoRPCResult.LeoResponse -> {
                handleGetUrlRPCResponse(
                    result.response,
                    DocumentData(
                        documentUri,
                        fileAttributes,
                        target,
                        fileExtension,
                    ),
                    phoneNumber,
                    temporaryToken,
                    documentSHA256,
                    context,
                    previousPath,
                )
            }
            is LeoRPCResult.LeoError -> {
                handleGetUrlRPCError(context, result.error, target)
            }
        }
    }

    private suspend fun getDocumentUploadURLForSignedUser(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        context: Context,
        fileAttributes: FileAttributes,
        documentSHA256: String,
        previousPath: KYCPreviousPath,
    ) {
        when (
            val result = profileRepository.getDocumentUploadUrl(
                fileAttributes,
            )
        ) {
            is LeoRPCResult.LeoResponse -> {
                handleGetUrlRPCForSignedInUserResponse(
                    result.response,
                    DocumentData(
                        documentUri,
                        fileAttributes,
                        target,
                        fileExtension,
                    ),
                    documentSHA256,
                    context,
                    previousPath,
                )
            }
            is LeoRPCResult.LeoError -> {
                // No error to handle
            }
        }
    }

    private fun handleGetUrlRPCResponse(
        response: GetUrlRPC.Response,
        documentData: DocumentData,
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        sha256: String,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        vmIOScope.launch {
            DocumentUploadHelper.uploadDocument(
                response.uploadDestinationURL,
                documentData.documentUri,
                {
                    handleException(
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageServerError),
                        null,
                        null,
                    )
                },
                {
                    handleImageUploadedResponse(
                        it,
                        phoneNumber,
                        temporaryToken,
                        sha256,
                        documentData,
                        context,
                        previousPath,
                    )
                },
                {
                    handleException(
                        context.getString(R.string.alertTitleProfileImageUploadFailed),
                        context.getString(R.string.alertMessageDocumentUploadFailed),
                        null,
                        null,
                    )
                },
            )
        }
    }

    private fun handleGetUrlRPCForSignedInUserResponse(
        response: GetUrlForSignedInUserRPC.Response,
        documentData: DocumentData,
        sha256: String,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        vmIOScope.launch {
            DocumentUploadHelper.uploadDocument(
                response.uploadDestinationURL,
                documentData.documentUri,
                {
                    handleException(
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageServerError),
                        null,
                        null,
                    )
                },
                {
                    handleImageUploadedResponse(
                        it,
                        null,
                        null,
                        sha256,
                        documentData,
                        context,
                        previousPath,
                    )
                },
                {
                    handleException(
                        context.getString(R.string.alertTitleProfileImageUploadFailed),
                        context.getString(R.string.alertMessageDocumentUploadFailed),
                        null,
                        null,
                    )
                },
            )
        }
    }

    private fun handleImageUploadedResponse(
        response: Response,
        phoneNumber: LeoPhoneNumber?,
        temporaryToken: UUID?,
        sha256: String,
        documentData: DocumentData,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        if (response.statusCode == 200) {
            if (previousPath == KYCPreviousPath.PROFILE) {
                getDocumentIdForSignInUser(sha256, documentData, context)
            } else {
                getDocumentId(phoneNumber!!, temporaryToken!!, sha256, documentData, context)
            }
        } else {
            handleException(
                context.getString(R.string.alertTitleFailedToUpload),
                context.getString(R.string.alertMessageFailedToUpload),
                null,
                documentData.uploadTarget,
            )
        }
    }

    private fun getDocumentId(
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        sha256: String,
        documentData: DocumentData,
        context: Context,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            kycRepository.getDocumentId(
                                phoneNumber,
                                temporaryToken,
                                sha256,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetDocumentIdResponse(
                                result.response.recordId,
                                documentData.documentUri,
                                documentData.uploadTarget,
                                documentData.fileExtension,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetDocumentIdError(
                                context,
                                result.error,
                                documentData.uploadTarget,
                            )
                        }
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        it.errorCode,
                        documentData.uploadTarget,
                        it.type,
                    )
                },
            )
        }
    }

    private fun getDocumentIdForSignInUser(
        sha256: String,
        documentData: DocumentData,
        context: Context,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            profileRepository.getDocumentIdForSignedInUser(
                                sha256,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetDocumentIdResponse(
                                result.response.recordId,
                                documentData.documentUri,
                                documentData.uploadTarget,
                                documentData.fileExtension,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetDocumentIdForSignedInUserError(
                                context,
                                result.error,
                                documentData.uploadTarget,
                            )
                        }
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        it.errorCode,
                        documentData.uploadTarget,
                        it.type,
                    )
                },
            )
        }
    }

    private fun handleGetDocumentIdResponse(
        recordId: UUID,
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
    ) {
        _currentState.postValue(
            KYCDocumentState.Data(
                DocumentViewState.DocumentUploaded(
                    documentUri,
                    target,
                    fileExtension,
                    recordId,
                ),
            ),
        )
    }

    private fun handleGetDocumentIdError(
        context: Context,
        error: GetDocumentIdRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        when (error) {
            is GetDocumentIdRPC.Error.PasswordValidatedTokenExpired -> {
                AuthExceptionHandler.getDocumentIdRPCExceptions(context, error).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        ERROR_CODE_TOKEN_EXPIRED,
                        documentUploadTarget,
                    )
                }
            }
            else -> {
                AuthExceptionHandler.getDocumentIdRPCExceptions(context, error).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        documentUploadTarget,
                    )
                }
            }
        }
    }

    private fun handleGetDocumentIdForSignedInUserError(
        context: Context,
        error: GetDocumentIdForSignedInUserRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        AuthExceptionHandler.getDocumentIdForSignedInUserRPCExceptions(context, error).apply {
            handleException(
                errorTitle,
                errorMessage,
                null,
                documentUploadTarget,
            )
        }
    }

    private fun handleGetUrlRPCError(
        context: Context,
        error: GetUrlRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        when (error) {
            is GetUrlRPC.Error.PasswordValidatedTokenExpired -> {
                AuthExceptionHandler.getURLRPCExceptions(error, context).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        ERROR_CODE_TOKEN_EXPIRED,
                        documentUploadTarget,
                    )
                }
            }
            else -> {
                AuthExceptionHandler.getURLRPCExceptions(error, context).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        documentUploadTarget,
                    )
                }
            }
        }
    }

    fun onConfirmClicked(
        signatureImageId: UUID?,
        nationalIdFrontImageId: UUID?,
        nationalIdBackImageId: UUID?,
        proofOfResidenceImageId: UUID?,
        passportFrontImageId: UUID?,
        passportBackImageId: UUID?,
        context: Context,
        passwordValidatedToken: UUID?,
        previousPath: KYCPreviousPath,
    ) {
        if (previousPath != KYCPreviousPath.PROFILE) {
            getPushNotificationToken(
                context,
                passwordValidatedToken!!,
                signatureImageId,
                nationalIdFrontImageId,
                nationalIdBackImageId,
                proofOfResidenceImageId,
                passportFrontImageId,
                passportBackImageId,
            )
        } else {
            if (validateImageIds(
                    signatureImageId,
                    nationalIdFrontImageId,
                    nationalIdBackImageId,
                    proofOfResidenceImageId,
                    context,
                )
            ) {
                _currentState.postValue(KYCDocumentState.Loading)
                vmIOScope.launch {
                    executeRPC(
                        context,
                        rpcBlock = {
                            val response = kycRepository.updatedKYCRPC(
                                signatureImageId!!,
                                nationalIdFrontImageId!!,
                                nationalIdBackImageId!!,
                                proofOfResidenceImageId!!,
                                passportFrontImageId,
                                passportBackImageId,
                            )

                            when (response) {
                                is LeoRPCResult.LeoResponse -> {
                                    handleSuccessfulUpdateKYCDataResponse()
                                }
                                is LeoRPCResult.LeoError -> {
                                    handleUpdateKYCDataErrorResponse(
                                        context,
                                        response.error,
                                    )
                                }
                            }
                        },
                        handleException = {
                            handleException(
                                it.errorTitle,
                                it.errorMessage,
                                it.errorCode,
                                null,
                                it.type,
                            )
                        },
                    )
                }
            }
        }
    }

    private fun getPushNotificationToken(
        context: Context,
        passwordValidatedToken: UUID,
        signatureImageId: UUID?,
        nationalIdFrontImageId: UUID?,
        nationalIdBackImageId: UUID?,
        proofOfResidenceImageId: UUID?,
        passportFrontImageId: UUID?,
        passportBackImageId: UUID?,
    ) {
        PushTokenHelper.init(context) {
            attemptSubmission(
                passwordValidatedToken,
                context,
                it,
                signatureImageId,
                nationalIdFrontImageId,
                nationalIdBackImageId,
                proofOfResidenceImageId,
                passportFrontImageId,
                passportBackImageId,
            )
        }
    }

    @SuppressLint("HardwareIds")
    private fun attemptSubmission(
        passwordValidatedToken: UUID,
        context: Context,
        pushToken: String?,
        signatureImageId: UUID?,
        nationalIdFrontImageId: UUID?,
        nationalIdBackImageId: UUID?,
        proofOfResidenceImageId: UUID?,
        passportFrontImageId: UUID?,
        passportBackImageId: UUID?,
    ) {
        if (validateImageIds(
                signatureImageId,
                nationalIdFrontImageId,
                nationalIdBackImageId,
                proofOfResidenceImageId,
                context,
            )
        ) {
            _currentState.postValue(KYCDocumentState.Loading)
            vmIOScope.launch {
                executeRPC(
                    context,
                    rpcBlock = {
                        val response = kycRepository.submitKYCData(
                            passwordValidatedToken,
                            signatureImageId!!,
                            nationalIdFrontImageId!!,
                            nationalIdBackImageId!!,
                            passportFrontImageId,
                            passportBackImageId,
                            proofOfResidenceImageId!!,
                            pushToken?.let { PushToken(it, App.BCN, FrontEndPlatform.ANDROID) },
                            getDeviceInformation(pushToken, context),
                        )

                        when (response) {
                            is LeoRPCResult.LeoResponse -> {
                                handleSuccessfulSubmitKYCDataResponse(response.response)
                            }
                            is LeoRPCResult.LeoError -> {
                                handleSubmitKYCDataErrorResponse(
                                    context,
                                    response.error,
                                )
                            }
                        }
                    },
                    handleException = {
                        handleException(
                            it.errorTitle,
                            it.errorMessage,
                            it.errorCode,
                            null,
                            it.type,
                        )
                    },
                )
            }
        }
    }

    private fun validateImageIds(
        signatureImageId: UUID?,
        nationalIdFrontImageId: UUID?,
        nationalIdBackImageId: UUID?,
        proofOfResidenceImageId: UUID?,
        context: Context,
    ): Boolean {
        return when {
            signatureImageId == null -> {
                handleException(
                    context.getString(R.string.alertTitleMissingDocuments),
                    context.getString(R.string.alertMessageMissingSignature),
                    null,
                    null,
                )
                false
            }
            nationalIdFrontImageId == null -> {
                handleException(
                    context.getString(R.string.alertTitleMissingDocuments),
                    context.getString(R.string.alertMessageMissingNationalIdFront),
                    null,
                    null,
                )
                false
            }
            nationalIdBackImageId == null -> {
                handleException(
                    context.getString(R.string.alertTitleMissingDocuments),
                    context.getString(R.string.alertMessageMissingNationalIdBack),
                    null,
                    null,
                )
                false
            }
            proofOfResidenceImageId == null -> {
                handleException(
                    context.getString(R.string.alertTitleMissingDocuments),
                    context.getString(R.string.alertMessageMissingProofOfResidence),
                    null,
                    null,
                )
                false
            }
            else -> true
        }
    }

    private fun handleSubmitKYCDataErrorResponse(
        context: Context,
        error: SubmitKYCDataRPC.Error,
    ) {
        AuthExceptionHandler.submitKYCDataRPCException(error, context).apply {
            handleException(errorTitle, errorMessage, errorCode, null, type)
        }
    }

    private fun handleUpdateKYCDataErrorResponse(
        context: Context,
        error: UpdateKYCDataRPC.Error,
    ) {
        AuthExceptionHandler.updateKYCDataRPCException(context, error).apply {
            handleException(errorTitle, errorMessage, errorCode, null, type)
        }
    }

    private fun handleSuccessfulSubmitKYCDataResponse(response: SubmitKYCDataRPC.Response) {
        vmIOScope.launch {
            KeyStoreHelper.storeSLT(response.slt)
            KeyStoreHelper.storeLLT(response.llt)
        }
        _currentState.postValue(KYCDocumentState.SuccessfulSubmit)
    }

    private fun handleSuccessfulUpdateKYCDataResponse() {
        _currentState.postValue(KYCDocumentState.SuccessfulSubmit)
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(KYCDocumentState.AcceptInput)
    }

    fun onErrorDialogDismissed() {
        _currentState.postValue(
            KYCDocumentState.AcceptInput,
        )
    }

    private fun handleException(
        title: String,
        error: String,
        errorCode: Int? = null,
        documentUploadTarget: DocumentUploadTarget?,
        type: ErrorType = ErrorType.DIALOG,
    ) {
        _currentState.postValue(
            KYCDocumentState.InlineError(
                UIError(
                    type,
                    title,
                    error,
                    errorCode,
                ),
                errorCode,
                documentUploadTarget,
            ),
        )
    }

    fun onSuccessfulSubmission(controller: NavController, previousPath: KYCPreviousPath) {
        _currentState.postValue(KYCDocumentState.AcceptInput)
        KYCSharedPreference.clearStoredDetails()
        if (previousPath == KYCPreviousPath.PROFILE) {
            controller.popBackStack(R.id.moneyScreenFragment, false)
        } else {
            LandingScreenSharedPreference.setLandingScreenAlreadyShown(true)
            controller.popBackStack()
            controller.navigate(R.id.home_nav)
        }
    }

    fun onDocumentUploadTapped(target: DocumentUploadTarget, showRemoveOption: Boolean) {
        _currentState.postValue(
            KYCDocumentState.Data(
                DocumentViewState.SelectDocumentAndUpload(target, showRemoveOption),
            ),
        )
    }

    fun onDocumentPickerFailure(context: Context, documentUploadTarget: DocumentUploadTarget) {
        handleException(
            context.getString(R.string.alertTitleFailedToPickDocument),
            context.getString(R.string.alertMessageFailedToPickDocument),
            null,
            documentUploadTarget,
        )
    }

    fun onDocumentUploaded(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                throw IllegalStateException("Profile image is not supported on KYC Document Screen.")
            }
            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.SignatureAcceptInput)
            }
            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.PassportFrontAcceptInput)
            }
            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.PassportBackAcceptInput)
            }
            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.ProofOfResidenceAcceptInput)
            }
            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                _currentState.postValue(KYCDocumentState.NationalIdFrontAcceptInput)
            }
            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                _currentState.postValue(KYCDocumentState.NationalIdBackAcceptInput)
            }
        }
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
    }

    fun onCameraPermissionRequested() {
        _currentState.postValue(KYCDocumentState.AcceptInput)
        _currentCameraState.postValue(KYCDocumentCameraState.CameraPermissionRequested)
    }

    fun onCameraPermissionGranted() {
        _currentCameraState.postValue(KYCDocumentCameraState.CameraPermissionGranted)
    }

    fun onCameraPermissionNeverAskAgainSelected() {
        _currentCameraState.postValue(KYCDocumentCameraState.CameraPermissionAlert)
    }

    fun onCameraPermissionDenied() {
        _currentCameraState.postValue(KYCDocumentCameraState.CameraPermissionDenied)
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
        _currentState.postValue(
            KYCDocumentState.AcceptInput,
        )
    }

    fun onCameraPermissionAlertDismissed() {
        _currentCameraState.postValue(KYCDocumentCameraState.CameraPermissionDenied)
    }

    fun onCameraPermissionDialogDismissed() {
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
        _currentState.postValue(
            KYCDocumentState.AcceptInput,
        )
    }

    fun onCameraPermissionGiven(target: DocumentUploadTarget) {
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                throw IllegalStateException("Profile image is not supported on KYC Document Screen.")
            }
            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.SignatureAcceptInput)
            }
            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.PassportFrontAcceptInput)
            }
            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.PassportBackAcceptInput)
            }
            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                _currentState.postValue(KYCDocumentState.ProofOfResidenceAcceptInput)
            }
            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                _currentState.postValue(KYCDocumentState.NationalIdFrontAcceptInput)
            }
            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                _currentState.postValue(KYCDocumentState.NationalIdBackAcceptInput)
            }
        }
    }

    fun canUpdateDocumentsIdMap(): Boolean {
        return canUpdateDocumentIdMap
    }

    fun onBackPressed(navController: NavController) {
        canUpdateDocumentIdMap = false
        navController.navigateUp()
        documentIdMap.clear()
        targetMutableList.clear()
        _currentState.postValue(KYCDocumentState.AcceptInput)
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
    }

    fun onDocumentPickerDismissed() {
        _currentState.postValue(KYCDocumentState.AcceptInput)
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
    }

    fun onCameraOpened() {
        _currentCameraState.postValue(KYCDocumentCameraState.Waiting)
    }

    companion object {
        const val ERROR_CODE_TOKEN_EXPIRED: Int = 1001
    }
}

sealed class DocumentViewState {
    data class InitializeDocumentSelection(val target: DocumentUploadTarget) :
        DocumentViewState()

    data class SelectDocumentAndUpload(
        val target: DocumentUploadTarget,
        val showRemoveOption: Boolean,
    ) :
        DocumentViewState()

    data class DocumentUploaded(
        val documentUri: Uri,
        val target: DocumentUploadTarget,
        val fileExtension: FileExtension,
        val documentId: UUID,
    ) :
        DocumentViewState()
}

sealed class KYCDocumentCameraState {
    object Waiting : KYCDocumentCameraState()
    object CameraPermissionRequested : KYCDocumentCameraState()
    object CameraPermissionDenied : KYCDocumentCameraState()
    object CameraPermissionGranted : KYCDocumentCameraState()
    object CameraPermissionAlert : KYCDocumentCameraState()
}

sealed class KYCDocumentState {
    object AcceptInput : KYCDocumentState()
    object SignatureAcceptInput : KYCDocumentState()
    object PassportFrontAcceptInput : KYCDocumentState()
    object PassportBackAcceptInput : KYCDocumentState()
    object NationalIdFrontAcceptInput : KYCDocumentState()
    object NationalIdBackAcceptInput : KYCDocumentState()
    object ProofOfResidenceAcceptInput : KYCDocumentState()
    object Loading : KYCDocumentState()
    data class Data(val documentViewState: DocumentViewState) : KYCDocumentState()
    data class InlineError(
        val error: UIError,
        val errorCode: Int?,
        val documentUploadTarget: DocumentUploadTarget?,
    ) : KYCDocumentState()

    object SuccessfulSubmit : KYCDocumentState()
}
