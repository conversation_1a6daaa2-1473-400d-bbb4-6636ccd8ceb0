package com.resoluttech.core.kyc

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.openSystemSettings
import com.resoluttech.core.views.isNetworkConnected
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

class KYCQRScannerVM : ViewModel() {
    private val _cameraPermissionState: MutableLiveData<KYCScannerCameraState> =
        MutableLiveData(KYCScannerCameraState.Waiting)
    val cameraPermissionState: LiveData<KYCScannerCameraState> = _cameraPermissionState
    private val _qrScannerState: MutableLiveData<KYCQRCodeScannerState> =
        MutableLiveData(KYCQRCodeScannerState.AcceptInput)
    val qrScannerState: LiveData<KYCQRCodeScannerState> = _qrScannerState
    private var qrScannerCounter: Int = 0

    fun onCameraPermissionRequested() {
        _cameraPermissionState.postValue(KYCScannerCameraState.CameraPermissionRequested)
    }

    fun onCameraPermissionNeverAskAgainSelected() {
        _cameraPermissionState.postValue(KYCScannerCameraState.CameraPermissionAlert)
    }

    fun onCameraPermissionGranted() {
        _cameraPermissionState.postValue(KYCScannerCameraState.CameraPermissionGranted)
    }

    fun onCameraPermissionDenied() {
        _cameraPermissionState.postValue(KYCScannerCameraState.CameraPermissionDenied)
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
    }

    fun onCameraPermissionAlertDismissed() {
        _cameraPermissionState.postValue(KYCScannerCameraState.CameraPermissionDenied)
    }

    fun onInlineErrorDismiss(changeErrorText: () -> Unit) {
        changeErrorText()
        _qrScannerState.postValue(KYCQRCodeScannerState.AcceptInput)
    }

    fun onEnterManuallyTapped(
        navController: NavController,
    ) {
        _qrScannerState.postValue(KYCQRCodeScannerState.AcceptInput)
        navigateToEnterKYCScreen(
            navController,
            null,
        )
    }

    fun onAlertDialogDismissed() {
        _qrScannerState.postValue(KYCQRCodeScannerState.AcceptInput)
    }

    fun onInvalidImageSelectedFromGallery(context: Context) {
        _qrScannerState.postValue(KYCQRCodeScannerState.InlineError(context.getString(R.string.kycScanQRCodeFailedToVerifyNationalIdSubtitle)))
    }

    fun onReceiveQRValue(
        context: Context,
        scannedValue: String,
        navController: NavController,
        performHapticFeedback: () -> Unit,
    ) {
        if (context.isNetworkConnected()) {
            if (validateNationalIdQRCode(scannedValue)) {
                performHapticFeedback()
                var kycData: KYCDataUtils? = null
                try {
                    kycData = getKYCDataFromQRValue(scannedValue, context)
                } catch (e: ArrayIndexOutOfBoundsException) {
                    _qrScannerState.postValue(KYCQRCodeScannerState.EnterManually)
                } catch (e: DateTimeParseException) {
                    _qrScannerState.postValue(KYCQRCodeScannerState.EnterManually)
                }
                navigateToEnterKYCScreen(
                    navController,
                    kycData,
                )
            } else {
                if (qrScannerCounter < QR_SCANNER_COUNTER_LIMIT) {
                    qrScannerCounter++
                    _qrScannerState.postValue(KYCQRCodeScannerState.InlineError(context.getString(R.string.kycScanQRCodeFailedToVerifyNationalIdSubtitle)))
                } else {
                    qrScannerCounter = 0
                    _qrScannerState.postValue(KYCQRCodeScannerState.EnterManually)
                }
            }
        } else {
            _qrScannerState.postValue(KYCQRCodeScannerState.NoInternet)
        }
    }

    private fun navigateToEnterKYCScreen(
        navController: NavController,
        kycData: KYCDataUtils? = null,
    ) {
        val action = KYCQRScannerFragmentDirections.actionKYCQRScannerToEnterKYCDataFragment(
            kycData,
        )
        navController.navigate(action)
    }

    fun onArrowBackClicked(navController: NavController) {
        navController.navigateUp()
        _qrScannerState.postValue(KYCQRCodeScannerState.AcceptInput)
    }

    private fun validateNationalIdQRCode(value: String): Boolean {
        if (!value.startsWith(NATIONAL_ID_PREFIX, false)) {
            return false
        }

        if (value.count { it == NATIONAL_ID_VALUE_DELIMITER } != NATIONAL_ID_VALUE_DELIMITER_COUNT) {
            return false
        }

        if (!value.contains(NATIONAL_ID_GENDER_MALE, ignoreCase = true)) {
            return false
        }

        return true
    }

    private fun getKYCDataFromQRValue(value: String, context: Context): KYCDataUtils {
        val kycDetails =
            value.split(NATIONAL_ID_VALUE_DELIMITER).toMutableList().filter(String::isNotBlank)

        val kycDetailLength = kycDetails.size
        val userNames: MutableList<String> =
            kycDetails[kycDetailLength - 4].split(",", limit = 2).toMutableList()
        if (userNames.size == 1) {
            userNames.add(context.getEmptyString())
        }
        val formatter = DateTimeFormatter.ofPattern("dd MMM yyyy")
        return KYCDataUtils(
            kycDetails[kycDetailLength - 6].capitalizeName(),
            kycDetails[kycDetailLength - 5],
            userNames[0].capitalizeName(),
            userNames[1].replace(",", " ").trim().capitalizeName(),
            kycDetails[kycDetailLength - 3],
            LocalDate.parse(kycDetails[kycDetailLength - 2], formatter),
            LocalDate.parse(kycDetails[kycDetailLength - 1], formatter),
        )
    }

    private fun String.capitalizeName(): String = lowercase().split(" ").joinToString(" ") {
        it.replaceFirstChar(Char::uppercase)
    }
}

sealed class KYCScannerCameraState {
    object Waiting : KYCScannerCameraState()
    object CameraPermissionRequested : KYCScannerCameraState()
    object CameraPermissionDenied : KYCScannerCameraState()
    object CameraPermissionGranted : KYCScannerCameraState()
    object CameraPermissionAlert : KYCScannerCameraState()
}

sealed class KYCQRCodeScannerState {
    object AcceptInput : KYCQRCodeScannerState()
    data class InlineError(val errorMessage: String) : KYCQRCodeScannerState()
    object EnterManually : KYCQRCodeScannerState()
    object NoInternet : KYCQRCodeScannerState()
}

private const val NATIONAL_ID_PREFIX = "03~I<MWI"
private const val NATIONAL_ID_VALUE_DELIMITER = '~'
private const val NATIONAL_ID_VALUE_DELIMITER_COUNT = 11
private const val NATIONAL_ID_GENDER_MALE = "Male"
private const val QR_SCANNER_COUNTER_LIMIT = 3
