package com.resoluttech.core.kyc

import android.content.Context
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.document.FileAttributes
import com.resoluttech.bcn.document.GetDocumentIdForSignedInUserRPC
import com.resoluttech.bcn.document.GetDocumentIdRPC
import com.resoluttech.bcn.document.GetUrlForSignedInUserRPC
import com.resoluttech.bcn.document.GetUrlRPC
import com.resoluttech.bcn.profile.GetKYCDataRPC
import com.resoluttech.bcn.types.Gender
import com.resoluttech.bcncore.R
import com.resoluttech.core.profile.ProfileRepository
import com.resoluttech.core.rpcexceptionhandlers.AuthExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.MAX_IMAGE_HEIGHT
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.MAX_IMAGE_WIDTH
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.ONE_KB_IN_BYTES
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getAssetSize
import com.resoluttech.core.utils.getByteArray
import com.resoluttech.core.utils.getSHA256
import com.resoluttech.core.utils.openSystemSettings
import com.resoluttech.core.utils.toLocalDate
import com.suryadigital.leo.kedwig.Response
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoEmailId
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID
import com.resoluttech.bcn.profile.VerifyKYCDataRPC as ProfileVerifyKYCDataRPC
import com.resoluttech.bcn.signUpIn.VerifyKYCDataRPC as SignUpInVerifyKYCDataRPC

class KYCDataVM : ViewModel() {
    private val _currentState = MutableLiveData<KYCDataState>(KYCDataState.AcceptInput)
    val currentState: LiveData<KYCDataState> = _currentState
    private val _currentCameraState: MutableLiveData<KYCDataCameraState> =
        MutableLiveData(KYCDataCameraState.Waiting)
    val currentCameraState: LiveData<KYCDataCameraState> = _currentCameraState
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val kycRepository = KYCRepository()
    var profileImageUri: Uri? = null
    val documentIdMap: java.util.HashMap<String, UUID?> = HashMap()
    private var gender: Gender? = null
    private val profileRepository = ProfileRepository()
    private var signatureId: UUID? = null
    private var passportFrontId: UUID? = null
    private var passportBackId: UUID? = null
    private var proofOfResidenceId: UUID? = null
    private var nationalIdFrontId: UUID? = null
    private var nationalIdBackId: UUID? = null

    fun onDocumentSelected(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        phoneNumber: LeoPhoneNumber?,
        temporaryToken: UUID?,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        _currentState.postValue(
            KYCDataState.Data(
                ProfileDocumentViewState.InitializeDocumentSelection(target),
            ),
        )
        if (!validateDocumentFileSize(documentUri, context)) {
            _currentState.postValue(
                KYCDataState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleInvalidFileSize),
                        context.getString(
                            R.string.alertMessageInvalidFileSize,
                            DocumentUploadHelper.MIN_DOCUMENT_SIZE,
                            DocumentUploadHelper.MAX_DOCUMENT_SIZE,
                        ),
                    ),
                    null,
                    null,
                    target,
                ),
            )
            return
        }
        if (!validateDocumentDimension(documentUri, context)) {
            onInvalidImageDimension(documentUri, context, target)
            return
        }

        if (target == DocumentUploadTarget.PROFILE_IMAGE) {
            if (!validateProfileImage(documentUri, context)) {
                onInvalidImageDimension(documentUri, context, target)
                return
            }
        }

        vmIOScope.launch {
            val extension = when (fileExtension) {
                FileExtension.JPG -> FileAttributes.FileExtension.JPG
                FileExtension.PNG -> FileAttributes.FileExtension.PNG
                FileExtension.PDF -> FileAttributes.FileExtension.PDF
            }
            val documentSHA256 = getSHA256(getByteArray(context, documentUri))
            val fileAttributes = FileAttributes(
                documentSHA256,
                documentUri.lastPathSegment,
                extension,
                context.getAssetSize(documentUri)!!.toInt(),
            )

            executeRPC(
                context,
                rpcBlock = {
                    if (previousPath == KYCPreviousPath.PROFILE) {
                        getDocumentUploadURLForSignedUser(
                            documentUri,
                            target,
                            fileExtension,
                            context,
                            fileAttributes,
                            documentSHA256,
                            previousPath,
                        )
                    } else {
                        getDocumentUploadURL(
                            documentUri,
                            target,
                            fileExtension,
                            phoneNumber!!,
                            temporaryToken!!,
                            context,
                            fileAttributes,
                            documentSHA256,
                            previousPath,
                        )
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        null,
                        it.errorCode,
                        target,
                        it.type,
                    )
                },
            )
        }
    }

    /***
     * This function will show a alert dialog when dimension of the document uploaded are less than
     * MAX_IMAGE_WIDTH x MAX_IMAGE_HEIGHT along with the dimension of the document uploaded.
     */
    private fun onInvalidImageDimension(documentUri: Uri, context: Context, target: DocumentUploadTarget) {
        val inputStream = context.contentResolver.openInputStream(documentUri)
        val bitmap = BitmapFactory.decodeStream(inputStream)
        _currentState.postValue(
            KYCDataState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleInvalidImageDimensions),
                    context.getString(
                        R.string.alertMessageInvalidImageDimensions,
                        bitmap.width,
                        bitmap.height,
                    ),
                ),
                null,
                null,
                target,
            ),
        )
    }

    private suspend fun getDocumentUploadURL(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        context: Context,
        fileAttributes: FileAttributes,
        documentSHA256: String,
        previousPath: KYCPreviousPath,
    ) {
        when (
            val result = kycRepository.getDocumentUploadUrl(
                phoneNumber,
                temporaryToken,
                fileAttributes,
            )
        ) {
            is LeoRPCResult.LeoResponse -> {
                handleGetUrlRPCResponse(
                    result.response,
                    DocumentData(
                        documentUri,
                        fileAttributes,
                        target,
                        fileExtension,
                    ),
                    phoneNumber,
                    temporaryToken,
                    documentSHA256,
                    context,
                    previousPath,
                )
            }
            is LeoRPCResult.LeoError -> {
                handleGetUrlRPCError(context, result.error, target)
            }
        }
    }

    private suspend fun getDocumentUploadURLForSignedUser(
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
        context: Context,
        fileAttributes: FileAttributes,
        documentSHA256: String,
        previousPath: KYCPreviousPath,
    ) {
        when (
            val result = profileRepository.getDocumentUploadUrl(
                fileAttributes,
            )
        ) {
            is LeoRPCResult.LeoResponse -> {
                handleGetUrlRPCForSignedInUserResponse(
                    result.response,
                    DocumentData(
                        documentUri,
                        fileAttributes,
                        target,
                        fileExtension,
                    ),
                    documentSHA256,
                    context,
                    previousPath,
                )
            }
            is LeoRPCResult.LeoError -> {
                // No error to handle
            }
        }
    }

    private fun handleGetUrlRPCForSignedInUserResponse(
        response: GetUrlForSignedInUserRPC.Response,
        documentData: DocumentData,
        sha256: String,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        vmIOScope.launch {
            DocumentUploadHelper.uploadDocument(
                response.uploadDestinationURL,
                documentData.documentUri,
                {
                    handleException(
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageServerError),
                        null,
                        null,
                        null,
                    )
                },
                {
                    handleImageUploadedResponse(
                        it,
                        null,
                        null,
                        sha256,
                        documentData,
                        context,
                        previousPath,
                    )
                },
                {
                    handleException(
                        context.getString(R.string.alertTitleProfileImageUploadFailed),
                        context.getString(R.string.alertMessageProfileImageUploadFailed),
                        null,
                        null,
                        null,
                    )
                },
            )
        }
    }

    fun getKYCData(kycData: KYCDataUtils?, kycPreviousPath: KYCPreviousPath, context: Context) {
        if (_currentState.value == KYCDataState.AcceptInput) {
            _currentState.postValue(KYCDataState.FullScreenLoading)
            when (kycPreviousPath) {
                KYCPreviousPath.PROFILE -> {
                    vmIOScope.launch {
                        executeRPC(
                            context,
                            rpcBlock = {
                                when (
                                    val result =
                                        kycRepository.getKYCData()
                                ) {
                                    is LeoRPCResult.LeoResponse -> {
                                        handleGetKYCResponse(
                                            result.response,
                                            kycData,
                                        )
                                    }
                                    is LeoRPCResult.LeoError -> {
                                        // This RPC does not have any error.
                                    }
                                }
                            },
                            handleException = {
                                handleException(
                                    it.errorTitle,
                                    it.errorMessage,
                                    null,
                                    it.errorCode,
                                    null,
                                    it.type,
                                )
                            },
                        )
                    }
                }
                else -> {
                    _currentState.postValue(KYCDataState.PrefillData(kycData))
                }
            }
        }
    }

    private fun handleGetUrlRPCResponse(
        response: GetUrlRPC.Response,
        documentData: DocumentData,
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        sha256: String,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        vmIOScope.launch {
            DocumentUploadHelper.uploadDocument(
                response.uploadDestinationURL,
                documentData.documentUri,
                {
                    handleException(
                        context.getString(R.string.alertTitleServerError),
                        context.getString(R.string.alertMessageServerError),
                        null,
                        null,
                        null,
                    )
                },
                {
                    handleImageUploadedResponse(
                        it,
                        phoneNumber,
                        temporaryToken,
                        sha256,
                        documentData,
                        context,
                        previousPath,
                    )
                },
                {
                    handleException(
                        context.getString(R.string.alertTitleProfileImageUploadFailed),
                        context.getString(R.string.alertMessageProfileImageUploadFailed),
                        null,
                        null,
                        null,
                    )
                },
            )
        }
    }

    private fun handleImageUploadedResponse(
        response: Response,
        phoneNumber: LeoPhoneNumber?,
        temporaryToken: UUID?,
        sha256: String,
        documentData: DocumentData,
        context: Context,
        previousPath: KYCPreviousPath,
    ) {
        if (response.statusCode == 200) {
            if (previousPath == KYCPreviousPath.PROFILE) {
                getDocumentIdForSignInUser(sha256, documentData, context)
            } else {
                getDocumentId(phoneNumber!!, temporaryToken!!, sha256, documentData, context)
            }
        } else {
            handleException(
                context.getString(R.string.alertTitleFailedToUpload),
                context.getString(R.string.alertMessageFailedToUpload),
                null,
                null,
                documentData.uploadTarget,
            )
        }
    }

    private fun getDocumentIdForSignInUser(
        sha256: String,
        documentData: DocumentData,
        context: Context,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            profileRepository.getDocumentIdForSignedInUser(
                                sha256,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetDocumentIdResponse(
                                result.response.recordId,
                                documentData.documentUri,
                                documentData.uploadTarget,
                                documentData.fileExtension,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetDocumentIdForSignedInUserError(
                                context,
                                result.error,
                                documentData.uploadTarget,
                            )
                        }
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        null,
                        it.errorCode,
                        documentData.uploadTarget,
                        it.type,
                    )
                },
            )
        }
    }

    private fun handleGetDocumentIdForSignedInUserError(
        context: Context,
        error: GetDocumentIdForSignedInUserRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        AuthExceptionHandler.getDocumentIdForSignedInUserRPCExceptions(context, error).apply {
            handleException(
                errorTitle,
                errorMessage,
                null,
                null,
                documentUploadTarget,
            )
        }
    }

    private fun getDocumentId(
        phoneNumber: LeoPhoneNumber,
        temporaryToken: UUID,
        sha256: String,
        documentData: DocumentData,
        context: Context,
    ) {
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            kycRepository.getDocumentId(
                                phoneNumber,
                                temporaryToken,
                                sha256,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetDocumentIdResponse(
                                result.response.recordId,
                                documentData.documentUri,
                                documentData.uploadTarget,
                                documentData.fileExtension,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleGetDocumentIdError(
                                context,
                                result.error,
                                documentData.uploadTarget,
                            )
                        }
                    }
                },
                handleException = {
                    handleException(
                        it.errorTitle,
                        it.errorMessage,
                        null,
                        it.errorCode,
                        documentData.uploadTarget,
                        it.type,
                    )
                },
            )
        }
    }

    private fun handleGetDocumentIdResponse(
        recordId: UUID,
        documentUri: Uri,
        target: DocumentUploadTarget,
        fileExtension: FileExtension,
    ) {
        profileImageUri = documentUri
        _currentState.postValue(
            KYCDataState.Data(
                ProfileDocumentViewState.DocumentUploaded(
                    documentUri,
                    target,
                    fileExtension,
                    recordId,
                ),
            ),
        )
    }

    private fun handleGetKYCResponse(
        response: GetKYCDataRPC.Response,
        scannedKYCData: KYCDataUtils?,
    ) {
        response.apply {
            signatureId = signatureImageId
            nationalIdFrontId = nationalIdFrontPhotoId
            nationalIdBackId = nationalIdBackPhotoId
            proofOfResidenceId = proofOfResidencePhotoId
            passportFrontId = passportFrontPhotoId
            passportBackId = passportBackPhotoId
            _currentState.postValue(
                KYCDataState.PrefillData(
                    KYCDataUtils(
                        scannedKYCData?.lastname ?: kycData.userPersonalDetails.lastName,
                        scannedKYCData?.nationalId ?: kycData.userPersonalDetails.nationalId,
                        scannedKYCData?.firstName ?: kycData.userPersonalDetails.firstName,
                        scannedKYCData?.otherNames ?: kycData.userPersonalDetails.otherName,
                        scannedKYCData?.gender ?: kycData.userPersonalDetails.gender.value,
                        scannedKYCData?.dateOfBirth ?: kycData.userPersonalDetails.dateOfBirth,
                        scannedKYCData?.dateOfIssue ?: kycData.nationalIdIssueDate,
                        kycData.nationalIdExpiryDate,
                        kycData.emailId?.value,
                        kycData.placeOfBirth,
                        kycData.residentAddress,
                        kycData.postalAddress,
                    ),
                ),
            )
        }
    }

    private fun handleGetDocumentIdError(
        context: Context,
        error: GetDocumentIdRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        when (error) {
            is GetDocumentIdRPC.Error.PasswordValidatedTokenExpired -> {
                AuthExceptionHandler.getDocumentIdRPCExceptions(context, error).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        ERROR_CODE_TOKEN_EXPIRED,
                        documentUploadTarget,
                    )
                }
            }
            else -> {
                AuthExceptionHandler.getDocumentIdRPCExceptions(context, error).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        null,
                        documentUploadTarget,
                    )
                }
            }
        }
    }

    private fun handleGetUrlRPCError(
        context: Context,
        error: GetUrlRPC.Error,
        documentUploadTarget: DocumentUploadTarget,
    ) {
        when (error) {
            is GetUrlRPC.Error.PasswordValidatedTokenExpired -> {
                AuthExceptionHandler.getURLRPCExceptions(error, context).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        ERROR_CODE_TOKEN_EXPIRED,
                        documentUploadTarget,
                    )
                }
            }
            else -> {
                AuthExceptionHandler.getURLRPCExceptions(error, context).apply {
                    handleException(
                        errorTitle,
                        errorMessage,
                        null,
                        null,
                        documentUploadTarget,
                    )
                }
            }
        }
    }

    private fun validateDocumentDimension(uri: Uri, context: Context): Boolean {
        val inputStream = context.contentResolver.openInputStream(uri)
        val bitmap = BitmapFactory.decodeStream(inputStream)
        return bitmap.width == MAX_IMAGE_WIDTH && bitmap.height == MAX_IMAGE_HEIGHT
    }

    private fun validateDocumentFileSize(uri: Uri, context: Context): Boolean {
        val assetSize = context.getAssetSize(uri)
        return if (assetSize == null) {
            false
        } else {
            // The file size should be in 10 KBs to 5 MBs
            (assetSize / ONE_KB_IN_BYTES) in DocumentUploadHelper.MIN_DOCUMENT_SIZE_KB..DocumentUploadHelper.MAX_DOCUMENT_SIZE_KB
        }
    }

    private fun validateProfileImage(uri: Uri, context: Context): Boolean {
        val inputStream = context.contentResolver.openInputStream(uri)
        val bitmap = BitmapFactory.decodeStream(inputStream)
        return bitmap.width == MAX_IMAGE_WIDTH && bitmap.height == MAX_IMAGE_HEIGHT
    }

    fun onNextClicked(
        context: Context,
        passwordValidatedToken: UUID?,
        firstName: String,
        lastName: String?,
        dateOfBirth: String,
        otherNames: String?,
        nrbNumber: String,
        emailId: String,
        phoneNumber: String?,
        placeOfBirth: String,
        nationalIdIssueDate: String,
        nationalIdExpiryDate: String,
        residentAddress: String,
        postalAddress: String,
        profileId: UUID?,
        previousPath: KYCPreviousPath,
    ) {
        if (validateInput(
                firstName,
                dateOfBirth,
                nrbNumber,
                profileId,
                emailId,
                residentAddress,
                postalAddress,
                placeOfBirth,
                nationalIdIssueDate,
                nationalIdExpiryDate,
                context,
                previousPath,
            )
        ) {
            _currentState.postValue(KYCDataState.Loading)
            vmIOScope.launch {
                executeRPC(
                    context,
                    rpcBlock = {
                        if (previousPath != KYCPreviousPath.PROFILE) {
                            signUpInVerifyKYCData(
                                context,
                                passwordValidatedToken!!,
                                firstName,
                                lastName,
                                dateOfBirth,
                                otherNames,
                                nrbNumber,
                                emailId,
                                phoneNumber!!,
                                placeOfBirth,
                                nationalIdIssueDate,
                                nationalIdExpiryDate,
                                residentAddress,
                                postalAddress,
                                profileId,
                            )
                        } else {
                            profileVerifyKYCData(
                                context,
                                firstName,
                                lastName,
                                dateOfBirth,
                                otherNames,
                                nrbNumber,
                                emailId,
                                placeOfBirth,
                                nationalIdIssueDate,
                                nationalIdExpiryDate,
                                residentAddress,
                                postalAddress,
                                profileId,
                            )
                        }
                    },
                    handleException = {
                        handleException(
                            it.errorTitle,
                            it.errorMessage,
                            null,
                            it.errorCode,
                            null,
                            it.type,
                        )
                    },
                )
            }
        }
    }

    private suspend fun signUpInVerifyKYCData(
        context: Context,
        passwordValidatedToken: UUID,
        firstName: String,
        lastName: String?,
        dateOfBirth: String,
        otherNames: String?,
        nrbNumber: String,
        emailId: String,
        phoneNumber: String,
        placeOfBirth: String,
        nationalIdIssueDate: String,
        nationalIdExpiryDate: String,
        residentAddress: String,
        postalAddress: String,
        profileId: UUID?,
    ) {
        val response = kycRepository.signUpInVerifyKYCData(
            LeoPhoneNumber(phoneNumber),
            passwordValidatedToken,
            emailId.let {
                if (it.isBlank()) {
                    null
                } else {
                    LeoEmailId(it)
                }
            },
            profileId!!,
            firstName,
            placeOfBirth,
            lastName,
            otherNames,
            gender!!,
            dateOfBirth.toLocalDate(),
            nationalIdIssueDate.toLocalDate(),
            nationalIdExpiryDate.toLocalDate(),
            nrbNumber,
            residentAddress,
            postalAddress,
        )

        when (response) {
            is LeoRPCResult.LeoResponse -> {
                handleSuccessfulVerifyKYCDataResponse()
            }
            is LeoRPCResult.LeoError -> {
                handleSignUpInVerifyKYCDataErrorResponse(
                    context,
                    response.error,
                )
            }
        }
    }

    private suspend fun profileVerifyKYCData(
        context: Context,
        firstName: String,
        lastName: String?,
        dateOfBirth: String,
        otherNames: String?,
        nrbNumber: String,
        emailId: String,
        placeOfBirth: String,
        nationalIdIssueDate: String,
        nationalIdExpiryDate: String,
        residentAddress: String,
        postalAddress: String,
        profileId: UUID?,
    ) {
        val response = kycRepository.profileVerifyKYCData(
            emailId.let {
                if (it.isBlank()) {
                    null
                } else {
                    LeoEmailId(it)
                }
            },
            profileId,
            firstName,
            placeOfBirth,
            lastName,
            otherNames,
            gender!!,
            dateOfBirth.toLocalDate(),
            nationalIdIssueDate.toLocalDate(),
            nationalIdExpiryDate.toLocalDate(),
            nrbNumber,
            residentAddress,
            postalAddress,
        )

        when (response) {
            is LeoRPCResult.LeoResponse -> {
                handleSuccessfulVerifyKYCDataResponse()
            }
            is LeoRPCResult.LeoError -> {
                handleProfileVerifyKYCDataErrorResponse(
                    context,
                    response.error,
                )
            }
        }
    }

    private fun handleSignUpInVerifyKYCDataErrorResponse(
        context: Context,
        error: SignUpInVerifyKYCDataRPC.Error,
    ) {
        AuthExceptionHandler.verifyKYCData(context, error).apply {
            handleException(errorTitle, errorMessage, null, errorCode, null)
        }
    }

    private fun handleProfileVerifyKYCDataErrorResponse(
        context: Context,
        error: ProfileVerifyKYCDataRPC.Error,
    ) {
        AuthExceptionHandler.verifyKYCData(context, error).apply {
            handleException(errorTitle, errorMessage, null, null, null)
        }
    }

    private fun handleSuccessfulVerifyKYCDataResponse() {
        _currentState.postValue(KYCDataState.SuccessfulSubmit)
    }

    fun onKYCDataSubmitted(
        navController: NavController,
    ) {
        _currentState.postValue(KYCDataState.AcceptInput)
        val action =
            EnterKYCDataFragmentDirections.actionEnterKYCDataFragmentToKYCDocumentsFragment(
                signatureId,
                nationalIdFrontId,
                nationalIdBackId,
                proofOfResidenceId,
                passportFrontId,
                passportBackId,
            )
        navController.navigate(action)
    }

    private fun validateInput(
        firstName: String,
        dateOfBirth: String,
        nationalIdNumber: String,
        profileImageId: UUID?,
        emailId: String,
        residentAddress: String,
        postalAddress: String,
        placeOfBirth: String,
        nationalIdIssueDate: String,
        nationalIdExpiryDate: String,
        context: Context,
        previousPath: KYCPreviousPath,
    ): Boolean {
        if (profileImageId == null && previousPath != KYCPreviousPath.PROFILE) {
            handleException(
                context.getString(R.string.alertTitleMissingProfilePicture),
                context.getString(R.string.alertMessageMissingProfilePicture),
                null,
                null,
                null,
            )
            return false
        }
        if (firstName.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingFirstName),
                ErrorField.FIRST_NAME,
                null,
                null,
            )
            return false
        }
        if (dateOfBirth.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingPlaceOfBirth),
                ErrorField.DATE_OF_BIRTH,
                null,
                null,
            )
            return false
        }
        if (gender == null) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingGender),
                ErrorField.GENDER,
                null,
                null,
            )
            return false
        }
        if (nationalIdNumber.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingNationalIdNumber),
                ErrorField.NRB_NUMBER,
                null,
                null,
            )
            return false
        }
        if (!nationalIdNumber.matches(Regex("^[a-zA-Z0-9]+\$"))) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingNationalIdNumber),
                ErrorField.NRB_NUMBER,
                null,
                null,
            )
            return false
        }
        if (nationalIdNumber.length > 300) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingNationalIdNumber),
                ErrorField.NRB_NUMBER,
                null,
                null,
            )
            return false
        }
        if (emailId.isNotEmpty()) {
            try {
                LeoEmailId(emailId)
            } catch (e: Exception) {
                handleException(
                    context.getString(R.string.alertTitleMissingDetails),
                    context.getString(R.string.alertMessageMissingEmailID),
                    ErrorField.EMAIL,
                    null,
                    null,
                )
                return false
            }
        }
        if (placeOfBirth.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingPlaceOfBirth),
                ErrorField.PLACE_OF_BIRTH,
                null,
                null,
            )
            return false
        }
        if (nationalIdIssueDate.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingIssueDate),
                ErrorField.NATIONAL_ID_ISSUE_DATE,
                null,
                null,
            )
            return false
        }
        if (nationalIdExpiryDate.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingExpiryDate),
                ErrorField.NATIONAL_ID_EXPIRY_DATE,
                null,
                null,
            )
            return false
        }
        if (residentAddress.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingResidentialAddress),
                ErrorField.FIRST_NAME,
                null,
                null,
            )
            return false
        }
        if (postalAddress.isEmpty()) {
            handleException(
                context.getString(R.string.alertTitleMissingDetails),
                context.getString(R.string.alertMessageMissingPostalAddress),
                ErrorField.FIRST_NAME,
                null,
                null,
            )
            return false
        }
        return true
    }

    fun onGenderDropdownUpdated(gender: Gender) {
        this.gender = gender
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(KYCDataState.AcceptInput)
    }

    fun onErrorDialogDismissed() {
        _currentState.postValue(
            KYCDataState.AcceptInput,
        )
    }

    private fun handleException(
        title: String,
        error: String,
        errorField: ErrorField?,
        errorCode: Int? = null,
        documentUploadTarget: DocumentUploadTarget?,
        type: ErrorType = ErrorType.DIALOG,
    ) {
        _currentState.postValue(
            KYCDataState.Error(
                UIError(
                    type,
                    title,
                    error,
                    errorCode,
                ),
                errorField,
                errorCode,
                documentUploadTarget,
            ),
        )
    }

    fun onDocumentUploadTapped(target: DocumentUploadTarget, showRemoveOption: Boolean) {
        _currentState.postValue(
            KYCDataState.Data(
                ProfileDocumentViewState.SelectDocumentAndUpload(target, showRemoveOption),
            ),
        )
    }

    fun onDocumentPickerFailure(context: Context, documentUploadTarget: DocumentUploadTarget) {
        handleException(
            context.getString(R.string.alertTitleFailedToPickDocument),
            context.getString(R.string.alertMessageFailedToPickDocument),
            null,
            null,
            documentUploadTarget,
        )
    }

    fun onDocumentUploaded() {
        _currentState.postValue(
            KYCDataState.AcceptInput,
        )
    }

    fun onCameraPermissionRequested() {
        _currentState.postValue(KYCDataState.AcceptInput)
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionRequested)
    }

    fun onCameraPermissionGranted() {
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionGranted)
    }

    fun onCameraPermissionNeverAskAgainSelected() {
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionAlert)
    }

    fun onCameraPermissionDenied() {
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionDenied)
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
        _currentCameraState.postValue(KYCDataCameraState.Waiting)
        _currentState.postValue(
            KYCDataState.AcceptInput,
        )
    }

    fun onCameraPermissionAlertDismissed() {
        _currentCameraState.postValue(KYCDataCameraState.CameraPermissionDenied)
    }

    fun onCameraPermissionDialogDismissed() {
        _currentCameraState.postValue(KYCDataCameraState.Waiting)
    }

    fun onBackPressed(navController: NavController) {
        _currentState.postValue(KYCDataState.AcceptInput)
        _currentCameraState.postValue(KYCDataCameraState.Waiting)
        navController.navigateUp()
        profileImageUri = null
        documentIdMap.clear()
    }

    fun onDocumentPickerDismissed() {
        _currentState.postValue(KYCDataState.AcceptInput)
    }

    fun onOptionSelectedDocumentPickerDialog() {
        _currentState.postValue(KYCDataState.AcceptInput)
    }

    enum class ErrorField {
        FIRST_NAME, LAST_NAME, DATE_OF_BIRTH, OTHER_NAME, NRB_NUMBER, GENDER, EMAIL, PLACE_OF_BIRTH, NATIONAL_ID_ISSUE_DATE, NATIONAL_ID_EXPIRY_DATE
    }

    companion object {
        const val ERROR_CODE_TOKEN_EXPIRED: Int = 1001
    }
}

sealed class ProfileDocumentViewState {
    data class InitializeDocumentSelection(val target: DocumentUploadTarget) :
        ProfileDocumentViewState()

    data class SelectDocumentAndUpload(
        val target: DocumentUploadTarget,
        val showRemoveOption: Boolean,
    ) :
        ProfileDocumentViewState()

    data class DocumentUploaded(
        val documentUri: Uri,
        val target: DocumentUploadTarget,
        val fileExtension: FileExtension,
        val documentId: UUID,
    ) :
        ProfileDocumentViewState()
}

sealed class KYCDataCameraState {
    object Waiting : KYCDataCameraState()
    object CameraPermissionRequested : KYCDataCameraState()
    object CameraPermissionDenied : KYCDataCameraState()
    object CameraPermissionGranted : KYCDataCameraState()
    object CameraPermissionAlert : KYCDataCameraState()
}

sealed class KYCDataState {
    object AcceptInput : KYCDataState()
    object Loading : KYCDataState()
    object FullScreenLoading : KYCDataState()
    data class Data(val documentViewState: ProfileDocumentViewState) : KYCDataState()
    data class PrefillData(val kycData: KYCDataUtils?) : KYCDataState()
    data class Error(
        val error: UIError,
        val errorField: KYCDataVM.ErrorField?,
        val errorCode: Int?,
        val documentUploadTarget: DocumentUploadTarget?,
    ) : KYCDataState()

    object SuccessfulSubmit : KYCDataState()
}
