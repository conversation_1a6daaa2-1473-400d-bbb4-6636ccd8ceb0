package com.resoluttech.core.kyc

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import timber.log.Timber

class KYCInputSelectionFragment : BaseFragment(), BaseFragment.NetworkListener {

    private lateinit var scanQRCodeButton: MaterialButton
    private lateinit var enterManuallyButton: MaterialButton
    private val kycDetails: KYCDetails by lazy(KYCSharedPreference::getKYCDetails)

    private val kycInputSelectionVM: KYCInputSelectionVM by navGraphViewModels(R.id.kyc_nav)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_kyc_input_selection, container, false)
        shouldAuthenticate = kycDetails.previousPath == KYCPreviousPath.PROFILE
        initViews(view)
        setupScanQRCodeButton()
        setupEnterManuallyButton()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupToolbar()
        networkListenerCallback = this
    }

    private fun initViews(view: View) {
        scanQRCodeButton = view.findViewById(R.id.scan_qr_button)
        enterManuallyButton = view.findViewById(R.id.enter_manually_button)
    }

    private fun setupToolbar() {
        if (kycDetails.previousPath == KYCPreviousPath.PROFILE) {
            showToolbar()
            setDefaultToolbar(getString(R.string.profileScreenOptionUpdateKYCDetails))
        } else {
            showToolbar()
            setDefaultToolbar(getString(R.string.kycDetailsTitle))
        }
        setupBackPressed {
            kycInputSelectionVM.onArrowBackClicked(
                findNavController(),
                kycDetails.previousPath,
            )
        }
    }

    private fun setupScanQRCodeButton() {
        scanQRCodeButton.setOnClickListener {
            kycInputSelectionVM.onScanQRCodeClicked(findNavController())
        }
    }

    private fun setupEnterManuallyButton() {
        enterManuallyButton.setOnClickListener {
            kycInputSelectionVM.onEnterManuallyClicked(
                findNavController(),
            )
        }
    }

    override fun onNetworkAvailable() {
        Timber.d("The app is online again. Nothing to update in this screen")
    }
}
