package com.resoluttech.core.kyc

import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.navigateSafe

class KYCInputSelectionVM : ViewModel() {

    fun onScanQRCodeClicked(navController: NavController) {
        navController.navigateSafe(R.id.action_KYCInputSelectionFragment_to_KYCQRScanner)
    }

    fun onEnterManuallyClicked(
        navController: NavController,
    ) {
        val action = KYCInputSelectionFragmentDirections.actionKYCInputSelectionFragmentToEnterKYCDataFragment(
            null,
        )
        navController.navigateSafe(action.actionId, action.arguments)
    }

    fun onArrowBackClicked(navController: NavController, previousPath: KYCPreviousPath) {
        when (previousPath) {
            KYCPreviousPath.PROFILE -> navController.navigateUp()
            KYCPreviousPath.SIGN_IN -> navController.popBackStack(R.id.sign_in_nav, false)
            KYCPreviousPath.SIGN_UP -> navController.popBackStack(R.id.sign_up_nav, false)
        }
    }
}
