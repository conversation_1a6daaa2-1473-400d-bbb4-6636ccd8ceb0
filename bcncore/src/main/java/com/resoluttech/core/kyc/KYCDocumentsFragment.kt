package com.resoluttech.core.kyc

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import coil.load
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.SIGN_UP_IN_SESSION_EXPIRED
import com.resoluttech.core.utils.KYCDetails
import com.resoluttech.core.utils.KYCPreviousPath
import com.resoluttech.core.utils.KYCSharedPreference
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.utils.showToolbar
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.DocumentPickerDialog
import com.suryadigital.leo.libui.contactview.ContactIconView
import com.suryadigital.leo.libui.imagecrop.ImagePicker
import com.suryadigital.leo.libui.qrcode.checkCameraPermissionGranted
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber
import java.util.UUID

class KYCDocumentsFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    DocumentPickerDialog.DocumentPickerListener,
    ImagePicker.ImagePickerFailureListener,
    BaseFragment.NetworkListener {

    private val kycDocumentsVM: KYCDocumentsVM by navGraphViewModels(R.id.kyc_nav)

    //region Signature Document
    private lateinit var signatureImageTV: TextView
    private lateinit var uploadSignatureImageIV: ImageView
    private lateinit var loadingSignatureImagePB: ProgressBar
    private lateinit var cameraSignatureImageIV: ImageView
    private lateinit var signatureImageCIV: ContactIconView
    //endregion

    //region National Id Front Document
    private lateinit var nationalIdFrontImageTV: TextView
    private lateinit var uploadNationalIdFrontImageIV: ImageView
    private lateinit var loadingNationalIdFrontImagePB: ProgressBar
    private lateinit var cameraNationalIdFrontImageIV: ImageView
    private lateinit var nationalIdFrontImageCIV: ContactIconView
    //endregion

    //region National Id Back Document
    private lateinit var nationalIdBackImageTV: TextView
    private lateinit var uploadNationalIdBackImageIV: ImageView
    private lateinit var loadingNationalIdBackImagePB: ProgressBar
    private lateinit var cameraNationalIdBackImageIV: ImageView
    private lateinit var nationalIdBackImageCIV: ContactIconView
    //endregion

    //region Passport Front Document
    private lateinit var passportFrontImageTV: TextView
    private lateinit var uploadPassportFrontImageIV: ImageView
    private lateinit var loadingPassportFrontImagePB: ProgressBar
    private lateinit var cameraPassportFrontImageIV: ImageView
    private lateinit var passportFrontImageCIV: ContactIconView
    //endregion

    //region Passport Back Document
    private lateinit var passportBackImageTV: TextView
    private lateinit var uploadPassportBackImageIV: ImageView
    private lateinit var loadingPassportBackImagePB: ProgressBar
    private lateinit var cameraPassportBackImageIV: ImageView
    private lateinit var passportBackImageCIV: ContactIconView
    //endregion

    //region Proof Of Residence Document
    private lateinit var proofOfResidenceImageTV: TextView
    private lateinit var uploadProofOfResidenceImageIV: ImageView
    private lateinit var loadingProofOfResidenceImagePB: ProgressBar
    private lateinit var cameraProofOfResidenceImageIV: ImageView
    private lateinit var proofOfResidenceImageCIV: ContactIconView
    //endregion

    private lateinit var confirmButton: MaterialButton
    private val kycDetails: KYCDetails by lazy(KYCSharedPreference::getKYCDetails)

    private var documentUploadTarget: DocumentUploadTarget? = null

    private val args: KYCDocumentsFragmentArgs by navArgs()

    //region Document Picker Intent
    private val startForDocumentPickerResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let {
                    kycDocumentsVM.onDocumentSelected(
                        it,
                        documentUploadTarget!!,
                        FileExtension.PDF,
                        kycDetails.phoneNumber?.let(::LeoPhoneNumber),
                        kycDetails.passwordValidatedToken?.let(UUID::fromString),
                        requireContext(),
                        kycDetails.previousPath,
                    )
                }
            }
        }
    //endregion

    //region Camera Permission Intent
    private val startForCameraPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                kycDocumentsVM.onCameraPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.CAMERA)) {
                kycDocumentsVM.onCameraPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Camera permission denied, unable to proceed with image capture.")
                kycDocumentsVM.onCameraPermissionDenied()
            }
        }
    //endregion

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_kyc_documents, container, false)
        initViews(view)
        setupListeners()
        setupConfirmButton()
        setupBackPressed { kycDocumentsVM.onBackPressed(findNavController()) }
        kycDocumentsVM.targetMutableList.forEach(::setImage)
        prefillData()
        shouldAuthenticate = kycDetails.previousPath == KYCPreviousPath.PROFILE
        return view
    }

    override fun onResume() {
        super.onResume()
        kycDocumentsVM.init()
    }

    private fun prefillData() {
        args.apply {
            signatureImageId?.let {
                setImage(target = DocumentUploadTarget.SIGNATURE_DOCUMENT)
                kycDocumentsVM.documentIdMap[KEY_SIGNATURE_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.SIGNATURE_DOCUMENT)
            }
            nationalIdImageFrontId?.let {
                setImage(target = DocumentUploadTarget.NATIONAL_ID_FRONT)
                kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_FRONT_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.NATIONAL_ID_FRONT)
            }
            nationalIdImageBackId?.let {
                setImage(target = DocumentUploadTarget.NATIONAL_ID_BACK)
                kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_BACK_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.NATIONAL_ID_BACK)
            }
            proofOfResidenceImageId?.let {
                setImage(target = DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT)
                kycDocumentsVM.documentIdMap[KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT)
            }
            passportFrontImageId?.let {
                setImage(target = DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT)
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT)
            }
            passportBackImageId?.let {
                setImage(target = DocumentUploadTarget.PASSPORT_BACK_DOCUMENT)
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] = it
                kycDocumentsVM.targetMutableList.add(DocumentUploadTarget.PASSPORT_BACK_DOCUMENT)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setStatusBarColor()
        setupToolbar()
        networkListenerCallback = this
        kycDocumentsVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        kycDocumentsVM.currentCameraState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(state: KYCDocumentState) {
        when (state) {
            is KYCDocumentState.AcceptInput -> {
                handleAcceptInput()
            }

            is KYCDocumentState.InlineError -> {
                handleInlineErrorState(state)
            }

            is KYCDocumentState.Loading -> {
                handleLoadingState()
            }

            is KYCDocumentState.SuccessfulSubmit -> {
                handleSuccessfulSubmission()
            }

            is KYCDocumentState.Data -> {
                handleDocumentState(state)
            }
            KYCDocumentState.NationalIdBackAcceptInput -> {
                handleNationalIdBackAcceptInput()
            }
            KYCDocumentState.NationalIdFrontAcceptInput -> {
                handleNationalIdFrontAcceptInput()
            }
            KYCDocumentState.PassportBackAcceptInput -> {
                handlePassportBackAcceptInput()
            }
            KYCDocumentState.PassportFrontAcceptInput -> {
                handlePassportFrontAcceptInput()
            }
            KYCDocumentState.ProofOfResidenceAcceptInput -> {
                handleProofOfResidenceAcceptInput()
            }
            KYCDocumentState.SignatureAcceptInput -> {
                handleSignatureAcceptInput()
            }
        }
    }

    private fun handleDocumentState(state: KYCDocumentState.Data) {
        when (val imageState = state.documentViewState) {
            is DocumentViewState.DocumentUploaded -> {
                handleDocumentUploadedState(imageState)
            }

            is DocumentViewState.InitializeDocumentSelection -> {
                handleDocumentInitializationState(imageState)
            }

            is DocumentViewState.SelectDocumentAndUpload -> {
                handleDocumentSelectionAndUpload(imageState.target, imageState.showRemoveOption)
            }
        }
    }

    private fun reactToState(state: KYCDocumentCameraState) {
        when (state) {
            is KYCDocumentCameraState.CameraPermissionAlert -> {
                handleCameraPermissionAlertState()
            }

            is KYCDocumentCameraState.CameraPermissionDenied -> {
                showCameraPermissionDeniedError()
            }

            is KYCDocumentCameraState.CameraPermissionGranted -> {
                startCamera()
            }

            is KYCDocumentCameraState.CameraPermissionRequested -> {
                requestCameraPermission()
            }

            is KYCDocumentCameraState.Waiting -> {
                handleWaitingOnCameraState()
            }
        }
    }

    private fun showCameraPermissionDeniedError() {
        showCameraPermissionAlert(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            message = getString(R.string.kycCameraPermissionDeniedSubtitle),
            CAMERA_PERMISSION_DENIED_DIALOG_CODE,
            getString(R.string.alertActionDismiss),
            requireContext().getEmptyString(),
        )
    }

    private fun requestCameraPermission() {
        startForCameraPermissionResult.launch(Manifest.permission.CAMERA)
    }

    private fun handleWaitingOnCameraState() {
        // Nothing to do. Waiting for user to tap on `Capture an Image` from dialog.
    }

    private fun startCamera() {
        ImagePicker.openCamera(this)
        kycDocumentsVM.onCameraOpened()
    }

    private fun handleCameraPermissionAlertState() {
        showCameraPermissionAlert(
            title = getString(R.string.alertTitleCameraAccessNotPermitted),
            getString(R.string.kycCameraPermissionDeniedSubtitle),
            CAMERA_PERMISSION_DIALOG_CODE,
            getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun showCameraPermissionAlert(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleDocumentUploadedState(state: DocumentViewState.DocumentUploaded) {
        if (kycDocumentsVM.canUpdateDocumentsIdMap()) {
            kycDocumentsVM.targetMutableList.add(state.target)
            kycDocumentsVM.targetMutableList.distinct()
            when (state.target) {
                DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
                DocumentUploadTarget.SIGNATURE_DOCUMENT -> kycDocumentsVM.documentIdMap[KEY_SIGNATURE_DOCUMENT_ID] =
                    state.documentId

                DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] =
                    state.documentId

                DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] =
                    state.documentId

                DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> kycDocumentsVM.documentIdMap[KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID] =
                    state.documentId

                DocumentUploadTarget.NATIONAL_ID_FRONT -> kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_FRONT_DOCUMENT_ID] =
                    state.documentId

                DocumentUploadTarget.NATIONAL_ID_BACK -> kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_BACK_DOCUMENT_ID] =
                    state.documentId
            }
            when (state.fileExtension) {
                FileExtension.JPG -> setImage(state.target)
                FileExtension.PNG -> setImage(state.target)
                FileExtension.PDF -> setImage(state.target)
            }
            kycDocumentsVM.onDocumentUploaded(state.target)
        }
    }

    private fun handleDocumentInitializationState(documentSelectionState: DocumentViewState.InitializeDocumentSelection) {
        setLoadingForTarget(documentSelectionState.target)
    }

    private fun setLoadingForTarget(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                loadingSignatureImagePB.visibility = View.VISIBLE
                uploadSignatureImageIV.visibility = View.INVISIBLE
            }

            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                loadingPassportFrontImagePB.visibility = View.VISIBLE
                uploadPassportFrontImageIV.visibility = View.INVISIBLE
            }

            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                loadingPassportBackImagePB.visibility = View.VISIBLE
                uploadPassportBackImageIV.visibility = View.INVISIBLE
            }

            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                loadingProofOfResidenceImagePB.visibility = View.VISIBLE
                uploadProofOfResidenceImageIV.visibility = View.INVISIBLE
            }

            DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                loadingNationalIdFrontImagePB.visibility = View.VISIBLE
                uploadNationalIdFrontImageIV.visibility = View.INVISIBLE
            }

            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                loadingNationalIdBackImagePB.visibility = View.VISIBLE
                uploadNationalIdBackImageIV.visibility = View.INVISIBLE
            }
        }
    }

    private fun handleDocumentSelectionAndUpload(
        target: DocumentUploadTarget,
        showRemoveOption: Boolean,
    ) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
            else -> {
                // Nothing to handle
            }
        }
        val documentPickerDialog = DocumentPickerDialog()
        documentPickerDialog.setArguments(true, showRemoveOption, target)
        documentPickerDialog.show(
            childFragmentManager,
            DocumentPickerDialog.TAG,
        )
    }

    private fun handleAcceptInput() {
        dismissProgressDialog()
        resetAllLoading()
    }

    private fun handleSignatureAcceptInput() {
        dismissProgressDialog()
        loadingSignatureImagePB.visibility = View.INVISIBLE
    }

    private fun handlePassportFrontAcceptInput() {
        dismissProgressDialog()
        loadingPassportFrontImagePB.visibility = View.INVISIBLE
    }

    private fun handlePassportBackAcceptInput() {
        dismissProgressDialog()
        loadingPassportBackImagePB.visibility = View.INVISIBLE
    }

    private fun handleNationalIdFrontAcceptInput() {
        dismissProgressDialog()
        loadingNationalIdFrontImagePB.visibility = View.INVISIBLE
    }

    private fun handleNationalIdBackAcceptInput() {
        dismissProgressDialog()
        loadingNationalIdBackImagePB.visibility = View.INVISIBLE
    }

    private fun handleProofOfResidenceAcceptInput() {
        dismissProgressDialog()
        loadingProofOfResidenceImagePB.visibility = View.INVISIBLE
    }

    private fun handleSuccessfulSubmission() {
        dismissProgressDialog()
        if (kycDetails.previousPath == KYCPreviousPath.PROFILE) {
            showErrorDialog(
                title = getString(R.string.alertTitleUpdatedKYCDetails),
                message = getString(R.string.alertMessageUpdatedKYCDetails),
                UPDATE_SUCCESS_ID,
            )
        } else {
            kycDocumentsVM.onSuccessfulSubmission(findNavController(), kycDetails.previousPath)
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleInlineErrorState(state: KYCDocumentState.InlineError) {
        hideKeyboard()
        dismissProgressDialog()
        if (state.documentUploadTarget != null) {
            resetImageLoading(state.documentUploadTarget)
            documentUploadTarget = null
        }
        when (state.error.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(
                    state.error.errorMessage,
                    requireView(),
                    kycDocumentsVM::onInlineErrorDismissed,
                )
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    state.error.errorTitle,
                    state.error.errorMessage,
                    state.errorCode ?: DialogCodes.KYC_DATA_ERROR_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun initViews(view: View) {
        confirmButton = view.findViewById(R.id.confirm_button)

        //region Signature Document
        signatureImageTV = view.findViewById(R.id.add_signature)
        uploadSignatureImageIV = view.findViewById(R.id.upload_signature)
        loadingSignatureImagePB = view.findViewById(R.id.signature_loading)
        cameraSignatureImageIV = view.findViewById(R.id.signature_camera_iv)
        signatureImageCIV = view.findViewById(R.id.signature_iv)
        //endregion

        //region National Id Front
        nationalIdFrontImageTV = view.findViewById(R.id.add_national_id_front)
        uploadNationalIdFrontImageIV = view.findViewById(R.id.upload_national_id_front)
        loadingNationalIdFrontImagePB = view.findViewById(R.id.national_id_front_loading)
        cameraNationalIdFrontImageIV = view.findViewById(R.id.national_id_front_camera_iv)
        nationalIdFrontImageCIV = view.findViewById(R.id.national_id_front_iv)
        //endregion

        //region National Id Back
        nationalIdBackImageTV = view.findViewById(R.id.add_national_id_back)
        uploadNationalIdBackImageIV = view.findViewById(R.id.upload_national_id_back)
        loadingNationalIdBackImagePB = view.findViewById(R.id.national_id_back_loading)
        cameraNationalIdBackImageIV = view.findViewById(R.id.national_id_back_camera_iv)
        nationalIdBackImageCIV = view.findViewById(R.id.national_id_back_iv)
        //endregion

        //region Passport Front
        passportFrontImageTV = view.findViewById(R.id.add_passport_front)
        uploadPassportFrontImageIV = view.findViewById(R.id.upload_passport_front)
        loadingPassportFrontImagePB = view.findViewById(R.id.passport_front_loading)
        cameraPassportFrontImageIV = view.findViewById(R.id.passport_front_camera_iv)
        passportFrontImageCIV = view.findViewById(R.id.passport_front_iv)
        //endregion

        //region Passport Back
        passportBackImageTV = view.findViewById(R.id.add_passport_back)
        uploadPassportBackImageIV = view.findViewById(R.id.upload_passport_back)
        loadingPassportBackImagePB = view.findViewById(R.id.passport_back_loading)
        cameraPassportBackImageIV = view.findViewById(R.id.passport_back_camera_iv)
        passportBackImageCIV = view.findViewById(R.id.passport_back_iv)
        //endregion

        //region Residence Proof
        proofOfResidenceImageTV = view.findViewById(R.id.add_residence)
        uploadProofOfResidenceImageIV = view.findViewById(R.id.upload_residence)
        loadingProofOfResidenceImagePB = view.findViewById(R.id.residence_loading)
        cameraProofOfResidenceImageIV = view.findViewById(R.id.residence_camera_iv)
        proofOfResidenceImageCIV = view.findViewById(R.id.residence_iv)
        //endregion
    }

    private fun setupToolbar() {
        showToolbar()
        setDefaultToolbar(getString(R.string.kycUploadPhotosTitle))
        val params = signatureImageCIV.layoutParams as ConstraintLayout.LayoutParams
        params.setMargins(0, 0, 0, 0)
        signatureImageCIV.layoutParams = params
    }

    private fun setupListeners() {
        //region Signature Document
        uploadSignatureImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.SIGNATURE_DOCUMENT,
                false,
            )
        }
        cameraSignatureImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.SIGNATURE_DOCUMENT,
                false,
            )
        }
        signatureImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.SIGNATURE_DOCUMENT,
                false,
            )
        }
        //endregion

        //region National Id Front
        uploadNationalIdFrontImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_FRONT,
                false,
            )
        }
        cameraNationalIdFrontImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_FRONT,
                false,
            )
        }
        nationalIdFrontImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_FRONT,
                false,
            )
        }
        //endregion

        //region National Id Back
        uploadNationalIdBackImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_BACK,
                false,
            )
        }
        cameraNationalIdBackImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_BACK,
                false,
            )
        }
        nationalIdBackImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.NATIONAL_ID_BACK,
                false,
            )
        }
        //endregion

        //region Passport Front
        uploadPassportFrontImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] != null,
            )
        }
        cameraPassportFrontImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] != null,
            )
        }
        passportFrontImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] != null,
            )
        }
        //endregion

        //region Passport Back
        uploadPassportBackImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_BACK_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] != null,
            )
        }
        cameraPassportBackImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_BACK_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] != null,
            )
        }
        passportBackImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PASSPORT_BACK_DOCUMENT,
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] != null,
            )
        }
        //endregion

        //region Residence Proof
        uploadProofOfResidenceImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT,
                false,
            )
        }
        cameraProofOfResidenceImageIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT,
                false,
            )
        }
        proofOfResidenceImageCIV.setOnClickListener {
            kycDocumentsVM.onDocumentUploadTapped(
                DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT,
                false,
            )
        }
        //endregion

        ImagePicker.setImagePickerFailureListener(this)
    }

    private fun deleteImageOffline(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.SIGNATURE_DOCUMENT,
            DocumentUploadTarget.NATIONAL_ID_FRONT,
            DocumentUploadTarget.NATIONAL_ID_BACK,
            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT,
            -> {
                // Cannot remove image for mandatory field, it can only be changed
            }

            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                cameraPassportFrontImageIV.visibility = View.INVISIBLE
                passportFrontImageCIV.visibility = View.INVISIBLE
                passportFrontImageCIV.imageView.setImageResource(0)
                uploadPassportFrontImageIV.visibility = View.VISIBLE
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] = null
                updateImageTextViews(
                    passportFrontImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportFront),
                )
            }

            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                cameraPassportBackImageIV.visibility = View.INVISIBLE
                passportBackImageCIV.visibility = View.INVISIBLE
                passportBackImageCIV.imageView.setImageResource(0)
                uploadPassportBackImageIV.visibility = View.VISIBLE
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] = null
                updateImageTextViews(
                    passportBackImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportBack),
                )
            }

            DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
        }
    }

    @SuppressLint("HardwareIds")
    private fun setupConfirmButton() {
        confirmButton.setOnClickListener {
            if (isDocumentBeingUploaded()) {
                showErrorDialog(
                    getString(R.string.alertTitleWaitForUpload),
                    getString(R.string.alertMessageWaitForUpload),
                    DialogCodes.KYC_DOCUMENT_UPLOAD,
                )
            } else {
                kycDocumentsVM.onConfirmClicked(
                    kycDocumentsVM.documentIdMap[KEY_SIGNATURE_DOCUMENT_ID],
                    kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_FRONT_DOCUMENT_ID],
                    kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_BACK_DOCUMENT_ID],
                    kycDocumentsVM.documentIdMap[KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID],
                    kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID],
                    kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID],
                    requireContext(),
                    kycDetails.passwordValidatedToken?.let(UUID::fromString),
                    kycDetails.previousPath,
                )
            }
        }
    }

    private fun isDocumentBeingUploaded(): Boolean {
        return loadingSignatureImagePB.isVisible || loadingNationalIdFrontImagePB.isVisible || loadingNationalIdBackImagePB.isVisible || loadingPassportFrontImagePB.isVisible || loadingPassportBackImagePB.isVisible || loadingProofOfResidenceImagePB.isVisible
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (resultCode == AppCompatActivity.RESULT_OK) {
            when (requestCode) {
                ImagePicker.IMAGE_PICKER_GALLERY_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        data,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = true,
                    )
                }

                ImagePicker.IMAGE_PICKER_CAMERA_REQUEST_CODE -> {
                    ImagePicker.cropImage(
                        null,
                        this,
                        DocumentUploadHelper.MAX_IMAGE_WIDTH,
                        DocumentUploadHelper.MAX_IMAGE_HEIGHT,
                        IMAGE_COMPRESSION_QUALITY,
                        freeStyleCropEnabled = true,
                    )
                }

                ImagePicker.IMAGE_CROP_REQUEST_CODE -> {
                    val resultUri = ImagePicker.getOutput(data!!)!!
                    kycDocumentsVM.onDocumentSelected(
                        resultUri,
                        documentUploadTarget!!,
                        FileExtension.JPG,
                        kycDetails.phoneNumber?.let(::LeoPhoneNumber),
                        kycDetails.passwordValidatedToken?.let(UUID::fromString),
                        requireContext(),
                        kycDetails.previousPath,
                    )
                }

                ImagePicker.IMAGE_CROP_RESULT_ERROR_CODE -> {
                    resetImageLoading(documentUploadTarget!!)
                    val cropError = ImagePicker.getError(data!!)
                    cropError?.message?.let {
                        Timber.tag(TAG).d("ImagePicker: $it")
                    }
                }
            }
        }
    }

    private fun setImage(
        target: DocumentUploadTarget,
    ) {
        when (target) {
            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                uploadSignatureImageIV.visibility = View.INVISIBLE
                cameraSignatureImageIV.visibility = View.VISIBLE
                signatureImageCIV.visibility = View.VISIBLE
                signatureImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                loadingSignatureImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    signatureImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfSignature),
                )
            }

            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                uploadNationalIdFrontImageIV.visibility = View.INVISIBLE
                cameraNationalIdFrontImageIV.visibility = View.VISIBLE
                nationalIdFrontImageCIV.visibility = View.VISIBLE
                nationalIdFrontImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                loadingNationalIdFrontImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    nationalIdFrontImageTV,
                    requireContext().getString(R.string.kycNationalIdFrontTitle),
                )
            }

            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                uploadNationalIdBackImageIV.visibility = View.INVISIBLE
                cameraNationalIdBackImageIV.visibility = View.VISIBLE
                nationalIdBackImageCIV.visibility = View.VISIBLE
                nationalIdBackImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                loadingNationalIdBackImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    nationalIdBackImageTV,
                    requireContext().getString(R.string.kycNationalIdBackTitle),
                )
            }

            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                uploadPassportFrontImageIV.visibility = View.INVISIBLE
                cameraPassportFrontImageIV.visibility = View.VISIBLE
                passportFrontImageCIV.visibility = View.VISIBLE
                passportFrontImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                loadingPassportFrontImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    passportFrontImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportFront),
                )
            }

            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                uploadPassportBackImageIV.visibility = View.INVISIBLE
                cameraPassportBackImageIV.visibility = View.VISIBLE
                passportBackImageCIV.visibility = View.VISIBLE
                passportBackImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                loadingPassportBackImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    passportBackImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportBack),
                )
            }

            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                uploadProofOfResidenceImageIV.visibility = View.INVISIBLE
                cameraProofOfResidenceImageIV.visibility = View.VISIBLE
                proofOfResidenceImageCIV.visibility = View.VISIBLE
                loadingProofOfResidenceImagePB.visibility = View.INVISIBLE
                proofOfResidenceImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                updateImageTextViews(
                    proofOfResidenceImageTV,
                    requireContext().getString(R.string.kycUploadProofOfResidence),
                )
            }

            DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
        }
    }

    private fun updateImageTextViews(textView: TextView, text: String, isUploaded: Boolean = true) {
        textView.text = text
        if (isUploaded) {
            textView.setTextColor(requireContext().getColor(R.color.titleTextColor))
        } else {
            textView.setTextColor(requireContext().getColor(R.color.unavailableDocumentTextColor))
        }
    }

    private fun setReUploadImages(
        target: DocumentUploadTarget,
    ) {
        when (target) {
            DocumentUploadTarget.PROFILE_IMAGE -> {
                throw IllegalStateException("Profile image is not supported in this screen.")
            }

            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                uploadSignatureImageIV.visibility = View.VISIBLE
                cameraSignatureImageIV.visibility = View.INVISIBLE
                signatureImageCIV.visibility = View.INVISIBLE
                loadingSignatureImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    signatureImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfSignature),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_SIGNATURE_DOCUMENT_ID] = null
            }

            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                uploadPassportFrontImageIV.visibility = View.VISIBLE
                cameraPassportFrontImageIV.visibility = View.INVISIBLE
                passportFrontImageCIV.visibility = View.INVISIBLE
                loadingPassportFrontImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    passportFrontImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportFront),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] = null
            }

            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                uploadPassportBackImageIV.visibility = View.VISIBLE
                cameraPassportBackImageIV.visibility = View.INVISIBLE
                passportBackImageCIV.visibility = View.INVISIBLE
                loadingPassportBackImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    passportBackImageTV,
                    requireContext().getString(R.string.kycUploadPhotoOfPassportBack),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] = null
            }

            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                uploadProofOfResidenceImageIV.visibility = View.VISIBLE
                cameraProofOfResidenceImageIV.visibility = View.INVISIBLE
                proofOfResidenceImageCIV.visibility = View.INVISIBLE
                loadingProofOfResidenceImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    proofOfResidenceImageTV,
                    requireContext().getString(R.string.kycUploadPhotoPickerProofOfResidence),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID] = null
            }

            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                uploadNationalIdFrontImageIV.visibility = View.VISIBLE
                cameraNationalIdFrontImageIV.visibility = View.INVISIBLE
                nationalIdFrontImageCIV.visibility = View.INVISIBLE
                loadingNationalIdFrontImagePB.visibility = View.INVISIBLE
                nationalIdFrontImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
                updateImageTextViews(
                    nationalIdFrontImageTV,
                    requireContext().getString(R.string.kycNationalIdFrontTitle),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_FRONT_DOCUMENT_ID] = null
            }

            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                uploadNationalIdBackImageIV.visibility = View.VISIBLE
                cameraNationalIdBackImageIV.visibility = View.INVISIBLE
                nationalIdBackImageCIV.visibility = View.INVISIBLE
                loadingNationalIdBackImagePB.visibility = View.INVISIBLE
                updateImageTextViews(
                    nationalIdBackImageTV,
                    requireContext().getString(R.string.kycNationalIdBackTitle),
                    false,
                )
                kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_BACK_DOCUMENT_ID] = null
            }
        }
    }

    private fun showImagePickingInstructionToast(documentUploadTarget: DocumentUploadTarget) {
        if (documentUploadTarget == DocumentUploadTarget.PROFILE_IMAGE) {
            Toast.makeText(
                requireContext(),
                requireContext().getString(R.string.toastMessageImageDimensions),
                Toast.LENGTH_LONG,
            ).show()
        }
    }

    private fun pickDocument() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "application/pdf"
        }

        startForDocumentPickerResult.launch(intent)
    }

    private fun resetImageLoading(target: DocumentUploadTarget) {
        when (target) {
            DocumentUploadTarget.SIGNATURE_DOCUMENT -> {
                loadingSignatureImagePB.visibility = View.INVISIBLE
                uploadSignatureImageIV.visibility = View.VISIBLE
            }

            DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT -> {
                loadingPassportFrontImagePB.visibility = View.INVISIBLE
                uploadPassportFrontImageIV.visibility = View.VISIBLE
            }

            DocumentUploadTarget.PASSPORT_BACK_DOCUMENT -> {
                loadingPassportBackImagePB.visibility = View.INVISIBLE
                uploadPassportBackImageIV.visibility = View.VISIBLE
            }

            DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT -> {
                loadingProofOfResidenceImagePB.visibility = View.INVISIBLE
                uploadProofOfResidenceImageIV.visibility = View.VISIBLE
            }

            DocumentUploadTarget.PROFILE_IMAGE -> throw IllegalStateException("Profile image is not supported in this screen.")
            DocumentUploadTarget.NATIONAL_ID_FRONT -> {
                loadingNationalIdFrontImagePB.visibility = View.INVISIBLE
                uploadNationalIdFrontImageIV.visibility = View.VISIBLE
            }

            DocumentUploadTarget.NATIONAL_ID_BACK -> {
                loadingNationalIdBackImagePB.visibility = View.INVISIBLE
                uploadNationalIdBackImageIV.visibility = View.VISIBLE
            }
        }
    }

    private fun resetAllLoading() {
        loadingSignatureImagePB.visibility = View.INVISIBLE
        loadingPassportFrontImagePB.visibility = View.INVISIBLE
        loadingNationalIdFrontImagePB.visibility = View.INVISIBLE
        loadingNationalIdBackImagePB.visibility = View.INVISIBLE
        loadingPassportBackImagePB.visibility = View.INVISIBLE
        loadingProofOfResidenceImagePB.visibility = View.INVISIBLE

        /***
         * Once we have reset all loading progress bar and made them invisible we should check if
         * a document was uploaded or not for the respective progress bar, according to that we need
         * to make other images on this screen visible or invisible or else a blank space might get
         * shown on this screen.
         */

        if (kycDocumentsVM.documentIdMap[KEY_SIGNATURE_DOCUMENT_ID] != null) {
            uploadSignatureImageIV.visibility = View.INVISIBLE
            cameraSignatureImageIV.visibility = View.VISIBLE
            signatureImageCIV.visibility = View.VISIBLE
            signatureImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
        } else {
            uploadSignatureImageIV.visibility = View.VISIBLE
            cameraSignatureImageIV.visibility = View.INVISIBLE
            signatureImageCIV.visibility = View.INVISIBLE
        }

        if (kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_FRONT_DOCUMENT_ID] != null) {
            uploadNationalIdFrontImageIV.visibility = View.INVISIBLE
            cameraNationalIdFrontImageIV.visibility = View.VISIBLE
            nationalIdFrontImageCIV.visibility = View.VISIBLE
            nationalIdFrontImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
        } else {
            uploadNationalIdFrontImageIV.visibility = View.VISIBLE
            cameraNationalIdFrontImageIV.visibility = View.INVISIBLE
            nationalIdFrontImageCIV.visibility = View.INVISIBLE
        }

        if (kycDocumentsVM.documentIdMap[KEY_NATIONAL_ID_BACK_DOCUMENT_ID] != null) {
            uploadNationalIdBackImageIV.visibility = View.INVISIBLE
            cameraNationalIdBackImageIV.visibility = View.VISIBLE
            nationalIdBackImageCIV.visibility = View.VISIBLE
            nationalIdBackImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
        } else {
            uploadNationalIdBackImageIV.visibility = View.VISIBLE
            cameraNationalIdBackImageIV.visibility = View.INVISIBLE
            nationalIdBackImageCIV.visibility = View.INVISIBLE
        }

        if (kycDocumentsVM.documentIdMap[KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID] != null) {
            uploadProofOfResidenceImageIV.visibility = View.INVISIBLE
            cameraProofOfResidenceImageIV.visibility = View.VISIBLE
            proofOfResidenceImageCIV.visibility = View.VISIBLE
            proofOfResidenceImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
        } else {
            uploadProofOfResidenceImageIV.visibility = View.VISIBLE
            cameraProofOfResidenceImageIV.visibility = View.INVISIBLE
            proofOfResidenceImageCIV.visibility = View.INVISIBLE
        }

        if (kycDocumentsVM.documentIdMap[KEY_PASSPORT_FRONT_DOCUMENT_ID] != null) {
            uploadPassportFrontImageIV.visibility = View.INVISIBLE
            cameraPassportFrontImageIV.visibility = View.VISIBLE
            passportFrontImageCIV.visibility = View.VISIBLE
            passportFrontImageCIV.imageView.load(R.drawable.ic_uploaded_icon)
        } else {
            uploadPassportFrontImageIV.visibility = View.VISIBLE
            cameraPassportFrontImageIV.visibility = View.INVISIBLE
            passportFrontImageCIV.visibility = View.INVISIBLE
        }

        if (kycDocumentsVM.documentIdMap[KEY_PASSPORT_BACK_DOCUMENT_ID] != null) {
            uploadPassportBackImageIV.visibility = View.INVISIBLE
            cameraPassportBackImageIV.visibility = View.VISIBLE
            passportBackImageCIV.visibility = View.VISIBLE
        } else {
            uploadPassportBackImageIV.visibility = View.VISIBLE
            cameraPassportBackImageIV.visibility = View.INVISIBLE
            passportBackImageCIV.visibility = View.INVISIBLE
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycDocumentsVM.onOpenSettingsClicked(
                requireContext(),
            )

            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                kycDocumentsVM.onErrorDialogDismissed()
            }

            CAMERA_PERMISSION_DENIED_DIALOG_CODE -> {
                kycDocumentsVM.onCameraPermissionDialogDismissed()
                kycDocumentsVM.onErrorDialogDismissed()
            }

            KYCDataVM.ERROR_CODE_TOKEN_EXPIRED -> {
                when (kycDetails.previousPath) {
                    KYCPreviousPath.SIGN_IN -> {
                        findNavController().popBackStack(R.id.sign_in_nav, true)
                    }

                    KYCPreviousPath.SIGN_UP -> {
                        findNavController().popBackStack(R.id.sign_up_nav, true)
                    }

                    KYCPreviousPath.PROFILE -> {
                        findNavController().popBackStack(R.id.home_nav, true)
                    }
                }
            }

            UPDATE_SUCCESS_ID -> {
                kycDocumentsVM.onSuccessfulSubmission(findNavController(), kycDetails.previousPath)
            }

            SIGN_UP_IN_SESSION_EXPIRED -> {
                when (kycDetails.previousPath) {
                    KYCPreviousPath.SIGN_IN -> {
                        findNavController().popBackStack(R.id.sign_in_nav, true)
                    }

                    KYCPreviousPath.SIGN_UP -> {
                        findNavController().popBackStack(R.id.sign_up_nav, true)
                    }

                    KYCPreviousPath.PROFILE -> {
                        throw IllegalStateException("SIGNUP_SESSION_EXPIRED cannot be encountered from Update KYC Flow")
                    }
                }
            }

            DialogCodes.INVALID_SIGNATURE_IMAGE_ID_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.SIGNATURE_DOCUMENT)
            }

            DialogCodes.INVALID_NATIONAL_ID_FRONT_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.NATIONAL_ID_FRONT)
            }

            DialogCodes.INVALID_NATIONAL_ID_BACK_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.NATIONAL_ID_BACK)
            }

            DialogCodes.INVALID_PROOF_OF_RESIDENCE_ID_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.PROOF_OF_RESIDENCE_DOCUMENT)
            }

            DialogCodes.INVALID_PASSPORT_FRONT_ID_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.PASSPORT_FRONT_DOCUMENT)
            }

            DialogCodes.INVALID_PASSPORT_BACK_ID_DIALOG_CODE -> {
                setReUploadImages(DocumentUploadTarget.PASSPORT_BACK_DOCUMENT)
            }

            DialogCodes.KYC_DOCUMENT_UPLOAD -> {
                // Nothing to handle as we are waiting for documents to get uploaded
            }

            else -> {
                kycDocumentsVM.onErrorDialogDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CAMERA_PERMISSION_DIALOG_CODE -> kycDocumentsVM.onCameraPermissionAlertDismissed()
        }
    }

    override fun captureImageFromCamera(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        if (!checkCameraPermissionGranted(requireContext())) {
            kycDocumentsVM.onCameraPermissionRequested()
        } else {
            showImagePickingInstructionToast(documentUploadTarget)
            startCamera()
            kycDocumentsVM.onCameraPermissionGiven(documentUploadTarget)
        }
    }

    override fun pickImageFromGallery(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        showImagePickingInstructionToast(documentUploadTarget)
        ImagePicker.openImageGallery(this)
        kycDocumentsVM.onCameraPermissionGiven(documentUploadTarget)
    }

    override fun pickDocumentFromStorage(documentUploadTarget: DocumentUploadTarget) {
        this.documentUploadTarget = documentUploadTarget
        pickDocument()
        kycDocumentsVM.onCameraPermissionGiven(documentUploadTarget)
    }

    override fun removeDocument(documentUploadTarget: DocumentUploadTarget) {
        deleteImageOffline(documentUploadTarget)
        kycDocumentsVM.onCameraPermissionGiven(documentUploadTarget)
    }

    override fun dismissed() {
        kycDocumentsVM.onDocumentPickerDismissed()
    }

    override fun imagePickerFailure(error: Exception) {
        kycDocumentsVM.onDocumentPickerFailure(requireContext(), documentUploadTarget!!)
        Timber.tag(TAG).e(error)
    }

    override fun onNetworkAvailable() {
        prefillData()
    }
}

private const val TAG = "KYCDocumentFragment"
private const val KEY_SIGNATURE_DOCUMENT_ID = "signature_document_id"
private const val KEY_NATIONAL_ID_FRONT_DOCUMENT_ID = "national_id_front_document_id"
private const val KEY_NATIONAL_ID_BACK_DOCUMENT_ID = "national_id_back_document_id"
private const val KEY_PASSPORT_FRONT_DOCUMENT_ID = "passport_front_document_id"
private const val KEY_PASSPORT_BACK_DOCUMENT_ID = "passport_back_document_id"
private const val KEY_PROOF_OF_RESIDENCE_DOCUMENT_ID = "proof_of_residence_document_id"
private const val UPDATE_SUCCESS_ID = 1
