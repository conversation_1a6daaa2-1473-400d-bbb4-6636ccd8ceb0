package com.resoluttech.core.kyc

import android.net.Uri
import com.resoluttech.bcn.document.FileAttributes

enum class DocumentUploadTarget {
    PROFILE_IMAGE, SIGNATURE_DOCUMENT, PASSPORT_FRONT_DOCUMENT, PASSPORT_BACK_DOCUMENT, PROOF_OF_RESIDENCE_DOCUMENT, NATIONAL_ID_FRONT, NATIONAL_ID_BACK
}

enum class FileExtension {
    JPG, PNG, PDF
}

data class DocumentData(
    val documentUri: Uri,
    val fileAttributes: FileAttributes,
    val uploadTarget: DocumentUploadTarget,
    val fileExtension: FileExtension,
)
