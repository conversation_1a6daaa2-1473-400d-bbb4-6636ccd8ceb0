package com.resoluttech.core.kyc

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.time.LocalDate

@Parcelize
data class KYCDataUtils(
    val lastname: String?,
    val nationalId: String,
    val firstName: String,
    val otherNames: String?,
    val gender: String,
    val dateOfBirth: LocalDate,
    val dateOfIssue: LocalDate,
    val dateOfExpiry: LocalDate? = null,
    val emailId: String? = null,
    val placeOfBirth: String? = null,
    val residentAddress: String? = null,
    val postalAddress: String? = null,
) : Parcelable
