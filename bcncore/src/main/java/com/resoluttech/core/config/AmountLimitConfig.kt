package com.resoluttech.core.config

import com.resoluttech.core.views.AmountEditText
import java.lang.IllegalStateException

data class AmountLimitConfig(val beforeDecimal: Int, val precision: Int) {
    init {
        // To enforce that the decimal limit doesn't exceeds the multiplication factor, else it will result in
        // double value instead of long which is required.
        if (precision > AmountEditText.amountMultiplicationFactor) {
            throw IllegalStateException("Amount can be formatted up-to ${AmountEditText.amountMultiplicationFactor} decimal places.")
        }
    }
}
