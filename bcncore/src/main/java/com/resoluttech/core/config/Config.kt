package com.resoluttech.core.config

class Config {
    companion object {
        // These constants are related to all the ET lengths.
        const val TEXT_COUNTER_THRESHOLD: Double = 0.80
        const val MAX_REMARKS_LENGTH: Int = 200
        const val MAX_REMARKS_LINES: Int = 8
        const val MAX_WALLET_DISPLAY_NAME_LENGTH: Int = 200
        const val MAX_FIRST_NAME_LENGTH: Int = 200
        const val MAX_LAST_NAME_LENGTH: Int = 200
        const val MAX_OTHER_NAME_LENGTH: Int = 200
        const val MAX_NATIONAL_ID_LENGTH: Int = 300
        const val MAX_PLACE_OF_BIRTH_LENGTH: Int = 150
        const val MAX_RESIDENT_ADDRESS_LENGTH: Int = 200
        const val MAX_POSTAL_ADDRESS_LENGTH: Int = 200
        const val MAX_SECURITY_ANSWER_LENGTH: Int = 200
        const val MAX_BILLER_INPUT_LENGTH: Int = 500
        const val MAX_PASSWORD_LENGTH: Int = 100
        const val MIN_PASSWORD_LENGTH: Int = 8
        const val MAX_ALLOWED_MONTHLY_RECURRENCE: Long = 11
        const val MAX_ALLOWED_WEEKLY_RECURRENCE: Long = 23
        const val MAX_UTILITY_NICKNAME_LENGTH: Int = 30
        const val ET_RESET_INDEX: Int = 1

        // These constants are related to all the AmountEditText lengths.
        const val MINIMUM_INPUT_AMOUNT: Double = 1.0

        // These constants are related to all the AmountEditText lengths.
        const val OTP_LENGTH: Int = 6

        // These constants are related to Session PIN.
        const val SESSION_PIN_WARNING_ATTEMPT_LEFT: Int = 1

        // These are all the generic constants.
        const val FIRST_INDEX: Int = 0

        // These constants are related to Push Token
        const val PUSH_TOKEN_SERVER_UPDATE_TIME_INTERVAL_IN_DAYS: Int = 7

        // These constants are related to Privacy Policy
        const val PRIVACY_POLICY_URL: String = "https://bcn-ws-download-page.s3.af-south-1.amazonaws.com/0.0.7/privacy-policy.html"
    }
}
