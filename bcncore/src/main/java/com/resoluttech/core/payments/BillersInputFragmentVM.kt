package com.resoluttech.core.payments

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.payments.BillerUserInputValue
import com.resoluttech.bcn.payments.CreateBillPaymentRequestRPC
import com.resoluttech.bcncore.R
import com.resoluttech.core.payments.model.PaymentChargeBreakdownItem
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.PaymentsRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.logout
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.util.UUID

class BillersInputFragmentVM : ViewModel() {

    private val _currentState = MutableLiveData<BillersInputState>(BillersInputState.AcceptInput)
    val currentState: LiveData<BillersInputState> = _currentState
    private var vMScopeOnIO = viewModelScope + Dispatchers.IO
    private var paymentRepository = PaymentRepository()
    var selectedAccountId: UUID? = null

    fun onProgressButtonClicked(navController: NavController) {
        _currentState.postValue(BillersInputState.RequestAuthentication)
        navController.navigate(R.id.action_billersInputFragment_to_auth_provider_nav)
    }

    fun onUserAuthenticationSuccess(
        billerUserInputValue: MutableList<BillerUserInputValue>,
        billerId: String,
        context: Context,
    ) {
        if (selectedAccountId == null) {
            _currentState.postValue(
                BillersInputState.Error(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleNoWalletSelected),
                        context.getString(R.string.alertMessageNoWalletSelected),
                    ),
                ),
            )
            return
        }
        _currentState.postValue(BillersInputState.Loading)
        vMScopeOnIO.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result = paymentRepository.createBillerPaymentRequest(
                            selectedAccountId
                                ?: throw IllegalArgumentException("Account Id cannot be null"),
                            billerId,
                            billerUserInputValue,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            _currentState.postValue(BillersInputState.Data(result.response))
                        }

                        is LeoRPCResult.LeoError -> {
                            PaymentsRPCExceptionHandler.createBillerPaymentRequestRPCErrorMessage(
                                context,
                                result.error,
                                SelectedAccountHelper.getAccountFromId(
                                    selectedAccountId
                                        ?: throw IllegalArgumentException("Account Id cannot be null"),
                                ).name,
                            ).apply {
                                handleRPCExceptionCase(this)
                            }
                        }
                    }
                },
                handleException = ::handleRPCExceptionCase,
            )
        }
    }

    fun onUserAuthenticationFailed(context: Context) {
        _currentState.postValue(
            BillersInputState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleBiometryAuthenticationFailure),
                    context.getString(R.string.alertMessageBiometryAuthenticationFailure),
                    ERROR_CODE_APP_AUTHENTICATION_FAILED,
                ),
            ),
        )
    }

    fun onUserAuthenticationCancelled() {
        _currentState.postValue(BillersInputState.AcceptInput)
    }

    fun onDataAvailable(
        biller: Biller,
        navController: NavController,
        response: CreateBillPaymentRequestRPC.Response,
    ) {
        _currentState.postValue(BillersInputState.AcceptInput)
        val args = Bundle()
        args.putParcelable(
            BILLER_DETAIL_KEY,
            Biller(biller.billerId, biller.nameEN, biller.nameNY, listOf(), biller.icon),
        )
        val chargeBreakdownItem = response.confirmationValues.map {
            PaymentChargeBreakdownItem(
                it.fieldName,
                it.fieldValue,
            )
        }
        args.putParcelableArrayList(
            KEY_BREAKDOWN_LIST,
            ArrayList(chargeBreakdownItem),
        )
        args.putString(
            RECORD_ID,
            response.recordId.toString(),
        )
        args.putString(
            SELECTED_WALLET_ID,
            (selectedAccountId ?: throw IllegalArgumentException("Account Id cannot be null")).toString(),
        )
        navController.navigate(
            R.id.action_billersInputFragment_to_paymentDetailConfirmationFragment,
            args,
        )
    }

    fun getLocalizedString(en: String, ny: String, context: Context): String {
        return when (LocaleManager.getCurrentLocale(context)) {
            SupportedLocale.EN_US -> en
            SupportedLocale.NYANJA -> ny
        }
    }

    private fun handleRPCExceptionCase(uiError: UIError) {
        _currentState.postValue(BillersInputState.Error(uiError))
    }

    fun onPlanNotSelected(context: Context, billerUserInput: BillerUserInput) {
        _currentState.postValue(
            BillersInputState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitlePaymentsMissingFields),
                    context.getString(
                        R.string.alertMessagePaymentsMissingFields,
                    ),
                ),
            ),
        )
    }

    fun onInputNotEntered(context: Context, billerUserInput: BillerUserInput) {
        _currentState.postValue(
            BillersInputState.Error(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitlePaymentsMissingFields),
                    context.getString(
                        R.string.alertMessagePaymentsMissingFields,
                    ),
                ),
            ),
        )
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(BillersInputState.AcceptInput)
    }

    fun onAlertDialogDismissed() {
        _currentState.postValue(BillersInputState.AcceptInput)
    }

    fun onAuthenticationSuccess() {
        _currentState.postValue(BillersInputState.AcceptInput)
    }

    fun onAuthenticationFailed(activity: FragmentActivity?) {
        vMScopeOnIO.launch {
            logout(activity)
        }
    }
}

sealed class BillersInputState {
    object AcceptInput : BillersInputState()
    object Loading : BillersInputState()
    object RequestAuthentication : BillersInputState()
    data class Data(val data: CreateBillPaymentRequestRPC.Response) :
        BillersInputState()

    data class Error(val error: UIError) : BillersInputState()
}

const val ERROR_CODE_APP_AUTHENTICATION_FAILED: Int = 101
