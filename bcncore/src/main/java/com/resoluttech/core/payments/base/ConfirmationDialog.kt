package com.resoluttech.core.payments.base

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.payments.model.PaymentChargeBreakdownItem
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.views.BaseDialogFragment
import java.util.UUID

class ConfirmationDialog : BaseDialogFragment() {

    interface DialogListener {
        fun onProceed(transactionId: UUID)
        fun onDeclined()
    }

    private lateinit var breakdownRV: RecyclerView
    private lateinit var payableAmountTV: TextView
    private lateinit var payableAmountLayout: LinearLayout
    private lateinit var declineButton: MaterialButton
    private lateinit var proceedButton: MaterialButton

    private val transactionId: UUID by lazy { UUID.fromString(arguments?.getString(KEY_TRANSACTION_ID)) ?: throw IllegalArgumentException("TransactionId cannot be null.") }
    private val payableAmount: String? by lazy { arguments?.getString(KEY_PAYABLE_AMOUNT) }
    private val breakDownList: List<PaymentChargeBreakdownItem> by lazy {
        BundleUtils.getBundlesParcelableArrayList(requireArguments(), KEY_BREAKDOWN_LIST, PaymentChargeBreakdownItem::class.java) ?: throw IllegalStateException("Payment Breakdown details cannot be null.")
    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.payment_confirmation_dialog, container)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(rootView)
        isCancelable = false
        return rootView
    }

    private fun initViews(rootView: View) {
        breakdownRV = rootView.findViewById(R.id.breakdown_list)
        payableAmountTV = rootView.findViewById(R.id.payable_amount_tv)
        declineButton = rootView.findViewById(R.id.decline)
        proceedButton = rootView.findViewById(R.id.proceed)
        payableAmountLayout = rootView.findViewById(R.id.payable_amount)

        if (payableAmount != null) {
            payableAmountTV.text = payableAmount
            payableAmountLayout.visibility = View.VISIBLE
        } else {
            payableAmountLayout.visibility = View.GONE
        }

        declineButton.setOnClickListener {
            if (parentFragment != null) {
                (parentFragment as DialogListener).onDeclined()
                dismiss()
            }
        }

        proceedButton.setOnClickListener {
            if (parentFragment != null) {
                (parentFragment as DialogListener).onProceed(transactionId)
                dismiss()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        breakdownRV.adapter = BreakdownListAdapter(breakDownList)
    }

    companion object {

        const val TAG: String = "ConfirmationDialog"

        /*
            Using starter pattern.
            Thanks: https://blog.mindorks.com/learn-to-write-good-code-in-android-starter-pattern
         */
        fun newInstance(transactionId: UUID, payableAmount: String?, paymentChargeBreakdownItemList: ArrayList<PaymentChargeBreakdownItem>): ConfirmationDialog {
            val fragment = ConfirmationDialog()
            val args = Bundle()
            args.putString(KEY_TRANSACTION_ID, "$transactionId")
            if (payableAmount != null) {
                args.putString(KEY_PAYABLE_AMOUNT, payableAmount)
            }
            args.putParcelableArrayList(KEY_BREAKDOWN_LIST, paymentChargeBreakdownItemList)
            fragment.arguments = args
            return fragment
        }
    }
}

internal class BreakdownListAdapter(private val paymentChargeBreakdownItemList: List<PaymentChargeBreakdownItem>) : RecyclerView.Adapter<BreakdownListAdapter.BreakdownItemViewHolder>() {

    class BreakdownItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val description: TextView = itemView.findViewById(R.id.description_tv)
        val quantity: TextView = itemView.findViewById(R.id.quantity_tv)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BreakdownItemViewHolder {
        return BreakdownItemViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.item_breakdown, parent, false))
    }

    override fun onBindViewHolder(holder: BreakdownItemViewHolder, position: Int) {
        val data = paymentChargeBreakdownItemList[position]
        holder.description.text = data.description
        holder.quantity.text = data.quantity
    }

    override fun getItemCount(): Int {
        return paymentChargeBreakdownItemList.size
    }
}

private const val KEY_BREAKDOWN_LIST = "ConfirmationDialog.paymentChargeBreakdownList"
private const val KEY_TRANSACTION_ID = "ConfirmationDialog.transactionId"
private const val KEY_PAYABLE_AMOUNT = "ConfirmationDialog.payableAmount"
