package com.resoluttech.core.payments

import com.resoluttech.bcn.payments.BillerUserInputValue
import com.resoluttech.bcn.payments.ConfirmBillPaymentRequestRPC
import com.resoluttech.bcn.payments.CreateBillPaymentRequestRPC
import com.resoluttech.bcn.payments.GetBillersRPC
import com.resoluttech.bcn.types.CounterpartyId
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.rpc.LeoServerException
import com.suryadigital.leo.rpc.LeoUnauthenticatedException
import com.suryadigital.leo.rpc.LeoUnauthorizedException
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.util.UUID

class PaymentRepository {

    private val getBillersRPC: GetBillersRPC by KoinJavaComponent.inject(GetBillersRPC::class.java)
    private val createBillPaymentRequestRPC: CreateBillPaymentRequestRPC by KoinJavaComponent.inject(CreateBillPaymentRequestRPC::class.java)
    private val confirmBillPaymentRequestRPC: ConfirmBillPaymentRequestRPC by KoinJavaComponent.inject(ConfirmBillPaymentRequestRPC::class.java)
    private val paymentDataPersistor = PaymentDataPersistor()

    @Throws(
        LeoUnauthenticatedException::class,
        LeoUnauthorizedException::class,
        LeoUnsupportedClientException::class,
        LeoServerException::class,
        LeoInvalidResponseException::class,
    )
    suspend fun refreshBillerScreenData(): LeoRPCResult<GetBillersRPC.Response, GetBillersRPC.Error> {
        val rpcResult = getBillersRPC.execute(GetBillersRPC.Request)
        if (rpcResult is LeoRPCResult.LeoResponse) {
            if (rpcResult.response != getCachedBillerLayout()) {
                clearPaymentDataCache()
                cacheBillerLayout(rpcResult.response)
                Timber.tag(TAG).i("Biller data written to cache.")
            } else {
                Timber.tag(TAG).i("Cached payment data is same as data from server.")
            }
        } else {
            clearPaymentDataCache()
        }

        return rpcResult
    }

    fun getCachedBillerLayout(): GetBillersRPC.Response? {
        return paymentDataPersistor.getCachedBillerData()
    }

    private fun clearPaymentDataCache() {
        paymentDataPersistor.clearPaymentDataCache()
    }

    private fun cacheBillerLayout(paymentData: GetBillersRPC.Response) {
        paymentDataPersistor.writeBillerLayoutFile(paymentData)
    }

    suspend fun createBillerPaymentRequest(senderAccountId: UUID, billerId: String, userInput: List<BillerUserInputValue>): LeoRPCResult<CreateBillPaymentRequestRPC.Response, CreateBillPaymentRequestRPC.Error> {
        return createBillPaymentRequestRPC.execute(
            CreateBillPaymentRequestRPC.Request(
                senderAccountId,
                CounterpartyId(billerId),
                userInput,
            ),
        )
    }

    suspend fun confirmBillerPaymentRequest(recordId: UUID): LeoRPCResult<ConfirmBillPaymentRequestRPC.Response, ConfirmBillPaymentRequestRPC.Error> {
        return confirmBillPaymentRequestRPC.execute(
            ConfirmBillPaymentRequestRPC.Request(
                recordId,
            ),
        )
    }
}

private const val TAG = "PaymentRepository"
