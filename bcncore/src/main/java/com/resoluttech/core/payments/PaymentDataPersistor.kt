package com.resoluttech.core.payments

import android.content.Context
import com.resoluttech.bcn.payments.GetBillersRPC
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.io.File
import java.time.Instant

class PaymentDataPersistor {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private var paymentDataFile = getPaymentDataFile()
    private var billerDataFile = getBillerDataFile()
    private val json: Json by KoinJavaComponent.inject(Json::class.java)

    fun writeBillerLayoutFile(billerData: GetBillersRPC.Response) {
        validatePaymentDataObserver()
        val jsonString = Json.encodeToString(billerData)
        billerDataFile.writeBytes(jsonString.toByteArray())
    }

    fun clearPaymentDataCache() {
        context.applicationContext.deleteFile(PAYMENT_DATA_FILE_NAME)
    }

    fun clearBillerDataCache() {
        context.applicationContext.deleteFile(BILLER_DATA_FILE_NAME)
    }

    fun getCachedBillerData(): GetBillersRPC.Response? {
        validateBillerDataFile()
        return if (billerDataFile.exists()) {
            val paymentLayoutString = billerDataFile.readText()
            deserializeBillerLayout(paymentLayoutString)
        } else {
            Timber.tag(TAG).d("No cached biller file found")
            null
        }
    }

    private fun validatePaymentDataObserver() {
        if (!paymentDataFile.exists()) {
            paymentDataFile = getPaymentDataFile()
        }
    }

    private fun getPaymentDataFile(): File {
        val file = File(context.applicationContext.filesDir, PAYMENT_DATA_FILE_NAME)
        file.createNewFile()
        return file
    }

    private fun getBillerDataFile(): File {
        val file = File(context.applicationContext.filesDir, BILLER_DATA_FILE_NAME)
        file.createNewFile()
        return file
    }

    private fun validateBillerDataFile() {
        if (getBillerDataAgeMillis() > PAYMENT_DATA_CACHE_VALIDITY_MILLIS) {
            Timber.tag(TAG).w(
                """Cached payment data is older than
                | $PAYMENT_DATA_CACHE_VALIDITY_MILLIS milli seconds, it will be cleared.
                """.trimMargin(),
            )
            clearPaymentDataCache()
        } else {
            Timber.tag(TAG).d("Cached payment data is valid.")
        }
    }

    private fun getBillerDataAgeMillis(): Long {
        return Instant.now().toEpochMilli() - billerDataFile.lastModified()
    }

    private fun deserializeBillerLayout(billerDataString: String): GetBillersRPC.Response? {
        return try {
            val retrievedValue: GetBillersRPC.Response = json.decodeFromString<GetBillersRPC.Response>(billerDataString)
            Timber.tag(TAG).d("Biller layout Value updated")
            retrievedValue
        } catch (serializationException: SerializationException) {
            Timber.tag(TAG).d("Unable to serialize the object to PaymentLayout")
            null
        }
    }
}

private const val TAG = "PaymentDataPersistor"
private const val PAYMENT_DATA_FILE_NAME = "paymentData.json"
private const val BILLER_DATA_FILE_NAME = "billerData.json"
private const val PAYMENT_DATA_CACHE_VALIDITY_MILLIS = 2592000000 * 3 // 3 months.
