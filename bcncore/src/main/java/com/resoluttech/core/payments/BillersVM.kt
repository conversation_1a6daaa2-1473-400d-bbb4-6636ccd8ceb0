package com.resoluttech.core.payments

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.resoluttech.bcn.payments.Biller
import com.resoluttech.bcn.payments.GetBillersRPC
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.executeRPC
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class BillersVM : ViewModel() {

    private val _currentState = MutableLiveData<BillersScreenState>(BillersScreenState.Loading)
    val currentState: LiveData<BillersScreenState> get() = _currentState
    private val paymentRepository = PaymentRepository()
    private var vMScopeOnIO = viewModelScope + Dispatchers.IO

    fun getBillerScreenData(context: Context) {
        vMScopeOnIO.launch {
            val cachedBillerData = paymentRepository.getCachedBillerLayout()
            if (cachedBillerData != null) {
                postPaymentData(cachedBillerData)
            }
            refreshBillerData(context, cachedBillerData)
        }
    }

    private suspend fun refreshBillerData(
        context: Context,
        cachedBillerData: GetBillersRPC.Response?,
    ) {
        if (cachedBillerData == null) {
            _currentState.postValue(BillersScreenState.Loading)
        }
        if (_currentState.value is BillersScreenState.Data && (_currentState.value as BillersScreenState.Data).data == cachedBillerData?.billers) {
            return
        }
        executeRPC(
            context,
            rpcBlock = {
                when (val result = paymentRepository.refreshBillerScreenData()) {
                    is LeoRPCResult.LeoResponse -> {
                        if (result.response != cachedBillerData) {
                            postPaymentData(result.response)
                        } else {
                            // If response and cached data is same then we don't want to refresh the
                            // list
                        }
                    }

                    is LeoRPCResult.LeoError -> {
                        // No errors to handle.
                    }
                }
            },
            handleException = {
                _currentState.postValue(BillersScreenState.Error(it, cachedBillerData))
            },
        )
    }

    fun onInlineErrorDismissed() {
        val cachedBillerData = paymentRepository.getCachedBillerLayout()
        if (cachedBillerData != null) {
            postPaymentData(cachedBillerData)
        }
    }

    private fun postPaymentData(response: GetBillersRPC.Response) {
        if (response.billers.isNotEmpty()) {
            _currentState.postValue(
                BillersScreenState.Data(
                    response.billers,
                ),
            )
        } else {
            _currentState.postValue(
                BillersScreenState.NoData,
            )
        }
    }

    fun onSnackBarDismissed(data: GetBillersRPC.Response?) {
        if (data != null) {
            postPaymentData(data)
        } else {
            _currentState.postValue(BillersScreenState.AcceptInput)
        }
    }

    fun onDialogDismissed() {
        _currentState.postValue(BillersScreenState.AcceptInput)
    }
}

sealed class BillersScreenState {
    object Loading : BillersScreenState()
    object AcceptInput : BillersScreenState()
    object NoData : BillersScreenState()
    data class Data(val data: List<Biller>) : BillersScreenState()
    data class Error(val error: UIError, val data: GetBillersRPC.Response?) : BillersScreenState()
}
