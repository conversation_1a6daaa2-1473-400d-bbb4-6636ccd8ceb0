package com.resoluttech.core.payments.model

import android.os.Parcel
import android.os.Parcelable

/**
 * This class holds information about payment charges. This class can be used to to store quantity
 * with a key (description), which can be shown to user.
 */
data class PaymentChargeBreakdownItem(val description: String, val quantity: String) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: throw IllegalArgumentException("Description should not be null."),
        parcel.readString() ?: throw IllegalArgumentException("Quantity should not be null."),
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(description)
        parcel.writeString(quantity)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<PaymentChargeBreakdownItem> {
        override fun createFromParcel(parcel: Parcel): PaymentChargeBreakdownItem {
            return PaymentChargeBreakdownItem(parcel)
        }

        override fun newArray(size: Int): Array<PaymentChargeBreakdownItem?> {
            return arrayOfNulls(size)
        }
    }
}
