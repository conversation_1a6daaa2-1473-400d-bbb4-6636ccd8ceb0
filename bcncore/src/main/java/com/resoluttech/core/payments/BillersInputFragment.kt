package com.resoluttech.core.payments

import android.graphics.Typeface
import android.os.Bundle
import android.text.InputType
import android.util.TypedValue
import android.view.ContextThemeWrapper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.payments.BillerUserInputValue
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog.ActionListener
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.BCNAppAppAuthenticationProvider
import com.resoluttech.core.utils.getDrawable
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getProcessedValue
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.SingleLineCharacterLimitTextWatcher
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_NAME
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import java.text.DecimalFormatSymbols
import java.util.UUID

class BillersInputFragment :
    BaseFragment(),
    AmountEditText.ErrorListener,
    ActionListener,
    AppAuthenticationStatus,
    BaseFragment.NetworkListener {

    private lateinit var rootLayout: LinearLayout
    private lateinit var biller: Biller
    private lateinit var nextButton: MaterialButton

    private val billersInputFragmentVM: BillersInputFragmentVM by navGraphViewModels(R.id.billers_nav)
    private val dropdownItemMap: MutableMap<Int, List<BillerPlanDropdownAdapter.BillerPlanDropdownItem>> =
        HashMap()
    private val billerUserInputValue = mutableListOf<BillerUserInputValue>()
    private var rootView: View? = null

    private lateinit var itemWalletSelector: ConstraintLayout
    private lateinit var accountName: TextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        if (rootView == null) {
            rootView = inflater.inflate(R.layout.fragment_billers_input, container, false).apply {
                if (arguments != null) {
                    biller = BundleUtils.getBundlesParcelable(
                        requireArguments(),
                        BILLER_DETAIL_KEY,
                        Biller::class.java,
                    ) ?: throw IllegalStateException("Biller details cannot be null.")
                } else {
                    throw IllegalStateException("Biller details cannot be null.")
                }
                initViews(this)
                setupAuthenticationStatusListener()
            }
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(
            billersInputFragmentVM.getLocalizedString(
                biller.nameEN,
                biller.nameNY ?: biller.nameEN,
                requireContext(),
            ),
        )
        setupWalletSelectorListeners()
        networkListenerCallback = this
        billersInputFragmentVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    override fun onDestroy() {
        super.onDestroy()
        BCNAppAppAuthenticationProvider.setListener(null)
    }

    private fun setupAuthenticationStatusListener() {
        BCNAppAppAuthenticationProvider.setListener(this)
    }

    private fun reactToState(state: BillersInputState) {
        when (state) {
            is BillersInputState.AcceptInput -> {
                dismissProgressDialog()
            }
            is BillersInputState.Loading -> {
                handleLoadingState()
            }
            is BillersInputState.Error -> {
                handleErrorState(state.error)
            }
            is BillersInputState.Data -> {
                billersInputFragmentVM.onDataAvailable(
                    biller,
                    findNavController(),
                    state.data,
                )
            }
            is BillersInputState.RequestAuthentication -> {
                handleLoadingState()
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                billersInputFragmentVM::onInlineErrorDismissed,
            )
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.BILLER_ERROR_DIALOG,
                    forceOutUserDestination = ForceOutUserDestination.PAYMENT,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun initViews(view: View) {
        rootLayout = view.findViewById(R.id.root_layout)
        val billerPayload = biller.userInputPayload
        addMaterialButton(billerPayload, view)
        billerPayload.forEachIndexed { index, billerUserInput ->
            when (val valueType = billerUserInput.valueType) {
                is ValueType.OptionType -> {
                    addTextView(
                        billersInputFragmentVM.getLocalizedString(
                            billerUserInput.nameEN,
                            billerUserInput.nameNY ?: billerUserInput.nameEN,
                            requireContext(),
                        ),
                    )
                    addDropdown(valueType, index)
                }
                else -> {
                    addTextInputLayout(
                        billersInputFragmentVM.getLocalizedString(
                            billerUserInput.nameEN,
                            billerUserInput.nameNY ?: billerUserInput.nameEN,
                            requireContext(),
                        ),
                        billerUserInput.valueType,
                        index,
                    )
                }
            }
        }
        addAccountDropdown(view)
    }

    private fun addTextInputLayout(hint: String, valueType: ValueType, index: Int) {
        val textInputLayout = TextInputLayout(requireContext(), null, R.style.TextInputLayoutStyle)
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        layoutParams.setMargins(
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_12dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
        )
        textInputLayout.id = index
        val textInputLayoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        textInputLayout.layoutParams = textInputLayoutParams
        textInputLayout.hint = hint
        textInputLayout.defaultHintTextColor =
            requireContext().getColorStateList(R.color.text_input_activation_color)
        textInputLayout.hintTextColor =
            requireContext().getColorStateList(R.color.text_input_activation_color)
        when (valueType) {
            is ValueType.AmountType -> {
                val editText = AmountEditText(
                    ContextThemeWrapper(
                        textInputLayout.context,
                        R.style.BillersTextInputEditTextCursorTheme,
                    ),
                )
                editText.setErrorListener(this)
                textInputLayout.suffixText = UserSharedPreference.getUserPrimaryCurrency()
                textInputLayout.setSuffixTextColor(requireContext().getColorStateList(R.color.titleDescriptionTextColor))
                setupEditText(editText)
                textInputLayout.addView(editText)
            }
            is ValueType.LongType -> {
                val editText = TextInputEditText(
                    ContextThemeWrapper(
                        textInputLayout.context,
                        R.style.BillersTextInputEditTextCursorTheme,
                    ),
                )
                editText.inputType = InputType.TYPE_CLASS_NUMBER
                setupEditText(editText)
                editText.addTextChangedListener(
                    SingleLineCharacterLimitTextWatcher(
                        textInputLayout = textInputLayout,
                        maxLength = Config.MAX_BILLER_INPUT_LENGTH,
                    ),
                )
                textInputLayout.addView(editText)
            }
            else -> {
                val editText = TextInputEditText(
                    ContextThemeWrapper(
                        textInputLayout.context,
                        R.style.BillersTextInputEditTextCursorTheme,
                    ),
                )
                setupEditText(editText)
                editText.addTextChangedListener(
                    SingleLineCharacterLimitTextWatcher(
                        textInputLayout = textInputLayout,
                        maxLength = Config.MAX_BILLER_INPUT_LENGTH,
                    ),
                )
                textInputLayout.addView(editText)
            }
        }
        rootLayout.addView(textInputLayout, layoutParams)
    }

    private fun addMaterialButton(fields: List<BillerUserInput>, view: View) {
        nextButton = view.findViewById(R.id.ok_button)
        nextButton.setOnClickListener {
            handleButtonOnClick(fields)
        }
    }

    private fun handleButtonOnClick(fields: List<BillerUserInput>) {
        billerUserInputValue.clear()
        fields.forEachIndexed { index, billerUserInput ->
            when (billerUserInput.valueType) {
                is ValueType.OptionType -> {
                    val dropdown = rootLayout.findViewById<TextDropdown>(index)
                    when (val item = dropdownItemMap[index]!![dropdown.selectedItemPosition]) {
                        is BillerPlanDropdownAdapter.BillerPlanDropdownItem.Header -> {
                            billersInputFragmentVM.onPlanNotSelected(
                                requireContext(),
                                billerUserInput,
                            )
                            return
                        }
                        is BillerPlanDropdownAdapter.BillerPlanDropdownItem.Plan -> {
                            billerUserInputValue.add(
                                BillerUserInputValue(
                                    billerUserInput.id,
                                    BillerUserInputValue.FieldValue.Option(
                                        id = UUID.fromString(item.id),
                                    ),
                                ),
                            )
                        }
                    }
                }
                is ValueType.AmountType -> {
                    rootLayout.findViewById<TextInputLayout>(index).editText?.text.toString().let {
                        if (it.isNotEmpty()) {
                            billerUserInputValue.add(
                                BillerUserInputValue(
                                    billerUserInput.id,
                                    BillerUserInputValue.FieldValue.Input(
                                        inputValue = getFormattedAmountText(it).toDouble().getProcessedValue()
                                            .toString(),
                                    ),
                                ),
                            )
                        } else {
                            billersInputFragmentVM.onInputNotEntered(
                                requireContext(),
                                billerUserInput,
                            )
                            return
                        }
                    }
                }
                else -> {
                    rootLayout.findViewById<TextInputLayout>(index).editText?.text.toString().let {
                        if (it.isNotEmpty()) {
                            billerUserInputValue.add(
                                BillerUserInputValue(
                                    billerUserInput.id,
                                    BillerUserInputValue.FieldValue.Input(
                                        inputValue = it,
                                    ),
                                ),
                            )
                        } else {
                            billersInputFragmentVM.onInputNotEntered(
                                requireContext(),
                                billerUserInput,
                            )
                            return
                        }
                    }
                }
            }
        }
        billersInputFragmentVM.onProgressButtonClicked(
            findNavController(),
        )
    }

    private fun addTextView(label: String) {
        val textView = TextView(requireContext())
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        layoutParams.setMargins(
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_16dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
        )
        textView.text = getString(R.string.dropdownLabel, label)
        textView.typeface = Typeface.create(textView.typeface, Typeface.BOLD)
        textView.setTextColor(requireContext().getColor(R.color.sectionHeadingColor))
        textView.layoutParams = layoutParams
        rootLayout.addView(textView)
    }

    private fun addDropdown(
        optionTypeValue: ValueType.OptionType,
        index: Int,
    ) {
        val dropdown = TextDropdown(requireContext())
        dropdown.id = index
        val layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
        )
        layoutParams.setMargins(
            resources.getDimensionPixelSize(R.dimen.dimen_1dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_1dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
        )
        dropdown.layoutParams = layoutParams
        dropdown.setPopupBackgroundDrawable(getDrawable(R.drawable.bg_popup_menu))
        dropdown.setBackgroundResource(R.drawable.bg_server_spinner)
        dropdown.dropDownVerticalOffset = resources.getDimensionPixelSize(R.dimen.dimen_52dp)
        dropdown.dropDownWidth = dropdown.layoutParams.width
        dropdown.minimumHeight = resources.getDimensionPixelSize(R.dimen.dimen_40dp)
        val planDropdownItem = mutableListOf<BillerPlanDropdownAdapter.BillerPlanDropdownItem>()
        planDropdownItem.add(
            BillerPlanDropdownAdapter.BillerPlanDropdownItem.Header(
                getString(R.string.paymentsOptionsTitle),
            ),
        )
        optionTypeValue.options.forEach { option ->
            planDropdownItem.add(
                BillerPlanDropdownAdapter.BillerPlanDropdownItem.Plan(
                    billersInputFragmentVM.getLocalizedString(
                        option.nameEN,
                        option.nameNY ?: option.nameEN,
                        requireContext(),
                    ),
                    option.amount,
                    option.optionId.toString(),
                ),
            )
        }
        dropdownItemMap[index] = planDropdownItem
        dropdown.setAdapter(
            BillerPlanDropdownAdapter(
                planDropdownItem,
                requireContext(),
            ),
        )
        dropdown.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(parent: AdapterView<*>?) {}

            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
            }
        }
        val dropdownUnderline = View(requireContext())
        val viewLayoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            resources.getDimensionPixelSize(R.dimen.dimen_1dp),
        )
        viewLayoutParams.setMargins(
            resources.getDimensionPixelSize(R.dimen.dimen_4dp),
            resources.getDimensionPixelSize(R.dimen.dimen_0dp),
            resources.getDimensionPixelSize(R.dimen.dimen_4dp),
            resources.getDimensionPixelSize(R.dimen.dimen_12dp),
        )
        dropdownUnderline.layoutParams = viewLayoutParams
        dropdownUnderline.setBackgroundColor(
            ContextCompat.getColor(
                requireContext(),
                R.color.editTextDivider,
            ),
        )
        rootLayout.addView(dropdown)
        rootLayout.addView(dropdownUnderline)
    }

    private fun addAccountDropdown(view: View) {
        itemWalletSelector = view.findViewById(R.id.item_select_wallet)
        accountName = view.findViewById(R.id.account_name_tv)

        val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            ?: throw IllegalStateException("Selected account cannot be null")
        accountName.text = selectedAccount.name
        billersInputFragmentVM.selectedAccountId = UUID.fromString(selectedAccount.id)
        itemWalletSelector.setOnClickListener {
            if (billersInputFragmentVM.selectedAccountId != null) {
                findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}&selectedAccountId=${billersInputFragmentVM.selectedAccountId}".toUri())
            } else {
                findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}".toUri())
            }
        }
    }

    private fun setupWalletSelectorListeners() {
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            billersInputFragmentVM.selectedAccountId = it
        }
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>(
            KEY_SELECTED_ACCOUNT_NAME,
        )?.observe(
            viewLifecycleOwner,
        ) {
            accountName.text = it
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        billersInputFragmentVM.onAlertDialogDismissed()
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                leoServerExceptionHandler(findNavController())
            }
            ERROR_CODE_APP_AUTHENTICATION_FAILED -> {
                billersInputFragmentVM.onAuthenticationFailed(activity)
            }
            else -> {
                billersInputFragmentVM.onAlertDialogDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        billersInputFragmentVM.onAlertDialogDismissed()
    }

    override fun onAppAuthenticationSuccessful(navController: NavController) {
        if (billersInputFragmentVM.currentState.value is BillersInputState.RequestAuthentication) {
            billersInputFragmentVM.onUserAuthenticationSuccess(
                billerUserInputValue,
                biller.billerId,
                requireContext(),
            )
        } else {
            billersInputFragmentVM.onAuthenticationSuccess()
        }
    }

    override fun onAppAuthenticationFailed(navController: NavController) {
        billersInputFragmentVM.onUserAuthenticationFailed(requireContext())
    }

    override fun onAppAuthenticationCancelled(navController: NavController) {
        billersInputFragmentVM.onUserAuthenticationCancelled()
    }

    override fun onNetworkAvailable() {
        billersInputFragmentVM.onInlineErrorDismissed()
    }

    override fun onAmountInput(isValid: Boolean) {
        /*** Since the Next Button is always enabled in Payments Screen we don't need to verify if
         * Amount is valid or not and hence there is nothing to handle in this function.
         */
    }

    /**
     * It removes the grouping separator(,) from the entered input and returns cleaned string that
     * can be converted to long or double.
     * */
    private fun getFormattedAmountText(text: String): String {
        val groupingSeparator = DecimalFormatSymbols.getInstance().groupingSeparator
        return text.replace("$groupingSeparator", requireContext().getEmptyString()).trim()
    }

    private fun setupEditText(editText: EditText) {
        editText.setPadding(
            resources.getDimensionPixelSize(R.dimen.dimen_8dp),
            resources.getDimensionPixelSize(R.dimen.dimen_8dp),
            resources.getDimensionPixelSize(R.dimen.dimen_8dp),
            resources.getDimensionPixelSize(R.dimen.dimen_20dp),
        )
        editText.setTextSize(
            TypedValue.COMPLEX_UNIT_PX,
            resources.getDimensionPixelSize(R.dimen.text_16sp).toFloat(),
        )
        editText.typeface = Typeface.create(editText.typeface, Typeface.NORMAL)
    }
}
