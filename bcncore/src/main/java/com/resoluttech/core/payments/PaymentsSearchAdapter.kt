package com.resoluttech.core.payments

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.assets.Item
import com.resoluttech.bcn.assets.ItemAction
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.localisedText
import com.suryadigital.leo.libui.contactview.ContactIconView

class PaymentSearchViewHolder(view: View) : RecyclerView.ViewHolder(view) {

    private val iconView: ContactIconView = view.findViewById(R.id.item_icon)
    private val itemNameTV: TextView = view.findViewById(R.id.item_name)

    fun bind(
        context: Context,
        item: Item,
        listener: PaymentsSearchAdapter.ItemClickListener,
    ) {
        if (item.title != null) {
            itemNameTV.text = item.title!!.localisedText(LocaleManager.getCurrentLocale(context))
        }

        item.imageURL?.apply {
            iconView.imageView.loadImage(
                getUri(context),
            )
        }

        itemView.setOnClickListener {
            listener.onClick(item.action)
        }
    }
}

class PaymentsSearchAdapter(
    private val context: Context,
    private val listener: ItemClickListener,
) :
    RecyclerView.Adapter<PaymentSearchViewHolder>() {

    private val items: MutableList<Item> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PaymentSearchViewHolder {
        val view =
            LayoutInflater.from(context).inflate(R.layout.item_container_search, parent, false)
        return PaymentSearchViewHolder(view)
    }

    override fun onBindViewHolder(holder: PaymentSearchViewHolder, position: Int) {
        holder.bind(context, items[position], listener)
    }

    override fun getItemCount(): Int = items.size

    @SuppressLint("NotifyDataSetChanged")
    fun updateItems(items: List<Item>) {
        this.items.clear()
        this.items.addAll(items)
        notifyDataSetChanged()
    }

    interface ItemClickListener {
        fun onClick(action: ItemAction)
    }
}
