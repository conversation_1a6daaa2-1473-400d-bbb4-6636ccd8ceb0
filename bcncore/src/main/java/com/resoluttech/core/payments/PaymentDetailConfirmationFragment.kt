package com.resoluttech.core.payments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcncore.R
import com.resoluttech.core.payments.base.BreakdownListAdapter
import com.resoluttech.core.payments.model.PaymentChargeBreakdownItem
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import java.time.Instant
import java.util.UUID

class PaymentDetailConfirmationFragment : BaseFragment(), AlertDialog.ActionListener {

    private lateinit var breakdownRV: RecyclerView
    private lateinit var declineButton: MaterialButton
    private lateinit var proceedButton: MaterialButton
    private lateinit var biller: Biller
    private val paymentDetailConfirmationVM: PaymentDetailConfirmationVM by navGraphViewModels(R.id.billers_nav)
    private val breakDownList: List<PaymentChargeBreakdownItem> by lazy {
        BundleUtils.getBundlesParcelableArrayList(
            requireArguments(),
            KEY_BREAKDOWN_LIST,
            PaymentChargeBreakdownItem::class.java,
        ) ?: throw IllegalArgumentException("Breakdown list should not be null")
    }
    private val recordId: String by lazy {
        arguments?.getString(
            RECORD_ID,
        ) ?: throw IllegalArgumentException("Record Id cannot be null.")
    }
    private val selectedWalletId: String by lazy {
        arguments?.getString(
            SELECTED_WALLET_ID,
        ) ?: throw IllegalArgumentException("Record Id cannot be null.")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_payment_detail_confirmation, container, false)
        biller = BundleUtils.getBundlesParcelable(
            requireArguments(),
            BILLER_DETAIL_KEY,
            Biller::class.java,
        ) ?: throw IllegalStateException("Biller details cannot be null.")
        initViews(view)
        setupDeclineButton()
        setupProceedButton()
        setupRecyclerView()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.paymentDetailsTitle))
        paymentDetailConfirmationVM.currentState.observe(
            viewLifecycleOwner,
            Observer(::reactToState),
        )
    }

    private fun setupRecyclerView() {
        breakdownRV.adapter = BreakdownListAdapter(breakDownList)
    }

    private fun reactToState(state: PaymentDetailConfirmationState) {
        when (state) {
            is PaymentDetailConfirmationState.Loading -> {
                handleLoadingState()
            }
            is PaymentDetailConfirmationState.Error -> {
                handleErrorState(state.error)
            }
            is PaymentDetailConfirmationState.SuccessfulTransaction -> {
                handleSuccessfulTransaction(
                    state.transactionId,
                    state.amount,
                    state.transactionSucceededAt,
                    state.transactionDescription,
                    state.transactionStatusItemDetail,
                )
            }
            PaymentDetailConfirmationState.Data -> {
                dismissProgressDialog()
            }
        }
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleSuccessfulTransaction(
        transactionId: String,
        amount: Amount,
        transactionSucceededAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        setImmediateBackstackDestinationId(R.id.billersFragment)
        paymentDetailConfirmationVM.handleSuccessfulTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionSucceededAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
    }

    private fun handleErrorState(uiError: UIError) {
        dismissProgressDialog()
        when (uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                uiError.errorMessage,
                requireView(),
                paymentDetailConfirmationVM::onInlineErrorDismissed,
            )
            ErrorType.DIALOG -> {
                showErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: DialogCodes.BILLER_ERROR_CODE,
                    forceOutUserDestination = ForceOutUserDestination.PAYMENT,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun initViews(view: View) {
        breakdownRV = view.findViewById(R.id.breakdown_list)
        declineButton = view.findViewById(R.id.decline_button)
        proceedButton = view.findViewById(R.id.proceed_button)
    }

    private fun setupDeclineButton() {
        declineButton.setOnClickListener {
            paymentDetailConfirmationVM.onDeclineClicked(findNavController())
        }
    }

    private fun setupProceedButton() {
        proceedButton.setOnClickListener {
            paymentDetailConfirmationVM.onProceedClicked(
                context = requireContext(),
                recordId = UUID.fromString(recordId),
                selectedWalletId = UUID.fromString(selectedWalletId),
            )
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        if (dialogId == DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
            leoServerExceptionHandler(findNavController())
        } else {
            paymentDetailConfirmationVM.onAlertDialogDismissed(findNavController())
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        paymentDetailConfirmationVM.onAlertDialogDismissed(findNavController())
    }
}

internal const val KEY_BREAKDOWN_LIST = "paymentChargeBreakdownList"
internal const val RECORD_ID = "recordId"
internal const val SELECTED_WALLET_ID = "selectedWalletId"
