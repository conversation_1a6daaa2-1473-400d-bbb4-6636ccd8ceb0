package com.resoluttech.core.payments

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.payments.ConfirmBillPaymentRequestRPC
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.PaymentsRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatus
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.time.Instant
import java.util.UUID

class PaymentDetailConfirmationVM : ViewModel() {

    private val _currentState =
        MutableLiveData<PaymentDetailConfirmationState>(PaymentDetailConfirmationState.Data)
    val currentState: LiveData<PaymentDetailConfirmationState> = _currentState
    private var vMScopeOnIO = viewModelScope + Dispatchers.IO
    private var paymentRepository = PaymentRepository()

    fun onProceedClicked(
        context: Context,
        recordId: UUID,
        selectedWalletId: UUID,
    ) {
        _currentState.postValue(PaymentDetailConfirmationState.Loading)
        vMScopeOnIO.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val result = paymentRepository.confirmBillerPaymentRequest(recordId)) {
                        is LeoRPCResult.LeoResponse -> {
                            handleResponse(result.response, recordId, context)
                        }
                        is LeoRPCResult.LeoError -> {
                            PaymentsRPCExceptionHandler.confirmBillerPaymentRequestRPCErrorMessage(
                                context = context,
                                error = result.error,
                                walletName = SelectedAccountHelper.getAccountFromId(
                                    selectedWalletId,
                                ).name,
                            ).apply {
                                handleRPCExceptionCase(this)
                            }
                        }
                    }
                },
                handleException = ::handleRPCExceptionCase,
            )
        }
    }

    private fun handleRPCExceptionCase(uiError: UIError) {
        _currentState.postValue(PaymentDetailConfirmationState.Error(uiError))
    }

    fun onInlineErrorDismissed() {
        _currentState.postValue(PaymentDetailConfirmationState.Data)
    }

    fun onAlertDialogDismissed(navController: NavController) {
        _currentState.postValue(PaymentDetailConfirmationState.Data)
        navController.popBackStack(R.id.billers_nav, false)
    }

    private fun handleResponse(
        response: ConfirmBillPaymentRequestRPC.Response,
        recordId: UUID,
        context: Context,
    ) {
        _currentState.postValue(
            PaymentDetailConfirmationState.SuccessfulTransaction(
                "$recordId",
                response.amount,
                response.succeededAt,
                response.transactionDetail.description?.let {
                    context.getLocalizedString(
                        it.en,
                        it.ny,
                    )
                },
                response.transactionDetail.itemDetail,
            ),
        )
    }

    fun handleSuccessfulTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        succeededAtTimestamp: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.SuccessfulTransaction(
            SuccessfulTransactionDetails(
                context.getString(R.string.transactionStatusSuccessLabel),
                getFormattedDate(context, succeededAtTimestamp),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )

        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            data,
        )
        navController.navigate(R.id.transaction_status_nav, args)
        _currentState.postValue(PaymentDetailConfirmationState.Data)
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onDeclineClicked(navController: NavController) {
        navController.popBackStack()
    }
}

sealed class PaymentDetailConfirmationState {
    object Data : PaymentDetailConfirmationState()
    object Loading : PaymentDetailConfirmationState()

    data class Error(val error: UIError) : PaymentDetailConfirmationState()
    data class SuccessfulTransaction(
        val transactionId: String,
        val amount: Amount,
        val transactionSucceededAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) : PaymentDetailConfirmationState()
}
