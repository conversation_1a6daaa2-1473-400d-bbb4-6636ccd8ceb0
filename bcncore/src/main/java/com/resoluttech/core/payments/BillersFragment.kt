package com.resoluttech.core.payments

import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import coil.load
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcn.payments.Biller
import com.resoluttech.bcn.payments.BillerUserInputField
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID
import com.resoluttech.core.utils.getEmptyString
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.BaseFragment
import kotlinx.parcelize.Parcelize
import java.util.UUID

class BillersFragment :
    BaseFragment(),
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener,
    BillersListAdapter.BillersListListListener {

    private val billersVM = ViewModelProvider.NewInstanceFactory().create(
        BillersVM::class.java,
    )
    private lateinit var progressBar: ProgressBar
    private lateinit var errorLayout: LinearLayout
    private lateinit var errorTitle: TextView
    private lateinit var errorMessage: TextView
    private lateinit var errorIV: ImageView
    private lateinit var retryButton: MaterialButton
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout
    private lateinit var billersListRV: RecyclerView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_billers, container, false)
        initViews(view)
        setPTR()
        return view
    }

    private fun initViews(view: View) {
        progressBar = view.findViewById(R.id.progress_bar)
        errorLayout = view.findViewById(R.id.error_view)
        errorTitle = view.findViewById(R.id.fullScreenErrorTitle)
        errorMessage = view.findViewById(R.id.fullScreenErrorMessage)
        errorIV = view.findViewById(R.id.error_icon)
        retryButton = view.findViewById(R.id.retry_button)
        swipeToRefreshLayout = view.findViewById(R.id.swipe_to_refresh)
        billersListRV =
            view.findViewById(R.id.biller_rv)

        retryButton.setOnClickListener {
            billersVM.getBillerScreenData(requireContext())
        }
    }

    private fun setPTR() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            billersVM.getBillerScreenData(requireContext())
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.homeScreenPaymentsTitle), isBackArrowShown = false)
        networkListenerCallback = this
        billersVM.getBillerScreenData(requireContext())
        billersVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun reactToState(state: BillersScreenState) {
        when (state) {
            is BillersScreenState.Data -> {
                handleBillersScreenData(state.data)
            }

            is BillersScreenState.Loading -> {
                handleBillersLoading()
            }

            is BillersScreenState.Error -> {
                handleErrorState(state)
            }

            BillersScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            BillersScreenState.NoData -> {
                handleNoDataState()
            }
        }
    }

    private fun handleAcceptInputState() {
        swipeToRefreshLayout.isRefreshing = false
        progressBar.visibility = View.GONE
    }

    private fun handleNoDataState() {
        progressBar.visibility = View.GONE
        billersListRV.visibility = View.GONE
        errorLayout.visibility = View.VISIBLE
        retryButton.visibility = View.VISIBLE
        swipeToRefreshLayout.isRefreshing = false
        swipeToRefreshLayout.isEnabled = true
        retryButton.visibility = View.GONE

        errorTitle.visibility = View.VISIBLE
        errorIV.load(ContextCompat.getDrawable(requireContext(), R.drawable.ic_no_statements))
        errorTitle.text = getString(R.string.selectDestinationNoProviders)
        errorMessage.text = requireContext().getEmptyString()
    }

    private fun handleErrorState(state: BillersScreenState.Error) {
        if (state.error.errorCode != LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID) {
            handleNonLeoServerException(state)
        }
        when (state.error.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(state.error.errorMessage, requireView()) {
                    billersVM.onSnackBarDismissed(state.data)
                }
            }

            ErrorType.DIALOG -> {
                showErrorDialog(
                    state.error.errorTitle,
                    state.error.errorMessage,
                    state.error.errorCode ?: DialogCodes.BILLER_ERROR_DIALOG,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleNonLeoServerException(state: BillersScreenState.Error) {
        progressBar.visibility = View.GONE
        errorLayout.visibility = View.VISIBLE
        retryButton.visibility = View.VISIBLE
        billersListRV.visibility = View.GONE
        if (state.data == null) {
            if (state.error.errorTitle.isBlank()) {
                errorTitle.visibility = View.GONE
            } else {
                errorTitle.visibility = View.VISIBLE
                errorTitle.text = state.error.errorTitle
            }
            errorMessage.text = state.error.errorMessage
            errorIV.load(ContextCompat.getDrawable(requireContext(), R.drawable.ic_no_internet))
            swipeToRefreshLayout.isRefreshing = false
            swipeToRefreshLayout.isEnabled = false
        } else {
            handleBillersScreenData(state.data.billers)
            swipeToRefreshLayout.isRefreshing = false
            swipeToRefreshLayout.isEnabled = true
        }
    }

    private fun handleBillersScreenData(data: List<Biller>) {
        progressBar.visibility = View.GONE
        billersListRV.visibility = View.VISIBLE
        errorLayout.visibility = View.GONE
        swipeToRefreshLayout.isRefreshing = false
        swipeToRefreshLayout.isEnabled = true
        billersListRV.layoutManager = GridLayoutManager(requireContext(), 4)
        billersListRV.adapter = BillersListAdapter(this, data)
    }

    private fun handleBillersLoading() {
        progressBar.visibility = View.VISIBLE
        billersListRV.visibility = View.GONE
        errorLayout.visibility = View.GONE
        swipeToRefreshLayout.isRefreshing = false
        swipeToRefreshLayout.isEnabled = false
    }

    private fun getValueType(valueType: BillerUserInputField.ValueType): ValueType {
        return when (valueType) {
            is BillerUserInputField.ValueType.LongType -> ValueType.LongType
            is BillerUserInputField.ValueType.StringType -> ValueType.StringType
            is BillerUserInputField.ValueType.AmountType -> ValueType.AmountType
            is BillerUserInputField.ValueType.OptionType -> ValueType.OptionType(
                valueType.options.map {
                    BillerUserInputOption(
                        it.optionId,
                        it.label.en,
                        it.label.ny,
                        it.amount.amount,
                        it.amount.currency.currencyCode,
                    )
                },
            )
        }
    }

    override fun onNetworkAvailable() {
        billersVM.getBillerScreenData(requireContext())
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> leoServerExceptionHandler(
                findNavController(),
            )

            else -> billersVM.onDialogDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        billersVM.onDialogDismissed()
    }

    override fun onBillerSelected(biller: Biller) {
        val itemBillerInput = biller.userInputFields.map {
            BillerUserInput(
                it.fieldId,
                it.label.en,
                it.label.ny,
                getValueType(it.valueType),
            )
        }
        val args = Bundle()
        args.putParcelable(
            BILLER_DETAIL_KEY,
            Biller(
                biller.billerId.id,
                biller.displayName.en,
                biller.displayName.ny,
                itemBillerInput,
                biller.icon.getUri(requireContext()),
            ),
        )
        findNavController().navigateSafe(
            R.id.action_billerFragment_to_billerInputFragment,
            args,
        )
    }
}

const val BILLER_DETAIL_KEY: String = "BILLER_DETAIL"

@Parcelize
data class Biller(
    val billerId: String,
    val nameEN: String,
    val nameNY: String?,
    val userInputPayload: List<BillerUserInput>,
    val icon: Uri,
) : Parcelable

@Parcelize
data class BillerUserInput(
    val id: UUID,
    val nameEN: String,
    val nameNY: String?,
    val valueType: ValueType,
) : Parcelable

sealed class ValueType : Parcelable {
    @Parcelize
    object StringType : ValueType()

    @Parcelize
    object AmountType : ValueType()

    @Parcelize
    object LongType : ValueType()

    @Parcelize
    data class OptionType(val options: List<BillerUserInputOption>) : ValueType()
}

@Parcelize
data class BillerUserInputOption(
    val optionId: UUID,
    val nameEN: String,
    val nameNY: String?,
    val amount: Long,
    val currency: String,
) : Parcelable
