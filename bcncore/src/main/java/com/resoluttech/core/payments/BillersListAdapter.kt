package com.resoluttech.core.payments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.payments.Biller
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.localisedText
import com.suryadigital.leo.libui.listview.ListAdapter

class BillersListAdapter(
    private val listener: BillersListListListener,
    private val listOfBillers: List<Biller>,
) : ListAdapter<Biller, RecyclerView.ViewHolder>(listOfBillers) {

    interface BillersListListListener {
        fun onBillerSelected(biller: <PERSON><PERSON>)
    }

    override fun filter(query: String) {
        // Default action is already handled
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return BillersViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_biller, parent, false),
        )
    }

    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        val item = listOfBillers[position]
        val viewHolder = (holder as BillersViewHolder)
        viewHolder.apply {
            billerIconIV.loadImage(item.icon.getUri(viewHolder.itemView.context))
            billerNameTV.text =
                item.displayName.localisedText(LocaleManager.getCurrentLocale(viewHolder.itemView.context))
            rootItem.setOnClickListener {
                listener.onBillerSelected(item)
            }
        }
    }

    private class BillersViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val rootItem: LinearLayout = itemView.findViewById(R.id.root_item)
        val billerIconIV: ImageView = itemView.findViewById(R.id.biller_icon_iv)
        val billerNameTV: TextView = itemView.findViewById(R.id.biller_name_tv)
    }
}
