package com.resoluttech.core.payments

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getUserFacingValue
import com.suryadigital.leo.libui.textdropdown.AbstractTextDropDownAdapter

class BillerPlanDropdownAdapter(
    private val items: List<BillerPlanDropdownItem>,
    private val context: Context,
) : AbstractTextDropDownAdapter<BillerPlanDropdownAdapter.BillerPlanDropdownItem>(items) {

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: BillerPlanViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.biller_plan_dropdown_item, parent, false)
            viewHolder =
                BillerPlanViewHolder(
                    view,
                )
            view.tag = viewHolder
        } else {
            view = convertView
            viewHolder = view.tag as BillerPlanViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is BillerPlanDropdownItem.Plan -> {
                    planName.visibility = View.VISIBLE
                    planAmount.visibility = View.VISIBLE
                    planName.text = item.label
                    planAmount.text = displayAmountWithCurrency(
                        UserSharedPreference.getUserPrimaryCurrency(),
                        item.amount.getUserFacingValue(),
                    )
                }
                is BillerPlanDropdownItem.Header -> {
                    planName.visibility = View.GONE
                    planAmount.visibility = View.GONE
                }
            }
        }
        return view
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View? {
        val view: View?
        val viewHolder: ViewHolder
        if (convertView == null) {
            val inflater = LayoutInflater.from(parent.context)
            view = inflater.inflate(R.layout.biller_plan_item_view, parent, false)
            viewHolder =
                ViewHolder(
                    view,
                )
            convertView?.tag = viewHolder
        } else {
            view = convertView
            viewHolder = convertView.tag as ViewHolder
        }
        viewHolder.apply {
            when (val item = items[position]) {
                is BillerPlanDropdownItem.Plan -> {
                    planName.text = item.label
                    planName.setTextColor(context.getColor(R.color.subtitleTextColor))
                    planName.typeface = Typeface.create(planName.typeface, Typeface.NORMAL)
                }
                is BillerPlanDropdownItem.Header -> {
                    planName.setTextColor(context.getColor(R.color.editTextHintColor))
                    planName.text = item.displayLabel
                    planName.typeface = Typeface.create(planName.typeface, Typeface.NORMAL)
                }
            }
        }
        return view
    }

    private class BillerPlanViewHolder(view: View) {
        var planName: TextView = view.findViewById(
            R.id.plan_name,
        )
        var planAmount: TextView = view.findViewById(
            R.id.plan_amount,
        )
    }

    private class ViewHolder(view: View) {
        var planName: TextView = view.findViewById(
            R.id.dropdown_item_text_view,
        )
    }

    sealed class BillerPlanDropdownItem {
        data class Plan(val label: String, val amount: Long, val id: String) :
            BillerPlanDropdownItem()

        data class Header(val displayLabel: String) : BillerPlanDropdownItem()
    }
}
