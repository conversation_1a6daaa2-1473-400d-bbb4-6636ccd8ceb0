package com.resoluttech.core.loadwallet

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.CountDownTimer
import android.view.InflateException
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.addCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.listeners.DialogListener
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.uicomponents.OTPChargesAlertDialog
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.DialogCodes.Companion.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID
import com.resoluttech.core.utils.DialogCodes.Companion.LOAD_MONEY_INSTRUCTIONS_CODE
import com.resoluttech.core.utils.DialogCodes.Companion.LOAD_WALLET_MONEY_FAILED
import com.resoluttech.core.utils.DialogCodes.Companion.WEB_VIEW_NOT_INSTALLED
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.showAlertDialog
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.LimitedCharacterEditText
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_NAME
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import timber.log.Timber
import java.time.Instant
import java.util.UUID

class LoadWalletFragment :
    BaseFragment(),
    AmountEditText.ErrorListener,
    AlertDialog.ActionListener,
    DialogListener,
    BaseFragment.NetworkListener {

    private val loadWalletVM: LoadWalletVM by navGraphViewModels(R.id.load_wallet_nav)

    private lateinit var progressBar: ProgressBar
    private lateinit var amountET: AmountEditText
    private lateinit var privateRemarkTIL: TextInputLayout
    private lateinit var privateRemarkET: LimitedCharacterEditText
    private lateinit var nextButton: MaterialButton
    private lateinit var inlineErrorSnackBar: Snackbar
    private lateinit var transactionFeeAlertDialog: OTPChargesAlertDialog
    private lateinit var paymentWV: WebView
    private lateinit var dataView: ConstraintLayout
    private var isAppClosed: Boolean = false
    private var isUserSelectingWallet: Boolean = false

    private lateinit var itemWalletSelector: ConstraintLayout
    private lateinit var accountName: TextView
    private lateinit var selectWallet: TextView
    var isWebViewInstalled: Boolean = true

    private val timeoutTimer = object : CountDownTimer(WEB_VIEW_TIMEOUT, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            if (millisUntilFinished / 1000 == 60L) {
                Toast.makeText(
                    requireContext(),
                    getString(R.string.toastMessageOneMinuteLeft),
                    Toast.LENGTH_LONG,
                ).show()
            }
        }

        override fun onFinish() {
            if (isWebViewInstalled) {
                showPaymentFailedDialog()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return try {
            isWebViewInstalled = true
            inflater.inflate(R.layout.fragment_load_wallet, container, false)
        } catch (e: InflateException) {
            isWebViewInstalled = false
            Timber.tag(TAG).e(e)
            showAlertDialog(
                requireContext().getString(R.string.alertTitleLoadWalletPaymentGatewayTimeout),
                requireContext().getString(R.string.alertMessageLoadWalletNoBrowser),
                WEB_VIEW_NOT_INSTALLED,
            )
            setDefaultToolbar(
                getString(R.string.loadWalletViewTitle),
            )
            null
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(
            getString(R.string.loadWalletViewTitle),
        )
        initViews()
        setupOnBackPressedListener()
        setupWalletSelectorListeners()
        setupWalletSelector()
        networkListenerCallback = this
        loadWalletVM.currentState.observe(viewLifecycleOwner, ::reactToState)
    }

    private fun setupWalletSelector() {
        requireView().apply {
            itemWalletSelector = findViewById(R.id.item_select_wallet)
            accountName = findViewById(R.id.account_name_tv)
            selectWallet = findViewById(R.id.pay_from_account_label)
        }
        val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            ?: throw IllegalStateException("Selected account cannot be null")
        accountName.text = selectedAccount.name
        selectWallet.text = getString(R.string.loadMoneyLoadToWallet)
        loadWalletVM.onAccountSelected(
            accountId = UUID.fromString(selectedAccount.id),
            displayName = selectedAccount.name,
        )
        itemWalletSelector.setOnClickListener {
            isUserSelectingWallet = true
            if (loadWalletVM.selectedAccountId != null) {
                findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}&selectedAccountId=${loadWalletVM.selectedAccountId}".toUri())
            } else {
                findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.MONEY_TRANSFER_SCREENS.name}".toUri())
            }
        }
    }

    private fun setupWalletSelectorListeners() {
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            loadWalletVM.selectedAccountId = it
            isUserSelectingWallet = false
        }
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<String>(
            KEY_SELECTED_ACCOUNT_NAME,
        )?.observe(
            viewLifecycleOwner,
        ) {
            accountName.text = it
            loadWalletVM.selectedAccountName = it
            isUserSelectingWallet = false
        }
    }

    private fun reactToState(state: LoadWalletScreenState) {
        when (state) {
            LoadWalletScreenState.AcceptInput -> {
                handleAcceptInputState()
            }

            is LoadWalletScreenState.InlineError -> {
                handleInlineError(state)
            }

            is LoadWalletScreenState.InlineLoading -> {
                handleInlineLoadingState()
            }

            is LoadWalletScreenState.InitiateMPGSProcess -> {
                handleInitiateMPGSProcessState()
            }

            is LoadWalletScreenState.RequestCreated -> {
                handleRequestCreatedState(state)
            }

            is LoadWalletScreenState.PaymentSuccess -> {
                handlePaymentSuccess(state)
            }

            is LoadWalletScreenState.PaymentPending -> {
                handlePaymentPending(state)
            }

            is LoadWalletScreenState.StartMPGSProcess -> {
                startLoadMoneyProcess(state)
            }
        }
    }

    private fun setupOnBackPressedListener() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            timeoutTimer.cancel()
            if (paymentWV.isVisible) {
                loadWalletVM.onLoadWalletCancelled()
            } else {
                findNavController().navigateUp()
            }
        }
    }

    private fun handlePaymentSuccess(state: LoadWalletScreenState.PaymentSuccess) {
        showInlineLoading(true)
        showViewState(dataView)

        handleSuccessfulTransaction(
            state.transactionId.toString(),
            state.amount,
            state.succeededAt,
            state.transactionDescription,
            state.transactionStatusItemDetail,
        )
    }

    private fun handleSuccessfulTransaction(
        transactionId: String,
        amount: Amount,
        transactionSucceededAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        loadWalletVM.handleSuccessfulTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionSucceededAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
        dismissProgressDialog()
    }

    private fun handlePaymentPending(state: LoadWalletScreenState.PaymentPending) {
        showInlineLoading(false)
        showViewState(dataView)

        handlePendingTransaction(
            state.transactionId.toString(),
            state.amount,
            state.createdAt,
            state.transactionDescription,
            state.transactionStatusItemDetail,
        )
        dismissProgressDialog()
    }

    private fun handlePendingTransaction(
        transactionId: String,
        amount: Amount,
        transactionCreatedAt: Instant,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        dismissProgressDialog()
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        loadWalletVM.handlePendingTransaction(
            requireContext(),
            transactionId,
            amount,
            transactionCreatedAt,
            findNavController(),
            transactionDescription,
            transactionStatusItemDetail,
        )
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
        showViewState(dataView)
        showInlineLoading(false)
        dismissDialog()
        dismissSnackbar()
    }

    private fun handleRequestCreatedState(state: LoadWalletScreenState.RequestCreated) {
        showInlineLoading(false)
        showViewState(dataView)
        transactionFeeAlertDialog =
            OTPChargesAlertDialog.newInstance(
                null,
                state.amount,
                state.accountName,
                true,
                getString(R.string.alertTransactionFeeChargesInfo),
            )
        transactionFeeAlertDialog.setArguments(false)
        transactionFeeAlertDialog.show(childFragmentManager, OTPChargesAlertDialog.TAG)
    }

    private fun handleInitiateMPGSProcessState() {
        showLoadMoneyInstructions()
    }

    private fun showLoadMoneyInstructions() {
        showErrorDialog(
            getString(R.string.alertTitleLoadWalletTimerConfirmation),
            getString(R.string.alertMessageLoadWalletTimerConfirmation),
            LOAD_MONEY_INSTRUCTIONS_CODE,
            getString(R.string.alertActionProceed),
            getString(R.string.alertActionDecline),
        )
    }

    private fun showViewState(view: View) {
        when (view) {
            dataView -> {
                paymentWV.loadUrl("about:blank")
                paymentWV.visibility = View.GONE
                progressBar.visibility = View.GONE
                dataView.visibility = View.VISIBLE
            }

            paymentWV -> {
                paymentWV.visibility = View.VISIBLE
                progressBar.visibility = View.VISIBLE
                dataView.visibility = View.GONE
            }
        }
    }

    private fun handleInlineLoadingState() {
        showInlineLoading(false)
        showViewState(dataView)
        dismissDialog()
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleInlineError(state: LoadWalletScreenState.InlineError) {
        showViewState(dataView)
        dismissProgressDialog()
        timeoutTimer.cancel()
        when (state.uiError.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(state.uiError.errorMessage)
            ErrorType.DIALOG -> {
                showInlineErrorDialog(
                    state.uiError.errorTitle,
                    state.uiError.errorMessage,
                    state.uiError.errorCode ?: DialogCodes.LOAD_WALLET_SCREEN_ERROR_CODE,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineErrorSnackBar(
        errorMessage: String,
    ) {
        inlineErrorSnackBar = Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackBar.let {
            it.setAction(R.string.alertActionDismiss) {
                loadWalletVM.inlineErrorDismissed()
            }
            it.show()
        }
    }

    private fun showInlineErrorDialog(
        errorTitle: String,
        errorMessage: String,
        errorCode: Int,
    ) {
        showErrorDialog(
            title = errorTitle,
            message = errorMessage,
            dialogId = errorCode,
            forceOutUserDestination = ForceOutUserDestination.HOME,
        )
    }

    private fun initViews() {
        view?.apply {
            setupProgressbar(this)
            setupDataView(this)
            setupAmountEditText(this)
            setupCurrencySuffix(this)
            setupNextButton(this)
            setupRemarkField(this)
            setupPaymentWebView(this)
        }
    }

    private fun setupProgressbar(view: View) {
        progressBar = view.findViewById(R.id.progressBar)
    }

    private fun setupDataView(view: View) {
        view.apply {
            dataView = findViewById(R.id.data_view)
        }
    }

    /*
        Allowing JavaScript content to be executed within the application via WebView might give the
        opportunity to an attacker to execute arbitrary JavaScript code in order to perform malicious
        actions.
     */
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupPaymentWebView(view: View) {
        view.apply {
            paymentWV = findViewById(R.id.payment_page_web_view)
            paymentWV.settings.apply {
                javaScriptEnabled = true
            }
        }
        paymentWV.apply {
            addJavascriptInterface(WebAppInterface(requireContext(), loadWalletVM), "android")
            webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(
                    view: WebView,
                    request: WebResourceRequest,
                ): Boolean {
                    view.loadUrl(request.url.toString())
                    return false
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    /*
                        Initiating the payment process by showing MPGS payment page.
                        Read more: https://eu-gateway.mastercard.com/api/documentation/integrationGuidelines/hostedCheckout/integrationModelHostedCheckout.html?locale=en_US
                     */
                    view?.loadUrl("javascript:Checkout.showPaymentPage()")
                    progressBar.visibility = View.INVISIBLE
                }
            }
        }
    }

    private fun setupAmountEditText(rootView: View) {
        amountET = rootView.findViewById(R.id.amount_edit_text)
        amountET.setErrorListener(this)
    }

    private fun setupCurrencySuffix(rootView: View) {
        val currencySuffixTextView = rootView.findViewById<TextView>(R.id.currency_suffix)
        currencySuffixTextView.text = UserSharedPreference.getUserPrimaryCurrency()
    }

    private fun setupNextButton(rootView: View) {
        nextButton = rootView.findViewById(R.id.proceed_button)
        nextButton.enable(false)
        nextButton.addContentDescriptionString(
            R.string.axLoadWalletAttemptPaymentDisabled,
            requireContext(),
        )
        nextButton.setOnClickListener {
            hideKeyboard()
            loadWalletVM.onNextButtonTapped(
                requireContext(),
                amountET.getFormattedAmount(),
            )
        }
    }

    private fun setupRemarkField(view: View) {
        privateRemarkET = view.findViewById(R.id.private_remark_et)
        privateRemarkTIL = view.findViewById(R.id.private_remark_til)
        privateRemarkET.setTextInputLayout(privateRemarkTIL, Config.MAX_REMARKS_LENGTH)
    }

    private fun showInlineLoading(shouldShow: Boolean) {
        showViewState(dataView)
        if (shouldShow) {
            dismissProgressDialog()
            showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
        } else {
            dismissProgressDialog()
        }
    }

    private fun dismissDialog() {
        if (::transactionFeeAlertDialog.isInitialized) {
            transactionFeeAlertDialog.dismiss()
        }
    }

    private fun dismissSnackbar() {
        if (::inlineErrorSnackBar.isInitialized) {
            inlineErrorSnackBar.dismiss()
        }
    }

    override fun onAmountInput(isValid: Boolean) {
        nextButton.enable(isValid)
        if (isValid) {
            nextButton.addContentDescriptionString(
                R.string.axLoadWalletAttemptPaymentEnabled,
                requireContext(),
            )
        } else {
            nextButton.addContentDescriptionString(
                R.string.axLoadWalletAttemptPaymentDisabled,
                requireContext(),
            )
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                leoServerExceptionHandler(findNavController())
            }

            LOAD_WALLET_MONEY_FAILED -> {
                findNavController().popBackStack()
            }

            LOAD_MONEY_INSTRUCTIONS_CODE -> {
                loadWalletVM.startLoadWallet()
            }

            WEB_VIEW_NOT_INSTALLED -> {
                loadWalletVM.onWebViewNotInstalled(findNavController())
            }

            else -> {
                loadWalletVM.inlineErrorDismissed()
            }
        }
    }

    private fun startLoadMoneyProcess(state: LoadWalletScreenState.StartMPGSProcess) {
        dismissProgressDialog()
        showViewState(paymentWV)
        timeoutTimer.start()
        paymentWV.loadUrl(state.paymentPageURL.toString())
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onProceed() {
        loadWalletVM.onProceedWithTransactionFee(requireContext(), privateRemarkET.getInputText())
    }

    override fun onClickTermsAndCondition() {
        throw IllegalStateException("onClickTermsAndCondition action is not applicable")
    }

    override fun onDeclined() {
        loadWalletVM.inlineErrorDismissed()
        dismissProgressDialog()
    }

    override fun onResume() {
        super.onResume()
        if (isAppClosed && isWebViewInstalled) {
            showPaymentFailedDialog()
        }
    }

    fun showPaymentFailedDialog() {
        showErrorDialog(
            title = getString(R.string.alertTitleLoadWalletPaymentGatewayTimeout),
            message = getString(R.string.alertMessageLoadWalletPaymentGatewayTimeout),
            dialogId = LOAD_WALLET_MONEY_FAILED,
        )
    }

    override fun onStop() {
        super.onStop()
        if (!isUserSelectingWallet && isWebViewInstalled) {
            cancelLoadMoney()
        }
    }

    private fun cancelLoadMoney() {
        timeoutTimer.cancel()
        paymentWV.destroy()
        isAppClosed = true
    }

    companion object {
        const val WEB_VIEW_TIMEOUT: Long = 180000L // 3 minutes = 3 * 60 * 1000 millis
    }

    override fun onNetworkAvailable() {
        if (isWebViewInstalled) {
            loadWalletVM.inlineErrorDismissed()
        }
    }
}

private const val TAG = "LoadWalletFragment"
