package com.resoluttech.core.loadwallet

import android.content.Context
import android.webkit.JavascriptInterface

class WebAppInterface(private val context: Context, private val loadWalletVM: LoadWalletVM) {

    @JavascriptInterface
    fun onPaymentCompleted(resultIndicator: String, sessionVersion: String) {
        loadWalletVM.onPaymentCompleted(context, resultIndicator, sessionVersion)
    }

    @JavascriptInterface
    fun onPaymentFailed(error: String) {
        loadWalletVM.onPaymentFailed(context, error)
    }

    @JavascriptInterface
    fun onPaymentCancelled() {
        loadWalletVM.onPaymentCancelled(context)
    }
}
