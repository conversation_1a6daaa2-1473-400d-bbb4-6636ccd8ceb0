package com.resoluttech.core.loadwallet

import com.resoluttech.bcn.transfers.ConfirmLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.CreateLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.LoadMoneyFromMPGSRPC
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.FrontEndPlatform
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent
import java.util.UUID

class LoadWalletRepository {

    private val createLoadMoneyFromMPGSRPC: CreateLoadMoneyFromMPGSRPC by KoinJavaComponent.inject(
        CreateLoadMoneyFromMPGSRPC::class.java,
    )

    private val loadMoneyFromMPGSRPC: LoadMoneyFromMPGSRPC by KoinJavaComponent.inject(
        LoadMoneyFromMPGSRPC::class.java,
    )

    private val confirmLoadMoneyFromMPGSRPC: ConfirmLoadMoneyFromMPGSRPC by KoinJavaComponent.inject(
        ConfirmLoadMoneyFromMPGSRPC::class.java,
    )

    suspend fun createLoadMoneyRequest(
        accountId: UUID,
        amount: Amount,
    ): LeoRPCResult<CreateLoadMoneyFromMPGSRPC.Response, CreateLoadMoneyFromMPGSRPC.Error> {
        return createLoadMoneyFromMPGSRPC.execute(
            CreateLoadMoneyFromMPGSRPC.Request(
                accountId,
                amount,
            ),
        )
    }

    suspend fun loadMoneyFromMPGSRequest(
        recordId: UUID,
        privateRemarks: String?,
    ): LeoRPCResult<LoadMoneyFromMPGSRPC.Response, LoadMoneyFromMPGSRPC.Error> {
        return loadMoneyFromMPGSRPC.execute(
            LoadMoneyFromMPGSRPC.Request(
                recordId,
                privateRemarks,
                FrontEndPlatform.ANDROID,
            ),
        )
    }

    suspend fun confirmLoadMoneyFromMPGSRequest(
        recordId: UUID,
        status: ConfirmLoadMoneyFromMPGSRPC.Request.Status,
    ): LeoRPCResult<ConfirmLoadMoneyFromMPGSRPC.Response, ConfirmLoadMoneyFromMPGSRPC.Error> {
        return confirmLoadMoneyFromMPGSRPC.execute(
            ConfirmLoadMoneyFromMPGSRPC.Request(
                recordId,
                status,
            ),
        )
    }
}
