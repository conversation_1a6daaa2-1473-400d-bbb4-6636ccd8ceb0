package com.resoluttech.core.loadwallet

import android.content.Context
import android.os.Bundle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.ConfirmLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.CreateLoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.LoadMoneyFromMPGSRPC
import com.resoluttech.bcn.transfers.TransferStatus
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.LoadWalletExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.Remarks
import com.resoluttech.core.transactions.areRemarksValid
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.views.FormattedAmount
import com.resoluttech.core.views.PendingTransactionDetails
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatus
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.net.URL
import java.time.Instant
import java.util.UUID

class LoadWalletVM : ViewModel() {

    private var _currentState: MutableLiveData<LoadWalletScreenState> =
        MutableLiveData(LoadWalletScreenState.AcceptInput)
    val currentState: LiveData<LoadWalletScreenState> = _currentState
    private val json: Json by KoinJavaComponent.inject(Json::class.java)

    private var vmIoScope = viewModelScope + Dispatchers.IO
    private val repository = LoadWalletRepository()
    var selectedAccountId: UUID? = null
    var selectedAccountName: String? = null

    fun onNextButtonTapped(context: Context, formattedAmount: FormattedAmount?) {
        if (!isAmountValid(context, formattedAmount)) return
        if (selectedAccountId == null) {
            _currentState.postValue(
                LoadWalletScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleNoLoadWalletSelected),
                        context.getString(R.string.alertMessageNoLoadWalletSelected),
                    ),
                ),
            )
            return
        }
        _currentState.postValue(LoadWalletScreenState.InlineLoading)
        vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val amount = Amount(
                        formattedAmount!!.processedValue,
                        Currency(UserSharedPreference.getUserPrimaryCurrency()),
                    )
                    when (
                        val result = repository.createLoadMoneyRequest(
                            selectedAccountId!!,
                            amount,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleCreateRequestResponse(result.response, amount)
                        }
                        is LeoRPCResult.LeoError -> {
                            handleCreateRequestError(context, result.error, selectedAccountName!!)
                        }
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleCreateRequestResponse(
        response: CreateLoadMoneyFromMPGSRPC.Response,
        amount: Amount,
    ) {
        _currentState.postValue(
            LoadWalletScreenState.RequestCreated(
                response.recordId,
                amount,
                response.confirmationExpiresAt,
                selectedAccountName!!,
            ),
        )
    }

    private fun handleCreateRequestError(
        context: Context,
        error: CreateLoadMoneyFromMPGSRPC.Error,
        accountName: String,
    ) {
        LoadWalletExceptionHandler.getCreateLoadMoneyFromMPGSErrorMessage(
            context,
            error,
            accountName,
        ).apply {
            showError()
        }
    }

    fun onProceedWithTransactionFee(context: Context, privateRemarks: String?) {
        val requestCreatedDetails = (currentState.value as LoadWalletScreenState.RequestCreated)
        _currentState.postValue(LoadWalletScreenState.InlineLoading)
        if (!areRemarksValid(null, privateRemarks)) {
            _currentState.postValue(
                LoadWalletScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionInvalidNarration),
                        context.getString(R.string.alertMessageTransactionInvalidNarration),
                    ),
                ),
            )
            return
        }
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    when (
                        val result =
                            repository.loadMoneyFromMPGSRequest(
                                requestCreatedDetails.recordId,
                                privateRemarks,
                            )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleMPGSRequestResponse(
                                requestCreatedDetails.recordId,
                                requestCreatedDetails.amount,
                                Remarks(null, privateRemarks),
                                result.response,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleMPGSRequestError(
                                context,
                                result.error,
                                requestCreatedDetails.accountName,
                            )
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleMPGSRequestResponse(
        recordId: UUID,
        amount: Amount,
        remarks: Remarks,
        response: LoadMoneyFromMPGSRPC.Response,
    ) {
        _currentState.postValue(
            LoadWalletScreenState.InitiateMPGSProcess(
                recordId,
                response.mpgsHostedCheckoutURL,
                amount,
                remarks,
            ),
        )
    }

    private fun handleMPGSRequestError(
        context: Context,
        error: LoadMoneyFromMPGSRPC.Error,
        accountName: String,
    ) {
        LoadWalletExceptionHandler.getLoadMoneyFromMPGSErrorMessage(context, error, accountName)
            .apply {
                showError()
            }
    }

    private fun isAmountValid(context: Context, formattedAmount: FormattedAmount?): Boolean {
        if (formattedAmount == null) {
            _currentState.postValue(
                LoadWalletScreenState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return false
        }
        return true
    }

    private fun UIError.showError() {
        _currentState.postValue(LoadWalletScreenState.InlineError(this))
    }

    fun inlineErrorDismissed() {
        _currentState.postValue(LoadWalletScreenState.AcceptInput)
    }

    fun onPaymentCompleted(context: Context, resultIndicator: String, sessionVersion: String) {
        updateServerForTransferStatus(
            context,
            ConfirmLoadMoneyFromMPGSRPC.Request.Status.Completed(
                resultIndicator = resultIndicator,
                sessionVersion = sessionVersion,
            ),
        )
    }

    private fun updateServerForTransferStatus(
        context: Context,
        status: ConfirmLoadMoneyFromMPGSRPC.Request.Status,
    ) {
        vmIoScope.launch {
            executeRPC(
                context,
                {
                    val recordId: UUID
                    val amount: Amount
                    (currentState.value as LoadWalletScreenState.StartMPGSProcess).apply {
                        recordId = this.recordId
                        amount = this.amount
                    }

                    _currentState.postValue(LoadWalletScreenState.InlineLoading)

                    when (
                        val result = repository.confirmLoadMoneyFromMPGSRequest(
                            recordId,
                            status,
                        )
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleConfirmRequestResponse(
                                context,
                                result.response,
                                recordId,
                                amount,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            handleConfirmRequestResponseError(
                                result.error,
                            )
                        }
                    }
                },
                {
                    it.showError()
                },
            )
        }
    }

    private fun handleConfirmRequestResponse(
        context: Context,
        response: ConfirmLoadMoneyFromMPGSRPC.Response,
        recordId: UUID,
        amount: Amount,
    ) {
        when (val status = response.paymentStatus) {
            is TransferStatus.Success -> {
                _currentState.postValue(
                    LoadWalletScreenState.PaymentSuccess(
                        recordId,
                        amount,
                        status.succeededAt,
                        response.transactionDetail.description?.let {
                            context.getLocalizedString(
                                it.en,
                                it.ny,
                            )
                        },
                        response.transactionDetail.itemDetail,
                    ),
                )
            }
            is TransferStatus.Failed -> {
                _currentState.postValue(
                    LoadWalletScreenState.InlineError(
                        UIError(
                            ErrorType.DIALOG,
                            context.getString(R.string.alertTitleLoadWalletPaymentGatewayTimeout),
                            context.getString(R.string.alertMessageLoadWalletPaymentGatewayTimeout),
                        ),
                    ),
                )
            }
            is TransferStatus.Pending -> {
                _currentState.postValue(
                    LoadWalletScreenState.PaymentPending(
                        recordId,
                        amount,
                        status.createdAt,
                        response.transactionDetail.description?.let {
                            context.getLocalizedString(
                                it.en,
                                it.ny,
                            )
                        },
                        response.transactionDetail.itemDetail,
                    ),
                )
            }
        }
    }

    private fun handleConfirmRequestResponseError(
        error: ConfirmLoadMoneyFromMPGSRPC.Error,
    ) {
        LoadWalletExceptionHandler.getConfirmLoadMoneyFromMPGSErrorMessage(
            error,
        ).apply {
            showError()
        }
    }

    @Serializable
    data class MPGSError(
        val cause: String,
        val explanation: String,
    )

    fun onPaymentFailed(context: Context, error: String) {
        Timber.tag(TAG).i("MPGS error: $error")
        val errorCause = json.decodeFromString(
            MPGSError.serializer(),
            error,
        ).cause
        updateServerForTransferStatus(
            context,
            ConfirmLoadMoneyFromMPGSRPC.Request.Status.Failed(
                error = errorCause,
            ),
        )
    }

    fun onPaymentCancelled(context: Context) {
        _currentState.postValue(
            LoadWalletScreenState.InlineError(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleLoadWalletCancelled),
                    context.getString(R.string.alertMessageLoadWalletCancelled),
                ),
            ),
        )
    }

    fun handleSuccessfulTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        transactionSucceededAt: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.SuccessfulTransaction(
            SuccessfulTransactionDetails(
                context.getString(R.string.transactionStatusSuccessLabel),
                getFormattedDate(context, transactionSucceededAt),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )

        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            data,
        )
        navController.navigate(R.id.transaction_status_nav, args)
    }

    fun handlePendingTransaction(
        context: Context,
        transactionId: String,
        amount: Amount,
        transactionCreatedAt: Instant,
        navController: NavController,
        transactionDescription: String?,
        transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) {
        val data = TransactionStatus.PendingTransaction(
            PendingTransactionDetails(
                context.getString(R.string.transactionStatusPendingLabel),
                getFormattedDate(context, transactionCreatedAt),
                transactionId,
                amount.amount,
                amount.currency.currencyCode,
                transactionDescription,
                transactionStatusItemDetail.getItemDetails(context),
            ),
        )

        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            data,
        )
        navController.navigate(R.id.transaction_status_nav, args)
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onLoadWalletCancelled() {
        _currentState.postValue(LoadWalletScreenState.AcceptInput)
    }

    fun onAccountSelected(accountId: UUID, displayName: String) {
        selectedAccountId = accountId
        selectedAccountName = displayName
    }

    fun startLoadWallet() {
        val state = _currentState.value
        if (state is LoadWalletScreenState.InitiateMPGSProcess) {
            _currentState.postValue(
                LoadWalletScreenState.StartMPGSProcess(
                    state.recordId,
                    state.paymentPageURL,
                    state.amount,
                    state.remarks,
                ),
            )
        } else {
            throw IllegalStateException("Start MPGS process from invalid state")
        }
    }

    fun onWebViewNotInstalled(navController: NavController) {
        navController.navigateUp()
    }
}

sealed class LoadWalletScreenState {
    object AcceptInput : LoadWalletScreenState()
    object InlineLoading : LoadWalletScreenState()
    data class InlineError(val uiError: UIError) : LoadWalletScreenState()
    data class RequestCreated(
        val recordId: UUID,
        val amount: Amount,
        val confirmationExpiresAt: Instant,
        val accountName: String,
    ) : LoadWalletScreenState()

    data class InitiateMPGSProcess(
        val recordId: UUID,
        val paymentPageURL: URL,
        val amount: Amount,
        val remarks: Remarks,
    ) :
        LoadWalletScreenState()

    data class StartMPGSProcess(
        val recordId: UUID,
        val paymentPageURL: URL,
        val amount: Amount,
        val remarks: Remarks,
    ) : LoadWalletScreenState()

    data class PaymentPending(
        val transactionId: UUID,
        val amount: Amount,
        val createdAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) : LoadWalletScreenState()

    data class PaymentSuccess(
        val transactionId: UUID,
        val amount: Amount,
        val succeededAt: Instant,
        val transactionDescription: String?,
        val transactionStatusItemDetail: List<com.resoluttech.bcn.types.TransactionStatusItemDetail>,
    ) :
        LoadWalletScreenState()
}

private const val TAG = "LoadMoneyVM"
