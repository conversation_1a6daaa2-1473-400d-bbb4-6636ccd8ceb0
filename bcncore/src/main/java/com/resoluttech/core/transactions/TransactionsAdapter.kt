package com.resoluttech.core.transactions

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcn.assets.LocalizedImage
import com.resoluttech.bcn.transactions.TransactionStatus
import com.resoluttech.bcncore.R
import com.resoluttech.core.transactions.models.TransactionType
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.TransitionListAdapter
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.getUri
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.loadImage
import com.resoluttech.core.utils.localisedText
import com.suryadigital.leo.libui.contactview.ContactIconView

class TransactionsAdapter(val transactions: List<TransactionType>) :
    TransitionListAdapter<TransactionType, RecyclerView.ViewHolder>(transactions) {

    private var mutableTransactionList: MutableList<TransactionType> = transactions as MutableList<TransactionType>
    override fun onBindView(holder: RecyclerView.ViewHolder, position: Int) {
        when (getItemViewType(position)) {
            DATA -> bindDataViewHolder(
                holder as DataViewHolder,
                mutableTransactionList[position] as TransactionType.Data,
                position,
            )
            HEADER -> bindHeaderViewHolder(
                holder as HeaderViewHolder,
                mutableTransactionList[position] as TransactionType.Header,
            )
        }
    }

    /**
     * This method uses DiffUtil, a utility class that calculates the difference between two lists
     * and outputs a list of update operations that converts the first list into the second one.
     */
    fun updateTransactionList(transactions: List<TransactionType>) {
        val transactionDiffUtil = TransactionDiffUtilChecker(mutableTransactionList, transactions)
        val result = DiffUtil.calculateDiff(transactionDiffUtil)
        mutableTransactionList.clear()
        mutableTransactionList.addAll(transactions)
        result.dispatchUpdatesTo(this)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            DATA -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_transactions, parent, false)
                DataViewHolder(itemView)
            }
            HEADER -> {
                val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_list_header, parent, false)
                HeaderViewHolder(itemView)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (mutableTransactionList[position]) {
            is TransactionType.Header -> HEADER
            is TransactionType.Data -> DATA
        }
    }

    private fun bindHeaderViewHolder(
        headerViewHolder: HeaderViewHolder,
        header: TransactionType.Header,
    ) {
        headerViewHolder.header.text = header.string
    }

    private fun bindDataViewHolder(
        dataViewHolder: DataViewHolder,
        data: TransactionType.Data,
        position: Int,
    ) {
        val context = dataViewHolder.itemView.context
        with(dataViewHolder) {
            setTitleAndDescription(data, context)
            handleFailureCase(data, context)
            setAmount(data)
            handleTransactionStatus(data)
            setProfileImage(context, data.transaction.image)
            sharedView.transitionName = String.format(context.getString(R.string.transactionStatusItem), position.toString())
        }
    }

    private fun DataViewHolder.handleTransactionStatus(data: TransactionType.Data) {
        when (data.transaction.status) {
            is TransactionStatus.Success -> {
                setArrowIcon(data)
            }
            is TransactionStatus.Failure -> {
                arrowIB.setBackgroundResource(R.drawable.ic_failed_transaction)
            }
            is TransactionStatus.Cancelled -> {
                arrowIB.setBackgroundResource(R.drawable.ic_cancelled_transaction)
            }
            is TransactionStatus.Expired -> {
                arrowIB.setBackgroundResource(R.drawable.ic_expired_transaction)
            }
            is TransactionStatus.Pending -> {
                arrowIB.setBackgroundResource(R.drawable.ic_pending_transaction)
            }
            is TransactionStatus.Reversed -> {
                arrowIB.setBackgroundResource(R.drawable.ic_reverse_icon)
            }
        }
    }

    private fun DataViewHolder.setAmount(
        data: TransactionType.Data,
    ) {
        amountTV.text = displayAmountWithCurrency(
            data.transaction.amount.currency.currencyCode,
            data.transaction.amount.amount.getUserFacingValue(),
        )
    }

    private fun DataViewHolder.setTitleAndDescription(
        data: TransactionType.Data,
        context: Context,
    ) {
        titleTV.text = data.transaction.title.localisedText(LocaleManager.getCurrentLocale(context))
        val description =
            data.transaction.description?.localisedText(LocaleManager.getCurrentLocale(context))
        if (!description.isNullOrEmpty()) {
            subtitleTV.text = description
        } else {
            subtitleTV.visibility = View.GONE
        }
    }

    private fun DataViewHolder.handleFailureCase(
        data: TransactionType.Data,
        context: Context,
    ) {
        val status = data.transaction.status
        if (status is TransactionStatus.Failure) {
            failureReasonTV.visibility = View.VISIBLE
            failureReasonTV.text = status.failureReason.localisedText(LocaleManager.getCurrentLocale(context))
        } else {
            failureReasonTV.visibility = View.GONE
        }
    }

    private fun DataViewHolder.setProfileImage(
        context: Context,
        imageUri: LocalizedImage?,
    ) {
        if (imageUri == null) {
            profileCIV.imageView.setImageResource(R.drawable.ic_exchange)
        } else {
            profileCIV.imageView.loadImage(imageUri.getUri(context))
        }
    }

    private fun DataViewHolder.setArrowIcon(data: TransactionType.Data) {
        if (data.transaction.isCredit) {
            arrowIB.setBackgroundResource(R.drawable.ic_credited_to)
        } else {
            arrowIB.setBackgroundResource(R.drawable.ic_debited_from)
        }
    }
}

private const val DATA = 2
private const val HEADER = 1

private class HeaderViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    val header: TextView = view.findViewById(R.id.list_category_tv)
}

private class DataViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    val titleTV: TextView = view.findViewById(R.id.title_tv)
    val subtitleTV: TextView = view.findViewById(R.id.subtitle_tv)
    val failureReasonTV: TextView = view.findViewById(R.id.error_message_tv)
    val amountTV: TextView = view.findViewById(R.id.amount_tv)
    val profileCIV: ContactIconView = view.findViewById(R.id.contact_icon_view)
    val arrowIB: ImageButton = view.findViewById(R.id.transaction_typ_icon)
    val sharedView: ConstraintLayout = view.findViewById(R.id.shared_view)
}

private class TransactionDiffUtilChecker(
    private val oldList: List<TransactionType>,
    private val newList: List<TransactionType>,
) : DiffUtil.Callback() {
    override fun getOldListSize(): Int = oldList.size

    override fun getNewListSize(): Int = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}
