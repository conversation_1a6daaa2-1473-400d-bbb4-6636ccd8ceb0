package com.resoluttech.core.transactions

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcn.transactions.Transaction
import com.resoluttech.bcn.transactions.TransactionStatus
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.models.TransactionType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes.Companion.TRANSACTION_LIST_DIALOG_CODE
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.ToolbarSettable
import com.resoluttech.core.utils.TransitionListAdapter
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.setProgressBackground
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.DividerItemDecorator
import com.resoluttech.core.views.walletselector.KEY_SELECTED_ACCOUNT_ID
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import com.suryadigital.leo.libui.listview.ListRecyclerView
import timber.log.Timber
import java.util.UUID

class TransactionsFragment :
    BaseFragment(),
    TransitionListAdapter.OnItemClickListener,
    AlertDialog.ActionListener,
    BaseFragment.NetworkListener {

    private val transactionVM: TransactionVM = ViewModelProvider.NewInstanceFactory().create(
        TransactionVM::class.java,
    )

    private lateinit var selectedWalletNameTV: TextView
    private lateinit var layoutLoadingStateView: ProgressBar
    private lateinit var errorTitleTV: TextView
    private lateinit var errorMessageTV: TextView
    private lateinit var retryBT: MaterialButton
    private lateinit var fullScreenErrorStateView: LinearLayout
    private lateinit var noDataView: View
    private lateinit var noDataTV: TextView
    private lateinit var transferNowButton: MaterialButton
    private lateinit var inlineErrorSnackbar: Snackbar
    private lateinit var transactionRV: ListRecyclerView
    private lateinit var swipeToRefreshLayout: SwipeRefreshLayout
    private lateinit var transactionAdapter: TransactionsAdapter
    private lateinit var transactions: List<TransactionType>
    private var selectedAccountPosition = -1
    private var newSelectedAccountPosition = -1

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_transactions, container, false)
        initView(rootView)
        setToolbar()
        presentTransactionsData()
        setSwipeRefreshLayout()
        setupRetryButton()
        setupTransferNowButton()
        return rootView
    }

    private fun initView(rootView: View) {
        layoutLoadingStateView = rootView.findViewById(R.id.loading)
        errorTitleTV = rootView.findViewById(R.id.fullScreenErrorTitle)
        errorMessageTV = rootView.findViewById(R.id.fullScreenErrorMessage)
        retryBT = rootView.findViewById(R.id.retry_button)
        fullScreenErrorStateView = rootView.findViewById(R.id.error_view)
        transactionRV = rootView.findViewById(R.id.transaction_recycler_view)
        swipeToRefreshLayout = rootView.findViewById(R.id.tranasction_swipe_to_refresh)
        noDataView = rootView.findViewById(R.id.no_data_view)
        noDataTV = rootView.findViewById(R.id.no_data_tv)
        transferNowButton = rootView.findViewById(R.id.transfer_now_bt)
        transactionAdapter = TransactionsAdapter(arrayListOf())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        networkListenerCallback = this
        postponeEnterTransition()
        view.doOnPreDraw { startPostponedEnterTransition() }
        transactionVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<UUID>(
            KEY_SELECTED_ACCOUNT_ID,
        )?.observe(
            viewLifecycleOwner,
        ) {
            transactionVM.onAccountChange(requireContext(), it)
        }
    }

    private fun setToolbar() {
        val activity = requireActivity() as ToolbarSettable
        val toolbarView =
            layoutInflater.inflate(R.layout.transaction_toolbar, activity.toolbar, false)
        activity.toolbar.apply {
            visibility = View.VISIBLE
            removeAllViews()
            addView(toolbarView)
            selectedWalletNameTV = toolbarView.findViewById(R.id.selected_wallet_name_tv)
            val account = SelectedAccountHelper.getSelectedAccount()
            if (account != null) {
                selectedWalletNameTV.enable()
                selectedWalletNameTV.text = account.name
                transactionVM.onAccountChange(requireContext(), UUID.fromString(account.id))
            } else {
                selectedWalletNameTV.disable()
                selectedWalletNameTV.text =
                    requireContext().getString(R.string.moneyScreenAvailableBalancePlaceholder)
            }
            selectedWalletNameTV.setOnClickListener {
                findNavController().navigate("resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.TRANSACTIONS_SCREEN.name}".toUri())
            }
        }
    }

    private fun reactToState(state: TransactionsScreenState) {
        when (state) {
            is TransactionsScreenState.Loading -> {
                showViewForState(layoutLoadingStateView)
            }
            is TransactionsScreenState.Error -> {
                handleErrorState(
                    state.uiError,
                    state.transactions,
                )
            }
            is TransactionsScreenState.FullScreeError -> {
                handleFullScreenErrorState(state.uiError)
            }
            is TransactionsScreenState.Data -> {
                handleDataState(state.transactions)
            }
            is TransactionsScreenState.NoData -> {
                handleNoDataState()
            }
            is TransactionsScreenState.InlineLoading -> {
                handleInlineLoadingState()
            }
            TransactionsScreenState.AcceptInput -> {
                handleAcceptInputState()
            }
        }
    }

    private fun handleNoDataState() {
        showViewForState(noDataView)
        transactionAdapter.updateTransactionList(listOf())
    }

    private fun handleAcceptInputState() {
        swipeToRefreshLayout.isRefreshing = false
        layoutLoadingStateView.visibility = View.GONE
    }

    private fun setupTransferNowButton() {
        transferNowButton.setOnClickListener {
            transactionVM.onTransferNowTapped(findNavController())
        }
    }

    private fun handleInlineLoadingState() {
        handleSwipeRefreshLoading(true)
        layoutLoadingStateView.visibility = View.GONE
        noDataView.visibility = View.GONE
        hideInlineError()
    }

    private fun handleFullScreenErrorState(uiError: UIError) {
        swipeToRefreshLayout.isRefreshing = false
        showViewForState(fullScreenErrorStateView)
        if (uiError.errorTitle.isBlank()) {
            errorTitleTV.visibility = View.GONE
        } else {
            errorTitleTV.visibility = View.VISIBLE
            errorTitleTV.text = uiError.errorTitle
        }
        errorMessageTV.text = uiError.errorMessage
    }

    private fun handleErrorState(uiError: UIError, transactions: List<TransactionType>) {
        swipeToRefreshLayout.isRefreshing = false
        handleDataState(transactions)
        when (uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineErrorSnackBar(uiError.errorMessage, transactions)
            }
            ErrorType.DIALOG -> {
                showInlineErrorDialog(
                    uiError.errorTitle,
                    uiError.errorMessage,
                    uiError.errorCode ?: TRANSACTION_LIST_DIALOG_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun setupRetryButton() {
        retryBT.setOnClickListener {
            transactionVM.onRetryTapped(requireContext())
        }
    }

    private fun showInlineErrorDialog(
        errorTitle: String,
        errorMessage: String,
        errorCodes: Int,
    ) {
        showErrorDialog(
            errorTitle,
            message = errorMessage,
            dialogId = errorCodes,
        )
    }

    private fun showInlineErrorSnackBar(
        error: String,
        transactions: List<TransactionType>?,
    ) {
        inlineErrorSnackbar =
            Snackbar.make(requireView(), error, Snackbar.LENGTH_INDEFINITE)
        inlineErrorSnackbar.let {
            it.setAction(R.string.alertActionDismiss) {
                transactionVM.onInlineErrorDismissed(transactions)
            }
            it.show()
        }
    }

    private fun hideInlineError() {
        if (::inlineErrorSnackbar.isInitialized && inlineErrorSnackbar.isShown) {
            inlineErrorSnackbar.dismiss()
        } else {
            Timber.tag(TAG).w("The snackbar is already hidden")
        }
    }

    private fun setSwipeRefreshLayout() {
        swipeToRefreshLayout.setProgressBackground()
        swipeToRefreshLayout.setOnRefreshListener {
            transactionVM.onSwipeToRefresh(
                requireContext(),
            )
        }
    }

    private fun handleSwipeRefreshLoading(shouldShowLoading: Boolean) {
        if (shouldShowLoading && !swipeToRefreshLayout.isRefreshing) {
            swipeToRefreshLayout.isRefreshing = true
        } else if (!shouldShowLoading && swipeToRefreshLayout.isRefreshing) {
            swipeToRefreshLayout.isRefreshing = false
        }
    }

    private fun handleDataState(transactions: List<TransactionType>) {
        showViewForState(transactionRV)
        handleSwipeRefreshLoading(false)
        hideInlineError()
        if (selectedAccountPosition != newSelectedAccountPosition) {
            transactionAdapter.updateTransactionList(transactions)
            selectedAccountPosition = newSelectedAccountPosition
            presentTransactionsData()
        } else {
            transactionAdapter.updateTransactionList(transactions)
        }
        transactionAdapter.apply {
            setOnItemClickListener(this@TransactionsFragment)
        }
        this.transactions = transactions
    }

    private fun presentTransactionsData() {
        val dividerItemDecoration = DividerItemDecorator(
            ContextCompat.getDrawable(requireContext(), R.drawable.divider),
            DividerItemDecorator.LIST_MARGIN_0,
            listOf(TYPE_DATA),
        )
        transactionRV.apply {
            adapter = transactionAdapter.apply {
                setOnItemClickListener(this@TransactionsFragment)
                addItemDecoration(dividerItemDecoration)
                itemAnimator = null
            }
        }
    }

    private fun showViewForState(stateView: View) {
        when (stateView) {
            layoutLoadingStateView -> {
                fullScreenErrorStateView.visibility = View.GONE
                transactionRV.visibility = View.GONE
                layoutLoadingStateView.visibility = View.VISIBLE
                noDataView.visibility = View.GONE
                handleSwipeRefreshLoading(false)
            }
            fullScreenErrorStateView -> {
                layoutLoadingStateView.visibility = View.GONE
                transactionRV.visibility = View.GONE
                fullScreenErrorStateView.visibility = View.VISIBLE
                noDataView.visibility = View.GONE
                handleSwipeRefreshLoading(false)
            }
            transactionRV -> {
                layoutLoadingStateView.visibility = View.GONE
                fullScreenErrorStateView.visibility = View.GONE
                noDataView.visibility = View.GONE
                transactionRV.visibility = View.VISIBLE
                handleSwipeRefreshLoading(false)
            }
            noDataView -> {
                layoutLoadingStateView.visibility = View.GONE
                fullScreenErrorStateView.visibility = View.GONE
                transactionRV.visibility = View.GONE
                noDataView.visibility = View.VISIBLE
                handleSwipeRefreshLoading(false)
            }
        }
    }

    override fun onItemClicked(pos: Int, sharedView: ConstraintLayout?) {
        when (val type = transactions[pos]) {
            is TransactionType.Data -> {
                when (val status = type.transaction.status) {
                    is TransactionStatus.Success -> {
                        handleSuccessfulState(
                            type.transaction,
                            status,
                            sharedView,
                        )
                    }
                    is TransactionStatus.Failure -> {
                        handleFailureState(type.transaction, status, sharedView)
                    }
                    is TransactionStatus.Expired -> {
                        handleExpiredState(type.transaction, status, sharedView)
                    }
                    is TransactionStatus.Cancelled -> {
                        handleCancelledState(type.transaction, status, sharedView)
                    }
                    is TransactionStatus.Pending -> {
                        handlePendingState(type.transaction, status, sharedView)
                    }
                    is TransactionStatus.Reversed -> {
                        handleReverseState(type.transaction, status, sharedView)
                    }
                }
            }
            else -> {
                // Ignoring as user can click on header as well and we don't want to crash the app
            }
        }
    }

    private fun handleSuccessfulState(
        transaction: Transaction,
        status: TransactionStatus.Success,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onSuccessfulItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    private fun handleFailureState(
        transaction: Transaction,
        status: TransactionStatus.Failure,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onFailureItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    private fun handleExpiredState(
        transaction: Transaction,
        status: TransactionStatus.Expired,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onExpiredItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    private fun handleCancelledState(
        transaction: Transaction,
        status: TransactionStatus.Cancelled,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onCancelledItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    private fun handlePendingState(
        transaction: Transaction,
        status: TransactionStatus.Pending,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onPendingItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    private fun handleReverseState(
        transaction: Transaction,
        status: TransactionStatus.Reversed,
        sharedView: ConstraintLayout?,
    ) {
        transactionVM.onReverseItemSelection(
            requireContext(),
            transaction,
            status,
            findNavController(),
            sharedView,
        )
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("Positive action occurred on alert dialog having id $dialogId.")
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        transactionVM.refreshTransactionsList(requireContext(), UUID.fromString(SelectedAccountHelper.getSelectedAccount()!!.id))
    }
}

const val REFRESH_TRANSACTION_LIST_TIME_INTERVAL_IN_SECONDS: Int = 20
private const val TAG = "TransactionFragment"
const val TYPE_DATA: Int = 2
