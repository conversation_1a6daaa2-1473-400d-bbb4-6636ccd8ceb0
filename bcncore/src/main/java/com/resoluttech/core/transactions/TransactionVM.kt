package com.resoluttech.core.transactions

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.FragmentNavigatorExtras
import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.resoluttech.bcn.transactions.InvalidRemarkTypeException
import com.resoluttech.bcn.transactions.Remark
import com.resoluttech.bcn.transactions.Transaction
import com.resoluttech.bcn.transactions.TransactionStatus
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.TransactionRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transactions.models.TransactionComparator
import com.resoluttech.core.transactions.models.TransactionType
import com.resoluttech.core.uicomponents.CreatableAccountListAdapter
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.TransactionRemarkSharedPreference
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.utils.localisedText
import com.resoluttech.core.views.CancelledTransactionDetails
import com.resoluttech.core.views.ExpiredTransactionDetails
import com.resoluttech.core.views.FailedTransactionDetails
import com.resoluttech.core.views.PendingTransactionDetails
import com.resoluttech.core.views.ReverseTransactionDetails
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import kotlinx.parcelize.Parcelize
import timber.log.Timber
import java.time.Instant
import java.util.UUID

class TransactionVM : ViewModel() {

    private val _accountsDropdownList =
        mutableListOf<CreatableAccountListAdapter.CreatableAccountDropdownItem>()
    private val repository = TransactionsRepository()
    private val vmIoScope = viewModelScope + Dispatchers.IO
    private val _currentState: MutableLiveData<TransactionsScreenState> =
        MutableLiveData(TransactionsScreenState.Loading)
    val currentState: LiveData<TransactionsScreenState> = _currentState
    private var transactionJob: Job? = null
    private var showProgressBar: Boolean = true
    internal var privateRemark: String? = null
    internal var transactionID: String? = null

    init {
        SelectedAccountHelper.getPersistedAccounts().forEach {
            _accountsDropdownList.add(
                CreatableAccountListAdapter.CreatableAccountDropdownItem.Account(
                    it.name,
                    null,
                    UUID.fromString(it.id),
                ),
            )
        }
    }

    fun onAccountChange(context: Context, accountId: UUID) {
        refreshTransactionsList(context, accountId)
        SelectedAccountHelper.setSelectedAccountId(context, accountId)
    }

    fun refreshTransactionsList(context: Context, accountId: UUID) {
        vmIoScope.launch {
            val savedTransactionUUID: String? =
                TransactionRemarkSharedPreference.getTransactionAccountID()
            if (savedTransactionUUID != null && savedTransactionUUID == "$accountId") {
                if (Instant.now().epochSecond - Instant.ofEpochMilli(
                        TransactionRemarkSharedPreference.getTransactionRPCSuccess(),
                    ).epochSecond > REFRESH_TRANSACTION_LIST_TIME_INTERVAL_IN_SECONDS
                ) {
                    getTransactionsList(context, accountId)
                } else {
                    getCachedTransactionsList(context, accountId)
                }
            } else {
                getTransactionsList(context, accountId)
            }
        }
    }

    private fun getCachedTransactionsList(context: Context, accountId: UUID) {
        val cachedTransactionData = repository.getCachedTransactionLayout()?.get(accountId)
        val transactionsTypeList = cachedTransactionData?.let {
            if (it.transactions.isNotEmpty()) {
                convertToTransactionTypeList(it.transactions, context)
            } else {
                null
            }
        }
        if (transactionsTypeList != null) {
            _currentState.postValue(TransactionsScreenState.Data(transactionsTypeList))
        } else {
            _currentState.postValue(TransactionsScreenState.NoData)
        }
    }

    private fun getTransactionsList(context: Context, accountId: UUID) {
        val cachedTransactionData = repository.getCachedTransactionLayout()?.get(accountId)
        if (transactionJob != null) {
            transactionJob!!.cancel()
        }
        if ("$accountId" != SelectedAccountHelper.getSelectedAccount()!!.id || showProgressBar) {
            _currentState.postValue(TransactionsScreenState.Loading)
        } else {
            _currentState.postValue(TransactionsScreenState.InlineLoading)
        }
        transactionJob = vmIoScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (val response = repository.refreshTransactionScreenData(accountId)) {
                        is LeoRPCResult.LeoResponse -> {
                            transactionID = null
                            privateRemark = null
                            TransactionRemarkSharedPreference.setTransactionRPCSuccess()
                            TransactionRemarkSharedPreference.setTransactionAccountID(accountId)
                            handleRPCResponse(
                                context,
                                response.response,
                            )
                        }

                        is LeoRPCResult.LeoError -> TransactionRPCExceptionHandler.getTransactionRPCExceptionMessage(
                            context,
                            response.error,
                        ).apply {
                            handleErrorResponse(this, cachedTransactionData, context, accountId)
                        }
                    }
                },
                handleException = {
                    handleErrorResponse(it, cachedTransactionData, context, accountId)
                },
            )
        }
    }

    private fun handleErrorResponse(
        uiError: UIError,
        cachedTransactionData: GetTransactionsRPC.Response?,
        context: Context,
        accountId: UUID,
    ) {
        val transactionsTypeList = cachedTransactionData?.let {
            if (it.transactions.isNotEmpty()) {
                convertToTransactionTypeList(it.transactions, context)
            } else {
                null
            }
        }
        if (transactionsTypeList == null) {
            _currentState.postValue(TransactionsScreenState.FullScreeError(uiError))
        } else {
            _currentState.postValue(
                TransactionsScreenState.Error(
                    uiError,
                    transactionsTypeList,
                    accountId,
                ),
            )
        }
    }

    private fun handleRPCResponse(context: Context, response: GetTransactionsRPC.Response) {
        showProgressBar = false
        if (response.transactions.isNotEmpty()) {
            val transactionsTypeList = convertToTransactionTypeList(response.transactions, context)
            _currentState.postValue(TransactionsScreenState.Data(transactionsTypeList))
        } else {
            _currentState.postValue(TransactionsScreenState.NoData)
        }
    }

    private fun convertToTransactionTypeList(
        transactions: List<Transaction>,
        context: Context,
    ): List<TransactionType> {
        val sortedList = transactions.sortedWith(TransactionComparator())
        val transactionsTypeList = mutableListOf<TransactionType>()
        var currentHeader = getHeader(context, sortedList[0].status)
        transactionsTypeList.add(TransactionType.Header(currentHeader))
        transactionsTypeList.add(TransactionType.Data(sortedList[0]))
        for (i in 1 until sortedList.size) {
            val header = getHeader(context, sortedList[i].status)
            if (currentHeader != header) {
                transactionsTypeList.add(TransactionType.Header(header))
                transactionsTypeList.add(TransactionType.Data(sortedList[i]))
                currentHeader = header
            } else {
                transactionsTypeList.add(TransactionType.Data(sortedList[i]))
            }
        }
        return transactionsTypeList
    }

    private fun getDateString(context: Context, instant: Instant): String {
        return instant.getFormattedDateTime(
            context,
            dateTimeType = DateTimeType.HISTORY,
        )
    }

    private fun getHeader(context: Context, status: TransactionStatus): String {
        return when (status) {
            is TransactionStatus.Success -> getDateString(context, status.succeededAt)
            is TransactionStatus.Failure -> getDateString(context, status.failedAt)
            is TransactionStatus.Cancelled -> getDateString(context, status.cancelledAt)
            is TransactionStatus.Expired -> getDateString(context, status.expiredAt)
            is TransactionStatus.Pending -> getDateString(context, status.createdAt)
            is TransactionStatus.Reversed -> getDateString(context, status.reversedAt)
        }
    }

    fun onInlineErrorDismissed(transactions: List<TransactionType>?) {
        if (transactions != null) {
            _currentState.postValue(TransactionsScreenState.Data(transactions))
        } else {
            _currentState.postValue(TransactionsScreenState.AcceptInput)
        }
    }

    fun onSwipeToRefresh(context: Context) {
        getTransactionsList(
            context,
            UUID.fromString(SelectedAccountHelper.getSelectedAccount()!!.id),
        )
    }

    fun onSuccessfulItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Success,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.succeededAt)
        val successfulStateData =
            com.resoluttech.core.views.TransactionStatus.SuccessfulTransaction(
                SuccessfulTransactionDetails(
                    context.getString(R.string.transactionStatusSuccessLabel),
                    formattedDate,
                    transaction.recordId.toString(),
                    transaction.amount.amount,
                    transaction.amount.currency.currencyCode,
                    transaction.transactionDetail.description?.let {
                        context.getLocalizedString(
                            it.en,
                            it.ny,
                        )
                    },
                    transaction.transactionDetail.itemDetail.getItemDetails(context),
                    transaction.allowAddingPrivateRemark,
                ),
            )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            successfulStateData,
        )
        val extras: FragmentNavigator.Extras? = getNavigatorExtras(context, sharedView)
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            extras,
        )
    }

    private fun getNavigatorExtras(
        context: Context,
        sharedView: ConstraintLayout?,
    ): FragmentNavigator.Extras? {
        val transitionName = context.getString(R.string.transactionStatusTransitionName)
        var extras: FragmentNavigator.Extras? = null
        if (sharedView != null) {
            extras = FragmentNavigatorExtras(sharedView to transitionName)
        }
        return extras
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onPendingItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Pending,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.createdAt)
        val pendingTransactionData =
            com.resoluttech.core.views.TransactionStatus.PendingTransaction(
                PendingTransactionDetails(
                    context.getString(R.string.transactionStatusPendingLabel),
                    formattedDate,
                    transaction.recordId.toString(),
                    transaction.amount.amount,
                    transaction.amount.currency.currencyCode,
                    transaction.transactionDetail.description?.let {
                        context.getLocalizedString(
                            it.en,
                            it.ny,
                        )
                    },
                    transaction.transactionDetail.itemDetail.getItemDetails(context),
                    transaction.allowAddingPrivateRemark,
                ),
            )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            pendingTransactionData,
        )
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            getNavigatorExtras(context, sharedView),
        )
    }

    fun onReverseItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Reversed,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.reversedAt)
        val reverseTransactionData =
            com.resoluttech.core.views.TransactionStatus.ReverseTransaction(
                ReverseTransactionDetails(
                    context.getString(R.string.transactionStatusReversedLabel),
                    formattedDate,
                    transaction.recordId.toString(),
                    transaction.amount.amount,
                    transaction.amount.currency.currencyCode,
                    transaction.transactionDetail.description?.let {
                        context.getLocalizedString(
                            it.en,
                            it.ny,
                        )
                    },
                    transaction.transactionDetail.itemDetail.getItemDetails(context),
                ),
            )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            reverseTransactionData,
        )
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            getNavigatorExtras(context, sharedView),
        )
    }

    fun onCancelledItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Cancelled,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.cancelledAt)
        val cancelledTransactionData =
            com.resoluttech.core.views.TransactionStatus.CancelledTransaction(
                CancelledTransactionDetails(
                    context.getString(R.string.transactionStatusCancelledLabel),
                    formattedDate,
                    transaction.recordId.toString(),
                    transaction.amount.amount,
                    transaction.amount.currency.currencyCode,
                    transaction.transactionDetail.description?.let {
                        context.getLocalizedString(
                            it.en,
                            it.ny,
                        )
                    },
                    transaction.transactionDetail.itemDetail.getItemDetails(context),
                ),
            )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            cancelledTransactionData,
        )
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            getNavigatorExtras(context, sharedView),
        )
    }

    fun onExpiredItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Expired,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.expiredAt)
        val expiredTransactionData =
            com.resoluttech.core.views.TransactionStatus.ExpiredTransaction(
                ExpiredTransactionDetails(
                    context.getString(R.string.transactionStatusExpiredLabel),
                    formattedDate,
                    transaction.recordId.toString(),
                    transaction.amount.amount,
                    transaction.amount.currency.currencyCode,
                    transaction.transactionDetail.description?.let {
                        context.getLocalizedString(
                            it.en,
                            it.ny,
                        )
                    },
                    transaction.transactionDetail.itemDetail.getItemDetails(context),
                ),
            )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            expiredTransactionData,
        )
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            getNavigatorExtras(context, sharedView),
        )
    }

    fun onFailureItemSelection(
        context: Context,
        transaction: Transaction,
        status: TransactionStatus.Failure,
        controller: NavController,
        sharedView: ConstraintLayout?,
    ) {
        val formattedDate = getFormattedDate(context, status.failedAt)
        val failedTransactionData = com.resoluttech.core.views.TransactionStatus.FailedTransaction(
            FailedTransactionDetails(
                context.getString(R.string.transactionStatusFailedLabel),
                formattedDate,
                transaction.recordId.toString(),
                transaction.amount.amount,
                transaction.amount.currency.currencyCode,
                transaction.transactionDetail.description?.let {
                    context.getLocalizedString(
                        it.en,
                        it.ny,
                    )
                },
                status.failureReason.localisedText(LocaleManager.getCurrentLocale(context)),
                transaction.transactionDetail.itemDetail.getItemDetails(context),
            ),
        )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            failedTransactionData,
        )
        controller.navigate(
            R.id.transaction_status_nav,
            args,
            null,
            getNavigatorExtras(context, sharedView),
        )
    }

    fun onRetryTapped(context: Context) {
        showProgressBar = true
        getTransactionsList(
            context,
            UUID.fromString(SelectedAccountHelper.getSelectedAccount()!!.id),
        )
    }

    fun onTransferNowTapped(controller: NavController) {
        controller.navigate(R.id.send_money_nav)
    }
}

sealed class TransactionsScreenState {
    object Loading : TransactionsScreenState()
    data class Error(
        val uiError: UIError,
        val transactions: List<TransactionType>,
        val accountId: UUID,
    ) :
        TransactionsScreenState()

    data class FullScreeError(val uiError: UIError) : TransactionsScreenState()
    data class Data(val transactions: List<TransactionType>) : TransactionsScreenState()
    object InlineLoading : TransactionsScreenState()
    object NoData : TransactionsScreenState()
    object AcceptInput : TransactionsScreenState()
}

@Parcelize
data class Remarks(val publicRemark: String?, val privateRemark: String?) : Parcelable {
    companion object {
        /**
         * This builds the serializable [Remarks] object from given [Remark] string.
         * */
        fun build(remark: Remark?): Remarks? {
            return when (remark) {
                is Remark.PublicAndPrivate -> Remarks(remark.publicRemark, remark.privateRemark)
                is Remark.PublicOnly -> Remarks(remark.publicRemark, null)
                is Remark.PrivateOnly -> Remarks(null, remark.privateRemark)
                else -> null
            }
        }
    }
}

fun areRemarksValid(publicRemark: String?, privateRemark: String?): Boolean {
    return try {
        when {
            publicRemark != null && privateRemark != null -> Remark.PublicAndPrivate(
                privateRemark,
                publicRemark,
            )
            publicRemark == null && privateRemark != null -> Remark.PrivateOnly(
                privateRemark = privateRemark,
            )
            publicRemark != null && privateRemark == null -> Remark.PublicOnly(
                publicRemark = publicRemark,
            )
        }
        true
    } catch (e: InvalidRemarkTypeException) {
        Timber.tag("Invalid Remarks").e(e)
        false
    }
}
