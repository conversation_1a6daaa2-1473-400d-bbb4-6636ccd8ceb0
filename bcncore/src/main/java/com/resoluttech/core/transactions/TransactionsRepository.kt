package com.resoluttech.core.transactions

import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.rpc.LeoServerException
import com.suryadigital.leo.rpc.LeoUnauthenticatedException
import com.suryadigital.leo.rpc.LeoUnauthorizedException
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.util.UUID

class TransactionsRepository {

    private val transactionRPC: GetTransactionsRPC by KoinJavaComponent.inject(GetTransactionsRPC::class.java)
    private val transactionsDataPersistor = TransactionDataPersistor()

    @Throws(
        LeoUnauthenticatedException::class,
        LeoUnauthorizedException::class,
        LeoUnsupportedClientException::class,
        LeoServerException::class,
        LeoInvalidResponseException::class,
    )
    suspend fun refreshTransactionScreenData(accountId: UUID): LeoRPCResult<GetTransactionsRPC.Response, GetTransactionsRPC.Error> {
        val rpcResult = transactionRPC.execute(GetTransactionsRPC.Request(accountId))
        if (rpcResult is LeoRPCResult.LeoResponse) {
            if (rpcResult.response.transactions.take(100) != getCachedTransactionLayout()?.get(accountId)?.transactions) {
                cacheTransactionsLayout(rpcResult.response, accountId)
                Timber.tag(TAG).i("Transaction data written to cache.")
            } else {
                Timber.tag(TAG).i("Cached transaction data is same as data from server.")
            }
        } else {
            clearTransactionsDataCache()
        }

        return rpcResult
    }

    private fun cacheTransactionsLayout(
        transactionsData: GetTransactionsRPC.Response,
        accountId: UUID,
    ) {
        transactionsDataPersistor.writeTransactionsLayoutFile(transactionsData, accountId)
    }

    fun clearTransactionsDataCache() {
        transactionsDataPersistor.clearTransactionsDataCache()
    }

    fun getCachedTransactionLayout(): Map<UUID, GetTransactionsRPC.Response>? {
        return transactionsDataPersistor.getCachedTransactionsData()
    }
}

private const val TAG = "TransactionsRepository"
