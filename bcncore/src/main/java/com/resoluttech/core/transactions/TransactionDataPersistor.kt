package com.resoluttech.core.transactions

import android.content.Context
import android.os.Build
import android.os.FileObserver
import com.resoluttech.bcn.transactions.GetTransactionsRPC
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.io.File
import java.time.Instant
import java.util.UUID

class TransactionDataPersistor {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private var transactionsDataFile = getTransactionsDataFile()
    private val json: Json by KoinJavaComponent.inject(Json::class.java)
    private var fileObserver: FileObserver = getFileObserver(transactionsDataFile)

    fun writeTransactionsLayoutFile(
        transactionsData: GetTransactionsRPC.Response,
        accountId: UUID,
    ) {
        validateTransactionsDataObserver()
        val trimmedTransactionData =
            GetTransactionsRPC.Response(transactionsData.transactions.take(50))
        val transactions = mutableMapOf<String, String>()
        val cachedData = getCachedTransactionsData()
        if (cachedData != null) {
            if (cachedData.containsKey(accountId)) {
                cachedData.map {
                    if (accountId == it.key) {
                        transactions[it.key.toString()] = Json.encodeToString(trimmedTransactionData)
                    } else {
                        transactions[it.key.toString()] = Json.encodeToString(it.value)
                    }
                }
            } else {
                cachedData.map {
                    transactions[it.key.toString()] = Json.encodeToString(it.value)
                }
                transactions["$accountId"] = Json.encodeToString(trimmedTransactionData)
            }
        } else {
            transactions["$accountId"] = Json.encodeToString(trimmedTransactionData)
        }

        val transactionsString =
            Json.encodeToString(TransactionsList.serializer(), TransactionsList(transactions))
        transactionsDataFile.writeBytes(transactionsString.toByteArray())
    }

    fun clearTransactionsDataCache() {
        context.applicationContext.deleteFile(TRANSACTIONS_DATA_FILE_NAME)
    }

    fun getCachedTransactionsData(): Map<UUID, GetTransactionsRPC.Response>? {
        validateTransactionsDataFile()
        return if (transactionsDataFile.exists()) {
            val paymentLayoutString = transactionsDataFile.readText()
            deserializeTransactionsLayout(paymentLayoutString)
        } else {
            Timber.tag(TAG).d("No cached PaymentLayout file found")
            null
        }
    }

    @Suppress("DEPRECATION")
    private fun getFileObserver(file: File): FileObserver {
        val observer = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            object : FileObserver(file, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedTransactionsData()
                }
            }
        } else {
            object : FileObserver(file.path, CLOSE_WRITE) {
                override fun onEvent(event: Int, path: String?) {
                    getCachedTransactionsData()
                }
            }
        }
        observer.startWatching()
        return observer
    }

    private fun validateTransactionsDataObserver() {
        if (!transactionsDataFile.exists()) {
            transactionsDataFile = getTransactionsDataFile()
            fileObserver = getFileObserver(transactionsDataFile)
        }
    }

    private fun getTransactionsDataFile(): File {
        val file = File(context.applicationContext.filesDir, TRANSACTIONS_DATA_FILE_NAME)
        file.createNewFile()
        return file
    }

    private fun validateTransactionsDataFile() {
        if (getTransactionsDataAgeMillis() > TRANSACTIONS_DATA_CACHE_VALIDITY_MILLIS) {
            Timber.tag(TAG).w(
                """Cached transactions data is older than
                | $TRANSACTIONS_DATA_CACHE_VALIDITY_MILLIS milli seconds, it will be cleared.
                """.trimMargin(),
            )
            clearTransactionsDataCache()
        } else {
            Timber.tag(TAG).d("Cached transaction data is valid.")
        }
    }

    private fun getTransactionsDataAgeMillis(): Long {
        return Instant.now().toEpochMilli() - transactionsDataFile.lastModified()
    }

    private fun deserializeTransactionsLayout(transactionsDataString: String): Map<UUID, GetTransactionsRPC.Response>? {
        return try {
            val transactions = Json.decodeFromString(
                TransactionsList.serializer(),
                transactionsDataString,
            ).transactions
            val updatedTransactions = mutableMapOf<UUID, GetTransactionsRPC.Response>()

            transactions.map {
                val retrievedData: GetTransactionsRPC.Response = json.decodeFromString<GetTransactionsRPC.Response>(it.value)
                updatedTransactions[UUID.fromString(it.key)] = retrievedData
            }
            updatedTransactions
        } catch (serializationException: SerializationException) {
            Timber.tag(TAG).d("Unable to serialize the object to TransactionsLayout")
            clearTransactionsDataCache()
            null
        } catch (invalidResponseException: LeoInvalidResponseException) {
            Timber.tag(TAG).d("Unable to serialize the object to TransactionsLayout")
            clearTransactionsDataCache()
            null
        }
    }
}

private const val TAG = "TransactionDataPersistor"
private const val TRANSACTIONS_DATA_FILE_NAME = "transactionsData.json"
private const val TRANSACTIONS_DATA_CACHE_VALIDITY_MILLIS = 2592000000 * 3 // 3 months.

@Serializable
data class TransactionsList(val transactions: Map<String, String>)
