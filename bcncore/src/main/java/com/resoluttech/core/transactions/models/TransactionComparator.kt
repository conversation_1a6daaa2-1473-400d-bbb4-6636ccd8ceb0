package com.resoluttech.core.transactions.models

import com.resoluttech.bcn.transactions.Transaction
import com.resoluttech.bcn.transactions.TransactionStatus

class TransactionComparator : Comparator<Transaction> {
    override fun compare(t1: Transaction, t2: Transaction): Int {
        return (getTimeEpoch(t2.status) - getTimeEpoch(t1.status)).toInt()
    }

    private fun getTimeEpoch(status: TransactionStatus): Long {
        return when (status) {
            is TransactionStatus.Success -> status.succeededAt.epochSecond
            is TransactionStatus.Failure -> status.failedAt.epochSecond
            is TransactionStatus.Cancelled -> status.cancelledAt.epochSecond
            is TransactionStatus.Expired -> status.expiredAt.epochSecond
            is TransactionStatus.Pending -> status.createdAt.epochSecond
            is TransactionStatus.Reversed -> status.reversedAt.epochSecond
        }
    }
}
