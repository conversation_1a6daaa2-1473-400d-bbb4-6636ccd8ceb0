package com.resoluttech.core.landingscreen

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcncore.R

class LandingScreenVM : ViewModel() {

    private val _notificationPermissionState: MutableLiveData<LandingScreenNotificationState> =
        MutableLiveData(LandingScreenNotificationState.Waiting)
    val notificationPermissionState: LiveData<LandingScreenNotificationState> = _notificationPermissionState

    fun onSignUpButtonClicked(navController: NavController) {
        _notificationPermissionState.postValue(LandingScreenNotificationState.Waiting)
        navController.navigate(R.id.action_landingScreenFragment_to_signupRequirementsFragment)
    }

    fun isLandingScreenAlreadyShown(navController: NavController) {
        if (LandingScreenSharedPreference.getLandingScreenAlreadyShown()) {
            onSignInButtonClicked(navController)
        }
    }

    fun onSignInButtonClicked(navController: NavController) {
        _notificationPermissionState.postValue(LandingScreenNotificationState.Waiting)
        navController.navigate(R.id.action_landingScreenFragment_to_signInFragment)
    }

    fun askForNotificationPermissionAgain() {
        _notificationPermissionState.postValue(LandingScreenNotificationState.NotificationPermissionDenied)
    }

    fun notificationPermissionPrompted() {
        _notificationPermissionState.postValue(LandingScreenNotificationState.Waiting)
    }

    fun onNotificationPermissionGranted() {
        _notificationPermissionState.postValue(LandingScreenNotificationState.NotificationPermissionGranted)
    }

    companion object {
        const val PREVIOUS_PATH: String = "com.resoluttech.core.profile.ProfileFragment"
        const val PREVIOUS_PATH_KEY: String = "previousPath"
    }
}

sealed class LandingScreenNotificationState {
    object Waiting : LandingScreenNotificationState()
    object NotificationPermissionDenied : LandingScreenNotificationState()
    object NotificationPermissionGranted : LandingScreenNotificationState()
}
