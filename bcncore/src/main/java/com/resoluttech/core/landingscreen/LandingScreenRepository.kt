package com.resoluttech.core.landingscreen

import android.content.Context
import com.resoluttech.bcncore.R

class LandingScreenRepository {

    companion object {
        fun getLandingScreenData(context: Context): List<LandingScreenData> {
            return listOf(
                LandingScreenData(
                    LandingScreenBitmapSource.DrawableSource(R.drawable.ic_logo_send_money),
                    context.getString(R.string.onboardingGlobalTransfer),
                    context.getString(R.string.onboardingGlobalTransferSubtitle),
                ),
                LandingScreenData(
                    LandingScreenBitmapSource.DrawableSource(R.drawable.ic_logo_pay_bills),
                    context.getString(R.string.onboardingMultiCurrency),
                    context.getString(R.string.onboardingMultiCurrencySubtitle),
                ),
                LandingScreenData(
                    LandingScreenBitmapSource.DrawableSource(R.drawable.ic_logo_data_security),
                    context.getString(R.string.onboardingDataSecurity),
                    context.getString(R.string.onboardingDataSecuritySubtitle),
                ),
            )
        }
    }
}

data class LandingScreenData(val clipArt: LandingScreenBitmapSource, val title: String, val subtitle: String?)

sealed class LandingScreenBitmapSource {
    data class ServerSource(val clipArtURL: String) : LandingScreenBitmapSource()
    data class DrawableSource(val clipArt: Int) : LandingScreenBitmapSource()
}
