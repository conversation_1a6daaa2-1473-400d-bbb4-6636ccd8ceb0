package com.resoluttech.core.landingscreen

import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LanguageSharedPreference
import com.resoluttech.core.utils.SupportedLocale

class SelectLanguageVM : ViewModel() {

    fun onContinueButtonClicked(locale: SupportedLocale, onSuccess: () -> Unit) {
        LanguageSharedPreference.saveLocale(locale)
        onSuccess()
    }

    fun onLocaleAlreadySelected(navController: NavController) {
        navController.navigate(R.id.action_selectLanguageFragment_to_landingScreenFragment)
    }

    fun shouldAllowLocaleSelection(): Boolean {
//        TODO: https://bcnsurya.atlassian.net/browse/AP-56
//        return LanguageSharedPreference.getLocale() != null
        return true
    }
}
