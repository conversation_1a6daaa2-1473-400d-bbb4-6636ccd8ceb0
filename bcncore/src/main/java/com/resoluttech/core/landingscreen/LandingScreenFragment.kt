package com.resoluttech.core.landingscreen

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.DialogCodes.Companion.NOTIFICATION_PERMISSION_DIALOG_CODE
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.getHighlightedText
import com.resoluttech.core.utils.hideToolbar
import com.resoluttech.core.utils.setStatusBarColor
import com.resoluttech.core.utils.setupBackPressed
import com.resoluttech.core.utils.showToolbar
import com.suryadigital.leo.libui.landingscreen.LandingScreen
import com.suryadigital.leo.libui.landingscreen.LandingScreenViewsConfigurer
import timber.log.Timber

class LandingScreenFragment :
    Fragment(),
    LandingScreen.FinishButtonListener,
    LandingScreenViewsConfigurer,
    AlertDialog.ActionListener {

    private val landingScreenVM: LandingScreenVM by navGraphViewModels(R.id.landing_screen_nav)
    private lateinit var landingScreen: LandingScreen
    private lateinit var signInLabelTV: TextView
    private lateinit var landingScreenData: List<LandingScreenData>
    private val previousPath: String? by lazy { arguments?.getString(LandingScreenVM.PREVIOUS_PATH_KEY) }
    private var signUpButtonClicked: Boolean = false

    private val startForNotificationPermissionResult =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                landingScreenVM.onNotificationPermissionGranted()
            } else if (!shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                onActionTakenOnNotificationPermissionDialog()
            } else {
                Timber.tag(TAG)
                    .e("Notification permission denied, unable to proceed.")
                landingScreenVM.askForNotificationPermissionAgain()
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.fragment_landing_screen, container, false)
        landingScreenData = LandingScreenRepository.getLandingScreenData(requireContext())
        initViews(view)
        setupLandingScreen()
        setupRegistration()
        return view
    }

    /**
     * We are supressing ConvertLambdaToReference for observer initialisation was failing with
     * "java.lang.IllegalArgumentException: Cannot add the same observer with different lifecycles" error.
     * More information can be found in this article: https://julien-bouffard.medium.com/beware-of-observer-lambdas-with-android-livedata-b27ae935b420
     */
    @Suppress("ConvertLambdaToReference")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        landingScreenVM.notificationPermissionState.observe(
            viewLifecycleOwner,
        ) {
            reactToNotificationState(it)
        }
    }

    override fun onResume() {
        super.onResume()
        setStatusBarColor(R.color.colorPrimary)
        hideToolbar()
    }

    private fun setupLandingScreen() {
        landingScreenVM.isLandingScreenAlreadyShown(findNavController())
    }

    private fun initViews(view: View) {
        landingScreen = view.findViewById(R.id.landing_screen_view)
        signInLabelTV = view.findViewById(R.id.sign_in_label_tv)
        landingScreen.launch(landingScreenData.size, this, this)
    }

    override fun onFinishButtonClicked() {
        signUpButtonClicked = true
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!checkNotificationPermissionGranted()) {
                startForNotificationPermissionResult.launch(Manifest.permission.POST_NOTIFICATIONS)
            } else {
                landingScreenVM.onNotificationPermissionGranted()
            }
        } else {
            landingScreenVM.onSignUpButtonClicked(findNavController())
        }
    }

    private fun onActionTakenOnNotificationPermissionDialog() {
        if (signUpButtonClicked) {
            landingScreenVM.onSignUpButtonClicked(findNavController())
        } else {
            landingScreenVM.onSignInButtonClicked(findNavController())
        }
    }

    private fun reactToNotificationState(notificationState: LandingScreenNotificationState) {
        when (notificationState) {
            is LandingScreenNotificationState.Waiting -> {
                handleWaiting()
            }

            is LandingScreenNotificationState.NotificationPermissionDenied -> {
                handleNotificationDenied()
            }

            is LandingScreenNotificationState.NotificationPermissionGranted -> {
                handleNotificationPermissionGranted()
            }
        }
    }

    private fun handleWaiting() {
        // Nothing to handle
    }

    private fun handleNotificationDenied() {
        val alertDialog = AlertDialog.newInstance(
            getString(R.string.alertTitleNotificationNotAvailable),
            getString(R.string.alertMessageNotificationNotAvailable),
            NOTIFICATION_PERMISSION_DIALOG_CODE,
            positiveActionLabel = getString(R.string.alertActionYes),
            negativeActionLabel = getString(R.string.alertActionCancel),
            alertDialogButtonColor = null,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun handleNotificationPermissionGranted() {
        onActionTakenOnNotificationPermissionDialog()
    }

    private fun setupRegistration() {
        signInLabelTV.text = requireContext().getHighlightedText(
            getString(R.string.signUpInAlreadyHaveAccountLabel),
            getString(R.string.signUpInSignIn),
            startStringColor = R.color.descriptionTextColor,
            highlightedStringColor = R.color.highLightedTextColor,
        )
        signInLabelTV.setOnClickListener {
            signUpButtonClicked = false
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (!checkNotificationPermissionGranted()) {
                    startForNotificationPermissionResult.launch(Manifest.permission.POST_NOTIFICATIONS)
                } else {
                    landingScreenVM.onNotificationPermissionGranted()
                }
            } else {
                landingScreenVM.onSignInButtonClicked(findNavController())
            }
        }
    }

    override fun configureLandingScreenViews(
        position: Int,
        imageView: ImageView,
        titleTV: TextView,
        subtitleTV: TextView,
    ) {
        when (val clipArt = landingScreenData[position].clipArt) {
            is LandingScreenBitmapSource.DrawableSource -> {
                imageView.setImageResource(clipArt.clipArt)
                imageView.addContentDescriptionString(R.string.emptyString, requireContext())
            }

            is LandingScreenBitmapSource.ServerSource -> {
                // TODO https://app.clubhouse.io/resolut-tech/story/3269/use-data-from-server-to-show-the-data-in-landing-screen
            }
        }
        handleText(titleTV, subtitleTV, position)
        if (position == 0) {
            setupBackPressed(requireActivity()::finish)
        } else {
            setupBackPressed(findNavController()::navigateUp)
        }
    }

    private fun handleText(titleTV: TextView, subtitleTV: TextView, position: Int) {
        titleTV.text = landingScreenData[position].title
        if (landingScreenData[position].subtitle != null) {
            subtitleTV.visibility = View.VISIBLE
            subtitleTV.text = landingScreenData[position].subtitle
        } else {
            subtitleTV.visibility = View.GONE
        }
    }

    override fun onStop() {
        super.onStop()
        setStatusBarColor()
        showToolbar()
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            NOTIFICATION_PERMISSION_DIALOG_CODE -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    startForNotificationPermissionResult.launch(Manifest.permission.POST_NOTIFICATIONS)
                    landingScreenVM.notificationPermissionPrompted()
                }
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            NOTIFICATION_PERMISSION_DIALOG_CODE -> {
                landingScreenVM.notificationPermissionPrompted()
                onActionTakenOnNotificationPermissionDialog()
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun checkNotificationPermissionGranted(): Boolean {
        return ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.POST_NOTIFICATIONS,
        ) == PackageManager.PERMISSION_GRANTED
    }
}

private const val TAG = "LandingScreenFragment"
