package com.resoluttech.core.landingscreen

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.utils.LocaleManager
import com.resoluttech.core.utils.SupportedLocale
import com.resoluttech.core.utils.restartActivity
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showToolbar
import timber.log.Timber

class SelectLanguageFragment : Fragment(), LanguageSelectorAdapter.ItemClickListener {

    private lateinit var languageRV: RecyclerView
    private lateinit var continueButton: MaterialButton
    private lateinit var languages: List<LanguageSelectorAdapter.Language>
    private lateinit var languageSelectorAdapter: LanguageSelectorAdapter
    private val selectLanguageVM: SelectLanguageVM by navGraphViewModels(R.id.landing_screen_nav)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        setupLocaleSelection()
        val view = inflater.inflate(R.layout.fragment_select_language, container, false)
        setupToolbar()
        initView(view)
        setupLanguageRV()
        setupContinueButton()
        return view
    }

    private fun setupToolbar() {
        showToolbar()
        if (LandingScreenSharedPreference.getLandingScreenAlreadyShown()) {
            setDefaultToolbar(getString(R.string.languageSelectorChangeLanguage))
        } else {
            setDefaultToolbar(getString(R.string.languageSelectorTitleLabel), isBackArrowShown = false)
        }
    }

    private fun setupLocaleSelection() {
        if (selectLanguageVM.shouldAllowLocaleSelection()) {
            selectLanguageVM.onLocaleAlreadySelected(findNavController())
        } else {
            Timber.tag(TAG).i("Showing landing screens.")
        }
    }

    private fun initView(view: View) {
        continueButton = view.findViewById(R.id.continue_button)
        languageRV = view.findViewById(R.id.language_rv)
    }

    private fun setupContinueButton() {
        continueButton.setOnClickListener {
            selectLanguageVM.onContinueButtonClicked(
                LocaleManager.getCurrentLocale(requireContext()),
                this::restartActivity,
            )
        }
    }

    private fun setupLanguageRV() {
        languages = listOf(
            LanguageSelectorAdapter.Language(
                getString(R.string.languageSelectorOptionEnglish),
                SupportedLocale.EN_US.name,
                R.drawable.ic_flag_uk,
            ),
            LanguageSelectorAdapter.Language(
                getString(R.string.languageSelectorOptionNyanja),
                SupportedLocale.NYANJA.name,
                R.drawable.ic_flag_malawi,
            ),
        )
        languageSelectorAdapter = LanguageSelectorAdapter(requireContext(), this)
        languageRV.adapter = languageSelectorAdapter
        languageSelectorAdapter.updateItems(
            languages,
            LocaleManager.getCurrentLocale(requireContext()).name,
        )
    }

    override fun onClick(item: LanguageSelectorAdapter.Language) {
        val locale = SupportedLocale.valueOf(item.code)
        languageSelectorAdapter.updateItems(languages, locale.name)
        LocaleManager.setLocale(locale, requireContext())
    }
}

private const val TAG = "SelectLanguageFragment"
