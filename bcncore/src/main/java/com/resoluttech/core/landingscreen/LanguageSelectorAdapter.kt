package com.resoluttech.core.landingscreen

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import coil.load
import com.resoluttech.bcncore.R

class LanguageSelectorViewHolder(view: View) : RecyclerView.ViewHolder(view) {

    private val flagIV: ImageView = view.findViewById(R.id.flag_iv)
    private val selectorIV: ImageView = view.findViewById(R.id.selector_iv)
    private val languageNameTV: TextView = view.findViewById(R.id.language_name)

    fun bind(
        item: LanguageSelectorAdapter.Language,
        listener: LanguageSelectorAdapter.ItemClickListener,
        selectedLocaleCode: String?,
    ) {
        languageNameTV.text = item.label

        flagIV.load(item.flag)

        if (item.code == selectedLocaleCode) {
            selectorIV.load(R.drawable.ic_radiobox_marked)
        } else {
            selectorIV.load(R.drawable.ic_radiobox_blank)
        }

        itemView.setOnClickListener {
            listener.onClick(item)
        }
    }
}

class LanguageSelectorAdapter(
    private val context: Context,
    private val listener: ItemClickListener,
) :
    RecyclerView.Adapter<LanguageSelectorViewHolder>() {

    private val items: MutableList<Language> = mutableListOf()
    private var selectedLocaleCode: String? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LanguageSelectorViewHolder {
        val view =
            LayoutInflater.from(context).inflate(R.layout.item_language, parent, false)
        return LanguageSelectorViewHolder(view)
    }

    override fun onBindViewHolder(holder: LanguageSelectorViewHolder, position: Int) {
        holder.bind(items[position], listener, selectedLocaleCode)
    }

    override fun getItemCount(): Int = items.size

    @SuppressLint("NotifyDataSetChanged")
    fun updateItems(items: List<Language>, selectedLocaleCode: String) {
        this.items.clear()
        this.items.addAll(items)
        this.selectedLocaleCode = selectedLocaleCode
        notifyDataSetChanged()
    }

    interface ItemClickListener {
        fun onClick(item: Language)
    }

    data class Language(
        val label: String,
        val code: String,
        val flag: Int,
    )
}
