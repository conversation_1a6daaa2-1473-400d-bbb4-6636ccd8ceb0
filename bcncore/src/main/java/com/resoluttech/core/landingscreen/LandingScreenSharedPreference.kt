package com.resoluttech.core.landingscreen

import android.content.Context
import org.koin.java.KoinJavaComponent

object LandingScreenSharedPreference {

    private const val HARDCODE_LANDING_SCREEN_PREF = false
    private val context: Context by Ko<PERSON><PERSON><PERSON>Component.inject(Context::class.java)
    private val landingScreenPref = context.getSharedPreferences(LANDING_SCREEN_SP, Context.MODE_PRIVATE)

    fun getLandingScreenAlreadyShown(): Boolean {
        return landingScreenPref.getBoolean(LANDING_SCREEN_SP_KEY, HARDCODE_LANDING_SCREEN_PREF)
    }

    fun setLandingScreenAlreadyShown(isLandingScreenShown: Boolean) {
        with(landingScreenPref.edit()) {
            putBoolean(LANDING_SCREEN_SP_KEY, isLandingScreenShown)
            commit()
        }
    }
}
private const val LANDING_SCREEN_SP: String = "resolut.landing.sp"
private const val LANDING_SCREEN_SP_KEY: String = "resolut.landing.sp.key"
