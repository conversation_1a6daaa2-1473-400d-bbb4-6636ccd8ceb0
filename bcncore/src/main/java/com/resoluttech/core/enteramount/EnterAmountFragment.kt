package com.resoluttech.core.enteramount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config.Companion.MAX_REMARKS_LENGTH
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.utils.BundleUtils
import com.resoluttech.core.utils.DialogCodes.Companion.ENTER_AMOUNT_DIALOG_CODE
import com.resoluttech.core.utils.UserSharedPreference
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.hideKeyboard
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.LimitedCharacterEditText
import timber.log.Timber

class EnterAmountFragment : BaseFragment(), AlertDialog.ActionListener, AmountEditText.ErrorListener, BaseFragment.NetworkListener {

    private lateinit var amountET: AmountEditText
    private val enterAmountVM: EnterAmountVM by navGraphViewModels(R.id.enter_amount_nav)
    private lateinit var inlineSnackBar: Snackbar
    private lateinit var publicRemarkET: LimitedCharacterEditText
    private lateinit var publicRemarkTIL: TextInputLayout
    private lateinit var nextButton: MaterialButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val rootView = inflater.inflate(R.layout.fragment_enter_amount, container, false)
        setCurrencySuffix(rootView)
        setRemarksSupport(rootView)
        setupNextButton(rootView)
        setupEnteredValues()
        return rootView
    }

    private fun setupEnteredValues() {
        arguments?.apply {
            publicRemarkET.setText(getString(ENTERED_REMARK_VALUE))
            amountET.setText(getString(ENTERED_AMOUNT_VALUE))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(
            getToolBarLabel(),
        )
        findNavController().previousBackStackEntry!!.savedStateHandle.set(
            ENTERED_AMOUNT_KEY,
            null,
        )
        networkListenerCallback = this
        enterAmountVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
    }

    private fun setRemarksSupport(rootView: View) {
        publicRemarkET = rootView.findViewById(R.id.public_remark_et)
        publicRemarkTIL = rootView.findViewById(R.id.public_remark_til)
        publicRemarkET.setTextInputLayout(publicRemarkTIL, MAX_REMARKS_LENGTH)
    }

    private fun getToolBarLabel(): String {
        return when (BundleUtils.getBundlesSerializable(requireArguments(), ENTER_AMOUNT_USE_CASE, EnterAmountUseCase::class.java)) {
            EnterAmountUseCase.LOAD_MONEY -> {
                getString(R.string.loadWalletViewTitle)
            }
            EnterAmountUseCase.ACCOUNT_TO_ACCOUNT -> {
                getString(R.string.accountToAccountViewTitle)
            }
            else -> {
                throw Exception("Navigated from other than load money and account to account flow.")
            }
        }
    }

    private fun reactToState(state: EnterAmountState) {
        when (state) {
            is EnterAmountState.AcceptInput -> {
                // Nothing to handle
            }
            is EnterAmountState.InlineError -> {
                handleInlineErrorState(state)
            }
        }
    }

    private fun handleInlineErrorState(state: EnterAmountState.InlineError) {
        when (state.uiError.type) {
            ErrorType.SNACKBAR -> {
                showInlineError(state.uiError.errorMessage)
            }
            ErrorType.DIALOG -> {
                showInlineErrorDialog(state.uiError.errorTitle, state.uiError.errorMessage)
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun showInlineErrorDialog(errorTitle: String, errorMessage: String) {
        showErrorDialog(
            title = errorTitle,
            message = errorMessage,
            dialogId = ENTER_AMOUNT_DIALOG_CODE,
        )
    }

    private fun showInlineError(errorMessage: String) {
        hideKeyboard()
        inlineSnackBar = Snackbar.make(requireView(), errorMessage, Snackbar.LENGTH_INDEFINITE)
        inlineSnackBar.let {
            it.setAction(R.string.alertActionDismiss) {
                enterAmountVM.onDismissInlineError()
            }
            it.show()
        }
    }

    private fun setCurrencySuffix(rootView: View) {
        val currencySuffixTextView = rootView.findViewById<TextView>(R.id.currency_suffix)
        currencySuffixTextView.text = UserSharedPreference.getUserPrimaryCurrency()
    }

    private fun setupNextButton(rootView: View) {
        nextButton = rootView.findViewById(R.id.next_button)
        amountET = rootView.findViewById(R.id.amount_edit_text)
        amountET.setErrorListener(this)
        nextButton.isEnabled = false
        nextButton.setOnClickListener {
            enterAmountVM.onAmountEntered(
                requireContext(),
                amountET.getFormattedAmount(),
                publicRemarkET.getInputText(),
                findNavController(),
            )
        }

        amountET.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                publicRemarkET.requestFocus()
                true
            } else {
                false
            }
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        Timber.tag(TAG).i("Positive action occurred on alert dialog having id $dialogId.")
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    companion object {
        const val ENTERED_AMOUNT_VALUE: String = "ENTERED_AMOUNT_VALUE"
        const val ENTERED_REMARK_VALUE: String = "ENTERED_REMARK_VALUE"
        const val ENTERED_AMOUNT_KEY: String = "ENTERED_AMOUNT_KEY"
        const val ENTER_AMOUNT_USE_CASE: String = "ENTER_AMOUNT_USE_CASE"
        const val REMARK: String = "REMARK"
    }

    override fun onAmountInput(isValid: Boolean) {
        if (isValid) {
            nextButton.enable()
        } else {
            nextButton.disable()
        }
    }

    override fun onNetworkAvailable() {
        enterAmountVM.onDismissInlineError()
    }
}
private const val TAG = "EnterAmountFragment"
