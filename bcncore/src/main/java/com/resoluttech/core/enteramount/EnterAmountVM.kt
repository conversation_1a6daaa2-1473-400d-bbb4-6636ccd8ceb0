package com.resoluttech.core.enteramount

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.views.FormattedAmount
import org.koin.ext.getFullName
import timber.log.Timber

class EnterAmountVM : ViewModel() {

    private val _currentState = MutableLiveData<EnterAmountState>(EnterAmountState.AcceptInput)
    val currentState: LiveData<EnterAmountState> = _currentState

    /**
     * This method should be called once the amount has been successfully acquired.
     */
    fun onAmountEntered(
        context: Context,
        formattedAmount: FormattedAmount?,
        publicRemark: String?,
        navController: NavController,
    ) {
        if (!isAmountValid(context, formattedAmount)) {
            return
        } else {
            _currentState.postValue(EnterAmountState.AcceptInput)
            navController.previousBackStackEntry!!.savedStateHandle.set(
                EnterAmountFragment.ENTERED_AMOUNT_KEY,
                formattedAmount,
            )
            navController.previousBackStackEntry!!.savedStateHandle.set(
                EnterAmountFragment.REMARK,
                publicRemark,
            )
            if (!navController.popBackStack()) {
                Timber.tag(TAG)
                    .e("Navigation to previous screen from ${this::class.getFullName()} failed.")
            }
        }
    }

    private fun isAmountValid(context: Context, formattedAmount: FormattedAmount?): Boolean {
        if (formattedAmount == null) {
            _currentState.postValue(
                EnterAmountState.InlineError(
                    UIError(
                        ErrorType.DIALOG,
                        context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                        context.getString(R.string.alertMessageTransactionAmountMissingAlert),
                    ),
                ),
            )
            return false
        }
        return true
    }

    fun onDismissInlineError() {
        _currentState.postValue(EnterAmountState.AcceptInput)
    }
}

sealed class EnterAmountState {
    object AcceptInput : EnterAmountState()
    data class InlineError(val uiError: UIError) : EnterAmountState()
}

private const val TAG = "EnterAmountVM"
