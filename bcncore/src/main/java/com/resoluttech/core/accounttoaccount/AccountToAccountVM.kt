package com.resoluttech.core.accounttoaccount

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.ConfirmAccountToAccountRequestRPC
import com.resoluttech.bcn.transfers.CreateAccountToAccountRequestRPC
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcn.types.Currency
import com.resoluttech.bcncore.R
import com.resoluttech.core.payments.ERROR_CODE_APP_AUTHENTICATION_FAILED
import com.resoluttech.core.profile.manageaccount.ActiveAccountsItem
import com.resoluttech.core.rpcexceptionhandlers.AccountToAccountRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.utils.DateTimeType
import com.resoluttech.core.utils.SelectedAccountHelper
import com.resoluttech.core.utils.displayAmountWithCurrency
import com.resoluttech.core.utils.displayExchangeRateAmountWithCurrency
import com.resoluttech.core.utils.executeRPC
import com.resoluttech.core.utils.getFormattedDateTime
import com.resoluttech.core.utils.getLocalizedString
import com.resoluttech.core.utils.getProcessedValue
import com.resoluttech.core.utils.getUserFacingValue
import com.resoluttech.core.utils.logout
import com.resoluttech.core.views.SuccessfulTransactionDetails
import com.resoluttech.core.views.TransactionStatus
import com.resoluttech.core.views.TransactionStatusFragment
import com.resoluttech.core.views.getItemDetails
import com.suryadigital.leo.rpc.LeoRPCResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import java.time.Instant
import java.util.UUID

class AccountToAccountVM : ViewModel() {

    private var _currentState: MutableLiveData<AccountToAccountState> =
        MutableLiveData(AccountToAccountState.AcceptInput)
    val state: LiveData<AccountToAccountState> = _currentState
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val repository = AccountToAccountRepository()
    private var debitAccountSelectedIndex: Int = INITIAL_INDEX
    private var creditAccountSelectedIndex: Int = INITIAL_INDEX
    var debitAccountSelectedId: UUID? = null
    var creditAccountSelectedId: UUID? = null
    private lateinit var debitAccountCurrency: String
    private lateinit var creditAccountName: String
    private var isAccountInitialized: Boolean = false
    var isCreditAccountBeingSelected: Boolean = false
    var isDebitAccountBeingSelected: Boolean = false
    private var sendAmount: Long? = null
    private var responseData: CreateAccountToAccountRequestRPC.Response? = null
    private lateinit var debitWalletDetails: AccountToAccountSharedPrefHelper.AccountToAccountSelectedWallet
    private lateinit var creditWalletDetails: AccountToAccountSharedPrefHelper.AccountToAccountSelectedWallet

    private val listOfActiveAccounts: MutableList<ActiveAccountsItem> = mutableListOf()
    private val selectedAccount = SelectedAccountHelper.getSelectedAccount()

    fun onPayNowTapped(navController: NavController, context: Context) {
        if (
            (
                (debitAccountSelectedIndex != INITIAL_INDEX && creditAccountSelectedIndex != INITIAL_INDEX) ||
                    (debitAccountSelectedIndex != LAST_INDEX && creditAccountSelectedIndex != LAST_INDEX)
                ) &&
            debitAccountSelectedId != null && creditAccountSelectedId != null
        ) {
            _currentState.postValue(AccountToAccountState.RequestAuthentication)
            navController.navigate(R.id.action_accountToAccountFragment_to_auth_provider_nav)
        } else {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleReceiverAccountNotSelected),
                context.getString(R.string.alertMessageReceiverAccountNotSelected),
            ).showError()
        }
    }

    fun onUserAuthenticationSuccess(context: Context, enteredAmount: String, remark: String?) {
        _currentState.postValue(AccountToAccountState.Loading)
        if (enteredAmount.isEmpty()) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                context.getString(R.string.alertMessageTransactionAmountMissingAlert),
            ).showError()
            return
        }

        val amount = Amount(
            enteredAmount.toDouble().getProcessedValue(),
            Currency(debitAccountCurrency),
        )
        val creditAccount =
            (listOfActiveAccounts[creditAccountSelectedIndex] as ActiveAccountsItem.Account)
        val debitedAccount =
            (listOfActiveAccounts[debitAccountSelectedIndex] as ActiveAccountsItem.Account)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    val response = repository.createAccountToAccountTransferRequest(
                        amount,
                        UUID.fromString(debitedAccount.accountId),
                        UUID.fromString(creditAccount.accountId),
                    )
                    when (response) {
                        is LeoRPCResult.LeoResponse -> {
                            responseData = response.response
                            handleCreateRequestSuccessResponse(
                                context,
                                enteredAmount,
                                response.response,
                                remark,
                            )
                        }

                        is LeoRPCResult.LeoError -> handleCreateRequestErrorResponse(
                            context,
                            response.error,
                            creditAccount.accountName,
                            debitedAccount.accountName,
                        )
                    }
                },
                handleException = { it.showError() },
            )
        }
    }

    fun onUserAuthenticationFailed(context: Context) {
        _currentState.postValue(
            AccountToAccountState.InlineError(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleBiometryAuthenticationFailure),
                    context.getString(R.string.alertMessageBiometryAuthenticationFailure),
                    ERROR_CODE_APP_AUTHENTICATION_FAILED,
                ),
            ),
        )
    }

    fun onUserAuthenticationCancelled() {
        _currentState.postValue(AccountToAccountState.AcceptInput)
    }

    fun onAuthenticationFailed(activity: FragmentActivity?) {
        vmIOScope.launch {
            logout(activity)
        }
    }

    fun onAuthenticationSuccess() {
        _currentState.postValue(AccountToAccountState.AcceptInput)
    }

    private fun UIError.showError() {
        _currentState.postValue(AccountToAccountState.InlineError(this))
    }

    private fun handleCreateRequestErrorResponse(
        context: Context,
        error: CreateAccountToAccountRequestRPC.Error,
        creditedAccountName: String,
        debitedAccountName: String,
    ) {
        AccountToAccountRPCExceptionHandler.getCreateAccountToAccountRPCErrorMessage(
            context,
            error,
            creditedAccountName,
            debitedAccountName,
        )
            .apply {
                showError()
            }
    }

    private fun handleCreateRequestSuccessResponse(
        context: Context,
        amount: String,
        response: CreateAccountToAccountRequestRPC.Response,
        remark: String?,
    ) {
        try {
            sendAmount = amount.toDouble().getProcessedValue()
        } catch (e: NumberFormatException) {
            UIError(
                ErrorType.DIALOG,
                context.getString(R.string.alertTitleTransactionAmountMissingAlert),
                context.getString(R.string.alertMessageTransactionAmountMissingAlert),
            )
            return
        }
        val payableAmount = sendAmount!! + response.transactionFee.amount
        val confirmationData = AccountConfirmationData(
            sendingAmount = displayAmountWithCurrency(
                debitAccountCurrency,
                sendAmount!!.getUserFacingValue(),
            ),
            receiveAmount = response.receivingAmountExchangeRate?.let {
                displayAmountWithCurrency(
                    it.amount.currency.currencyCode,
                    it.amount.amount.getUserFacingValue(),
                )
            },
            exchangeRateFrom = displayExchangeRateAmountWithCurrency(
                debitAccountCurrency,
                DEFAULT_EXCHANGE_RATE_MULTIPLIER,
            ),
            exchangeRateToValue = response.receivingAmountExchangeRate?.exchangeRate?.toAmount?.let {
                displayExchangeRateAmountWithCurrency(
                    it.currency.currencyCode,
                    it.amount.getUserFacingValue(),
                )
            },
            payableAmount = displayAmountWithCurrency(
                debitAccountCurrency,
                payableAmount.getUserFacingValue(),
            ),
            transactionFee = displayAmountWithCurrency(
                debitAccountCurrency,
                response.transactionFee.amount.getUserFacingValue(),
            ),
            recordId = response.recordId.toString(),
            remark = remark,
            creditAccountName = creditAccountName,
        )
        _currentState.postValue(
            AccountToAccountState.RequestCreated(
                confirmationData,
                response.recordId,
            ),
        )
    }

    fun onAccountsAvailable() {
        if (!isAccountInitialized) {
            listOfActiveAccounts.clear()
            val selectedAccount = SelectedAccountHelper.getSelectedAccount()
            SelectedAccountHelper.getPersistedAccounts().map { account ->
                if (listOfActiveAccounts.find { it is ActiveAccountsItem.AccountHeader && it.currency.currencyCode == account.currencyCode } == null) {
                    listOfActiveAccounts.add(
                        ActiveAccountsItem.AccountHeader(
                            currency = Currency(account.currencyCode),
                        ),
                    )
                }
                listOfActiveAccounts.add(
                    ActiveAccountsItem.Account(
                        accountName = account.name,
                        balance = displayAmountWithCurrency(
                            currency = account.currencyCode,
                            userFacingAmount = account.balance.getUserFacingValue(),
                        ),
                        accountId = account.id,
                        currency = Currency(account.currencyCode),
                        isPrimary = account.isDefault,
                        isActive = true,
                        isSelected = selectedAccount?.id == account.id,
                    ),
                )
            }
            isAccountInitialized = true
        } else {
            Timber.tag(TAG)
                .d("Account list is already initialized.")
        }
    }

    fun onInlineErrorDismissed() {
        changeViewState(AccountToAccountState.AcceptInput)
    }

    private fun changeViewState(newState: AccountToAccountState) {
        _currentState.postValue(newState)
    }

    fun onRequestCreated(
        controller: NavController,
        confirmationData: AccountConfirmationData,
    ) {
        _currentState.postValue(AccountToAccountState.AcceptInput)
        val action =
            AccountToAccountPickerFragmentDirections
                .actionAccountToAccountFragmentToAccountToAccountConfirmationFragment(
                    confirmationData,
                )
        controller.navigate(action)
    }

    fun onProceedTapped(
        context: Context,
        recordId: UUID,
        remark: String?,
        accountName: String,
    ) {
        _currentState.postValue(AccountToAccountState.Loading)
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val response =
                            repository.confirmAccountToAccountTransferRequest(recordId, remark)
                    ) {
                        is LeoRPCResult.LeoResponse -> handleConfirmationSuccessResponse(
                            context,
                            response.response,
                            recordId,
                            sendAmount!!,
                            debitAccountCurrency,
                        )

                        is LeoRPCResult.LeoError -> handleConfirmationErrorResponse(
                            context,
                            response.error,
                            accountName,
                        )
                    }
                },
                handleException = {
                    it.showError()
                },
            )
        }
    }

    private fun handleConfirmationErrorResponse(
        context: Context,
        error: ConfirmAccountToAccountRequestRPC.Error,
        accountName: String,
    ) {
        AccountToAccountRPCExceptionHandler.getConfirmAccountToAccountRPCErrorMessage(
            context,
            error,
            accountName,
        )
            .apply {
                showError()
            }
    }

    private fun handleConfirmationSuccessResponse(
        context: Context,
        response: ConfirmAccountToAccountRequestRPC.Response,
        recordId: UUID,
        amount: Long,
        currency: String,
    ) {
        _currentState.postValue(AccountToAccountState.AcceptInput)
        val formattedDate = getFormattedDate(context, response.succeededAt)
        val successfulStateData = TransactionStatus.SuccessfulTransaction(
            SuccessfulTransactionDetails(
                context.getString(R.string.transactionStatusSuccessLabel),
                formattedDate,
                "$recordId",
                amount,
                currency,
                response.transactionDetail.description?.let {
                    context.getLocalizedString(
                        it.en,
                        it.ny,
                    )
                },
                response.transactionDetail.itemDetail.getItemDetails(context),
            ),
        )
        val args = Bundle()
        args.putParcelable(
            TransactionStatusFragment.TRANSACTION_DATA_KEY,
            successfulStateData,
        )
        _currentState.postValue(AccountToAccountState.RequestComplete(args))
    }

    private fun getFormattedDate(context: Context, transactionAt: Instant): String {
        return transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.TIME,
        ) + context.getString(R.string.dateTimeSeparator) + transactionAt.getFormattedDateTime(
            context = context,
            dateTimeType = DateTimeType.DATE,
        )
    }

    fun onDeclineTapped() {
        _currentState.postValue(AccountToAccountState.AcceptInput)
    }

    fun onRequestComplete(controller: NavController, transactionStatusDataBundle: Bundle) {
        controller.navigate(R.id.transaction_status_nav, transactionStatusDataBundle)
    }

    fun onCreate(context: Context) {
        vmIOScope.launch {
            if (_currentState.value != AccountToAccountState.RequestAuthentication) {
                var debitAccount = AccountToAccountSharedPrefHelper.getDebitAccountDetails()
                if (debitAccount.accountId == null && ::debitWalletDetails.isInitialized) {
                    debitAccount = debitWalletDetails
                } else {
                    debitWalletDetails = debitAccount
                }
                debitAccountSelectedId = debitAccount.accountId
                debitAccountSelectedIndex = debitAccount.accountIndex

                var creditAccount = AccountToAccountSharedPrefHelper.getCreditAccountDetails()
                if (creditAccount.accountId == null && ::creditWalletDetails.isInitialized) {
                    creditAccount = creditWalletDetails
                } else {
                    creditWalletDetails = creditAccount
                }
                creditAccountSelectedId = creditAccount.accountId
                creditAccountSelectedIndex = creditAccount.accountIndex

                // Debit Wallet

                val (debitAccountName, currencySuffix) = if (debitAccountSelectedIndex == LAST_INDEX) {
                    debitAccountSelectedId = null

                    val wallet =
                        listOfActiveAccounts[creditAccountSelectedIndex] as ActiveAccountsItem.Account
                    context.getString(R.string.selectAccountViewTitle) to wallet.currency.currencyCode
                } else if (debitAccountSelectedIndex != INITIAL_INDEX) {
                    if (listOfActiveAccounts.size == ACCOUNTS_LIST_SIZE_FOR_TWO_ACCOUNT) {
                        val index = if (isDebitAccountBeingSelected) {
                            debitAccountSelectedIndex
                        } else {
                            positionOfSecondWallet(creditAccountSelectedIndex)
                        }
                        val wallet =
                            listOfActiveAccounts[index] as ActiveAccountsItem.Account
                        debitAccountSelectedId = UUID.fromString(wallet.accountId)
                        debitAccountCurrency = wallet.currency.currencyCode
                        debitAccountSelectedIndex = index
                        wallet.accountName to wallet.currency.currencyCode
                        setDebitAccount(
                            debitAccountSelectedId.toString(),
                            wallet.accountName,
                            debitAccountSelectedIndex,
                        )
                        wallet.accountName to wallet.currency.currencyCode
                    } else if (debitAccountSelectedId == creditAccountSelectedId && isCreditAccountBeingSelected) {
                        debitAccountSelectedId = null
                        debitAccountSelectedIndex = LAST_INDEX
                        setDebitAccount(null, null, LAST_INDEX)
                        isCreditAccountBeingSelected = false

                        val wallet =
                            listOfActiveAccounts[creditAccountSelectedIndex] as ActiveAccountsItem.Account
                        context.getString(R.string.selectAccountViewTitle) to wallet.currency.currencyCode
                    } else {
                        val wallet =
                            listOfActiveAccounts[debitAccountSelectedIndex] as ActiveAccountsItem.Account
                        debitAccountSelectedId = UUID.fromString(wallet.accountId)
                        debitAccountCurrency = wallet.currency.currencyCode
                        wallet.accountName to wallet.currency.currencyCode
                    }
                } else {
                    var wallet: ActiveAccountsItem.Account? = null
                    listOfActiveAccounts.forEachIndexed { index, account ->
                        if (account is ActiveAccountsItem.Account && account.accountId == selectedAccount?.id) {
                            wallet = account
                            debitAccountSelectedId = UUID.fromString(account.accountId)
                            debitAccountSelectedIndex = index
                        }
                    }
                    if (wallet != null) {
                        setDebitAccount(
                            debitAccountSelectedId.toString(),
                            wallet?.accountName,
                            debitAccountSelectedIndex,
                        )
                        debitAccountCurrency = wallet!!.currency.currencyCode
                        wallet!!.accountName to wallet!!.currency.currencyCode
                    } else {
                        throw IllegalStateException("Selected Wallet cannot be null")
                    }
                }

                // Credit Wallet

                val creditAccountName =
                    if (listOfActiveAccounts.size == ACCOUNTS_LIST_SIZE_FOR_TWO_ACCOUNT) {
                        val index = if (isCreditAccountBeingSelected) {
                            creditAccountSelectedIndex
                        } else {
                            positionOfSecondWallet(debitAccountSelectedIndex)
                        }
                        val wallet =
                            listOfActiveAccounts[index] as ActiveAccountsItem.Account
                        creditAccountSelectedId = UUID.fromString(wallet.accountId)
                        creditAccountName = wallet.accountName
                        creditAccountSelectedIndex = index
                        setCreditAccount(
                            creditAccountSelectedId.toString(),
                            wallet.accountName,
                            index,
                        )
                        wallet.accountName
                    } else {
                        if (creditAccountSelectedId == debitAccountSelectedId && isDebitAccountBeingSelected) {
                            isDebitAccountBeingSelected = false
                            creditAccountSelectedId = null
                            creditAccountSelectedIndex = LAST_INDEX
                            setCreditAccount(null, null, LAST_INDEX)
                            context.getString(R.string.selectAccountViewTitle)
                        } else if (creditAccountSelectedIndex != INITIAL_INDEX && creditAccountSelectedIndex != LAST_INDEX) {
                            val wallet =
                                listOfActiveAccounts[creditAccountSelectedIndex] as ActiveAccountsItem.Account
                            creditAccountSelectedId = UUID.fromString(wallet.accountId)
                            creditAccountName = wallet.accountName
                            setCreditAccount(
                                creditAccountSelectedId.toString(),
                                wallet.accountName,
                                creditAccountSelectedIndex,
                            )
                            wallet.accountName
                        } else {
                            creditAccountSelectedId = null
                            creditAccountSelectedIndex = LAST_INDEX
                            setCreditAccount(null, null, LAST_INDEX)
                            context.getString(R.string.selectAccountViewTitle)
                        }
                    }

                _currentState.postValue(
                    AccountToAccountState.Data(
                        debitWalletName = debitAccountName,
                        creditWalletName = creditAccountName,
                        currencySuffix = currencySuffix,
                    ),
                )
            }
        }
    }

    private suspend fun setCreditAccount(
        accountId: String?,
        accountName: String?,
        accountIndex: Int,
    ) {
        AccountToAccountSharedPrefHelper.setCreditAccountId(accountId)
        AccountToAccountSharedPrefHelper.setCreditAccountName(accountName)
        AccountToAccountSharedPrefHelper.setCreditAccountIndex(accountIndex)
        creditWalletDetails = AccountToAccountSharedPrefHelper.getCreditAccountDetails()
    }

    private suspend fun setDebitAccount(
        accountId: String?,
        accountName: String?,
        accountIndex: Int,
    ) {
        AccountToAccountSharedPrefHelper.setDebitAccountId(accountId)
        AccountToAccountSharedPrefHelper.setDebitAccountName(accountName)
        AccountToAccountSharedPrefHelper.setDebitAccountIndex(accountIndex)
        debitWalletDetails = AccountToAccountSharedPrefHelper.getDebitAccountDetails()
    }

    private fun positionOfSecondWallet(position: Int): Int {
        return if (position == POSITION_OF_FIRST_ACCOUNT) {
            POSITION_OF_SECOND_ACCOUNT
        } else {
            POSITION_OF_FIRST_ACCOUNT
        }
    }

    @Suppress("ConvertLambdaToReference")
    fun onDestroy() {
        // We are using CoroutineScope here and not viewModelScope because viewModelScope cancels
        // the jobs once fragment is destroyed and hence this code will not execute.
        CoroutineScope(Dispatchers.IO).launch {
            AccountToAccountSharedPrefHelper.clearPreferences()
        }
    }
}

sealed class AccountToAccountState {
    object AcceptInput : AccountToAccountState()
    object Loading : AccountToAccountState()
    object RequestAuthentication : AccountToAccountState()
    data class InlineError(val uiError: UIError) : AccountToAccountState()
    data class RequestCreated(val confirmationData: AccountConfirmationData, val recordId: UUID) :
        AccountToAccountState()

    data class RequestComplete(val transactionScreenDataBundle: Bundle) : AccountToAccountState()
    object RequestDeclined : AccountToAccountState()
    data class Data(
        val debitWalletName: String,
        val creditWalletName: String,
        val currencySuffix: String,
    ) : AccountToAccountState()
}

// accountList is having separator and currency type in it, that's why when we are having 2 account the size of accountList is 5
const val INITIAL_INDEX: Int = -1
private const val LAST_INDEX: Int = -2
private const val POSITION_OF_FIRST_ACCOUNT = 1
private const val POSITION_OF_SECOND_ACCOUNT = 2
private const val ACCOUNTS_LIST_SIZE_FOR_TWO_ACCOUNT = 3
private const val DEFAULT_EXCHANGE_RATE_MULTIPLIER = 1.0
private const val TAG = "AccountToAccountVM"
