package com.resoluttech.core.accounttoaccount

import android.graphics.Color
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.views.BaseDialogFragment
import kotlinx.parcelize.Parcelize
import java.util.UUID

class AccountToAccountConfirmationDialogFragment : BaseDialogFragment(), AlertDialog.ActionListener {

    private val accountToAccountVM: AccountToAccountVM by navGraphViewModels(R.id.account_to_account_nav)
    private lateinit var sendAmountTV: TextView
    private lateinit var receiveAmountTV: TextView
    private lateinit var exchangeFromRateTV: TextView
    private lateinit var exchangeToRateTV: TextView
    private lateinit var payableAmountTV: TextView
    private lateinit var transactionFeeTV: TextView
    private lateinit var proceedButton: MaterialButton
    private lateinit var declineButton: MaterialButton
    private lateinit var exchangeRateLayout: LinearLayout
    private lateinit var receivingAmountLayout: LinearLayout

    private val args: AccountToAccountConfirmationDialogFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view =
            inflater.inflate(R.layout.fragment_account_to_account_confirmation, container, false)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initView(view)
        setupView()
        setupButtonActions()
        isCancelable = false
        return view
    }

    private fun setupButtonActions() {
        proceedButton.setOnClickListener {
            dismiss()
            accountToAccountVM.onProceedTapped(
                requireContext(),
                UUID.fromString(args.confirmationData.recordId),
                args.confirmationData.remark,
                args.confirmationData.creditAccountName,
            )
        }
        declineButton.setOnClickListener {
            dismiss()
            accountToAccountVM.onDeclineTapped()
        }
    }

    private fun setupView() {
        args.confirmationData.apply {
            sendAmountTV.text = sendingAmount
            receiveAmountTV.text = receiveAmount
            payableAmountTV.text = payableAmount
            exchangeFromRateTV.text = exchangeRateFrom
            exchangeToRateTV.text = exchangeRateToValue
            transactionFeeTV.text = transactionFee
            if (exchangeRateToValue == null) {
                exchangeRateLayout.visibility = View.GONE
                receivingAmountLayout.visibility = View.GONE
            } else {
                exchangeRateLayout.visibility = View.VISIBLE
                receivingAmountLayout.visibility = View.VISIBLE
            }
        }
    }

    private fun initView(view: View) {
        view.apply {
            receiveAmountTV = findViewById(R.id.receive_amount_tv)
            sendAmountTV = findViewById(R.id.send_amount_tv)
            payableAmountTV = findViewById(R.id.payable_amount_tv)
            exchangeFromRateTV = findViewById(R.id.exchange_from_rate_tv)
            exchangeToRateTV = findViewById(R.id.exchange_to_rate_tv)
            transactionFeeTV = findViewById(R.id.transaction_fee_tv)
            proceedButton = findViewById(R.id.proceed_button)
            declineButton = findViewById(R.id.decline_button)
            exchangeRateLayout = findViewById(R.id.exchange_rate_layout)
            receivingAmountLayout = findViewById(R.id.receiving_amount_layout)
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        accountToAccountVM.onInlineErrorDismissed()
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }
}

@Parcelize
data class AccountConfirmationData(
    val sendingAmount: String,
    val receiveAmount: String?,
    val exchangeRateFrom: String,
    val exchangeRateToValue: String?,
    val payableAmount: String,
    val transactionFee: String,
    val recordId: String,
    val remark: String?,
    val creditAccountName: String,
) : Parcelable
