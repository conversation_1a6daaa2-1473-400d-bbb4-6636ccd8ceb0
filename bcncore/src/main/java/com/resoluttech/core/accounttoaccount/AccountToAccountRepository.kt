package com.resoluttech.core.accounttoaccount

import com.resoluttech.bcn.transfers.ConfirmAccountToAccountRequestRPC
import com.resoluttech.bcn.transfers.CreateAccountToAccountRequestRPC
import com.resoluttech.bcn.types.Amount
import com.suryadigital.leo.rpc.LeoInvalidResponseException
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.rpc.LeoServerException
import com.suryadigital.leo.rpc.LeoUnauthenticatedException
import com.suryadigital.leo.rpc.LeoUnauthorizedException
import com.suryadigital.leo.rpc.LeoUnsupportedClientException
import org.koin.java.KoinJavaComponent
import java.util.UUID

class AccountToAccountRepository {
    private val requestsRPC: CreateAccountToAccountRequestRPC by KoinJavaComponent.inject(
        CreateAccountToAccountRequestRPC::class.java,
    )

    private val confirmRPC: ConfirmAccountToAccountRequestRPC by KoinJavaComponent.inject(
        ConfirmAccountToAccountRequestRPC::class.java,
    )

    @Throws(
        LeoUnauthenticatedException::class,
        LeoUnauthorizedException::class,
        LeoUnsupportedClientException::class,
        LeoServerException::class,
        LeoInvalidResponseException::class,
    )
    suspend fun createAccountToAccountTransferRequest(
        amount: Amount,
        debitAccountIdentifier: UUID,
        creditToAccountIdentifier: UUID,
    ): LeoRPCResult<CreateAccountToAccountRequestRPC.Response, CreateAccountToAccountRequestRPC.Error> {
        return requestsRPC.execute(
            CreateAccountToAccountRequestRPC.Request(
                amount,
                debitAccountIdentifier,
                creditToAccountIdentifier,
            ),
        )
    }

    @Throws(
        LeoUnauthenticatedException::class,
        LeoUnauthorizedException::class,
        LeoUnsupportedClientException::class,
        LeoServerException::class,
        LeoInvalidResponseException::class,
    )
    suspend fun confirmAccountToAccountTransferRequest(
        recordId: UUID,
        publicRemark: String?,
    ): LeoRPCResult<ConfirmAccountToAccountRequestRPC.Response, ConfirmAccountToAccountRequestRPC.Error> {
        return confirmRPC.execute(
            ConfirmAccountToAccountRequestRPC.Request(
                recordId,
                publicRemark,
            ),
        )
    }
}
