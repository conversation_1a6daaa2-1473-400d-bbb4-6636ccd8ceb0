package com.resoluttech.core.accounttoaccount

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import org.koin.java.KoinJavaComponent
import java.util.UUID

object AccountToAccountSharedPrefHelper {
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val accoutnToAccountPref = context.getSharedPreferences(ACCOUNT_TO_ACCOUNT_PREFERENCES, Context.MODE_PRIVATE)

    @Suppress("RedundantSuspendModifier")
    suspend fun setCreditAccountName(accountName: String?) {
        with(accoutnToAccountPref.edit()) {
            putString(KEY_CREDIT_ACCOUNT_NAME, accountName)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setCreditAccountId(accountId: String?) {
        with(accoutnToAccountPref.edit()) {
            putString(KEY_CREDIT_ACCOUNT_ID, accountId)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setCreditAccountIndex(accountIndex: Int) {
        with(accoutnToAccountPref.edit()) {
            putInt(KEY_CREDIT_ACCOUNT_INDEX, accountIndex)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getCreditAccountDetails(): AccountToAccountSelectedWallet {
        val accountId = accoutnToAccountPref.getString(KEY_CREDIT_ACCOUNT_ID, null)
        return AccountToAccountSelectedWallet(
            accountName = accoutnToAccountPref.getString(KEY_CREDIT_ACCOUNT_NAME, null),
            accountId = if (accountId != null) UUID.fromString(accountId) else null,
            accountIndex = accoutnToAccountPref.getInt(KEY_CREDIT_ACCOUNT_INDEX, -1),
        )
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setDebitAccountName(accountName: String?) {
        with(accoutnToAccountPref.edit()) {
            putString(KEY_DEBIT_ACCOUNT_NAME, accountName)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setDebitAccountId(accountId: String?) {
        with(accoutnToAccountPref.edit()) {
            putString(KEY_DEBIT_ACCOUNT_ID, accountId)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun setDebitAccountIndex(accountIndex: Int) {
        with(accoutnToAccountPref.edit()) {
            putInt(KEY_DEBIT_ACCOUNT_INDEX, accountIndex)
            commit()
        }
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun getDebitAccountDetails(): AccountToAccountSelectedWallet {
        val accountId = accoutnToAccountPref.getString(KEY_DEBIT_ACCOUNT_ID, null)
        return AccountToAccountSelectedWallet(
            accountName = accoutnToAccountPref.getString(KEY_DEBIT_ACCOUNT_NAME, null),
            accountId = if (accountId != null) UUID.fromString(accountId) else null,
            accountIndex = accoutnToAccountPref.getInt(KEY_DEBIT_ACCOUNT_INDEX, -1),
        )
    }

    @Suppress("RedundantSuspendModifier")
    suspend fun clearPreferences() {
        accoutnToAccountPref.edit(action = SharedPreferences.Editor::clear)
    }

    data class AccountToAccountSelectedWallet(
        val accountName: String?,
        val accountId: UUID?,
        val accountIndex: Int,
    )
}
private const val ACCOUNT_TO_ACCOUNT_PREFERENCES = "account_to_account_preference"

private const val KEY_CREDIT_ACCOUNT_NAME = "credit_account_name"
private const val KEY_CREDIT_ACCOUNT_ID = "credit_account_id"
private const val KEY_CREDIT_ACCOUNT_INDEX = "credit_account_index"

private const val KEY_DEBIT_ACCOUNT_NAME = "debit_account_name"
private const val KEY_DEBIT_ACCOUNT_ID = "debit_account_id"
private const val KEY_DEBIT_ACCOUNT_INDEX = "debit_account_index"
