package com.resoluttech.core.accounttoaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.lifecycle.Observer
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.button.MaterialButton
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputLayout
import com.resoluttech.bcncore.R
import com.resoluttech.core.config.Config
import com.resoluttech.core.payments.ERROR_CODE_APP_AUTHENTICATION_FAILED
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.ForceOutUserDestination
import com.resoluttech.core.utils.DialogCodes
import com.resoluttech.core.utils.addContentDescriptionString
import com.resoluttech.core.utils.apppin.AppAuthenticationStatus
import com.resoluttech.core.utils.apppin.BCNAppAppAuthenticationProvider
import com.resoluttech.core.utils.disable
import com.resoluttech.core.utils.enable
import com.resoluttech.core.utils.navigateSafe
import com.resoluttech.core.utils.setDefaultToolbar
import com.resoluttech.core.utils.setImmediateBackstackDestinationId
import com.resoluttech.core.utils.showErrorDialog
import com.resoluttech.core.utils.showInlineErrorSnackBar
import com.resoluttech.core.views.AmountEditText
import com.resoluttech.core.views.BaseFragment
import com.resoluttech.core.views.LimitedCharacterEditText
import com.resoluttech.core.views.walletselector.WalletSelectorPreviousPath
import timber.log.Timber

class AccountToAccountPickerFragment :
    BaseFragment(),
    AmountEditText.ErrorListener,
    AlertDialog.ActionListener,
    AppAuthenticationStatus,
    BaseFragment.NetworkListener {
    private val accountToAccountVM: AccountToAccountVM by navGraphViewModels(R.id.account_to_account_nav)
    private lateinit var privateRemarkTIL: TextInputLayout
    private lateinit var amountET: AmountEditText
    private lateinit var privateRemarkET: LimitedCharacterEditText
    private lateinit var payNowButton: MaterialButton
    private lateinit var errorSnackBar: Snackbar
    private lateinit var currencySuffixTV: TextView

    private lateinit var itemDebitWalletSelector: ConstraintLayout
    private lateinit var debitAccountName: TextView

    private lateinit var itemCreditWalletSelector: ConstraintLayout
    private lateinit var creditAccountName: TextView

    private var isAccountSelected: Boolean = false
    private var rootView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        if (rootView == null) {
            rootView =
                inflater.inflate(R.layout.fragment_account_to_account_picker, container, false)
                    ?.apply {
                        initViews(this)
                        setupNextButtonAction()
                        setAccountList()
                        setupAuthenticationStatusListener()
                    }
        }
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(getString(R.string.accountToAccountViewTitle))
        setImmediateBackstackDestinationId(R.id.moneyScreenFragment)
        networkListenerCallback = this
        accountToAccountVM.state.observe(viewLifecycleOwner, Observer(::reactToState))
        accountToAccountVM.onCreate(requireContext())
    }

    private fun setupAuthenticationStatusListener() {
        BCNAppAppAuthenticationProvider.setListener(this)
    }

    private fun initViews(view: View) {
        view.apply {
            itemDebitWalletSelector = findViewById(R.id.item_from_wallet)
            debitAccountName = findViewById(R.id.from_wallet_name_tv)
            itemCreditWalletSelector = findViewById(R.id.item_to_wallet)
            creditAccountName = findViewById(R.id.to_wallet_name_tv)
            privateRemarkET = findViewById(R.id.private_remark_et)
            privateRemarkTIL = findViewById(R.id.private_remark_til)
            amountET = findViewById(R.id.amount_et)
            payNowButton = findViewById(R.id.pay_now_button)
            currencySuffixTV = findViewById(R.id.currency_suffix)
        }
        amountET.setErrorListener(this)
        privateRemarkET.setTextInputLayout(privateRemarkTIL, Config.MAX_REMARKS_LENGTH)
        setDebitAccountDropdown()
        setCreditAccountDropdown()
    }

    override fun onResume() {
        super.onResume()
        isAccountSelected = false
    }

    override fun onDestroy() {
        super.onDestroy()
        accountToAccountVM.onDestroy()
    }

    private fun reactToState(state: AccountToAccountState) {
        when (state) {
            is AccountToAccountState.AcceptInput -> {
                handleAcceptInputState()
            }

            is AccountToAccountState.InlineError -> {
                handleInlineErrorState(state.uiError)
            }

            is AccountToAccountState.RequestComplete -> {
                handleRequestConfirmedState(state.transactionScreenDataBundle)
            }

            is AccountToAccountState.RequestCreated -> {
                handleRequestCreatedState(state.confirmationData)
            }

            is AccountToAccountState.Loading -> {
                handleLoadingState()
            }

            is AccountToAccountState.RequestDeclined -> {
                handleRequestDeclinedState()
            }

            is AccountToAccountState.Data -> {
                handleDataState(state)
            }

            is AccountToAccountState.RequestAuthentication -> {
                handleLoadingState()
            }
        }
    }

    private fun handleRequestDeclinedState() {
        dismissProgressDialog()
    }

    private fun handleRequestConfirmedState(transactionStatusDataBundle: Bundle) {
        dismissProgressDialog()
        accountToAccountVM.onRequestComplete(findNavController(), transactionStatusDataBundle)
    }

    private fun handleLoadingState() {
        dismissProgressDialog()
        showProgressDialog(childFragmentManager, getString(R.string.alertLoading))
    }

    private fun handleRequestCreatedState(
        confirmationData: AccountConfirmationData,
    ) {
        dismissProgressDialog()
        accountToAccountVM.onRequestCreated(
            findNavController(),
            confirmationData,
        )
    }

    private fun handleAcceptInputState() {
        dismissProgressDialog()
        if (::errorSnackBar.isInitialized && errorSnackBar.isShown) {
            errorSnackBar.dismiss()
        } else {
            Timber.tag(TAG)
                .i("Snackbar is already hidden.")
        }
    }

    private fun handleInlineErrorState(error: UIError) {
        dismissProgressDialog()
        when (error.type) {
            ErrorType.SNACKBAR -> showInlineErrorSnackBar(
                error.errorMessage,
                requireView(),
                accountToAccountVM::onInlineErrorDismissed,
            )

            ErrorType.DIALOG -> {
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    error.errorCode ?: DialogCodes.ACCOUNT_TO_ACCOUNT_DIALOG_CODE,
                    forceOutUserDestination = ForceOutUserDestination.HOME,
                )
            }

            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun setupNextButtonAction() {
        payNowButton.disable()
        payNowButton.addContentDescriptionString(
            R.string.axSendMoneyAttemptPaymentDisabledHint,
            requireContext(),
        )
        payNowButton.setOnClickListener {
            accountToAccountVM.onPayNowTapped(
                navController = findNavController(),
                context = requireContext(),
            )
        }
    }

    private fun setAccountList() {
        accountToAccountVM.onAccountsAvailable()
    }

    private fun handleDataState(state: AccountToAccountState.Data) {
        debitAccountName.text = state.debitWalletName
        creditAccountName.text = state.creditWalletName
        currencySuffixTV.text = state.currencySuffix
    }

    private fun setDebitAccountDropdown() {
        itemDebitWalletSelector.setOnClickListener {
            accountToAccountVM.isCreditAccountBeingSelected = false
            accountToAccountVM.isDebitAccountBeingSelected = true
            if (accountToAccountVM.debitAccountSelectedId != null) {
                findNavController().navigateSafe(
                    "resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.WALLET_TO_WALLET_DEBIT_WALLET.name}&selectedAccountId=${accountToAccountVM.debitAccountSelectedId}".toUri(),
                    isAccountSelected,
                )
            } else {
                findNavController().navigateSafe(
                    "resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.WALLET_TO_WALLET_DEBIT_WALLET.name}".toUri(),
                    isAccountSelected,
                )
            }
            isAccountSelected = true
        }
    }

    private fun setCreditAccountDropdown() {
        itemCreditWalletSelector.setOnClickListener {
            isAccountSelected
            accountToAccountVM.isCreditAccountBeingSelected = true
            accountToAccountVM.isDebitAccountBeingSelected = false
            if (accountToAccountVM.creditAccountSelectedId != null) {
                findNavController().navigateSafe(
                    "resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.WALLET_TO_WALLET_CREDIT_WALLET.name}&selectedAccountId=${accountToAccountVM.creditAccountSelectedId}".toUri(),
                    isAccountSelected,
                )
            } else {
                findNavController().navigateSafe(
                    "resoluttech://wallet_selector/?previousPath=${WalletSelectorPreviousPath.WALLET_TO_WALLET_CREDIT_WALLET.name}".toUri(),
                    isAccountSelected,
                )
            }
            isAccountSelected = true
        }
    }

    override fun onAmountInput(isValid: Boolean) {
        payNowButton.enable(isValid)
        if (isValid) {
            payNowButton.addContentDescriptionString(
                R.string.axAccountToAccountAttemptPaymentHint,
                requireContext(),
            )
        } else {
            payNowButton.addContentDescriptionString(
                R.string.axPaymentsAttemptPaymentDisabledHint,
                requireContext(),
            )
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                accountToAccountVM.onInlineErrorDismissed()
                leoServerExceptionHandler(findNavController())
            }

            ERROR_CODE_APP_AUTHENTICATION_FAILED -> {
                accountToAccountVM.onAuthenticationFailed(activity)
            }

            else -> accountToAccountVM.onInlineErrorDismissed()
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        throw IllegalStateException("Negative action occurred on alert dialog having id $dialogId.")
    }

    override fun onNetworkAvailable() {
        accountToAccountVM.onInlineErrorDismissed()
    }

    override fun onAppAuthenticationSuccessful(navController: NavController) {
        if (accountToAccountVM.state.value is AccountToAccountState.RequestAuthentication) {
            accountToAccountVM.onUserAuthenticationSuccess(
                context = requireContext(),
                enteredAmount = amountET.getInputText(),
                remark = privateRemarkET.getInputText(),
            )
        } else {
            accountToAccountVM.onAuthenticationSuccess()
        }
    }

    override fun onAppAuthenticationFailed(navController: NavController) {
        accountToAccountVM.onUserAuthenticationFailed(context = requireContext())
    }

    override fun onAppAuthenticationCancelled(navController: NavController) {
        accountToAccountVM.onUserAuthenticationCancelled()
    }
}

private const val TAG = "AccountToAccountFragment"
