package com.resoluttech.core.db

import android.content.Context
import android.content.Context.MODE_PRIVATE
import org.koin.java.KoinJavaComponent

object TinyDB {

    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val pref = context.getSharedPreferences(FILE_NAME, MODE_PRIVATE)

    fun storeWithKey(key: String, value: String) {
        with(pref.edit()) {
            putString(key, value)
            if (!commit()) {
                throw IllegalStateException("Unable to write to tiny db")
            }
        }
    }

    fun fetchWithKey(key: String): String? {
        return pref.getString(key, null)
    }
}

private const val FILE_NAME = "com.resoluttech.private.prefs"

const val KEY_SUPPORTED_COUNTRIES = "supported_countries"
