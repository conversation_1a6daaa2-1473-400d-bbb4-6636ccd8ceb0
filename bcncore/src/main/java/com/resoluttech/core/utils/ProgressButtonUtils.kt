package com.resoluttech.core.utils

import com.google.android.material.button.MaterialButton

/**
 * An utility function to enable and disable a progress button.
 *
 * @param isValid : Tells if we have to disable/enable the button. [isValid] = true value represents enabled state.
 * */
fun MaterialButton.enable(isValid: Boolean) {
    if (isValid) {
        enable()
    } else {
        disable()
    }
}
