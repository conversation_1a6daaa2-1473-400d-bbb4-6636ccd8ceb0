package com.resoluttech.core.utils

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import androidx.navigation.NavController
import com.resoluttech.bcn.assets.Destination
import com.resoluttech.bcn.assets.FallbackDestination
import com.resoluttech.bcn.assets.ItemAction
import com.resoluttech.core.home.HomeFragmentDirections
import timber.log.Timber

fun ItemAction.executeActionWith(navController: NavController, context: Context, useCase: ItemActionHandlerUseCase) {
    try {
        handleDestination(destinationType, navController, context, useCase)
    } catch (exception: Exception) {
        Timber.tag(TAG)
            .w("Unable to handle the primary action: $destinationType, reverting to fallback action.")
        fallbackAction?.let {
            handleFallbackDestination(it, navController, context, useCase)
        } ?: run {
            Timber.tag(TAG)
                .e("Unable to handle the fallback action: $destinationType.")
        }
    }
}

@Throws(RuntimeException::class, IllegalArgumentException::class)
private fun handleDestination(
    destination: Destination,
    navController: NavController,
    context: Context,
    useCase: ItemActionHandlerUseCase,
) {
    when (destination) {
        is Destination.InAppScreen -> {
            navController.navigate(destination.destination.toUri())
        }
        is Destination.InAppBrowser -> {
            openInternalBrowser(destination.destination.toString(), navController, useCase, context.getEmptyString())
        }
        is Destination.ExternalBrowser -> {
            openExternalBrowser(destination.destination.toString(), context)
        }
    }
}

@Throws(RuntimeException::class, IllegalArgumentException::class)
private fun handleFallbackDestination(
    fallbackDestination: FallbackDestination,
    navController: NavController,
    context: Context,
    useCase: ItemActionHandlerUseCase,
) {
    when (fallbackDestination) {
        is FallbackDestination.InAppBrowser -> {
            openInternalBrowser(fallbackDestination.destination.toString(), navController, useCase, context.getEmptyString())
        }
        is FallbackDestination.ExternalBrowser -> {
            openExternalBrowser(fallbackDestination.destination.toString(), context)
        }
    }
}

private fun openInternalBrowser(url: String, navController: NavController, useCase: ItemActionHandlerUseCase, toolbarTitle: String) {
    val action = when (useCase) {
        ItemActionHandlerUseCase.HOME -> HomeFragmentDirections.actionHomeFragmentToInAppBrowserFragment(url, shouldShowBackButton = false, toolbarTitle)
        ItemActionHandlerUseCase.VIEW_ALL -> {
            /***
             * As of now we are not using this feature and hence the navigation action has been
             * removed from the Project in the following PR:
             * 'https://github.com/Resolut-Tech/Android/pull/1127'
             */
            HomeFragmentDirections.actionHomeFragmentToInAppBrowserFragment(url, shouldShowBackButton = false, toolbarTitle)
        }
    }
    navController.navigate(action)
}

private fun openExternalBrowser(url: String, context: Context) {
    val browserIntent = Intent(
        Intent.ACTION_VIEW,
        url.toUri(),
    )
    context.startActivity(browserIntent)
}

enum class ItemActionHandlerUseCase {
    HOME, VIEW_ALL
}
private const val TAG = "ItemActionHandler"
