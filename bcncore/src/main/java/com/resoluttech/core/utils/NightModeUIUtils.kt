package com.resoluttech.core.utils

import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.resoluttech.bcncore.R

/**
 * This function is used to set the background of the pull to refresh the view.
 * This function sets the background color only in night mode. In the light mode the default color will be used.
 */

fun SwipeRefreshLayout.setProgressBackground() {
    setColorSchemeResources(R.color.colorPrimaryLight)
    if (isUIModeNight(resources.configuration.uiMode)) {
        setProgressBackgroundColorSchemeResource(R.color.pullToRefreshBgColor)
    }
}
