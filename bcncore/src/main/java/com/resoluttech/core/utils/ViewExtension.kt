package com.resoluttech.core.utils

import android.content.Context
import android.view.HapticFeedbackConstants
import android.view.View
import androidx.annotation.StringRes
import timber.log.Timber

fun View.performHapticFeedback(type: HapticConstant, context: Context, tag: String) {
    if (!performHapticFeedback(
            type.value,
            HapticFeedbackConstants.FLAG_IGNORE_VIEW_SETTING,
        )
    ) {
        Timber.tag(tag).w("Haptic feedback is not enabled, attempting to perform manual vibration.")
        if (!HapticUtils.performManualVibration(context, type)) {
            LogUtils.logDeviceSpecifications(tag)
        }
    }
}

fun View.addContentDescriptionString(@StringRes resId: Int, context: Context) {
    contentDescription = context.getString(resId)
}
fun View.addContentDescriptionString(@StringRes resId: Int, context: Context, vararg formatArgs: Any?) {
    contentDescription = context.getString(resId, *formatArgs)
}

fun View.enable() {
    isEnabled = true
    alpha = 1f
}

fun View.disable() {
    isEnabled = false
    alpha = 0.65f
}
