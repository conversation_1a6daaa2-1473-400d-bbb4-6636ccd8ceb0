package com.resoluttech.core.utils

import androidx.lifecycle.ViewModel

/**
 * [maxPublicRemarkLength] is scoped to the view-model in order to prevent the alteration from any
 * other navigation graph scope. for eg. if it is scoped with send_money navigation graph then only
 * send_money flow fragments will be able to update it's value, not from any other fragment outside send
 * money navigation graph.
 * */
class CounterpartyRemarkSupportVM : ViewModel() {

    /**
     * This method will be called to reset the value of [maxPublicRemarkLength] to null.
     * */
    fun onReset() {
        maxPublicRemarkLength = null
    }

    var maxPublicRemarkLength: Int? = null
}
