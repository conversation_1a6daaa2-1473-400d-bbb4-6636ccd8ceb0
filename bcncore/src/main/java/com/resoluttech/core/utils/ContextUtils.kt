package com.resoluttech.core.utils

import android.content.Context
import android.content.res.Resources
import android.net.Uri
import com.resoluttech.bcn.types.Locale
import com.resoluttech.core.utils.LocaleManager.Statics.EN_US
import com.resoluttech.core.utils.LocaleManager.Statics.NY

fun Context.getAssetSize(resourceUri: Uri): Long? {
    try {
        val descriptor = contentResolver.openAssetFileDescriptor(resourceUri, "r")
        val size = descriptor?.length ?: return 0
        descriptor.close()
        return size
    } catch (e: Resources.NotFoundException) {
        return null
    }
}

fun Context.getCurrentLocale(): Locale {
    return when (LocaleManager.getCurrentLocale(this)) {
        SupportedLocale.EN_US -> Locale(EN_US)
        SupportedLocale.NYANJA -> Locale(NY)
    }
}
