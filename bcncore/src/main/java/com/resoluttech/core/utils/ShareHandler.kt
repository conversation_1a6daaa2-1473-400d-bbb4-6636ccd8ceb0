package com.resoluttech.core.utils

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.net.Uri
import android.os.Build
import android.widget.Toast
import androidx.core.content.FileProvider
import com.resoluttech.bcncore.R
import com.resoluttech.core.kyc.FileExtension
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File

class ShareHandler {

    /**
     * This method saves the bitmap as a file and helps in sharing the file through various sources available.
     *
     * @param bitmap: The image that needs to be shared.
     * @param activity: Activity is required to share the image.
     * @param fileName: Name for the bitmap file. The filename should not include any extension.
     * @param fileExtension: Extension of the file. This is defaulted to PNG as
     * when this was set to JPEG, the image was loosing it's quality as JPEG uses lossy compression.
     * PNG should be used if we want to maintain a good quality images while sharing as PNG uses lossless
     * compression.
     * More details here https://www.adobe.com/in/creativecloud/file-types/image/comparison/jpeg-vs-png.html#:~:text=Lossless%20vs.%20lossy%20compression.
     */
    fun shareBitmap(
        bitmap: Bitmap,
        activity: Activity,
        fileName: String,
        message: String = "",
        fileExtension: FileExtension = FileExtension.PNG,
    ) {
        val file = getScreenshotFile(fileName, activity, fileExtension)
        val stream = ByteArrayOutputStream()
//      When this was set to JPEG, the image was loosing it's quality as JPEG uses lossy compression.
//      PNG should be used if we want to maintain a good quality images while sharing as PNG uses lossless compression.
//      More details here https://www.adobe.com/in/creativecloud/file-types/image/comparison/jpeg-vs-png.html#:~:text=Lossless%20vs.%20lossy%20compression.
        bitmap.compress(CompressFormat.PNG, 100, stream)
        val byteArray: ByteArray = stream.toByteArray()
        file.writeBytes(byteArray)
        stream.flush()
        stream.close()
        shareFile(file, activity, message)
    }

    private fun shareFile(file: File, activity: Activity, message: String = "") {
        val share = Intent(Intent.ACTION_SEND)
        share.type = "image/*"
        val screenshotUri: Uri = FileProvider.getUriForFile(
            activity,
            activity.applicationContext
                .packageName + ".provider",
            file,
        )
        share.putExtra(Intent.EXTRA_STREAM, screenshotUri)
        if (message.isNotEmpty()) {
            share.putExtra(Intent.EXTRA_TEXT, message)
        }
        val chooserIntent = Intent.createChooser(share, activity.getString(R.string.showQRCodeShareCodeLabelText))

        val listSize: Int =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                activity.packageManager.queryIntentActivities(
                    chooserIntent,
                    PackageManager.ResolveInfoFlags.of(0),
                ).size
            } else {
                activity.packageManager.queryIntentActivities(
                    chooserIntent,
                    0,
                ).size
            }
        if (listSize > 0) {
            activity.startActivity(chooserIntent)
        } else {
            Toast.makeText(
                activity,
                activity.getString(R.string.toastMessageNoAppToShareImage),
                Toast.LENGTH_SHORT,
            ).show()
            Timber.tag(TAG).i("No activity found to handle the image sharing.")
        }
    }

    private fun getScreenshotFile(
        fileName: String,
        activity: Activity,
        fileExtension: FileExtension,
    ): File {
        val file = File(activity.applicationContext.filesDir, fileName + ".${fileExtension.name.lowercase()}")
        if (file.exists()) {
            // To delete the older screenshots if taken and reduce the memory usage.
            file.delete()
        }
        file.createNewFile()
        return file
    }
}

private const val TAG = "ShareHandler"
