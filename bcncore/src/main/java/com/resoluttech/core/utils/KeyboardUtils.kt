package com.resoluttech.core.utils

import android.content.Context
import android.os.Build
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.fragment.app.Fragment
import timber.log.Timber

fun Fragment.hideKeyboard() {
    val inputManager = requireContext()
        .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val focusedView: View? = requireActivity().currentFocus

    if (focusedView != null) {
        inputManager.hideSoftInputFromWindow(focusedView.windowToken, 0)
    } else {
        Timber.tag(TAG).w("No view is focused, unable to hide the soft keyboard.")
    }
}

@Suppress("DEPRECATION")
fun Fragment.showKeyboard(view: View) {
    val inputManager = requireContext()
        .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
        inputManager.showSoftInput(view, InputMethodManager.SHOW_FORCED)
    } else {
        inputManager.showSoftInput(view, 0)
    }
}

private const val TAG = "KeyboardUtils"
