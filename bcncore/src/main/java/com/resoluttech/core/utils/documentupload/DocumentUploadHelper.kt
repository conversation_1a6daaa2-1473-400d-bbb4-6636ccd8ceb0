package com.resoluttech.core.utils.documentupload

import android.content.Context
import android.net.Uri
import com.resoluttech.core.utils.getEmptyString
import com.suryadigital.leo.kedwig.APIClient
import com.suryadigital.leo.kedwig.APIClientConfiguration
import com.suryadigital.leo.kedwig.Header
import com.suryadigital.leo.kedwig.Method
import com.suryadigital.leo.kedwig.NetworkException
import com.suryadigital.leo.kedwig.OkHttpAPIClient
import com.suryadigital.leo.kedwig.Request
import com.suryadigital.leo.kedwig.Response
import com.suryadigital.leo.kedwig.TimeoutException
import com.suryadigital.leo.kedwig.request
import org.koin.java.KoinJavaComponent
import java.net.URL

object DocumentUploadHelper {

    private val networkClientConfig: NetworkClientConfig by KoinJavaComponent.inject(NetworkClientConfig::class.java)
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    const val MAX_IMAGE_WIDTH: Int = 1000
    const val MAX_IMAGE_HEIGHT: Int = 1000
    const val MIN_DOCUMENT_SIZE_KB: Int = 10
    const val MAX_DOCUMENT_SIZE_KB: Int = 5000
    const val MIN_DOCUMENT_SIZE: String = "10KB"
    const val MAX_DOCUMENT_SIZE: String = "5MB"
    const val ONE_KB_IN_BYTES: Int = 1024

    /**
     * Suppressing the `BlockingMethodInNonBlockingContext` since the method is going to get invoked
     * from Coroutines which is going to run on Dispatchers.IO which should fix the problem but
     * IntelliJ doesn't know that and hence still giving the warning.
     */
    @Suppress("BlockingMethodInNonBlockingContext", "RedundantSuspendModifier")
    suspend fun uploadDocument(url: URL, documentUri: Uri, errorBlock: () -> Unit, successBlock: (Response) -> Unit, networkErrorBlock: () -> Unit) {
        try {
            val inputStream = context.contentResolver.openInputStream(documentUri)
            val content = inputStream!!.readBytes()
            inputStream.close()
            val apiClientConfiguration = APIClientConfiguration(
                baseURL = "$url",
                logConfiguration = networkClientConfig.logConfiguration,
                defaultHeaders = networkClientConfig.header,
                connectionTimeoutMS = DOCUMENT_TIMEOUT_SECONDS,
                socketTimeoutMS = DOCUMENT_TIMEOUT_SECONDS,
            )
            val apiClient: APIClient = OkHttpAPIClient(apiClientConfiguration)
            successBlock(apiClient.sendRequest(createRequest(content)))
        } catch (e: TimeoutException) {
            errorBlock()
        } catch (e: NetworkException) {
            networkErrorBlock()
        }
    }

    private fun createRequest(content: ByteArray): Request {
        return request {
            method = Method.PUT
            path = context.getEmptyString()
            headers {
                Header("Content-Type", "multipart/form-data")
            }
            body(content)
        }
    }
}

private const val DOCUMENT_TIMEOUT_SECONDS: Long = 300_000
