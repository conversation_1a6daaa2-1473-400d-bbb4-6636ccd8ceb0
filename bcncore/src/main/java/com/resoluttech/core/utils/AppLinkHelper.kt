package com.resoluttech.core.utils

import android.content.Context
import android.net.Uri
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.navigation.NavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.ResetPasswordAlertDialog
import com.resoluttech.core.uicomponents.ResetPasswordAlertDialogButtonColor
import com.resoluttech.core.views.isLaunchedFromHistory
import timber.log.Timber

fun checkForAppLinkArguments(
    activity: FragmentActivity,
    context: Context,
    navController: Nav<PERSON><PERSON><PERSON>er,
    childFragmentManager: FragmentManager,
    isUserSignedIn: Boolean,
) {
    val uri = activity.intent.data
    if (uri != null && isLaunchedFromHistory(activity)) {
        val username = getQueryParam(uri, KEY_USER_NAME, context)
        val userId = getQueryParam(uri, KEY_USER_ID, context)
        val accountId = getQueryParam(uri, KEY_ACCOUNT_ID, context)
        val endPoint = getQueryParam(uri, KEY_ENDPOINT, context)
        if (endPoint.isNotEmpty()) {
            val resetPasswordAlertDialog =
                if (isUserSignedIn) {
                    ResetPasswordAlertDialog.newInstance(
                        context.getString(R.string.alertTitleResetPasswordDeeplink),
                        context.getString(R.string.alertMessageResetPasswordDeeplinkLoggedIn),
                        context.getString(R.string.alertActionSignOut),
                        context.getString(R.string.alertActionCancel),
                        ResetPasswordAlertDialogButtonColor(
                            context.getColor(R.color.destructiveActionColor),
                            context.getColor(R.color.colorPrimaryLight),
                        ),
                        isUserSignedIn = true,
                    )
                } else {
                    ResetPasswordAlertDialog.newInstance(
                        context.getString(R.string.alertTitleResetPasswordDeeplink),
                        context.getString(R.string.alertMessageResetPasswordDeeplinkLoggedOut),
                        context.getString(R.string.alertActionDismiss),
                        context.getEmptyString(),
                        isUserSignedIn = false,
                    )
                }
            resetPasswordAlertDialog.show(childFragmentManager, TAG)
        } else {
            if (isUserSignedIn) {
                navController.navigate("resoluttechbcn://?username=$username&userId=$userId&accountId=$accountId&shouldNavigate=true".toUri())
            }
        }
    }
    /*
        The intent data should be set to null so when user press on back button then it comes on
        home screen and do not enter the `if` and again navigates user to money transfer screen.
     */
    activity.intent.data = null
}

private fun getQueryParam(uri: Uri, paramKey: String, context: Context): String {
    return try {
        uri.getQueryParameter(paramKey) ?: context.getEmptyString()
    } catch (e: IllegalArgumentException) {
        Timber.tag(TAG).e(e)
        context.getEmptyString()
    }
}

private const val KEY_ENDPOINT = "endpoint"
private const val TAG = "CheckForAppLinkHelper"
private const val KEY_USER_NAME: String = "username"
private const val KEY_USER_ID: String = "userId"
private const val KEY_ACCOUNT_ID: String = "accountId"
