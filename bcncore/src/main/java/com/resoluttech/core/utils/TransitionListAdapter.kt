package com.resoluttech.core.utils

import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.resoluttech.bcncore.R

/**
 * An abstract list adapter that can be extended to create custom recycler view adapters,
 * which can perform transition animation.
 */
abstract class TransitionListAdapter<T, VH : RecyclerView.ViewHolder>(val items: List<T>) :
    RecyclerView.Adapter<VH>() {

    private var onItemClickListener: OnItemClickListener? = null
    private var isItemSelected = false

    /**
     * Override this method in order to bind the data to the view inflated using [onCreateViewHolder]
     * in adapter.
     * */
    abstract fun onBindView(holder: VH, position: Int)
    override fun getItemCount(): Int = items.size

    /**
     * Override the [onBindViewHolder] from [RecyclerView.Adapter] in order to provide the click
     * event and position of views in recycler view to you.
     * */
    final override fun onBindViewHolder(holder: VH, position: Int) {
        holder.apply {
            itemView.setOnClickListener {
                if (!isItemSelected) {
                    onItemClickListener?.onItemClicked(
                        position,
                        holder.itemView.findViewById(R.id.shared_view),
                    )
                }
                isItemSelected = true
            }
            onBindView(this, position)
        }
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        this.onItemClickListener = onItemClickListener
    }

    /**
     * To receive click events on list item, you must register to [OnItemClickListener].
     */
    interface OnItemClickListener {
        /**
         * Called when user clicks on list item.
         */
        fun onItemClicked(pos: Int, sharedView: ConstraintLayout? = null)
    }
}
