package com.resoluttech.core.utils

import android.view.View
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcncore.R
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialog.Companion.DIALOG_TAG
import com.resoluttech.core.uicomponents.ForceOutUserDestination

fun Fragment.showErrorDialog(
    title: String,
    message: String,
    dialogId: Int,
    positiveActionLabel: String = getString(R.string.alertActionDismiss),
    negativeActionLabel: String = requireContext().getEmptyString(),
    forceOutUserDestination: ForceOutUserDestination? = null,
) {
    val alertDialog = AlertDialog.newInstance(
        title = title,
        message,
        dialogId,
        positiveActionLabel = positiveActionLabel.uppercase(),
        negativeActionLabel = negativeActionLabel.uppercase(),
        forceOutUserDestination = forceOutUserDestination,
    )
    alertDialog.setArguments(false)
    alertDialog.show(childFragmentManager, DIALOG_TAG)
}

fun Fragment.showAlertDialog(
    title: String,
    message: String,
    dialogId: Int,
    positiveActionLabel: String = getString(R.string.alertActionDismiss),
    negativeActionLabel: String = requireContext().getEmptyString(),
) {
    val alertDialog = AlertDialog.newInstance(
        title = title,
        message,
        dialogId,
        positiveActionLabel = positiveActionLabel,
        negativeActionLabel = negativeActionLabel,
    )
    alertDialog.setArguments(false)
    alertDialog.show(childFragmentManager, DIALOG_TAG)
}

/**
 * This method shows the inline error snack bar with provided message.
 *
 * @param errorMessage: The message needs to be displayed.
 * @param rootView: View to which the snack bar will be attached.
 * @param onDismiss : This is the callback for dismissing the inline error.
 * */
fun showInlineErrorSnackBar(
    errorMessage: String,
    rootView: View,
    onDismiss: () -> Unit,
) {
    val inlineErrorSnackBar =
        Snackbar.make(rootView, errorMessage, Snackbar.LENGTH_INDEFINITE)
    inlineErrorSnackBar.let {
        it.setAction(R.string.alertActionDismiss) {
            onDismiss()
            inlineErrorSnackBar.dismiss()
        }
    }
    inlineErrorSnackBar.show()
}

class DialogCodes {
    companion object {
        const val ACCOUNT_TO_ACCOUNT_DIALOG_CODE: Int = 100
        const val HOME_FRAGMENT_DIALOG_CODE: Int = 111
        const val TRANSACTION_LIST_DIALOG_CODE: Int = 112
        const val QR_FRAGMENT_DIALOG_CODE: Int = 120
        const val TRANSACTION_STATUS_FRAGMENT_DIALOG_CODE: Int = 121
        const val SEND_MONEY_DIALOG_CODE: Int = 122
        const val ENTER_AMOUNT_DIALOG_CODE: Int = 126
        const val SIGN_IN_ERROR_CODE: Int = 127
        const val SIGN_UP_ERROR_CODE: Int = 128
        const val CHANGE_PASSWORD_ERROR_CODE: Int = 129
        const val KYC_DATA_ERROR_CODE: Int = 130
        const val PASSWORD_CHANGED_CODE: Int = 131
        const val OTP_DIALOG_ERROR_CODE: Int = 132
        const val CONTACT_PICKER_DIALOG_CODE: Int = 134
        const val RECIPIENT_IDENTIFIER_DIALOG_CODE: Int = 135
        const val ENTER_PASSWORD_CODE: Int = 136
        const val SECURITY_QUESTIONS_DIALOG_CODE: Int = 138
        const val MONEY_SCREEN_ERROR_CODE: Int = 139
        const val LOAD_WALLET_SCREEN_ERROR_CODE: Int = 140
        const val LOAD_WALLET_MONEY_FAILED: Int = 141
        const val LOAD_MONEY_INSTRUCTIONS_CODE: Int = 142
        const val RESET_SUCCESS_DIALOG: Int = 143
        const val SIGN_OUT_ERROR_CODE: Int = 144
        const val NOTIFICATION_ERROR_CODE: Int = 145
        const val BILLER_ERROR_CODE: Int = 146
        const val MANAGE_ACCOUNT_ERROR_CODE: Int = 147
        const val TRUSTED_CONTACTS_ERROR_CODE: Int = 148
        const val REMOVE_TRUSTED_CONTACTS_CODE: Int = 149
        const val REQUEST_OTP_FOR_RESET_PASSWORD_BY_TRUSTED_CONTACTS_CODE: Int = 150
        const val BILLER_ERROR_DIALOG: Int = 151
        const val CONTACT_SUPPORT_ERROR_DIALOG_ID: Int = 225
        const val LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID: Int = 226
        const val STATEMENT_ERROR_CODE: Int = 227
        const val STATEMENT_DOCUMENT_NOT_FOUND: Int = 228
        const val CONFIRM_PHONE_NUMBER_DIALOG_ID: Int = 152
        const val CONFIRM_TRUSTED_CONTACT_PHONE_NUMBER: Int = 153
        const val SIGN_UP_IN_SESSION_EXPIRED: Int = 154
        const val NOTIFICATION_PERMISSION_DIALOG_CODE: Int = 155
        const val INVALID_SIGNATURE_IMAGE_ID_DIALOG_CODE: Int = 156
        const val INVALID_PASSPORT_FRONT_ID_DIALOG_CODE: Int = 157
        const val INVALID_PASSPORT_BACK_ID_DIALOG_CODE: Int = 158
        const val INVALID_PROOF_OF_RESIDENCE_ID_DIALOG_CODE: Int = 159
        const val INVALID_NATIONAL_ID_FRONT_DIALOG_CODE: Int = 160
        const val INVALID_NATIONAL_ID_BACK_DIALOG_CODE: Int = 161
        const val SESSION_PIN_MISMATCH_DIALOG_CODE: Int = 162
        const val MARK_WALLET_INACTIVE_CODE: Int = 163
        const val WEB_VIEW_NOT_INSTALLED: Int = 164
        const val BIO_METRIC_AUTHENTICATION_FAILED_DIALOG_CODE: Int = 165
        const val QR_CODE_SCANNER_NO_INTERNET: Int = 166
        const val FORCE_USER_OUT_OF_FLOW: Int = 168
        const val ERROR_CODE_SIGN_IN_AGAIN: Int = 169
        const val KYC_DOCUMENT_UPLOAD = 170
        const val PROFILE_PICTURE_UPLOAD = 171
    }
}
