package com.resoluttech.core.utils

import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.widget.EditText

/**
 * Returns EditText text without front and trailing blank spaces, if any.
 */
fun EditText.trim(): String = text.toString().trim()

/**
 * Returns EditText text with front and trailing blank spaces, if any.
 */
fun EditText.getString(): String = text.toString()

fun EditText.selectionActionModeCallback() {
    this.customSelectionActionModeCallback = object : ActionMode.Callback {
        override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): <PERSON><PERSON><PERSON> {
            mode?.finish()
            return false
        }

        override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
            mode?.finish()
            return false
        }

        override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
            mode?.finish()
            return false
        }

        override fun onDestroyActionMode(mode: ActionMode?) {
        }
    }
    isLongClickable = false
    setTextIsSelectable(false)
    cancelLongPress()
}
