package com.resoluttech.core.utils

import android.content.res.Resources

class UnitConverter {
    companion object {
        fun Int.getDPPixelValue(): Int {
            val displayDensity = Resources.getSystem().displayMetrics.density
            return (this * displayDensity).toInt()
        }

        fun Int.getPercentValue(maxValue: Int): Float {
            return ((this * maxValue) / 100).toFloat()
        }
    }
}
