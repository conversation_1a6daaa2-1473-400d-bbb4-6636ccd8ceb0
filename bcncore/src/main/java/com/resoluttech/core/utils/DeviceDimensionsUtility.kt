package com.resoluttech.core.utils

import android.content.Context

fun getCarouselImageWidth(context: Context): Int {
    /*
        Maintaining the width of a single carousel item to 85% of device's width.
     */
    return (context.resources.displayMetrics.widthPixels * 85) / 100
}

fun getSingleCarouselImageWidth(context: Context): Int {
    /*
       This method provides a width of 97% of current screen size and is used in the case of carousel with one item, so
       that it should match the screen width and fixes the issue of left alignment in carousel recycler view.
     */
    return (context.resources.displayMetrics.widthPixels * 97) / 100
}

fun getCarouselImageHeight(context: Context): Int {
    /*
        The height of carousel item gets calculated from width of device and aspect ratio of 5/16.
        The aspect ratio is found by trial and error method to match the design.
     */
    return (getCarouselImageWidth(context) * 5) / 16
}
