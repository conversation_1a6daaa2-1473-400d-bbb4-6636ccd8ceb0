package com.resoluttech.core.utils

import android.content.Context
import android.content.res.Configuration
import com.resoluttech.bcn.assets.MultiResolutionBitmapImage
import com.resoluttech.bcn.assets.ThemedColor
import com.resoluttech.bcn.assets.ThemedImage
import org.koin.java.KoinJavaComponent
import java.net.URL

private val context: Context by KoinJavaComponent.inject(Context::class.java)

fun ThemedImage.getImage(uiMode: Int): URL {
    return if (dark != null) {
        if (isUIModeNight(uiMode)) {
            dark!!.getSpecificResolutionImageURL()
        } else {
            light.getSpecificResolutionImageURL()
        }
    } else {
        light.getSpecificResolutionImageURL()
    }
}

/**
 * This method returns image URL based on device density.
 * Default is xxxhdpi as we don't want to the upscaling of lower density images but rather to down
 * sample a higher density image.
 */
internal fun MultiResolutionBitmapImage.getSpecificResolutionImageURL(): URL {
    return when (getDeviceScreenDensity(context)) {
        in MDPI -> mdpi.imageURL
        in XHDPI -> xhdpi.imageURL
        in XXHDPI -> xxhdpi.imageURL
        else -> xxxhdpi.imageURL
    }
}

fun ThemedColor.getColor(uiMode: Int): String {
    return if (isUIModeNight(uiMode)) {
        darkColor
    } else {
        lightColor
    }
}

fun isUIModeNight(uiMode: Int): Boolean {
    return ((uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES)
}

private fun getDeviceScreenDensity(context: Context): Float =
    context.resources.displayMetrics.density

// Thanks: https://developer.android.com/training/multiscreen/screendensities#TaskProvideAltBmp
private val MDPI = 0.0F..1.0F // for density 0.0x to 1x, will remain 1x
private val XHDPI = 1.1F..2.0F // for density 1.1x to 2x, will remain 2x
private val XXHDPI = 2.1F..3.0F // for density 2.1x to 3x, will remain 3x
