package com.resoluttech.core.utils

import android.content.Context
import android.util.Log
import coil.ImageLoader
import coil.disk.DiskCache
import coil.request.CachePolicy
import coil.request.ImageRequest
import coil.util.DebugLogger
import com.resoluttech.bcncore.BuildConfig
import okhttp3.Interceptor
import okhttp3.Response
import org.koin.java.KoinJavaComponent

class CoilHelper {

    companion object {

        private val context: Context by KoinJavaComponent.inject(Context::class.java)
        val requestBuilder: ImageRequest.Builder by KoinJavaComponent.inject(ImageRequest.Builder::class.java)

        // Thanks: https://github.com/coil-kt/coil/blob/master/coil-sample/src/main/java/coil/sample/Application.kt
        val imageLoader: ImageLoader by lazy(
            ImageLoader.Builder(context)
                .crossfade(true)
                .memoryCachePolicy(CachePolicy.DISABLED)
                .diskCache(
                    DiskCache.Builder()
                        .directory(context.cacheDir.resolve("image_cache"))::build,
                )
                .apply {
                    // Enable logging to the standard Android log if this is a debug build.
                    if (BuildConfig.DEBUG) {
                        logger(DebugLogger(Log.VERBOSE))
                    }
                }::build,
        )

        // Thanks: https://github.com/coil-kt/coil/blob/master/coil-sample/src/main/java/coil/sample/ResponseHeaderInterceptor.kt
        private class ResponseHeaderInterceptor(
            private val name: String,
            private val value: String,
        ) : Interceptor {

            override fun intercept(chain: Interceptor.Chain): Response {
                val response = chain.proceed(chain.request())
                return response.newBuilder().header(name, value).build()
            }
        }
    }
}
