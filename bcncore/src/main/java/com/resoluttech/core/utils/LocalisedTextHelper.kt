package com.resoluttech.core.utils

import com.resoluttech.bcn.assets.LocalizedText

fun LocalizedText.localisedText(currentLocale: SupportedLocale): String {
    return when (currentLocale) {
        SupportedLocale.EN_US -> en
        SupportedLocale.NYANJA -> ny ?: en
    }
}

fun List<LocalizedText>.localiseTextArray(currentLocale: SupportedLocale): ArrayList<String> {
    val list: ArrayList<String> = arrayListOf()
    forEach {
        list.add(it.localisedText(currentLocale))
    }
    return list
}
