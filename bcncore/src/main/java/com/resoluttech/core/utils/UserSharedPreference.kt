package com.resoluttech.core.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import java.lang.IllegalStateException

class UserSharedPreference {
    companion object {

        private const val HOME_USER_SP_FILE: String = "user.sp_file"
        private const val HOME_USER_KEY: String = "user.sp_key"
        private const val SHARE_REPORT_FILE: String = "report.sp_file"
        private const val SHARE_REPORT_KEY: String = "report.sp_key"
        private val context: Context by KoinJavaComponent.inject(Context::class.java)
        private val userPref = context.getSharedPreferences(HOME_USER_SP_FILE, Context.MODE_PRIVATE)
        private val shareReportPref = context.getSharedPreferences(SHARE_REPORT_FILE, Context.MODE_PRIVATE)

        fun saveUser(user: User) {
            val userString =
                Json.encodeToString(User.serializer(), user)
            with(userPref.edit()) {
                putString(HOME_USER_KEY, userString)
                apply()
            }
        }

        fun getUser(): User? {
            val user = userPref.getString(HOME_USER_KEY, null)
            return if (user != null) {
                Json.decodeFromString(
                    User.serializer(),
                    user,
                )
            } else {
                null
            }
        }

        fun getUserPrimaryCurrency(): String {
            val user = userPref.getString(HOME_USER_KEY, null)
            return if (user != null) {
                Json.decodeFromString(
                    User.serializer(),
                    user,
                ).primaryCurrency
            } else {
                throw IllegalStateException("Primary currency was not set")
            }
        }

        fun getUserCountryCurrency(): List<CountryCurrency> {
            val user = userPref.getString(HOME_USER_KEY, null)
            return if (user != null) {
                Json.decodeFromString(
                    User.serializer(),
                    user,
                ).countryCurrencies
            } else {
                throw IllegalStateException("Country currency was not set")
            }
        }

        fun getSupportedCountries(): List<Country> {
            val user = userPref.getString(HOME_USER_KEY, null)
            return if (user != null) {
                Json.decodeFromString(
                    User.serializer(),
                    user,
                ).supportedCountries
            } else {
                throw IllegalStateException("Country currency was not set")
            }
        }

        fun getCurrencyIcon(currencyCode: String): String {
            val user = userPref.getString(HOME_USER_KEY, null)
            return if (user != null) {
                Json.decodeFromString(
                    User.serializer(),
                    user,
                ).countryCurrencies.first { it.currencyCode == currencyCode }.flagURL
            } else {
                throw IllegalStateException("Country currency was not set")
            }
        }

        fun clearStoredUser() {
            userPref.edit(action = SharedPreferences.Editor::clear)
            shareReportPref.edit(action = SharedPreferences.Editor::clear)
        }

        fun saveShareReportEnableState(isEnabled: Boolean) {
            with(shareReportPref.edit()) {
                putBoolean(SHARE_REPORT_KEY, isEnabled)
                apply()
            }
        }
        fun getShareReportEnableState(): Boolean {
            return shareReportPref.getBoolean(SHARE_REPORT_KEY, true)
        }
    }
}

@Serializable
data class User(
    val userId: String,
    val name: String,
    val emailId: String?,
    val isEmailVerified: Boolean,
    val imageUrl: String,
    val phoneNumber: String,
    val primaryCurrency: String,
    val country: Country,
    val countryCurrencies: List<CountryCurrency>,
    val supportedCountries: List<Country>,
    val district: String,
)

@Serializable
data class Country(
    val displayNameEn: String,
    val displayNameNy: String?,
    val code: String,
    val phoneCode: String,
)

@Serializable
data class CountryCurrency(
    val currencyCode: String,
    val flagURL: String,
    val displayNameEn: String,
    val displayNameNy: String?,
)
