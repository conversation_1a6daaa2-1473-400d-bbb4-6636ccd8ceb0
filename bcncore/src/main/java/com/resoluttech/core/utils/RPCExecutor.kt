package com.resoluttech.core.utils

import android.content.Context
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorHandler
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.views.isNetworkConnected
import com.suryadigital.leo.kedwig.APIClientException
import com.suryadigital.leo.rpc.LeoRPCException

suspend fun executeRPC(
    context: Context,
    rpcBlock: suspend () -> Unit,
    handleException: (UIError) -> Unit,
    finallyBlock: () -> Unit = {},
) {
    try {
        if (!context.isNetworkConnected()) {
            handleException(
                UIError(
                    ErrorType.BANNER,
                    context.getString(R.string.alertTitleNoInternet),
                    context.getString(R.string.alertMessageNoInternet),
                ),
            )
        } else {
            rpcBlock()
        }
    } catch (rpcException: LeoRPCException) {
        ErrorHandler.handleRPCException(rpcException, context)?.apply {
            handleException(this)
        }
    } catch (clientException: APIClientException) {
        ErrorHandler.handleAPIClientException(clientException, context).apply {
            handleException(this)
        }
    } finally {
        finallyBlock()
    }
}
