package com.resoluttech.core.utils

import android.content.Context
import com.resoluttech.bcn.homeScreen.AccountLayout
import com.resoluttech.bcn.types.Currency
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import java.util.UUID

class SelectedAccountHelper {

    companion object {

        private val context: Context by KoinJavaComponent.inject(Context::class.java)

        /**
         * This method returns abstracted account object which is already stored, if there is no object
         * stored then it will return null.
         * */
        fun getSelectedAccount(): Account? {
            val selectedAccountId = getSelectedAccountId()
            return if (selectedAccountId != null) {
                getPersistedAccounts().firstOrNull { it.id == "$selectedAccountId" }
            } else {
                null
            }
        }

        /**
         * This method returns the index of default account for a currency, As we are not storing the actual account list,
         * We need to search the account in original account list. This method can be used as utility if you have account list
         * in hand and you want to get the index of selected account.
         *
         * @param accountList : Original account list is required to perform search on that.
         * @param currency : Currency for which primary account index is to be fetched.
         * */
        fun getDefaultAccountIndex(accountList: List<Account>, currency: Currency): Int {
            var accountIndex = 0
            val defaultAccount =
                getPersistedAccounts().firstOrNull { it.currencyCode == currency.currencyCode && it.isDefault }
            accountList.indices.forEach {
                if (defaultAccount?.id == accountList[it].id) {
                    accountIndex = it
                }
            }
            return accountIndex
        }

        private fun getSelectedAccountId(): UUID? {
            return context
                .getSharedPreferences(ACCOUNT_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
                .getString(
                    SELECTED_ACCOUNT_KEY,
                    null,
                )?.let(UUID::fromString)
        }

        /**
         * This method returns the index of selected account, As we are not storing the actual account list,
         * We need to search the account in original account list. This method can be used as utility if you have account list
         * in hand and you want to get the index of selected account.
         *
         * @param accountList : Original account list is required to perform search on that.
         * */
        fun getSelectedAccountIndex(
            accountList: List<AccountLayout>,
        ): Int {
            val selectedAccount = getSelectedAccount()
            return if (selectedAccount == null) {
                0
            } else {
                var accountIndex = 0
                accountList.indices.forEach { i ->
                    if (selectedAccount.id == accountList[i].account.accountId.toString()) {
                        accountIndex = i
                    }
                }
                return accountIndex
            }
        }

        /**
         * This method returns the account for the given account id.
         *
         * @param accountId: Account id is required to search using that.
         * */
        fun getAccountFromId(accountId: UUID): Account {
            val storedAccountList = getPersistedAccounts()
            return storedAccountList.first { account -> account.id == "$accountId" }
        }

        /**
         * This method will provide the list of stored accounts in shared preferences.
         *
         * */
        fun getPersistedAccounts(): List<Account> {
            val accountsString = context
                .getSharedPreferences(ACCOUNT_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
                .getString(
                    ACCOUNT_LIST_KEY,
                    null,
                )
            if (accountsString != null) {
                return Json.decodeFromString(AccountList.serializer(), accountsString).accounts
            } else {
                throw IllegalStateException("Account list not found in shared preferences.")
            }
        }

        /**
         * This method allow us to store the selected account id.
         *
         * @param accountId : Account id of selected account to be stored in shared preferences.
         * @param context : Context is required to access the shared preferences.
         * */
        fun setSelectedAccountId(context: Context, accountId: UUID) {
            val sharedPref =
                context.getSharedPreferences(ACCOUNT_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
            with(sharedPref.edit()) {
                putString(SELECTED_ACCOUNT_KEY, "$accountId")
                apply()
            }
        }

        fun clearSavedAccountList() {
            context.getSharedPreferences(ACCOUNT_SHARED_PREFERENCES_FILE, Context.MODE_PRIVATE)
                .edit().apply {
                    clear()
                    apply()
                }
        }

        const val ACCOUNT_SHARED_PREFERENCES_FILE: String = "ACCOUNT_SP_STATE"
        private const val SELECTED_ACCOUNT_KEY: String = "ACCOUNT_SELECTED"
        const val ACCOUNT_LIST_KEY: String = "ACCOUNT_LIST_KEY"
    }
}

@Serializable
data class AccountList(val accounts: List<Account>)

@Serializable
data class Account(
    val name: String,
    val id: String,
    val currencyCode: String,
    val isDefault: Boolean,
    val balance: Long,
)
