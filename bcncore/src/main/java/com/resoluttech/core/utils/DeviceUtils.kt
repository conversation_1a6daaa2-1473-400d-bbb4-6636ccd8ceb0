package com.resoluttech.core.utils

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import com.resoluttech.bcn.types.App
import com.resoluttech.bcn.types.DeviceInformation
import com.resoluttech.bcn.types.FrontEndPlatform
import com.resoluttech.bcn.types.PushToken
import com.resoluttech.core.config.AppBuildConfig
import org.koin.java.KoinJavaComponent
import java.net.Inet4Address
import java.net.NetworkInterface

private val appBuildConfig: AppBuildConfig by KoinJavaComponent.inject(AppBuildConfig::class.java)

@SuppressLint("HardwareIds")
fun getDeviceInformation(pushToken: String?, context: Context): DeviceInformation {
    return DeviceInformation(
        brand = Build.BRAND,
        model = Build.MODEL,
        deviceId = Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.ANDROID_ID,
        ),
        frontEndPlatform = FrontEndPlatform.ANDROID,
        osVersion = Build.VERSION.RELEASE,
        ipAddress = getIpv4HostAddress(),
        pushToken = pushToken?.let { PushToken(it, App.BCN, FrontEndPlatform.ANDROID) },
        applicationType = App.BCN,
        applicationVersion = appBuildConfig.versionName,
    )
}

private fun getIpv4HostAddress(): String? {
    NetworkInterface.getNetworkInterfaces()?.toList()?.map { networkInterface ->
        networkInterface.inetAddresses?.toList()?.find {
            !it.isLoopbackAddress && it is Inet4Address
        }?.let { return it.hostAddress }
    }
    return null
}
