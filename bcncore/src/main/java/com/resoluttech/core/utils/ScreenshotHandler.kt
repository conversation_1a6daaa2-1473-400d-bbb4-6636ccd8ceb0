package com.resoluttech.core.utils

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.Canvas
import android.widget.ScrollView
import androidx.core.graphics.createBitmap
import com.resoluttech.bcncore.R
import timber.log.Timber

class ScreenshotHandler {

    fun shareScreenshot(activity: Activity, view: ScrollView, fileName: String) {
        try {
            val bitmap = getBitmapFromView(
                view,
                view.getChildAt(0).height,
                view.getChildAt(0).width,
                activity,
            )
            ShareHandler().shareBitmap(bitmap, activity, fileName)
        } catch (e: Exception) {
            Timber.tag(TAG).e("Transaction bitmap is not generated due to $e.")
        }
    }

    private fun getBitmapFromView(
        view: ScrollView,
        totalHeight: Int,
        totalWidth: Int,
        activity: Activity,
    ): Bitmap {
        // Thanks : https://stackoverflow.com/a/19383606/5163725
        val returnedBitmap =
            createBitmap(totalWidth, totalHeight)
        val canvas = Canvas(returnedBitmap)
        val backgroundDrawable = view.background
        if (backgroundDrawable != null) {
            backgroundDrawable.draw(canvas)
        } else {
            canvas.drawColor(
                activity.getColor(R.color.windowBackground),
            )
        }
        view.draw(canvas)
        return returnedBitmap
    }
}

private const val TAG = "ScreenshotHandler"
