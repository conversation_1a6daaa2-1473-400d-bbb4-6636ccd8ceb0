package com.resoluttech.core.utils

import com.resoluttech.bcn.profile.SignOutUserRPC
import com.resoluttech.bcn.types.App
import com.resoluttech.bcn.types.FrontEndPlatform
import com.resoluttech.bcn.types.PushToken
import com.suryadigital.leo.rpc.LeoRPCResult
import org.koin.java.KoinJavaComponent

object SignOutRepository {
    private val signOutRPC: SignOutUserRPC by KoinJavaComponent.inject(SignOutUserRPC::class.java)
    suspend fun signOut(
        pushToken: String?,
        llt: String?,
    ): LeoRPCResult<SignOutUserRPC.Response, SignOutUserRPC.Error> {
        val signOut = signOutRPC.execute(
            SignOutUserRPC.Request(
                llt ?: throw IllegalStateException("LLT should not be null while signing out"),
                pushToken?.let {
                    PushToken(it, App.BCN, FrontEndPlatform.ANDROID)
                },
            ),
        )
        clearAllUserData()
        return signOut
    }
}
