package com.resoluttech.core.utils

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.suryadigital.leo.rpc.LeoUnsupportedClientException

class UnsupportedClientHandler {
    companion object {
        private var _currentState: MutableLiveData<UpdateAppViewState> =
            MutableLiveData(UpdateAppViewState.SupportedAppVersion)
        val currentState: LiveData<UpdateAppViewState> = _currentState

        /**
         * This function will validate the [cause] and if it is [LeoUnsupportedClientException]
         * it will change the live data state to [UnsupportedAppVersion], observer of [currentState]
         * can take appropriate actions
         */
        fun handleIfUnsupportedClient(cause: Throwable?) {
            if (cause is LeoUnsupportedClientException) {
                _currentState.postValue(UpdateAppViewState.UnsupportedAppVersion)
            }
        }

        fun handleIfSupportedClient() {
            _currentState.postValue(UpdateAppViewState.SupportedAppVersion)
        }

        fun handleUserOnUpdateAppScreen() {
            _currentState.postValue(UpdateAppViewState.UserOnUpdateAppScreen)
        }
    }
}

sealed class UpdateAppViewState {
    object UnsupportedAppVersion : UpdateAppViewState()
    object SupportedAppVersion : UpdateAppViewState()

    /**
     *We are changing the live data to this state so that the activity is not trapped in loop on
     * update app screen since current state would be unsupportedApp Version and observer will react
     * to that state again and again
     */
    object UserOnUpdateAppScreen : UpdateAppViewState()
}
