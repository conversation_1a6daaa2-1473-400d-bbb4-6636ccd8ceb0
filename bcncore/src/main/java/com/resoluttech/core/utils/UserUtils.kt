package com.resoluttech.core.utils

import androidx.fragment.app.FragmentActivity
import com.resoluttech.core.auth.KeyStoreHelper
import com.resoluttech.core.auth.app.AppPinHelper
import com.resoluttech.core.auth.biometric.BiometricPreferenceHelper
import com.resoluttech.core.home.HomeRepository
import com.resoluttech.core.home.MoneyScreenDataPersistor
import com.resoluttech.core.payments.PaymentDataPersistor
import com.resoluttech.core.transactions.TransactionsRepository

suspend fun logout(activity: FragmentActivity?) {
    clearAllUserData()
    activity?.apply {
        intent?.data = null
        finish()
        startActivity(activity.intent)
    }
}

suspend fun clearAllUserData() {
    KeyStoreHelper.clearAllTokens()
    HomeRepository().clearAllCachedHomeData()
    MoneyScreenDataPersistor().clearMoneyScreenDataCache()
    PaymentDataPersistor().clearBillerDataCache()
    TransactionsRepository().clearTransactionsDataCache()
    AppPinHelper.clearAppPin()
    BiometricPreferenceHelper.clearBiometricPreferences()
    UserSharedPreference.clearStoredUser()
    LanguageSharedPreference.clearStoredLocale()
}
