package com.resoluttech.core.utils.apppin

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import com.resoluttech.core.auth.app.AppPinAuthFragment

/**
 * Caller of App authentication for attempt transfer must implement this interface to receive status
 * call-backs.
 */
interface AppAuthenticationStatus {
    fun onAppAuthenticationSuccessful(navController: NavController)
    fun onAppAuthenticationFailed(navController: NavController)
    fun onAppAuthenticationCancelled(navController: NavController)
}

/**
 * This interface is responsible for route the app authentication mechanism.
 */
interface AppAuthenticator {
    /**
     * Payment screens can call this method to start App Pin/System Biometric authentication process
     * before attempting a transfer.
     *
     * @param navController: Used to navigate user to a particular authentication mechanism.
     * @param directions: Directions of source to destination.
     * @param path: Path/Flow from where user is entering the authentication process. i.e: SendMoney,
     * Cash In, etc.
     * @param listener: Status listener of the authentication. Each & every caller must implement this,
     * in order to receive authentication status callbacks.
     */
    fun authenticateUser(
        navController: NavController,
        directions: Int,
        path: String,
        listener: AppAuthenticationStatus,
    )
}

class BCNAppAppAuthenticationProvider : Fragment(), AppAuthenticationStatus {

    private val previousPath: String? by lazy { getPreviousPath(arguments) }

    override fun onStart() {
        super.onStart()
        // Choose on how to authenticate user. Later other authentication options can be added, in
        // that case we can initiate that authentication process here.
        AppPinAppAuthenticatorHelper.authenticateUser(
            findNavController(),
            R.id.action_AuthenticationProvider_to_AppPinAuthFragment,
            previousPath ?: PREVIOUS_PATH_SEND_MONEY_OR_BILLERS,
            this,
        )
    }

    override fun onAppAuthenticationSuccessful(navController: NavController) {
        navController.popBackStack()
        listener?.onAppAuthenticationSuccessful(navController)
    }

    override fun onAppAuthenticationFailed(navController: NavController) {
        navController.popBackStack()
        listener?.onAppAuthenticationFailed(navController)
    }

    override fun onAppAuthenticationCancelled(navController: NavController) {
        navController.popBackStack()
        listener?.onAppAuthenticationCancelled(navController)
    }

    companion object {
        private var listener: AppAuthenticationStatus? = null

        fun setListener(listener: AppAuthenticationStatus?) {
            this.listener = listener
        }
    }
}

/**
 * Authenticate user using AppPin/System Biometrics helper.
 */
object AppPinAppAuthenticatorHelper : AppAuthenticator {
    override fun authenticateUser(
        navController: NavController,
        directions: Int,
        path: String,
        listener: AppAuthenticationStatus,
    ) {
        AppPinAuthFragment.setAuthenticationStatusListener(listener)
        val args = Bundle()
        args.putString(KEY_PREVIOUS_PATH, path)
        navController.navigate(directions, args)
    }
}

fun getPreviousPath(args: Bundle?): String? {
    return args?.getString(KEY_PREVIOUS_PATH)
}

private const val KEY_PREVIOUS_PATH: String = "previousPath"
const val PREVIOUS_PATH_SEND_MONEY_OR_BILLERS: String = "previousPath.sendMoneyOrBillers"
