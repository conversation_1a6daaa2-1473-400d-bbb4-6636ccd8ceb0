package com.resoluttech.core.utils

import android.content.Context
import android.text.format.DateFormat
import com.resoluttech.bcncore.R
import java.lang.IllegalStateException
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

data class TimeDiff(val diffDays: Int, val diffHours: Int, val diffMinutes: Int)

fun Long.getTimeDifference(currentTime: Long): TimeDiff {
    val difference = this - currentTime

    val diffMinutes: Int = (difference / (60 * 1000) % 60).toInt()
    val diffHours: Int = (difference / (60 * 60 * 1000) % 24).toInt()
    val diffDays: Int = (difference / (24 * 60 * 60 * 1000)).toInt()

    return TimeDiff(diffDays, diffHours, diffMinutes)
}

private fun getDeviceTimeFormat(context: Context, secondsAllowed: Boolean = false): String {
    val is24HourFormat = DateFormat.is24HourFormat(context)
    return if (secondsAllowed) {
        if (is24HourFormat) {
            TIME_FORMAT_24_HRS_WITH_SECONDS
        } else {
            TIME_FORMAT_12_HRS_WITH_SECONDS
        }
    } else {
        if (is24HourFormat) {
            TIME_FORMAT_24_HRS
        } else {
            TIME_FORMAT_12_HRS
        }
    }
}

/**
 * An extension function on [Instant] which provides formatted date time with specified [DateTimeType].
 * The formatted time will respect the system date time format.
 *
 * @param context : Context is required in order to determine the system date time format.
 * @param dateTimeType: Sets type for date time formatting string as follows -
 * -[DateTimeType.DATE] It will return only formatted date string.
 * -[DateTimeType.TIME] It will return only formatted time string.
 * -[DateTimeType.TIME_DATE] It will return date time string which will be formatted as follow `Time-Date`, for example `12:34 PM 14, Nov 2020`.
 * -[DateTimeType.TIME_WITH_SECONDS] It will return date time string with seconds.
 * -[DateTimeType.HISTORY] It will return date in form of day(Monday, Tuesday,...) and date(12:34 PM 14, Nov 2020). If the date is in last 7 days,
 * The day is returned as the string, if it is older than 7 days, it is returned as date. This should be used in screens like transactions list, notification list etc.
 *
 * More details regarding the format can be found here https://bcnsurya.atlassian.net/browse/BW-123?focusedCommentId=14634.
 * @return Returns the formatted date time string.
 * */

@Throws(IllegalArgumentException::class)
fun Instant.getFormattedDateTime(
    context: Context,
    dateTimeType: DateTimeType,
): String {
    val localDate = LocalDateTime.ofInstant(this, ZoneId.systemDefault())
    return if (dateTimeType == DateTimeType.HISTORY) {
        val isOlderDate = isBefore(Instant.now().minus(HISTORY_SUPPORT_DAY_VALUE, ChronoUnit.DAYS))
        localDate.toLocalDate().handleHistoryDateTimePatter(
            context = context,
            isOlderDate = isOlderDate,
            dayPatternHandler = {
                val dateFormatter = DateTimeFormatter.ofPattern(DAY)
                localDate.format(dateFormatter)
            },
            oldDateTimePatternHandler = {
                val dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT)
                localDate.format(dateFormatter)
            },
        )
    } else {
        val pattern = getPattern(dateTimeType, context)
        val dateFormatter = DateTimeFormatter.ofPattern(pattern)
        return localDate.format(dateFormatter)
    }
}

@Throws(IllegalArgumentException::class)
fun LocalDate.getFormattedDateTime(
    context: Context,
    dateTimeType: DateTimeType,
): String {
    return if (dateTimeType == DateTimeType.HISTORY) {
        val isOlderDate = isBefore(LocalDate.now().minusDays(HISTORY_SUPPORT_DAY_VALUE))
        handleHistoryDateTimePatter(
            context = context,
            dayPatternHandler = {
                val dateFormatter = DateTimeFormatter.ofPattern(DAY)
                format(dateFormatter)
            },
            oldDateTimePatternHandler = {
                val dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT)
                format(dateFormatter)
            },
            isOlderDate = isOlderDate,
        )
    } else {
        val pattern = getPattern(dateTimeType, context)
        val dateFormatter = DateTimeFormatter.ofPattern(pattern)
        return format(dateFormatter)
    }
}

private fun LocalDate.handleHistoryDateTimePatter(
    context: Context,
    isOlderDate: Boolean,
    oldDateTimePatternHandler: () -> String,
    dayPatternHandler: () -> String,
): String {
    return if (isOlderDate) {
        oldDateTimePatternHandler()
    } else {
        if (this == LocalDate.now()) {
            context.getString(R.string.transactionsTodayTitle)
        } else if (this == LocalDate.now().minusDays(PREVIOUS_DAY_VALUE)) {
            context.getString(R.string.transactionsYesterdayTitle)
        } else {
            dayPatternHandler()
        }
    }
}

private fun getPattern(
    dateTimeType: DateTimeType,
    context: Context,
): String {
    return when (dateTimeType) {
        DateTimeType.TIME_DATE -> {
            context.getString(
                R.string.dateTimeFormat,
                getDeviceTimeFormat(context),
                DATE_FORMAT,
            )
        }

        DateTimeType.DATE -> {
            DATE_FORMAT
        }

        DateTimeType.TIME -> getDeviceTimeFormat(context)

        DateTimeType.TIME_WITH_SECONDS -> getDeviceTimeFormat(context, true)
        DateTimeType.HISTORY -> {
            // This is a special class and requires more validation that the normal pattern.
            throw IllegalStateException("This is special format and is already handled.")
        }
    }
}

fun String.toLocalDate(): LocalDate {
    val dateFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT)
    return LocalDate.parse(this, dateFormatter)
}

enum class DateTimeType {
    TIME_DATE,
    DATE,
    TIME,
    TIME_WITH_SECONDS,
    HISTORY,
}

private const val DATE_FORMAT = "dd, MMM yyyy"
private const val DAY = "EEEE"
private const val TIME_FORMAT_12_HRS = "hh:mm a"
private const val TIME_FORMAT_24_HRS = "HH:mm"
private const val TIME_FORMAT_12_HRS_WITH_SECONDS = "hh:mm:ss a"
private const val TIME_FORMAT_24_HRS_WITH_SECONDS = "HH:mm:ss"
private const val HISTORY_SUPPORT_DAY_VALUE = 7L
private const val PREVIOUS_DAY_VALUE = 1L
