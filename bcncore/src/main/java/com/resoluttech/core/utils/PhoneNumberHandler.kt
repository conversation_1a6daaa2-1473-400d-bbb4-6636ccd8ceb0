package com.resoluttech.core.utils

import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.suryadigital.leo.types.LeoInvalidLeoPhoneNumberException
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber

@Throws(IllegalStateException::class)
fun getValidPhoneNumber(value: String): String {
    return when {
        value.isPhoneNumberValid() -> {
            value
        }
        else -> {
            throw LeoInvalidLeoPhoneNumberException("Entered phone number: $value is invalid.")
        }
    }
}

fun isCountryCodeAttached(number: String): Boolean {
    return number.startsWith(COUNTRY_CODE_PREFIX)
}

fun String.isPhoneNumberValid(): Bo<PERSON>an {
    return try {
        LeoPhoneNumber(this)
        true
    } catch (invalidPhoneNumberException: LeoInvalidLeoPhoneNumberException) {
        Timber.tag(TAG).w("$this isn't a valid phone number.")
        false
    }
}

fun parsePhoneCode(phoneNumberStr: String): Int? {
    val phoneInstance = PhoneNumberUtil.getInstance()
    return try {
        val phoneNumber = phoneInstance.parse(phoneNumberStr, null)
        phoneNumber.countryCode
    } catch (e: NumberParseException) {
        Timber.w(message = e.stackTraceToString())
        null
    }
}

fun getPhoneNumberWithoutCountryCode(phoneNumberStr: String): String? {
    val phoneInstance = PhoneNumberUtil.getInstance()
    return try {
        val phoneNumber = phoneInstance.parse(phoneNumberStr, null)
        phoneNumber.nationalNumber.toString()
    } catch (e: NumberParseException) {
        Timber.w(message = e.stackTraceToString())
        null
    }
}

private const val COUNTRY_CODE_PREFIX = "+"
private const val TAG = "PhoneNumberHandler"
