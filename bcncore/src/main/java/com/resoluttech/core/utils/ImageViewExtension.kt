package com.resoluttech.core.utils

import android.net.Uri
import android.widget.ImageView
import coil.load
import com.resoluttech.bcncore.R
import timber.log.Timber

fun ImageView.loadImage(imageSource: Uri) {
    val request = CoilHelper.requestBuilder.data(imageSource)
        .target(
            onSuccess = {
                load(it, CoilHelper.imageLoader)
            },
            onError = {
                load(R.drawable.ic_image_placeholder)
                Timber.w("Failed to load image -> $imageSource")
            },
        ).build()
    CoilHelper.imageLoader.enqueue(request)
}

/**
 * This function scales the image uniformly so that both dimensions of the image will be equal to the
 * dimension of the Imageview .
 * */
fun ImageView.centerCrop() {
    scaleType = ImageView.ScaleType.CENTER_CROP
}
