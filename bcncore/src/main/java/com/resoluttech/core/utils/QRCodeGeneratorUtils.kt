package com.resoluttech.core.utils

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.Color
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import com.google.zxing.BarcodeFormat
import com.google.zxing.WriterException
import com.google.zxing.qrcode.QRCodeWriter
import com.resoluttech.core.transfers.config.QRCodeConfig
import com.suryadigital.leo.types.LeoPhoneNumber
import org.koin.java.KoinJavaComponent
import timber.log.Timber
import java.lang.IllegalStateException
import java.util.UUID

/*We are suppressing the lint warning as it didn't find the definition blocking and hence keep
giving warning of redundant modifier.
Caller should call this method in a coroutine, as we don't want to block Main
thread while generating the QR Code and hence the method is marked as suspending.*/
@Suppress("RedundantSuspendModifier")
suspend fun getQRCodeBitmap(qrCodeDeeplink: String): Bitmap {
    val writer = QRCodeWriter()
    try {
        val bitMatrix = writer.encode(qrCodeDeeplink, BarcodeFormat.QR_CODE, WIDTH, HEIGHT)
        val qrCodeBitmap = createBitmap(WIDTH, HEIGHT, Bitmap.Config.RGB_565)
        for (x in 0 until WIDTH) {
            for (y in 0 until HEIGHT) {
                qrCodeBitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
            }
        }
        return qrCodeBitmap
    } catch (e: WriterException) {
        throw IllegalStateException("QRCode bitmap is not generated due to $e.")
    }
}

/**
 * This method returns the QR code deeplink in the format which the BCN app accepts.
 *
 * @param userName : Represents recipient's name.
 * @param accountId : Represents a recipient's particular account.
 * */
fun getAccountQRCodeDeeplink(userName: String, accountId: UUID): String {
    val qrCodeConfig: QRCodeConfig by KoinJavaComponent.inject(
        QRCodeConfig::class.java,
    )
    return "${qrCodeConfig.scheme}://${qrCodeConfig.identifier}?username=$userName&accountId=$accountId"
}

/**
 * This method returns the QR code deeplink in the format which the Agency app accepts to create Cash In and Cash Out request.
 *
 * @param phoneNumber : Represents recipient's phone number.
 * */
fun getPhoneNumberQRCodeForCashRequestDeeplink(phoneNumber: LeoPhoneNumber): String {
    val qrCodeConfig: QRCodeConfig by KoinJavaComponent.inject(
        QRCodeConfig::class.java,
    )
    return "${qrCodeConfig.scheme}://${qrCodeConfig.identifier}?phoneNumber=${phoneNumber.value}"
}

/**
 * This method returns the QR code deeplink in the format which the BCN app accepts.
 *
 * @param userName : Represents recipient's name.
 * @param userId : Represents a BCN user.
 * */
fun getUserQRCodeDeeplink(userName: String, userId: UUID): String {
    val qrCodeConfig: QRCodeConfig by KoinJavaComponent.inject(
        QRCodeConfig::class.java,
    )
    return "${qrCodeConfig.scheme}://${qrCodeConfig.identifier}?username=$userName&userId=$userId"
}

fun shareQRCode(activity: Activity, bitmap: Bitmap, fileName: String, message: String = "") {
    try {
        ShareHandler().shareBitmap(bitmap, activity, fileName, message)
    } catch (e: Exception) {
        Timber.tag(TAG).e("QRCode bitmap cannot be shared due to $e.")
    }
}

private const val TAG = "QRCodeUtils"
private const val WIDTH = 256
private const val HEIGHT = 256
