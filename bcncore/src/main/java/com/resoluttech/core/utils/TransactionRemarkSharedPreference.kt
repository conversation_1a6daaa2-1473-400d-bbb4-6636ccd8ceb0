package com.resoluttech.core.utils

import android.content.Context
import org.koin.java.KoinJavaComponent
import java.time.Instant
import java.util.UUID

class TransactionRemarkSharedPreference {
    companion object {
        private const val TRANSACTION_SP_FILE: String = "transactions.sp_file"
        private const val TRANSACTION_RPC_SP_KEY: String = "transaction_rpc.sp_key"
        private const val TRANSACTION_ACCOUNT_ID_SP_KEY: String = "transaction_account_id.sp_key"
        private val context: Context by KoinJavaComponent.inject(Context::class.java)
        private val transactionRemarkSP =
            context.getSharedPreferences(TRANSACTION_SP_FILE, Context.MODE_PRIVATE)

        fun getTransactionRPCSuccess(): Long {
            return transactionRemarkSP.getLong(
                TRANSACTION_RPC_SP_KEY,
                Instant.now().minusSeconds(21).toEpochMilli(),
            )
        }

        fun setTransactionRPCSuccess() {
            with(transactionRemarkSP.edit()) {
                putLong(TRANSACTION_RPC_SP_KEY, Instant.now().toEpochMilli())
                apply()
            }
        }

        fun getTransactionAccountID(): String? {
            return transactionRemarkSP.getString(TRANSACTION_ACCOUNT_ID_SP_KEY, null)
        }

        fun setTransactionAccountID(uuid: UUID) {
            with(transactionRemarkSP.edit()) {
                putString(TRANSACTION_ACCOUNT_ID_SP_KEY, uuid.toString())
                apply()
            }
        }
    }
}
