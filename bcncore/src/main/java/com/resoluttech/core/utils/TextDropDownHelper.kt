package com.resoluttech.core.utils

import android.content.Context
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.uicomponents.AccountItem
import com.resoluttech.core.uicomponents.CreatableAccountListAdapter
import com.resoluttech.core.uicomponents.MultiCurrencyAccountDropdownListItem
import com.suryadigital.leo.libui.textdropdown.TextDropdown
import java.util.UUID

fun TextDropdown.setSelectionSelectedAccount(accountList: List<CreatableAccountListAdapter.CreatableAccountDropdownItem>) {
    var selectedAccountIndex = -1
    accountList.forEachIndexed { index, item ->
        when (item) {
            is CreatableAccountListAdapter.CreatableAccountDropdownItem.Account -> {
                if (item.accountId.toString() == (
                        SelectedAccountHelper.getSelectedAccount()
                            ?: throw IllegalStateException("Selected Account ID cannot be null")
                        ).id
                ) {
                    selectedAccountIndex = index
                }
            }

            CreatableAccountListAdapter.CreatableAccountDropdownItem.CreateAccount -> {
                // Ignoring this case
            }
        }
    }
    this.setSelection(selectedAccountIndex)
}

fun TextDropdown.setSelectionSelectedAccountFromList(accountList: List<Account>) {
    val selectedAccountIndex = accountList.indexOfFirst {
        it.id == (
            SelectedAccountHelper.getSelectedAccount()
                ?: throw IllegalStateException("Selected Account ID cannot be null")
            ).id
    }
    this.setSelection(selectedAccountIndex)
}

fun TextDropdown.setSelectionSelectedAccountOrDefaultAccount(
    accountList: List<Account>,
    currency: Currency,
) {
    accountList.indexOfFirst {
        it.id == (
            SelectedAccountHelper.getSelectedAccount()
                ?: throw IllegalStateException("Selected Account ID cannot be null")
            ).id
    }.let {
        if (it == -1) {
            this.setSelection(SelectedAccountHelper.getDefaultAccountIndex(accountList, currency))
        } else {
            this.setSelection(it)
        }
    }
}

fun TextDropdown.setSelectionSelectedAccountForMultiCurrencyDropdown(
    accountList: MutableList<MultiCurrencyAccountDropdownListItem>,
    currency: Currency,
) {
    val selectedAccount =
        accountList.filterIsInstance<MultiCurrencyAccountDropdownListItem.AvailableAccount>().find {
            it.accountId.toString() == (
                SelectedAccountHelper.getSelectedAccount()
                    ?: throw IllegalStateException("Selected Account ID cannot be null")
                ).id
        }
    val defaultAccount =
        accountList.filterIsInstance<MultiCurrencyAccountDropdownListItem.AvailableAccount>().find {
            it.accountId.toString() == SelectedAccountHelper.getPersistedAccounts()
                .firstOrNull { account ->
                    account.currencyCode == currency.currencyCode && account.isDefault
                }?.id
        }
    accountList.indexOfFirst {
        it == selectedAccount
    }.let {
        if (it == -1) {
            accountList.indexOfFirst { item ->
                item == defaultAccount
            }.let(this::setSelection)
        } else {
            this.setSelection(it)
        }
    }
}

fun TextDropdown.setSelectionDefaultAccount(accountList: List<Account>, defaultAccountId: String) {
    val defaultAccountIndex = accountList.indexOfFirst {
        it.id == defaultAccountId
    }
    this.setSelection(defaultAccountIndex)
}

fun getMultiCurrencyDropdownList(
    storedAccounts: List<Account>,
    acceptsCurrencies: List<String>,
    context: Context,
): List<MultiCurrencyAccountDropdownListItem> {
    val multiCurrencyAccounts: MutableList<MultiCurrencyAccountDropdownListItem> =
        mutableListOf()
    val availableCurrenciesSet = mutableSetOf<String>()
    val accountList = mutableListOf<AccountItem>()
    storedAccounts.forEach { account ->
        accountList.add(
            AccountItem(
                UUID.fromString(account.id),
                account.name,
                Currency(account.currencyCode),
            ),
        )
    }
    accountList.sortBy { it.currency.currencyCode }
    accountList.forEach { accountItem ->
        acceptsCurrencies.forEach { currency ->
            if (accountItem.currency.currencyCode == currency) {
                if (!availableCurrenciesSet.contains(currency)) {
                    val country = UserSharedPreference.getUserCountryCurrency()
                        .find { it.currencyCode == currency }
                        ?: throw IllegalStateException("User Country Currency cannot be null")
                    val displayLabel = when (LocaleManager.getCurrentLocale(context)) {
                        SupportedLocale.EN_US -> {
                            country.displayNameEn
                        }

                        SupportedLocale.NYANJA -> {
                            country.displayNameNy
                                ?: country.displayNameEn
                        }
                    }
                    multiCurrencyAccounts.add(
                        MultiCurrencyAccountDropdownListItem.AvailableAccountsHeader(
                            displayLabel,
                            Currency(currency),
                        ),
                    )
                    availableCurrenciesSet.add(currency)
                }
                multiCurrencyAccounts.add(
                    MultiCurrencyAccountDropdownListItem.AvailableAccount(
                        accountItem.name,
                        accountItem.id,
                        accountItem.currency.currencyCode,
                        showAccountId = true,
                    ),
                )
                if (accountList.last() != accountItem) {
                    multiCurrencyAccounts.add(MultiCurrencyAccountDropdownListItem.SubSeparator)
                }
            }
        }
    }
    return multiCurrencyAccounts
}
