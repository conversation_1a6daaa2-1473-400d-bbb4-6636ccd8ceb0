package com.resoluttech.core.utils

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.core.graphics.drawable.toDrawable
import com.google.android.material.button.MaterialButton
import com.hbb20.CountryCodePicker
import com.resoluttech.bcncore.R
import com.resoluttech.core.views.BaseDialogFragment
import com.resoluttech.core.views.setDialogSize

class CountryCodePickerDialog : BaseDialogFragment() {

    private lateinit var countryCodePicker: CountryCodePicker
    private lateinit var okButton: MaterialButton
    private lateinit var codeListener: CodeListener

    interface CodeListener {
        fun onCountryCodeSelection(contactWithCountryCode: String)
    }

    override fun onStart() {
        super.onStart()
        codeListener = parentFragment as CodeListener
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val view = inflater.inflate(R.layout.dialog_country_code_picker, container, false)
        dialog?.let {
            it.requestWindowFeature(Window.FEATURE_NO_TITLE)
            it.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        }
        initViews(view)
        setupOkAction()
        return view
    }

    private fun setupOkAction() {
        okButton.setOnClickListener {
            val contact = arguments?.getString(PHONE_NUMBER_KEY)
                ?: throw IllegalArgumentException("Selected code cannot be null")
            codeListener.onCountryCodeSelection(countryCodePicker.selectedCountryCodeWithPlus + contact)
            dismiss()
        }
    }

    private fun initViews(view: View) {
        countryCodePicker = view.findViewById(R.id.country_code_picker)
        var masterCountry = requireActivity().getEmptyString()
        UserSharedPreference.getSupportedCountries().forEach {
            masterCountry += "${it.code},"
        }
        val primaryCurrencyCountryCode = UserSharedPreference.getUser()?.country?.code
        countryCodePicker.setCustomMasterCountries(masterCountry)
        countryCodePicker.setCountryForNameCode(primaryCurrencyCountryCode)
        countryCodePicker.setDialogSize(requireContext())
        okButton = view.findViewById(R.id.ok_button)
    }

    companion object {
        const val PHONE_NUMBER_KEY: String = "phone_number_key"
        const val TAG: String = "country_picker_dialog"
        fun newInstance(contact: String): CountryCodePickerDialog {
            val countryCodePickerDialog = CountryCodePickerDialog()
            val bundle = Bundle()
            bundle.putString(PHONE_NUMBER_KEY, contact)
            countryCodePickerDialog.arguments = bundle
            return countryCodePickerDialog
        }
    }
}
