package com.resoluttech.core.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.core.graphics.scale
import androidx.core.util.isNotEmpty
import com.google.android.gms.vision.Frame
import com.google.android.gms.vision.barcode.Barcode
import com.google.android.gms.vision.barcode.BarcodeDetector
import com.google.zxing.NotFoundException
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper
import com.resoluttech.core.utils.documentupload.DocumentUploadHelper.ONE_KB_IN_BYTES
import timber.log.Timber
import java.io.FileNotFoundException
import java.io.InputStream
import java.security.MessageDigest

fun getByteArray(context: Context, documentUri: Uri): ByteArray {
    val inputStream = context.contentResolver.openInputStream(documentUri)
    val byteArray = inputStream!!.readBytes()
    inputStream.close()
    return byteArray
}

fun getSHA256(content: ByteArray): String {
    return MessageDigest
        .getInstance("SHA-256")
        .digest(content)
        .fold("") { str, it -> str + "%02x".format(it) }
}

fun validateDocument(uri: Uri, context: Context): Boolean {
    val assetSize = context.getAssetSize(uri)
    return if (assetSize == null) {
        false
    } else {
        // The file size should be in 10 KBs to 5 MBs
        (assetSize / ONE_KB_IN_BYTES) in DocumentUploadHelper.MIN_DOCUMENT_SIZE_KB..DocumentUploadHelper.MAX_DOCUMENT_SIZE_KB
    }
}

/**
 * Handles the QR code picked from the gallery
 * @param onBarcodeValueAvailable: This function is executed when the image contains a valid QR code.
 * @param onInvalidImageSelected: This function is executed when the image does not contain a valid QR code.
 * @param context: Context of an activity or fragment.
 * @param result: This stores the image data.
 * @param activity: Activity is required to get the image from result.
 */
fun handleQRCodeImagePickedFromGallery(
    activity: Activity,
    result: ActivityResult,
    context: Context,
    onBarcodeValueAvailable: (qrCodeValue: String) -> Unit,
    onInvalidImageSelected: (context: Context) -> Unit,
) {
    if (result.resultCode == Activity.RESULT_OK) {
        val data: Intent? = result.data
        if (data == null || data.data == null) {
            onInvalidImageSelected(context)
            return
        }
        try {
            val inputStream: InputStream? =
                activity.contentResolver?.openInputStream(data.data!!)
            if (inputStream == null) {
                onInvalidImageSelected(context)
            }
            val bitmap: Bitmap =
                BitmapFactory.decodeStream(inputStream)
            val reducedBitmap = if (bitmap.allocationByteCount > MAX_QR_CODE_SIZE) {
                // The google vision library cannot detect QR code in large images.
                getResizedBitmap(bitmap, MAX_QR_CODE_SIZE)
            } else {
                bitmap
            }
            bitmap.recycle()
            val barcodeDetector =
                BarcodeDetector.Builder(context)
                    .setBarcodeFormats(Barcode.QR_CODE).build()
            val barcodeFrame: Frame = Frame.Builder()
                .setBitmap(reducedBitmap)
                .build()
            try {
                val barcode = barcodeDetector.detect(barcodeFrame)
                if (barcode.isNotEmpty()) {
                    onBarcodeValueAvailable(barcode.valueAt(0).displayValue)
                } else {
                    onInvalidImageSelected(context)
                }
            } catch (e: IllegalArgumentException) {
                Timber.e(e)
                onInvalidImageSelected(context)
            } catch (e: NotFoundException) {
                Timber.e(e)
                onInvalidImageSelected(context)
            }
        } catch (e: FileNotFoundException) {
            Timber.e(e)
            onInvalidImageSelected(context)
        }
    }
}

fun chooseQrCodeFromGallery(imagePickerResultLauncher: ActivityResultLauncher<Intent>) {
    /***
     * We are using Intent.ACTION_GET_CONTENT because it will open systems best application to get
     * specified data in our case 'image' and will handle if all galleries application are disabled.
     * If we use Intent.ACTION_PICK and all the galleries application on system are disabled then
     * system doesn't handle this and app crashes.
     */
    val pickIntent = Intent(Intent.ACTION_GET_CONTENT)
    pickIntent.type = INTENT_TYPE
    imagePickerResultLauncher.launch(pickIntent)
}

/**
 * Reduces the size of the bitmap to specific size
 * @param bitmap
 * @param maxSize
 * @return resized bitmap
 */
fun getResizedBitmap(bitmap: Bitmap, maxSize: Int): Bitmap {
    var width = bitmap.width
    var height = bitmap.height
    val bitmapRatio = width.toFloat() / height.toFloat()
    if (bitmapRatio > 1) {
        width = maxSize
        height = (width / bitmapRatio).toInt()
    } else {
        height = maxSize
        width = (height * bitmapRatio).toInt()
    }
    return bitmap.scale(width, height)
}

private const val MAX_QR_CODE_SIZE = 1024
private const val INTENT_TYPE: String = "image/*"
