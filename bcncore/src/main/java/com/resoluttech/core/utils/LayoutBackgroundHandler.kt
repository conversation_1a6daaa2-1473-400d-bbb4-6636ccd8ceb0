package com.resoluttech.core.utils

import android.view.View
import com.resoluttech.bcn.assets.ThemedImage
import timber.log.Timber

/**
 * This function sets the background image to a View.
 *
 * @param backgroundImage: The image is to be set as the background of the View.
 */
fun View.setupLayoutBackground(backgroundImage: ThemedImage, uiMode: Int) {
    val request = CoilHelper.requestBuilder
        .data(backgroundImage.getImage(uiMode).toString())
        .target(
            onStart = {
                this.background = it
            },
            onSuccess = {
                this.background = it
            },
            onError = {
                this.background = it
                Timber.tag(TAG).e("Unable to load background image.")
            },
        )
        .build()
    CoilHelper.imageLoader.enqueue(request)
}

private const val TAG = "LayoutBackgroundHandler"
