package com.resoluttech.core.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import org.koin.java.KoinJavaComponent

object LanguageSharedPreference {

    private const val SELECT_LANGUAGE_SP: String = "resolut.language.sp"
    private const val SELECT_LANGUAGE_SP_KEY: String = "resolut.language.sp.key"
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val userPref by lazy {
        context.getSharedPreferences(
            SELECT_LANGUAGE_SP,
            Context.MODE_PRIVATE,
        )
    }

    fun saveLocale(locale: SupportedLocale) {
        with(userPref.edit()) {
            putString(SELECT_LANGUAGE_SP_KEY, locale.name)
            apply()
        }
    }

    fun getLocale(): String? {
        return userPref.getString(SELECT_LANGUAGE_SP_KEY, null)
    }

    fun clearStoredLocale() {
        LocaleManager.setLocale(SupportedLocale.EN_US, context)
        userPref.edit(action = SharedPreferences.Editor::clear)
    }
}
