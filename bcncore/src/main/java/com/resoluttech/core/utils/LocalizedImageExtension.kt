package com.resoluttech.core.utils

import android.content.Context
import android.net.Uri
import com.resoluttech.bcn.assets.LocalizedImage
import com.resoluttech.core.utils.LocaleManager.Statics.getCurrentLocale
import java.net.URL

fun LocalizedImage.getUri(context: Context): Uri {
    return Uri.parse(
        when (getCurrentLocale(context)) {
            SupportedLocale.EN_US -> en.getImage(context.resources.configuration.uiMode)
            SupportedLocale.NYANJA -> ny?.getImage(context.resources.configuration.uiMode)
                ?: en.getImage(context.resources.configuration.uiMode)
        }.toString(),
    )
}

fun URL.toUri(): Uri {
    return Uri.parse(toString())
}
