package com.resoluttech.core.utils

import android.graphics.drawable.Drawable
import android.view.Window
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.annotation.DrawableRes
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.resoluttech.bcncore.R
import org.koin.ext.getFullName
import timber.log.Timber

/**
 * This will set the default toolbar with specified [title].
 * @param isBackArrowShown: suggests if the back arrow button is visible.
 */
fun Fragment.setDefaultToolbar(
    title: String = requireContext().getEmptyString(),
    isBackArrowShown: Boolean = true,
) {
    val toolbarHandler = requireActivity() as ToolbarSettable
    val toolbarView: Toolbar = layoutInflater.inflate(
        R.layout.default_toolbar,
        toolbarHandler.toolbar,
        false,
    ) as Toolbar

    if (isBackArrowShown) {
        toolbarView.setNavigationIcon(R.drawable.ic_arrow_back)
        toolbarView.setNavigationOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }
        toolbarView.setNavigationContentDescription(R.string.axBackButtonLabel)
    }
    toolbarView.title = title
    toolbarView.setTitleTextAppearance(requireContext(), R.style.ToolbarStyle)
    toolbarHandler.toolbar.apply {
        removeAllViews()
        addView(toolbarView)
    }
}

/**
 * This method will set the toolbar background color as specified in [backgroundColor].
 * @param backgroundColor : Color that needs to be set in toolbar background.
 * */
fun Fragment.setToolbarBackgroundColor(backgroundColor: Int) {
    val activity = requireActivity() as AppCompatActivity
    val toolbar = activity.supportActionBar
    toolbar?.setBackgroundDrawable(
        ContextCompat.getColor(requireContext(), backgroundColor).toDrawable(),
    )
}

/**
 * This method will reset the toolbar color to colorPrimary.
 * */
fun Fragment.resetToolbarColor() {
    setToolbarBackgroundColor(R.color.colorPrimary)
}

/**
 * This method will change the color of status bar.
 * The default color will set to color primary.
 * */
fun Fragment.setStatusBarColor(statusColorId: Int = R.color.colorPrimary) {
    val window: Window = requireActivity().window
    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
    window.statusBarColor =
        requireContext().getColor(statusColorId)
}

/**
 * This method provides the destination Id of the fragment to navigate to on done/back action.
 * */
fun Fragment.getImmediateBackstackDestinationId(): Int? {
    return findNavController().previousBackStackEntry?.savedStateHandle?.get<Int>(
        NAVIGATE_TO_DESTINATION_ID,
    )
}

/**
 * An extension on fragment which allows to set the destination id where the next fragment can navigate.
 * For example if we are having a back stack [X->A->B->C] B can call this method to set the navigation destination
 * for C to A or X, so when C will call popBackstack([getImmediateBackstackDestinationId]), it will navigate to A instead of B.
 * */
fun Fragment.setImmediateBackstackDestinationId(@IdRes destination: Int) {
    findNavController().currentBackStackEntry?.savedStateHandle?.set(
        NAVIGATE_TO_DESTINATION_ID,
        destination,
    )
}

/**
 * An extension function on fragment which allows user to navigate to particular destination on system back press.
 * */
fun Fragment.setSystemBackPress(@IdRes destinationId: Int?) {
    val navController = findNavController()
    requireActivity().onBackPressedDispatcher.addCallback(
        viewLifecycleOwner,
        object :
            OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (destinationId != null) {
                    if (!navController.popBackStack(destinationId, false)) {
                        Timber.tag(this::class.java.name)
                            .e("Navigation to destination with id $destinationId from ${this::class.getFullName()} failed.")
                    }
                } else {
                    if (!navController.navigateUp()) {
                        Timber.tag(this::class.java.name)
                            .e("Navigation to previous screen from ${this::class.getFullName()} screen failed.")
                    }
                }
            }
        },
    )
}

/**
 * This method when called will hide the toolbar.
 */
fun Fragment.hideToolbar() {
    (this.activity as AppCompatActivity).supportActionBar!!.hide()
}

/**
 * This method when called will show the toolbar if it is hidden.
 */
fun Fragment.showToolbar() {
    (this.activity as AppCompatActivity).supportActionBar!!.show()
}

/**
 * This method gets the drawable resource corresponds to given drawable resource id.
 *
 * @param resId : The drawable resource id.
 * @return Drawable that can be set as resource on imageView.
 * */
fun Fragment.getDrawable(@DrawableRes resId: Int): Drawable? {
    return ContextCompat.getDrawable(requireContext(), resId)
}

fun Fragment.restartActivity() {
    activity?.intent?.data = null
    activity?.finish()
    activity?.startActivity(activity?.intent)
}

fun Fragment.setupBackPressed(function: () -> Unit) {
    requireActivity().onBackPressedDispatcher.addCallback(
        viewLifecycleOwner,
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                function()
            }
        },
    )
}

private const val NAVIGATE_TO_DESTINATION_ID: String =
    "TransactionFragment.navigate_to_immediate_back_stack"
