package com.resoluttech.core.utils

import android.content.Context
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.resoluttech.bcn.types.Amount
import com.resoluttech.bcncore.R
import java.text.DecimalFormat
import java.util.Locale

/**
 * This formats the amount value to user facing value without currency.
 *
 * @param amount: The amount value that needs to be formatted.
 * @return Formatted amount string, it doesn't include currency.
 * * */
fun Context.formattedAmountValue(amount: Long): String {
    return if (isDecimalRequired(amount)) {
        getString(R.string.formattedAmountValue)
    } else {
        getString(R.string.formattedAmountWithoutDecimalValue)
    }.run {
        String.format(Locale.getDefault(), this, amount.getUserFacingValue())
    }
}

/**
 * This function decides whether decimal points needs to be included or not in the formatted amount.
 * If amount is of form 123.00, It will return false.
 *
 * If the amount is of the form 123.45 then it will return true.
 *
 * @param amount: Amount which helps in deciding the format.
 * */
fun isDecimalRequired(amount: Long): Boolean {
    val userValue = amount.getUserFacingValue()
    val decimalPart = userValue - userValue.toLong()
    return decimalPart > 0
}

fun displayAmountWithCurrency(currency: String, userFacingAmount: Double): String {
    return String.format(
        Locale.getDefault(),
        "%s %s",
        currency,
        userFacingAmount.formatToAmount(),
    )
}

fun displayExchangeRateAmountWithCurrency(currency: String, userFacingAmount: Double): String {
    val amountFormatter: DecimalFormat =
        (DecimalFormat.getNumberInstance() as DecimalFormat).apply {
            applyLocalizedPattern("###,###.##")
        }

    return String.format(
        Locale.getDefault(),
        "%s %s",
        currency,
        amountFormatter.format(userFacingAmount),
    )
}

fun Context.getEmptyString(): String {
    return getString(R.string.emptyString)
}

fun Context.getAmountTooLargeString(maximumAllowedAmount: Amount?): String {
    return if (maximumAllowedAmount != null) {
        getString(
            R.string.alertMessageTransactionAmountTooLarge,
            displayAmountWithCurrency(
                maximumAllowedAmount.currency.currencyCode,
                maximumAllowedAmount.amount.getUserFacingValue(),
            ),
        )
    } else {
        getString(R.string.alertMessageTransactionAmountTooLargeNoAmount)
    }
}

fun Context.getAmountTooLessString(minimumAllowedAmount: Amount?): String {
    return if (minimumAllowedAmount != null) {
        getString(
            R.string.alertMessageTransactionAmountTooSmall,
            displayAmountWithCurrency(
                minimumAllowedAmount.currency.currencyCode,
                minimumAllowedAmount.amount.getUserFacingValue(),
            ),
        )
    } else {
        getString(R.string.alertMessageTransactionAmountTooSmallNoAmount)
    }
}

/**
 * This method can be used to highlight the required part of the string.
 * Input to this method is three parts of the string, starting part, highlighted part and the trailing part. Using the
 * combination any string can be highlighted.
 *
 * @param startingString : The first part of the string, this will not be highlighted.
 * @param trailingString : The end part of the string, this will not be highlighted.
 * @param highlightedString : The middle part of the string, this will be highlighted.
 *
 * @return Spannable highlighted string.
 * */
fun Context.getHighlightedText(
    startingString: String = getEmptyString(),
    highlightedString: String,
    trailingString: String = getEmptyString(),
    @ColorRes startStringColor: Int,
    @ColorRes highlightedStringColor: Int,
): Spannable {
    val tempString =
        "${startingString.trim()} ${highlightedString.trim()} ${trailingString.trim()}"
    val spannable: Spannable = SpannableString(tempString.trim())
    // Setting up color for starting string
    spannable.setSpan(
        ForegroundColorSpan(ContextCompat.getColor(this, startStringColor)),
        0,
        startingString.length,
        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
    )
    // Setting up color for string we want to highlight
    spannable.setSpan(
        ForegroundColorSpan(ContextCompat.getColor(this, highlightedStringColor)),
        startingString.length,
        startingString.length + highlightedString.length + 1,
        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
    )
    spannable.setSpan(
        StyleSpan(Typeface.BOLD),
        startingString.length,
        startingString.length + highlightedString.length + 1,
        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE,
    )
    return spannable
}

fun getDisplayNameWithEllipsis(displayName: String, length: Int): String {
    return if (displayName.length > length) {
        displayName.take(length - 3) + Typography.ellipsis
    } else {
        displayName
    }
}

fun Context.getLocalizedString(en: String, ny: String?): String {
    return when (LocaleManager.getCurrentLocale(this)) {
        SupportedLocale.EN_US -> en
        SupportedLocale.NYANJA -> ny ?: en
    }
}

fun String.getUTF8Size(): Int {
    return toByteArray(Charsets.UTF_8).size
}
