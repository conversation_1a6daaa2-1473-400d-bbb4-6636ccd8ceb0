package com.resoluttech.core.utils

import android.Manifest
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.google.android.material.snackbar.Snackbar
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.peertopeer.PeerTransfersVM
import com.resoluttech.core.uicomponents.AlertDialog
import com.resoluttech.core.uicomponents.AlertDialogButtonColor
import com.resoluttech.core.uicomponents.ProgressDialog
import com.resoluttech.core.utils.DialogCodes.Companion.CONTACT_PICKER_DIALOG_CODE
import com.resoluttech.core.views.BaseFragment
import com.suryadigital.leo.libui.contactpicker.Contact
import com.suryadigital.leo.libui.contactpicker.ContactPicker
import com.suryadigital.leo.libui.contactpicker.ContactPickerViewModel
import com.suryadigital.leo.libui.contactpicker.checkContactPermission
import com.suryadigital.leo.types.LeoInvalidLeoPhoneNumberException
import com.suryadigital.leo.types.LeoPhoneNumber
import timber.log.Timber

class ContactPickerFragment :
    BaseFragment(),
    ContactPicker.PermissionRequestListener,
    AlertDialog.ActionListener,
    CountryCodePickerDialog.CodeListener {
    private lateinit var contactPickerViewModel: ContactPickerViewModel
    private lateinit var contactPickerVM: ContactPickerVM
    private val peerTransfersVM: PeerTransfersVM by navGraphViewModels(R.id.peer_to_peer_transfer_nav)
    private var contactErrorSnackBar: Snackbar? = null
    private lateinit var progressDialog: ProgressDialog
    private val argumentBundle: Bundle by lazy(this@ContactPickerFragment::requireArguments)
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted ->
        if (isGranted) {
            contactPickerVM.onContactPermissionGranted()
            val fragment = childFragmentManager.fragments[0]
            if (fragment is ContactPicker) {
                fragment.initAdapter()
            }
        } else {
            if (!shouldShowRequestPermissionRationale(Manifest.permission.READ_CONTACTS)) {
                contactPickerVM.onContactPermissionNeverAskAgainSelected()
            } else {
                Timber.tag(TAG)
                    .e("Contact access permission denied, unable to proceed ith transfer.")
                contactPickerVM.onContactPermissionDenied()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        return inflater.inflate(R.layout.fragment_transfers_contact_picker, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDefaultToolbar(
            getString(R.string.contactsPickerViewTitle),
        )
        initVMs()
        contactPickerViewModel.getContactNumberLiveData().observe(viewLifecycleOwner) {
            if (it.contactNumber.isNotBlank() && it.isSelected) {
                when (
                    BundleUtils.getBundlesSerializable(
                        argumentBundle,
                        USE_CASE,
                        ContactPickerUseCase::class.java,
                    )
                ) {
                    ContactPickerUseCase.PEER_TO_PEER -> {
                        if (isCountryCodeAttached(it.contactNumber)) {
                            onValidContactSelection(it)
                        } else {
                            val countryPickerDialog =
                                CountryCodePickerDialog.newInstance(it.contactNumber)
                            countryPickerDialog.show(
                                childFragmentManager,
                                CountryCodePickerDialog.TAG,
                            )
                        }
                    }

                    ContactPickerUseCase.TRUSTED_CONTACTS -> {
                        onValidContactSelection(it)
                    }
                    else -> {
                        throw Exception("Navigated from other than peer to peer and trusted contacts flow.")
                    }
                }
            }
        }
        startContactSelection()
        contactPickerVM.currentState.observe(viewLifecycleOwner, Observer(::reactToState))
        contactPickerVM.currentContactSelectionState.observe(
            viewLifecycleOwner,
            Observer(::reactToContactState),
        )
    }

    private fun onValidContactSelection(it: Contact) {
        try {
            when (
                BundleUtils.getBundlesSerializable(
                    argumentBundle,
                    USE_CASE,
                    ContactPickerUseCase::class.java,
                )
            ) {
                ContactPickerUseCase.PEER_TO_PEER -> {
                    contactPickerVM.onContactPicked(
                        LeoPhoneNumber(getValidPhoneNumber(it.contactNumber)),
                        requireContext(),
                    )
                }
                ContactPickerUseCase.TRUSTED_CONTACTS -> {
                    setFragmentResult(
                        CONTACT_KEY,
                        bundleOf(
                            CONTACT_NUMBER_KEY to it.contactNumber,
                            CONTACT_NAME_KEY to it.name,
                        ),
                    )
                    contactPickerVM.onTrustedContactPicked(findNavController())
                }
                else -> {
                    throw Exception("Navigated from other than peer to peer and trusted contacts flow.")
                }
            }
            contactPickerViewModel.reset()
        } catch (exception: LeoInvalidLeoPhoneNumberException) {
            showErrorSnackbar(getString(R.string.alertMessageInvalidMobileNumber))
        } catch (illegalStateException: IllegalStateException) {
            contactPickerVM.onInvalidContactError(
                requireContext().getString(R.string.alertTitleInvalidMobileNumber),
                getString(R.string.alertMessageInvalidMobileNumber),
            )
        }
    }

    private fun initVMs() {
        contactPickerViewModel =
            ViewModelProvider(
                this,
                ViewModelProvider.NewInstanceFactory(),
            )[ContactPickerViewModel::class.java]
        contactPickerVM =
            ViewModelProvider(
                this,
                ViewModelProvider.NewInstanceFactory(),
            )[ContactPickerVM::class.java]
    }

    private fun startContactSelection() {
        val contactPicker = ContactPicker()
        contactPicker.setArguments(
            contactPickerViewModel,
            {},
        )
        contactPicker.setPermissionListener(this)
        childFragmentManager
            .beginTransaction()
            .replace(R.id.contact_picker_root, contactPicker)
            .commit()
    }

    private fun reactToState(contactPickerState: ContactPickerState) {
        when (contactPickerState) {
            is ContactPickerState.Content -> {
                dismissErrorSnackbar()
            }
            is ContactPickerState.InlineError -> {
                handleInlineErrorSnackbar(contactPickerState.error)
            }
            is ContactPickerState.Loading -> {
                handleLoadingState(contactPickerState)
            }
            is ContactPickerState.ValidContact -> {
                handleValidContactState(contactPickerState)
            }
        }
    }

    private fun handleInlineErrorSnackbar(error: UIError) {
        dismissDialog()
        when (error.type) {
            ErrorType.SNACKBAR -> {
                showErrorSnackbar(error.errorMessage)
            }
            ErrorType.DIALOG -> {
                showErrorDialog(
                    error.errorTitle,
                    error.errorMessage,
                    error.errorCode ?: CONTACT_PICKER_DIALOG_CODE,
                )
            }
            ErrorType.BANNER -> handleNetworkLostState()
        }
    }

    private fun handleValidContactState(contactPickerState: ContactPickerState.ValidContact) {
        setImmediateBackstackDestinationId(R.id.qrCode)
        dismissDialog()
        peerTransfersVM.onContactSelectedFromAddressBook(
            contactPickerState.bcnRecipient,
            contactPickerState.phoneNumber,
            contactPickerState.availableCurrencies,
            findNavController(),
        )
    }

    private fun dismissDialog() {
        if (::progressDialog.isInitialized) {
            progressDialog.dismiss()
        }
    }

    private fun handleLoadingState(contactPickerState: ContactPickerState.Loading) {
        dismissDialog()
        progressDialog = ProgressDialog.newInstance(
            getString(
                R.string.contactPickerLookingUpDetails,
                LeoPhoneNumber(contactPickerState.phoneNumber).getFormattedPhoneNumber(),
            ),
        )
        progressDialog.setArguments(false)
        progressDialog.show(childFragmentManager, ProgressDialog.TAG)
    }

    private fun reactToContactState(contactState: ContactPermissionState) {
        when (contactState) {
            is ContactPermissionState.ContactPermissionRequested -> {
                dismissErrorSnackbar()
                requestContactPermission()
            }
            is ContactPermissionState.ContactPermissionDenied -> {
                showErrorSnackbar(getString(R.string.contactsPermissionLabel))
            }
            is ContactPermissionState.ContactSelectionIdle -> {
                dismissErrorSnackbar()
            }
            is ContactPermissionState.ContactSelectionStarted -> {
                dismissErrorSnackbar()
            }
            is ContactPermissionState.ContactPermissionAlert -> {
                handleContactPermissionAlertState()
            }
        }
    }

    private fun requestContactPermission() {
        requestPermissionLauncher.launch(Manifest.permission.READ_CONTACTS)
    }

    private fun handleContactPermissionAlertState() {
        showContactPermissionAlert(
            getString(R.string.alertTitleContactAccessDenied),
            getString(R.string.alertMessageContactAccessDenied),
            CONTACT_PERMISSION_DIALOG_CODE,
            getString(R.string.alertActionOpenSettings),
            getString(R.string.alertActionCancel),
        )
    }

    private fun showContactPermissionAlert(
        title: String,
        message: String,
        dialogId: Int,
        positiveActionLabel: String,
        negativeActionLabel: String,
        alertDialogButtonColor: AlertDialogButtonColor? = null,
    ) {
        val alertDialog = AlertDialog.newInstance(
            title,
            message,
            dialogId,
            positiveActionLabel = positiveActionLabel,
            negativeActionLabel = negativeActionLabel,
            alertDialogButtonColor = alertDialogButtonColor,
        )
        alertDialog.setArguments(false)
        alertDialog.show(childFragmentManager, AlertDialog.DIALOG_TAG)
    }

    private fun showErrorSnackbar(error: String) {
        dismissDialog()
        contactErrorSnackBar = Snackbar.make(
            requireView(),
            error,
            Snackbar.LENGTH_INDEFINITE,
        )
        contactErrorSnackBar?.let {
            it.setAction(R.string.alertActionDismiss) {
                contactPickerVM.onContactPickerErrorDismissed()
            }
            it.show()
        }
    }

    private fun dismissErrorSnackbar() {
        dismissDialog()
        contactErrorSnackBar?.dismiss()
        contactErrorSnackBar = null
    }

    override fun onPause() {
        dismissErrorSnackbar()
        super.onPause()
    }

    override fun requestPermission() {
        if (!checkContactPermission(requireContext())) {
            contactPickerVM.onContactPermissionRequested()
        }
    }

    override fun onPositiveAction(dialogId: Int) {
        when (dialogId) {
            DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID -> {
                contactPickerVM.onErrorDialogDismissed()
                if (BundleUtils.getBundlesSerializable(
                        argumentBundle,
                        USE_CASE,
                        ContactPickerUseCase::class.java,
                    ) == ContactPickerUseCase.PEER_TO_PEER
                ) {
                    leoServerExceptionHandler(findNavController())
                }
            }
            CONTACT_PERMISSION_DIALOG_CODE -> {
                contactPickerVM.onOpenSettingsClicked(requireContext())
            }
            else -> {
                contactPickerVM.onErrorDialogDismissed()
            }
        }
    }

    override fun onNegativeAction(dialogId: Int) {
        when (dialogId) {
            CONTACT_PERMISSION_DIALOG_CODE -> contactPickerVM.onContactPermissionAlertDismissed()
        }
    }

    companion object {
        const val USE_CASE: String = "USE_CASE"

        enum class ContactPickerUseCase {
            PEER_TO_PEER,
            TRUSTED_CONTACTS,
        }
    }

    override fun onCountryCodeSelection(contactWithCountryCode: String) {
        onValidContactSelection(Contact(contactNumber = contactWithCountryCode))
    }
}

private const val TAG = "ContactPickerFragment"
private const val CONTACT_PERMISSION_DIALOG_CODE = 114
const val CONTACT_KEY: String = "contact"
const val CONTACT_NUMBER_KEY: String = "contactNumber"
const val CONTACT_NAME_KEY: String = "contactName"
