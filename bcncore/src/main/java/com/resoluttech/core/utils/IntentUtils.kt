package com.resoluttech.core.utils

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.net.toUri

object IntentUtils {
    fun callIntent(context: Context, phoneNumber: String) {
        val intent = Intent(Intent.ACTION_DIAL)
        intent.data = "tel:$phoneNumber".toUri()
        context.startActivity(intent)
    }

    /***
     * Starting from Android Build.VERSION_CODES.TIRAMISU getParcelabelExtra(String) is depreciated
     * and we should use type-safer getParcelabelExtra(String, Class).
     */
    @Suppress("DEPRECATION")
    fun <T> getIntentsParcelableExtra(intent: Intent, key: String, clazz: Class<T>): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(key, clazz)
        } else {
            intent.getParcelableExtra(key)
        }
    }
}
