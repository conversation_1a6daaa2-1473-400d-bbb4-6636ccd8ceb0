package com.resoluttech.core.utils

import android.content.Context
import com.resoluttech.bcn.assets.Item
import com.resoluttech.bcn.assets.LayoutSection
import timber.log.Timber
import java.util.Locale

/**
 * This method searches the payment options by comparing title with query string.
 * Search is based on fuzzy [JaroWinklerDistance] technique.
 *
 * @param context: Context is used to get the locale specific text.
 * @param source: List of layout section on which search will be performed.
 * @param query: Query string which will be compared with payment options title.
 *
 * @return Returns list of distinct [Item].
 * */
fun searchElements(
    context: Context,
    source: List<LayoutSection>,
    query: String,
): List<Item> {
    val result = mutableListOf<ItemWithPriority>()
    val jw = JaroWinklerDistance()
    source.forEach { layoutSection ->
        if (layoutSection is LayoutSection.ButtonContainer) {
            layoutSection.visibleButtons.forEach { item ->
                handleItemSearch(item, context, jw, query, result)
            }
            layoutSection.hiddenButtons?.forEach { item ->
                handleItemSearch(item, context, jw, query, result)
            }
        } else {
            Timber.tag(TAG).d("Skipping other layout sections search.")
        }
    }
    result.sortWith(PriorityComparator())
    val items = mutableListOf<Item>()
    result.forEach { items.add(it.item) }
    /*
    Since there are items that can be present in multiple sections,
    distinct items of the list are returned to prevent showing duplicate items
    in the search result
     */
    return items.distinct()
}

private fun handleItemSearch(item: Item, context: Context, jw: JaroWinklerDistance, query: String, result: MutableList<ItemWithPriority>) {
    if (item.title != null) {
        item.title?.localisedText(LocaleManager.getCurrentLocale(context))
            ?.apply {
                val score = jw.apply(
                    this.lowercase(Locale.getDefault()),
                    query.lowercase(
                        Locale.getDefault(),
                    ),
                )
                if (score > MIN_REQUIRED_SCORE) {
                    result.add(
                        ItemWithPriority(
                            item,
                            (score * SCORE_MULTIPLICATION_CONSTANT).toInt(),
                        ),
                    )
                }
            }
    }
}

private data class ItemWithPriority(val item: Item, val priority: Int = 0)
private class PriorityComparator : Comparator<ItemWithPriority> {
    override fun compare(i1: ItemWithPriority, i2: ItemWithPriority): Int =
        i2.priority - i1.priority
}

private const val MIN_REQUIRED_SCORE = 0.5
private const val SCORE_MULTIPLICATION_CONSTANT = 1000
private const val TAG = "SearchUtils"
