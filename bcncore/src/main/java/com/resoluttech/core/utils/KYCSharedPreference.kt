package com.resoluttech.core.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.koin.java.KoinJavaComponent
import java.lang.IllegalStateException

object KYCSharedPreference {
    private const val KYC_SP_FILE: String = "kyc.sp_file"
    private const val KYC_USER_KEY: String = "kyc.sp_key"
    private val context: Context by KoinJavaComponent.inject(Context::class.java)
    private val kycSP = context.getSharedPreferences(KYC_SP_FILE, Context.MODE_PRIVATE)

    fun saveKYCDetails(kycDetails: KYCDetails) {
        val userString =
            Json.encodeToString(KYCDetails.serializer(), kycDetails)
        with(kycSP.edit()) {
            putString(KYC_USER_KEY, userString)
            apply()
        }
    }

    fun getKYCDetails(): KYCDetails {
        val kycDetails = kycSP.getString(KYC_USER_KEY, null)
        return if (kycDetails != null) {
            Json.decodeFromString(
                KYCDetails.serializer(),
                kycDetails,
            )
        } else {
            throw IllegalStateException("KYC details not set")
        }
    }

    fun clearStoredDetails() {
        kycSP.edit(action = SharedPreferences.Editor::clear)
    }
}

@Serializable
data class KYCDetails(
    val passwordValidatedToken: String? = null,
    val phoneNumber: String? = null,
    val previousPath: KYCPreviousPath,
)

enum class KYCPreviousPath {
    SIGN_IN,
    SIGN_UP,
    PROFILE,
}
