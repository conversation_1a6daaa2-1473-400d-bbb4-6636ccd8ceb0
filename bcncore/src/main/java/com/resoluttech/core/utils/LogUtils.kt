package com.resoluttech.core.utils

import android.os.Build
import timber.log.Timber

class LogUtils {
    companion object {
        private val message: String =
            """The device specifications are as follows:
                        | Device: ${Build.DEVICE}
                        | Model: ${Build.MODEL}
                        | Brand: ${Build.BRAND}
                        | SDK Version: ${Build.VERSION.SDK_INT}
                        | MANUFACTURER: ${Build.MANUFACTURER}
                        | OS version: ${Build.VERSION.BASE_OS}
            """.trimMargin()

        fun logDeviceSpecifications(tag: String) {
            Timber.tag(tag).d(message)
        }
    }
}
