package com.resoluttech.core.utils

import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import java.io.Serializable

object BundleUtils {
    /***
     * Starting from Android Build.VERSION_CODES.TIRAMISU getParcelabelExtra(String) is depreciated
     * and we should use type-safer getParcelabelExtra(String, Class).
     */
    @Suppress("DEPRECATION")
    fun <T> getBundlesParcelable(bundle: Bundle, key: String?, clazz: Class<T>): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            bundle.getParcelable(key, clazz)
        } else {
            bundle.getParcelable(key)
        }
    }

    @Suppress("DEPRECATION")
    fun <T : Serializable?> getBundlesSerializable(
        bundle: Bundle,
        key: String?,
        clazz: Class<T>,
    ): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            bundle.getSerializable(key, clazz)
        } else {
            bundle.getSerializable(key) as? T
        }
    }

    @Suppress("DEPRECATION")
    fun <T : Parcelable?> getBundlesParcelableArrayList(
        bundle: Bundle,
        key: String,
        clazz: Class<T>,
    ): List<T>? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            bundle.getParcelableArrayList(key, clazz)
        } else {
            bundle.getParcelableArrayList(key)
        }
    }
}
