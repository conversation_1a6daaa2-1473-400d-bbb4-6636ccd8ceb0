package com.resoluttech.core.utils

import java.util.Arrays
import kotlin.math.max
import kotlin.math.min

// Thanks: https://commons.apache.org/sandbox/commons-text/jacoco/org.apache.commons.text.similarity/JaroWinklerDistance.java.html

/**
 * A similarity algorithm indicating the percentage of matched characters between two character sequences.
 *
 * <p>
 * The Jaro measure is the weighted sum of percentage of matched characters
 * from each file and transposed characters. <PERSON> increased this measure
 * for matching initial characters.
 * </p>
 */
class JaroWinklerDistance : SimilarityScore<Double> {
    override fun apply(left: CharSequence, right: CharSequence): Double {
        val defaultScalingFactor = 0.1
        val percentageRoundValue = 100.0
        val mtp = matches(left, right)
        val m = mtp[0].toDouble()
        if (m == 0.0) {
            return 0.0
        }
        val j = (m / left.length + m / right.length + (m - mtp[1]) / m) / 3
        val jw = if (j < 0.7) {
            j
        } else {
            j + min(
                defaultScalingFactor,
                1.0 / mtp[3],
            ) * mtp[2] * (1.0 - j)
        }
        return jw * percentageRoundValue / percentageRoundValue
    }

    companion object {
        /**
         * The default prefix length limit is set to four.
         */
        private const val PREFIX_LENGTH_LIMIT = 4

        /**
         * Represents a failed index search.
         */
        const val INDEX_NOT_FOUND: Int = -1

        /**
         * This method returns the Jaro-Winkler string matches, transpositions, prefix and max array.
         *
         * @param first the first string to be matched
         * @param second the second string to be matched
         * @return mtp array containing: matches, transpositions, prefix, and max length
         */
        fun matches(first: CharSequence, second: CharSequence): IntArray {
            val max: CharSequence
            val min: CharSequence
            if (first.length > second.length) {
                max = first
                min = second
            } else {
                max = second
                min = first
            }
            val range = max(max.length / 2 - 1, 0)
            val matchIndexes = IntArray(min.length)
            Arrays.fill(matchIndexes, -1)
            val matchFlags = BooleanArray(max.length)
            var matches = 0
            for (mi in min.indices) {
                val c1 = min[mi]
                var xi = max(mi - range, 0)
                val xn = min(mi + range + 1, max.length)
                while (xi < xn) {
                    if (!matchFlags[xi] && c1 == max[xi]) {
                        matchIndexes[mi] = xi
                        matchFlags[xi] = true
                        matches++
                        break
                    }
                    xi++
                }
            }
            val ms1 = CharArray(matches)
            val ms2 = CharArray(matches)
            run {
                var i = 0
                var si = 0
                while (i < min.length) {
                    if (matchIndexes[i] != -1) {
                        ms1[si] = min[i]
                        si++
                    }
                    i++
                }
            }
            var i = 0
            var si = 0
            while (i < max.length) {
                if (matchFlags[i]) {
                    ms2[si] = max[i]
                    si++
                }
                i++
            }
            val transpositions = ms1.indices.count { ms1[it] != ms2[it] }
            val prefix = (min.indices)
                .asSequence()
                .takeWhile { first[it] == second[it] }
                .count()
            return intArrayOf(matches, transpositions / 2, prefix, max.length)
        }
    }
}
