package com.resoluttech.core.utils

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import com.resoluttech.core.config.AmountLimitConfig
import com.resoluttech.core.config.Config
import com.resoluttech.core.views.AmountEditText
import org.koin.java.KoinJavaComponent
import java.lang.NullPointerException
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import kotlin.jvm.Throws

/**
 * Implements [TextWatcher] and formats the entered input in [AmountEditText] in realtime. The amount can have a maximum
 * up-to [AmountLimitConfig.beforeDecimal] digits before decimal and can be formatted up-to [AmountLimitConfig.precision] decimal places.
 *
 * @param editText : AmountEditText which uses this class internally to format the entered input.
 *
 * */
class AmountTextWatcher(
    private val editText: AmountEditText,
    private val context: Context,
) : TextWatcher {

    private var errorListener: AmountEditText.ErrorListener? = null
    private val amountFormatter: DecimalFormat =
        DecimalFormat.getNumberInstance() as DecimalFormat
    private val symbols: DecimalFormatSymbols = DecimalFormatSymbols.getInstance()
    private val groupingSeparator: Char = symbols.groupingSeparator
    private val decimalSeparator: Char = symbols.decimalSeparator
    private val amountLimitConfig: AmountLimitConfig by KoinJavaComponent.inject(AmountLimitConfig::class.java)
    private var currentString: String = context.getEmptyString()
    private var isTextChangeAllowed = false

    override fun afterTextChanged(s: Editable?) {
        isTextChangeAllowed = !isTextChangeAllowed
        val input = s.toString().trim()
        if (input.isEmpty()) return
        if (input == ".") {
            editText.setText(context.getEmptyString())
        }
        if (isTextChangeAllowed) {
            if (setAmountLimit(
                    input,
                    amountLimitConfig.beforeDecimal,
                    amountLimitConfig.precision,
                )
            ) {
                return
            }
            setupFormatter()
            formattedInput(input)?.apply {
                setOutput(first, second)
            }
        }
    }

    private fun setOutput(
        parts: List<String>,
        formattedNumber: String,
    ) {
        if (parts.size > 1) {
            val output = formattedNumber + decimalSeparator + parts[1]
            editText.setText(output)
        } else {
            editText.setText(formattedNumber)
        }
    }

    private fun formattedInput(input: String): Pair<List<String>, String>? {
        val cleanedInput = input.replace("$groupingSeparator", context.getEmptyString()).trim()
        currentString = input
        val parts = cleanedInput.split(decimalSeparator)
        parts[0].convertToLong()?.apply {
            val formattedNumber = amountFormatter.format(parts[0].convertToLong()).trim()
            return parts to formattedNumber
        }
        return null
    }

    private fun String.convertToLong(): Long? {
        return try {
            toLong()
        } catch (e: Exception) {
            // Discard the entered input if it's not valid, it will also discard copy paste of invalid input.
            editText.setText(context.getEmptyString())
            null
        }
    }

    private fun setupFormatter() {
        amountFormatter.applyLocalizedPattern("###,###")
        amountFormatter.maximumFractionDigits = 0
        amountFormatter.isDecimalSeparatorAlwaysShown = false
    }

    private fun setAmountLimit(
        input: String,
        limitBeforeDecimal: Int,
        limitAfterDecimal: Int,
    ): Boolean {
        if (input.contains(decimalSeparator)) {
            if (limitAfterDecimal == 0) {
                // If limit after decimal is zero it should not take `decimalSeparator` as input.
                val cleanedInput =
                    currentString.replace("$groupingSeparator", context.getEmptyString())
                val amount = cleanedInput.convertToLong()
                amount?.let {
                    editText.setText(
                        amountFormatter.format(
                            it,
                        ),
                    )
                }
                return true
            }

            // Take the fraction part after decimal and compare it's length with defined limit.
            val decimalCount = input.split(decimalSeparator)[1].length
            if (decimalCount > limitAfterDecimal) {
                // If decimal count is greater than limit then return the previous string.
                editText.setText(currentString)
                return true
            }
        } else {
            val cleanedInput = input.replace("$groupingSeparator", context.getEmptyString())
            if (cleanedInput.length > limitBeforeDecimal) {
                val amount = cleanedInput.take(limitBeforeDecimal).convertToLong()
                amount?.let {
                    editText.setText(
                        amountFormatter.format(
                            it,
                        ),
                    )
                }
                return true
            }
        }
        return false
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        if (isTextChangeAllowed) {
            // Will move the cursor to end of the amount string.
            editText.setSelection(s.toString().length)
        }
        handleErrorListener(s)
    }

    private fun handleErrorListener(s: CharSequence?) {
        errorListener!!.apply {
            val enteredString = s.toString()
            val cleanedString: String = if (enteredString.contains(",")) {
                enteredString.replace(",", "")
            } else {
                enteredString
            }
            var amount = 0.0
            try {
                amount = cleanedString.toDouble()
            } catch (e: NumberFormatException) {
                onAmountInput(false)
            }

            if (s.toString().isNotEmpty() && amount >= Config.MINIMUM_INPUT_AMOUNT) {
                onAmountInput(true)
            } else {
                onAmountInput(false)
            }
        }
    }

    /**
     * This function provides realtime-check on input, if input is invalid it will return onAmountInvalid(false),
     * else it will return onAmountInput(true). It is must to set the listener else it will throw [NullPointerException].
     * This listener can be used to block the user actions such as button click, if entered input is not valid.
     * */
    @Throws(NullPointerException::class)
    fun setErrorListener(errorListener: AmountEditText.ErrorListener) {
        this.errorListener = errorListener
    }
}
