package com.resoluttech.core.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.telephony.TelephonyManager
import com.resoluttech.core.utils.LocaleManager.Statics.EN_US_CODE
import com.resoluttech.core.utils.LocaleManager.Statics.NY_CODE
import timber.log.Timber
import java.util.Locale

class LocaleManager {
    companion object Statics {
        fun updateResources(
            context: Context,
        ): Context? {
            val locale = Locale(getCurrentLocale(context).identifier)
            val config = Configuration(context.resources.configuration).apply { setLocale(locale) }
            return context.createConfigurationContext(config)
        }

        // Suppressing suggestion to change `commit` to `apply` since we want a synchronous write here.
        @SuppressLint("ApplySharedPref")
        @Synchronized
        fun setLocale(target: SupportedLocale, context: Context) {
            val sharedPref =
                context.getSharedPreferences(SHARED_PREFERENCES_FILE_NAME, Context.MODE_PRIVATE)
            with(sharedPref.edit()) {
                putString(KEY, target.identifier)
                commit()
            }
            cachedLocale = target
        }

        /**
         * This method returns the index of current locale in the languageConfig
         *
         * @param languageSupportedList: List of all supported locale
         * @param context: context is needed to get the current locale
         */
        fun getCurrentLocaleIndex(
            languageSupportedList: Array<SupportedLocale>,
            context: Context,
        ): Int {
            languageSupportedList.forEachIndexed { index, it ->
                if (getCurrentLocale(context) == it) {
                    return index
                }
            }
            return 0
        }

        fun getCurrentLocale(context: Context): SupportedLocale {
            if (cachedLocale == null) {
                cachedLocale = readComputeSupportedLocale(context)
            }
            return cachedLocale!!
        }

        fun getCurrentMeasurementSystem(context: Context): MeasurementSystem {
            return when ((context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager).networkCountryIso) {
                // US, UK, Myanmar, Liberia
                "US", "GB", "MM", "LR" -> MeasurementSystem.IMPERIAL
                else -> MeasurementSystem.METRIC
            }
        }

        private fun readComputeSupportedLocale(context: Context): SupportedLocale {
            val preferredLocale = context
                .getSharedPreferences(SHARED_PREFERENCES_FILE_NAME, Context.MODE_PRIVATE)
                .getString(
                    KEY,
                    EN_US_CODE,
                )!!
            return when (preferredLocale) {
                EN_US_CODE -> {
                    SupportedLocale.EN_US
                }
                NY_CODE -> {
                    SupportedLocale.NYANJA
                }
                else -> {
                    Timber.tag(TAG).w("Unknown Locale: $preferredLocale")
                    SupportedLocale.EN_US
                }
            }
        }

        // NOTE: There are possible threading bugs here on occasion of reads and writes.
        // However, these are being ignored since the user is not going to change locales very often.
        // Therefore, writes will be avoided.
        private var cachedLocale: SupportedLocale? = null

        private const val KEY: String = "USER_PREFERRED_LANGUAGE"
        private const val SHARED_PREFERENCES_FILE_NAME: String = "SharedPreferencesFile"

        const val EN_US_CODE: String = "en_US"
        const val NY_CODE: String = "ny"
        const val EN_US: String = "EN_US"
        const val NY: String = "NY"
    }
}

enum class SupportedLocale(val identifier: String, val displayName: String) {
    EN_US(EN_US_CODE, getDisplayLanguage(EN_US_CODE)),
    NYANJA(NY_CODE, getDisplayLanguage(NY_CODE)),
}

enum class MeasurementSystem {
    IMPERIAL,
    METRIC,
}

fun getDisplayLanguage(languageCode: String): String {
    val code = languageCode.split("_")
    return Locale(code[0]).getDisplayLanguage(Locale(code[0]))
}

private const val TAG = "LocalisedTextHelper"
