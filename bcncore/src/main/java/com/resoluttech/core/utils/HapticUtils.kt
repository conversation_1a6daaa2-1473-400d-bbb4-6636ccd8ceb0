package com.resoluttech.core.utils

import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager

class HapticUtils {
    companion object {

        /**
         * This method perform manual haptic feedback as per the selected haptic type.
         *
         * @param type : [HapticConstant] defines the type of feedback.
         * @return true if haptic feedback is successful else return false. It will return false if vibrator
         * hardware is absent in device.
         * */
        internal fun performManualVibration(context: Context, type: HapticConstant): <PERSON><PERSON>an {
            val vibrator: Vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager =
                    context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }
            vibrator.apply {
                if (hasVibrator()) {
                    when (type) {
                        HapticConstant.HAPTIC_CODE_REJECT -> {
                            val rejectHapticPattern = longArrayOf(0, 100, 100, 0)
                            performHaptic(rejectHapticPattern)
                        }
                        HapticConstant.HAPTIC_CODE_CONFIRM -> {
                            val confirmHapticPattern = longArrayOf(0, 100)
                            performHaptic(confirmHapticPattern)
                        }
                    }
                } else {
                    return false
                }
            }
            return true
        }

        // Thanks: https://stackoverflow.com/a/52849878/5163725
        private fun Vibrator.performHaptic(rejectHapticPattern: LongArray) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrate(VibrationEffect.createWaveform(rejectHapticPattern, -1))
            } else {
                // Suppressing deprecation, the new API introduced is working for android API 26 and above.
                // Minimum SDK required is 23, so we are using older API.
                @Suppress("DEPRECATION")
                vibrate(rejectHapticPattern, -1)
            }
        }
    }
}

enum class HapticConstant(val value: Int) {
    HAPTIC_CODE_REJECT(17),
    HAPTIC_CODE_CONFIRM(16),
}
