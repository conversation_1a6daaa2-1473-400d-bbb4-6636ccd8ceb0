package com.resoluttech.core.utils

import android.content.Context
import android.os.Build
import com.resoluttech.bcncore.R
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.UIError
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.net.MalformedURLException
import java.net.SocketTimeoutException
import java.net.URL
import java.net.UnknownHostException

object DownloadManager {

    fun download(
        link: String,
        path: String,
        context: Context,
        onSuccessAction: () -> Unit,
        onErrorAction: (error: UIError) -> Unit,
    ) {
        try {
            val url = URL(link)
            val connection = url.openConnection()
            connection.connect()
            val length = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                connection.contentLengthLong
            } else {
                connection.contentLength.toLong()
            }
            url.openStream().use { input ->
                FileOutputStream(File(path)).use { output ->
                    val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
                    var bytesRead = input.read(buffer)
                    var bytesCopied = 0L
                    while (bytesRead >= 0) {
                        output.write(buffer, 0, bytesRead)
                        bytesCopied += bytesRead
                        if (bytesCopied == length) {
                            onSuccessAction()
                            break
                        }
                        bytesRead = input.read(buffer)
                    }
                }
            }
        } catch (e: UnknownHostException) {
            onErrorAction(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleServerError),
                    context.getString(R.string.alertMessageServerError),
                    DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID,
                ),
            )
        } catch (e: MalformedURLException) {
            onErrorAction(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleServerError),
                    context.getString(R.string.alertMessageServerError),
                    DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID,
                ),
            )
        } catch (e: IOException) {
            onErrorAction(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleServerError),
                    context.getString(R.string.alertMessageServerError),
                    DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID,
                ),
            )
        } catch (e: SocketTimeoutException) {
            onErrorAction(
                UIError(
                    ErrorType.DIALOG,
                    context.getString(R.string.alertTitleServerError),
                    context.getString(R.string.alertMessageServerError),
                    DialogCodes.LEO_SERVER_EXCEPTION_ERROR_DIALOG_ID,
                ),
            )
        }
    }
}
