package com.resoluttech.core.utils

import com.resoluttech.bcn.types.Amount
import com.resoluttech.core.config.AmountLimitConfig
import com.resoluttech.core.views.AmountEditText
import org.koin.java.KoinJavaComponent
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import kotlin.math.pow
import kotlin.math.roundToLong

/**
 * Call this method to get the rounded off and formatted balance according to the provided [locale].
 * Note: Returned balance will be rounded off to [AmountLimitConfig.precision] decimal places.
 */
fun Amount.formatted(locale: Locale? = null): String {
    val amountLimitConfig: AmountLimitConfig by KoinJavaComponent.inject(AmountLimitConfig::class.java)
    val balanceDivider = 10.0.pow(AmountEditText.amountMultiplicationFactor)
    val convertedBalance = (amount / balanceDivider)
        .toBigDecimal()
        .setScale(amountLimitConfig.precision, RoundingMode.DOWN)

    return if (locale != null) {
        DecimalFormat.getInstance(locale).format(convertedBalance)
    } else {
        DecimalFormat.getInstance().format(convertedBalance)
    }
}

/**
 * An extension function that converts the user facing amount value to
 * server compatible amount value by multiplying it with specified multiplication factor.
 *
 * @return Converted server compatible amount value.
 * */
fun Double.getProcessedValue(): Long {
    return (this * 10.0.pow(AmountEditText.amountMultiplicationFactor.toDouble())).roundToLong()
}

/**
 * An extension function that converts the amount value received from server to
 * user facing value by dividing it with specified multiplication factor.
 *
 * @return Converted user facing amount.
 * */
fun Long.getUserFacingValue(): Double {
    val decimalFormat = DecimalFormat("#.##")
    decimalFormat.roundingMode = RoundingMode.DOWN

    return decimalFormat.format(this / 10.0.pow(AmountEditText.amountMultiplicationFactor.toDouble())).toDouble()
}

/**
 * This method can be used to format the amount.
 * */
fun Double.formatToAmount(): String {
    val symbols = DecimalFormatSymbols()
    symbols.groupingSeparator = ','
    val pattern = "###,###.##"
    val decimalFormat = DecimalFormat(pattern, symbols)
    return decimalFormat.format(this)
}
