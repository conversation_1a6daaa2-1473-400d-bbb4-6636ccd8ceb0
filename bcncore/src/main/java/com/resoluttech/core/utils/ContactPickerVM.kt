package com.resoluttech.core.utils

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.resoluttech.bcn.transfers.GetBCNRecipientFromPhoneNumberRPC
import com.resoluttech.bcn.types.BCNUserDisplayInfo
import com.resoluttech.bcn.types.Currency
import com.resoluttech.core.rpcexceptionhandlers.ErrorType
import com.resoluttech.core.rpcexceptionhandlers.SendMoneyRPCExceptionHandler
import com.resoluttech.core.rpcexceptionhandlers.UIError
import com.resoluttech.core.transfers.peertopeer.SendMoneyRepository
import com.suryadigital.leo.rpc.LeoRPCResult
import com.suryadigital.leo.types.LeoPhoneNumber
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus

class ContactPickerVM : ViewModel() {

    private var _currentState: MutableLiveData<ContactPickerState> =
        MutableLiveData(ContactPickerState.Content)
    val currentState: LiveData<ContactPickerState> = _currentState
    private val vmIOScope = viewModelScope + Dispatchers.IO
    private val _currentContactSelectionState: MutableLiveData<ContactPermissionState> =
        MutableLiveData(ContactPermissionState.ContactSelectionIdle)
    val currentContactSelectionState: LiveData<ContactPermissionState> =
        _currentContactSelectionState

    fun onContactPickerErrorDismissed() {
        _currentState.postValue(
            ContactPickerState.Content,
        )
    }

    fun onInvalidContactError(title: String, error: String) {
        _currentState.postValue(
            ContactPickerState.InlineError(
                UIError(
                    ErrorType.SNACKBAR,
                    title,
                    error,
                ),
            ),
        )
    }

    fun onContactPermissionAlertDismissed() {
        _currentContactSelectionState.postValue(ContactPermissionState.ContactPermissionDenied)
    }

    fun onContactPermissionGranted() {
        _currentContactSelectionState.postValue(ContactPermissionState.ContactSelectionStarted)
    }

    fun onContactPermissionDenied() {
        _currentContactSelectionState.postValue(ContactPermissionState.ContactPermissionDenied)
    }

    fun onContactPermissionRequested() {
        _currentContactSelectionState.postValue(ContactPermissionState.ContactPermissionRequested)
    }

    fun onContactPermissionNeverAskAgainSelected() {
        _currentContactSelectionState.postValue(ContactPermissionState.ContactPermissionAlert)
    }

    fun onOpenSettingsClicked(context: Context) {
        openSystemSettings(context)
    }

    fun onTrustedContactPicked(navController: NavController) {
        navController.navigateUp()
    }

    fun onContactPicked(phoneNumber: LeoPhoneNumber, context: Context) {
        _currentState.postValue(ContactPickerState.Loading(phoneNumber.value))
        vmIOScope.launch {
            executeRPC(
                context,
                rpcBlock = {
                    when (
                        val result =
                            SendMoneyRepository().getUserDetailFromPhoneNumber(phoneNumber)
                    ) {
                        is LeoRPCResult.LeoResponse -> {
                            handleGetUserDetailsFromPhoneNumberResponse(
                                result.response,
                                phoneNumber,
                            )
                        }
                        is LeoRPCResult.LeoError -> {
                            SendMoneyRPCExceptionHandler.getUserFromPhoneNumberErrorMessage(
                                result.error,
                                context,
                            ).apply {
                                showError(this)
                            }
                        }
                    }
                },
                handleException = ::showError,
            )
        }
    }

    private fun handleGetUserDetailsFromPhoneNumberResponse(
        response: GetBCNRecipientFromPhoneNumberRPC.Response,
        phoneNumber: LeoPhoneNumber,
    ) {
        _currentState.postValue(
            ContactPickerState.ValidContact(
                response.bcnRecipient,
                phoneNumber,
                UserSharedPreference.getUserCountryCurrency().map { Currency(it.currencyCode) },
            ),
        )
    }

    private fun showError(error: UIError) {
        _currentState.postValue(
            ContactPickerState.InlineError(error),
        )
    }

    fun onErrorDialogDismissed() {
        _currentState.postValue(ContactPickerState.Content)
    }
}

sealed class ContactPickerState {
    object Content : ContactPickerState()
    data class Loading(val phoneNumber: String) : ContactPickerState()
    data class InlineError(val error: UIError) : ContactPickerState()
    data class ValidContact(
        val bcnRecipient: BCNUserDisplayInfo,
        val phoneNumber: LeoPhoneNumber,
        val availableCurrencies: List<Currency>,
    ) : ContactPickerState()
}

sealed class ContactPermissionState {
    object ContactPermissionRequested : ContactPermissionState()
    object ContactPermissionDenied : ContactPermissionState()
    object ContactSelectionStarted : ContactPermissionState()
    object ContactSelectionIdle : ContactPermissionState()
    object ContactPermissionAlert : ContactPermissionState()
}
