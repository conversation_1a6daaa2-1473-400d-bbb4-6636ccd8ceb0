<resources xmlns:tools="http://schemas.android.com/tools">
    <!--  Copyright © 2022 Resolut-Tech -->
    <!--  MARK: - Amount Format -->
    <string name="displayAmount">%1$s %2$s</string>
    <!--  MARK: - App ShortCuts -->
    <string name="appShortCutScanAndPay">Scan &amp; Pay</string>
    <string name="appShortCutSendMoney">Send Money</string>
    <string name="appShortCutLoadWallet">Load Wallet</string>
    <string name="appShortCutPayBills">Pay Bills</string>
    <string name="appShortCutTransactions">Transactions</string>
    <!--  MARK: - AlertController -->
    <string name="alertActionDismiss">Dismiss</string>
    <string name="alertActionTryAgain">Try Again</string>
    <string name="alertActionCancel">Cancel</string>
    <string name="alertActionConfirm">Confirm</string>
    <string name="alertActionNo">No</string>
    <string name="alertActionYes">Yes</string>
    <string name="alertActionSignOut">Sign Out</string>
    <string name="alertActionProceed">Proceed</string>
    <string name="alertActionDecline">Decline</string>
    <string name="alertActionSave">Save</string>
    <string name="alertActionRemove">Remove</string>
    <string name="alertActionUpdate">Update</string>
    <string name="alertActionOpenSettings">Open Settings</string>
    <string name="alertActionBack">Back</string>
    <!--  MARK: AlertTransactions -->
    <string name="alertTitleTransactionDetails">Transaction Details</string>
    <string name="alertTransactionDetailAmount">Amount</string>
    <string name="alertTransactionDetailTransactionFee">Transaction Fee</string>
    <string name="alertTransactionDetailAccount">Wallet</string>
    <string name="alertTransactionDetailPayableAmount">Payable Amount</string>
    <string name="alertTransactionDetailExchangeRate">Exchange Rate</string>
    <string name="alertTransactionDetailReceiveAmount">Recipient Receives</string>
    <string name="alertTransactionExchangeString">%1$s ⇋ %2$s</string>
    <string name="alertTransactionFeeChargesInfo">An additional fee may be charged by the card provider for this transaction.</string>
    <string name="alertTransactionTermsAndConditions">Terms &amp; Conditions</string>
    <!--  MARK: Alerts -->
    <string name="alertTitleGenericError">Something went wrong</string>
    <string name="alertMessageGenericError">It seems like something went wrong. Please try again after some time.</string>
    <!--  MARK: RuntimeErrors -->
    <string name="alertTitleServerError">Server Error</string>
    <string name="alertMessageServerError">Unable to reach our servers. Please try again.</string>
    <string name="alertTitleUnsupportedClient">App Update Required</string>
    <string name="alertMessageUnsupportedClient">Your app is out of date. Please update your app to continue using it.</string>
    <string name="alertTitleInvalidCredentials">Please Sign In Again</string>
    <string name="alertMessageInvalidCredentials">Your session has expired. Please Sign In again.</string>
    <string name="alertTitleUserDisabled">User Disabled</string>
    <string name="alertMessageUserDisabled">Your account is disabled. Please contact support.</string>
    <!--  MARK: SedwigErrors -->
    <string name="alertTitleFailedToDownload">Failed to Download File</string>
    <string name="alertMessageFailedToDownload">Your connection got interrupted while downloading the file. Please try again.</string>
    <string name="alertTitleFailedToUpload">Failed to Upload File</string>
    <string name="alertMessageFailedToUpload">There is something wrong with the selected file. Please choose another file.</string>
    <string name="alertTitleNoInternet">No Internet Connection</string>
    <string name="alertMessageNoInternet">Check your internet connection and try again.</string>
    <!--  MARK: Permissions -->
    <string name="alertTitleUnableToAccessCamera">Camera Inaccessible</string>
    <string name="alertMessageUnableToAccessCamera">Unable to access the device\'s camera at this point. Please try again later.</string>
    <string name="alertTitleCameraAccessNotPermitted">Camera Access Denied</string>
    <string name="alertMessageCameraAccessNotPermitted">Turn on Camera access in the Settings app to make payments.</string>
    <string name="alertTitleLocationAccessNotPermitted">Location Inaccessible</string>
    <string name="alertMessageLocationAccessNotPermitted">Turn on Location access in the Settings app to allow us to locate agents near you.</string>
    <string name="alertTitleContactsAccessNotPermitted">Contacts Inaccessible</string>
    <string name="alertMessageContactsAccessNotPermitted">Turn on Contacts access in the Settings app to find and pay Yafika mobile users from the Yafika mobile app.</string>
    <string name="alertTitleCameraNotAvailable">Camera Not Available</string>
    <string name="alertMessageCameraNotAvailable">Please use a device with camera access.</string>
    <!--  MARK: LanguageSelection -->
    <string name="alertTitleNoLanguageSelected">No Language Selected</string>
    <string name="alertMessageNoLanguageSelected">Please select a language to proceed.</string>
    <!--  MARK: SignUpIn -->
    <string name="alertTitleConfirmMobileNumber">Are You Sure?</string>
    <string name="alertMessageConfirmMobileNumber">Is this your mobile number \n%1$s?</string>
    <string name="alertTitleInvalidMobileNumber">Invalid Mobile Number</string>
    <string name="alertMessageInvalidMobileNumber">Please enter a valid mobile number.</string>
    <string name="alertTitleNoCountrySelected">No Country Selected</string>
    <string name="alertMessageNoCountrySelected">Please select a country.</string>
    <string name="alertTitleCouldNotSendOTP">Could Not Send OTP</string>
    <string name="alertMessageCouldNotSendOTP">There was an error while sending the OTP. Please try again.</string>
    <string name="alertTitleSignUpBlocked">Sign Up Blocked Temporarily</string>
    <string name="alertMessageSignUpBlocked">Sign up is blocked temporarily due to excessive attempts. Please try again later.</string>
    <string name="alertTitleSignInBlocked">Sign In Blocked Temporarily</string>
    <string name="alertMessageSignInBlocked">Your account is temporarily blocked. Please contact support.</string>
    <string name="alertTitleUserAlreadyExists">Mobile Number Registered</string>
    <string name="alertMessageUserAlreadyExists">The provided mobile number already exists. Please sign in to proceed.</string>
    <string name="alertTitleAccountDoesNotExist">Account Does Not Exist</string>
    <string name="alertMessageAccountDoesNotExist">There is no account associated with the provided mobile number.</string>
    <string name="alertTitleAccountDeactivated">Account Deactivated</string>
    <string name="alertMessageAccountDeactivated">Your account is deactivated. Please contact support.</string>
    <!--  MARK: OTPEntry -->
    <string name="alertTitleInvalidOTP">Invalid OTP</string>
    <string name="alertMessageInvalidOTP">The OTP you entered is incorrect. Please try again.</string>
    <string name="alertTitleSessionExpired">Session Expired</string>
    <string name="alertMessageSessionExpired">Your session has expired. Please try again.</string>
    <string name="alertTitleOTPExpired">OTP Expired</string>
    <string name="alertMessageOTPExpired">The OTP you entered is expired. Please try again.</string>
    <string name="alertTitleInactiveUser">Inactive User</string>
    <string name="alertMessageInactiveUser">This user seems to be inactive.</string>
    <string name="alertTitleOTPLimitReached">OTP Limit Reached</string>
    <string name="alertMessageOTPLimitReached">You have requested the maximum amount of OTPs allowed. Please try again later.</string>
    <string name="alertTitleResendOTPFailed">Could Not Send OTP</string>
    <string name="alertMessageResendOTPFailed">There was an error while sending the OTP. Please try again.</string>
    <!--  MARK: SetSecurityQuestion -->
    <string name="alertTitleStartOver">Start Over?</string>
    <string name="alertMessageStartOver">Are you sure you want to start over? You will have to sign-up again.</string>
    <string name="alertMessageStartOverSignIn">Are you sure you want to start over? You will have to sign-in again.</string>
    <string name="alertTitleMissingQuestions">Insufficient Questions Answered</string>
    <string name="alertMessageMissingQuestions">Please choose and answer the required number of security questions to proceed.</string>
    <string name="alertMessageMissingAnswers">Please answer all the security questions to proceed.</string>
    <!--  MARK: SetPassword -->
    <string name="alertTitlePasswordsDoNotMatch">Passwords Do Not Match</string>
    <string name="alertMessagePasswordsDoNotMatch">The passwords you entered do not match. Please try again.</string>
    <string name="alertTitleDuplicatePassword">Cannot Reuse Password</string>
    <string name="alertMessageDuplicatePassword">The new password cannot be the same as the old password. Please try again.</string>
    <string name="alertTitlePasswordWeak">Weak Password</string>
    <string name="alertMessagePasswordWeak">Chosen password is weak. Please try again with a stronger password.</string>
    <string name="alertTitleSessionTimedOut">Session Timed Out</string>
    <string name="alertMessageSessionTimedOut">Please try again.</string>
    <string name="alertTitleRequestFailed">Request Failed</string>
    <string name="alertMessageRequestFailed">Please try again.</string>
    <!--  MARK: EnterPassword -->
    <string name="alertTitleIncorrectPassword">Incorrect Password</string>
    <string name="alertMessageIncorrectPassword">The password you entered is incorrect. Please try again.</string>
    <!--  MARK: ForgotPassword -->
    <string name="alertTitleIncorrectAnswers">Incorrect Answers</string>
    <string name="alertMessageIncorrectAnswers">One or more answers were found to be incorrect. Please check and try again.</string>
    <string name="alertTitleInvalidAnswers">Invalid Answers</string>
    <string name="alertMessageInvalidAnswers">One or more answers were found to be invalid. Please check and try again.</string>
    <string name="alertTitleResetSuccessful">Reset Successful</string>
    <string name="alertMessageResetSuccessful">Password reset successful. Please sign in again.</string>
    <string name="alertTitleReusedPassword">Reused Password</string>
    <string name="alertMessageReusedPassword">Password cannot be reused. Please select a new password.</string>
    <!--  MARK: EnterKYCDetails -->
    <string name="alertTitleMissingDetails">Details Missing</string>
    <string name="alertMessageMissingDetails">Please fill in all the required fields.</string>
    <string name="alertMessageMissingFirstName">Please enter your first name.</string>
    <string name="alertMessageMissingLastName">Please enter your last name.</string>
    <string name="alertMessageMissingDateOfBirth">Please select your date of birth.</string>
    <string name="alertMessageMissingGender">Please select your gender.</string>
    <string name="alertMessageMissingNationalIdNumber">Please enter your National ID number.</string>
    <string name="alertMessageMissingPlaceOfBirth">Please enter your place of birth.</string>
    <string name="alertMessageMissingIssueDate">Please select your National ID issue date.</string>
    <string name="alertMessageMissingExpiryDate">Please select your National ID expiry date.</string>
    <string name="alertMessageMissingResidentialAddress">Please enter your residential address.</string>
    <string name="alertMessageMissingPostalAddress">Please enter your postal address.</string>
    <string name="alertTitleMissingProfilePicture">Profile Picture Missing</string>
    <string name="alertMessageMissingProfilePicture">Please upload a profile picture.</string>
    <string name="alertTitleMissingDocuments">Documents Missing</string>
    <string name="alertMessageMissingDocuments">Please fully upload all the required documents before continuing.</string>
    <string name="alertTitleWaitForUpload">Uploading Documents</string>
    <string name="alertMessageWaitForUpload">Please wait while the documents are being uploaded.</string>
    <string name="alertMessageDocumentUploadFailed">There was an error while trying to upload documents. Please check your internet connection and try again.</string>
    <string name="alertTitleWaitForPictureUpload">Uploading Picture</string>
    <string name="alertMessageWaitForPictureUpload">Please wait while the picture is being uploaded.</string>
    <string name="alertMessageMissingSignature">Document for Signature is missing. Please fully upload all the required documents before continuing.</string>
    <string name="alertMessageMissingNationalIdFront">Document for National ID - Front is missing. Please fully upload all the required documents before continuing.</string>
    <string name="alertMessageMissingNationalIdBack">Document for National ID - Back is missing. Please fully upload all the required documents before continuing.</string>
    <string name="alertMessageMissingProofOfResidence">Document for Proof of Residence is missing. Please fully upload all the required documents before continuing.</string>
    <string name="alertTitleFailedToPickDocument">Failed to Select Document</string>
    <string name="alertMessageFailedToPickDocument">Unable to load selected document. Please try again.</string>
    <string name="alertTitleFailedToPickImage">Failed to Select Picture</string>
    <string name="alertMessageFailedToPickImage">Unable to load selected picture. Please try again.</string>
    <string name="alertTitleInvalidImageDimensions">Invalid Image Dimensions</string>
    <string name="alertMessageInvalidImageDimensions">Please select an image of resolution 1000x1000 or more. Dimensions of selected image: %1$dx%2$d.</string>
    <string name="alertTitleInvalidPixelCount">Invalid Image</string>
    <string name="alertMessageInvalidPixelCount">Selected image does not match our requirements. Please try again with a different image.</string>
    <string name="alertTitleInvalidEmailId">Invalid Email ID</string>
    <string name="alertMessageInvalidEmailId">Email ID is not valid</string>
    <string name="alertTitleInvalidFileAttribute">Invalid File</string>
    <string name="alertMessageInvalidFileAttribute">Please try again.</string>
    <string name="alertTitleInvalidFileSize">Invalid File Size</string>
    <string name="alertMessageInvalidFileSize">The image size should be between %1$s and %2$s.</string>
    <string name="alertTitleInvalidAge">Invalid Age</string>
    <string name="alertMessageInvalidAge">The selected date of birth is invalid.</string>
    <string name="alertTitleUserTooYoung">User Too Young</string>
    <string name="alertMessageUserTooYoung">The user is too young to use the app. Minimum age to use the app is %1$d.</string>
    <string name="alertTitleInvalidIssueDate">Invalid Issue Date</string>
    <string name="alertMessageInvalidIssueDate">The National ID issue date is invalid. Please try again.</string>
    <string name="alertEnterDetails">Enter Details</string>
    <string name="alertNationalIdNotRecognisedTitle">National ID not recognised</string>
    <string name="alertNationalIdNotRecognisedSubtitle">You can try entering the KYC details manually as well</string>
    <string name="alertTitleInvalidKYCData">Invalid KYC Data</string>
    <string name="alertMessageInvalidKYCData">KYC data entered is incorrect. Please try again</string>
    <string name="alertTitleKYCDataNotFound">KYC Data Not Found</string>
    <string name="alertMessageKYCDataNotFound">The KYC data for the National ID entered was not found. Please try again.</string>
    <string name="alertTitleInvalidInputFormat">Invalid character</string>
    <string name="alertMessageInvalidInputFormat">Special characters are not allowed for %1$s. Please enter only alphabets and numbers.</string>
    <string name="alertTitleUpdatedKYCDetails">KYC Details Updated</string>
    <string name="alertMessageUpdatedKYCDetails">Your KYC details have been updated successfully.</string>
    <string name="alertTitleFlashNotAvailable">Torch Unavailable</string>
    <string name="alertMessageFlashNotAvailable">Torch is not available on this device.</string>
    <string name="alertTitleFlashNotAllowed">Access Denied</string>
    <string name="alertMessageFlashNotAllowed">Torch is not accessible on this device. Please provide required permissions and try again.</string>
    <!--  MARK: SessionPin -->
    <string name="alertTitlePinChanged">Session PIN Changed</string>
    <string name="alertMessagePinChanged">Your Session PIN has been changed successfully.</string>
    <string name="alertTitlePinDoesntMatch">PIN Doesn’t Match</string>
    <string name="alertMessagePinDoesntMatch">The PIN you entered doesn’t match. Please try again.</string>
    <string name="alertTitleIncorrectPin">Incorrect PIN</string>
    <string name="alertTitlePinAttemptsExhausted">Incorrect PIN</string>
    <string name="alertMessagePinAttemptsExhausted">You have exhausted the limit to enter the Session PIN. Please sign in again to setup a new Session PIN.</string>
    <string name="alertTitleSessionPinSignedOut">Are You Sure?</string>
    <string name="alertMessageSessionPinSignedOut">You will be signed out. Sign in again to set up a new Session PIN.</string>
    <string name="alertMessageSessionPinBiometricTouchIDReason">Please authenticate using %1$s.</string>
    <string name="alertTitleSessionPinTouchIDAuthSuccess">Authentication Success</string>
    <string name="alertMessageSessionPinTouchIDAuthSuccess">You have been authenticated successfully</string>
    <string name="alertTitleSessionPinBiometryUnavailable">%1$s unavailable</string>
    <string name="alertMessageSessionPinBiometryUnavailable">Your device is not configured for %1$s authentication.</string>
    <string name="alertTitleNoBiometricsPermission">%1$s Permission Denied</string>
    <string name="alertMessageNoBiometricsPermission">Please give %1$s access permission in the Settings app.</string>
    <string name="alertTitleBiometryAuthenticationFailure">Authentication Failed</string>
    <string name="alertMessageBiometryAuthenticationFailure">Your %1$s authentication has failed. %2$s authentication is disabled.</string>
    <string name="alertTitleFirstPromptBiometryDenied">%1$s Permission Denied</string>
    <string name="alertMessageFirstPromptBiometryDenied">Permission to use %1$s for authentication was denied. %2$s authentication is disabled.</string>
    <string name="alertTitleBiometryCancelled">%1$s Setup Cancelled</string>
    <string name="alertMessageBiometryCancelled">Authentication using %1$s was cancelled. %2$s authentication is disabled.</string>
    <string name="alertTitleBiometryLockedOut">%1$s Locked Out</string>
    <string name="alertMessageBiometryLockedOut">%1$s is locked out due to excessive attempts.</string>
    <string name="alertTitlePasscodeNotSetup">Passcode Not Set</string>
    <string name="alertMessagePasscodeNotSetup">Unable to use %1$s as passcode is not setup on your device.</string>
    <string name="alertTitleBiometryNotEnrolled">%1$s Not Enrolled</string>
    <string name="alertMessageBiometryNotEnrolled">Unable to use %1$s because you have not enrolled to use %2$s on the device.</string>
    <string name="alertTitleBiometryUnknownError">%1$s Unavailable</string>
    <string name="alertMessageBiometryUnknownError">Unable to set up %1$s due to an unknown error. Please try again later.</string>
    <!--  MARK: ProfileScreen -->
    <string name="alertTitleSignOut">Sign Out?</string>
    <string name="alertMessageSignOut">Are you sure that you want to sign out?</string>
    <string name="alertTitleProfileImageUploadFailed">Upload Failed</string>
    <string name="alertMessageProfileImageUploadFailed">There was an error while trying to upload your profile picture. Please check your internet connection and try again.</string>
    <string name="alertTitleProfileImageUploadSuccess">Successfully Uploaded</string>
    <string name="alertMessageProfileImageUploadSuccess">Profile picture has been uploaded successfully.</string>
    <!--  MARK: QRCodeScanner -->
    <string name="alertTitleFailedToStartCamera">Failed to Open QR Code Scanner</string>
    <string name="alertTitleInvalidUser">User Not found</string>
    <string name="alertMessageInvalidUser">Could not find the user on Yafika Mobile.</string>
    <string name="alertTitleInvalidCurrencyMismatch">Currency Mismatch</string>
    <string name="alertMessageInvalidCurrencyMismatch">Wallet and recipient\'s wallet currency does not match.</string>
    <string name="alertTitleNoContactPermission">Contacts Permission Denied</string>
    <string name="alertMessageNoContactPermission">Please give contacts access in the Settings app.</string>
    <string name="alertTitleInvalidPhoneNumber">Mobile Number Invalid</string>
    <string name="alertMessageInvalidPhoneNumber">Please enter a valid mobile number.</string>
    <string name="alertTitleInvalidQRCode">Image Invalid</string>
    <string name="alertMessageInvalidQRCode">There was an error while processing the image. Please check the image and try again.</string>
    <string name="alertTitleFailedToAccessFile">Unable To Access File</string>
    <string name="alertMessageFailedToAccessFile">We are unable to access the selected file. Please try selecting another file.</string>
    <!--  MARK: SendMoney -->
    <string name="alertTitleTransactionAmountMissingAlert">Invalid Transaction Amount</string>
    <string name="alertMessageTransactionAmountMissingAlert">Please enter a valid Transaction Amount.</string>
    <string name="alertTitleTransactionInvalidNarration">Invalid Narration</string>
    <string name="alertMessageTransactionInvalidNarration">Narration is too long.</string>
    <string name="alertTitleTransactionAccountDeactivated">Wallet Deactivated</string>
    <string name="alertMessageTransactionAccountDeactivated">We are unable to process this transaction as your wallet %1$s is deactivated.</string>
    <string name="alertTitleTransactionAmountTooLarge">Amount Too Large</string>
    <string name="alertMessageTransactionAmountTooLarge">We are unable to process this transaction since it exceeds the maximum transaction limit of %1$s.</string>
    <string name="alertMessageTransactionAmountTooLargeNoAmount">We are unable to process this transaction since it exceeds the maximum transaction limit.</string>
    <string name="alertTitleTransactionAmountTooSmall">Amount Too Small</string>
    <string name="alertMessageTransactionAmountTooSmall">We are unable to process this transaction since it is below the minimum transaction limit of %1$s.</string>
    <string name="alertMessageTransactionAmountTooSmallNoAmount">We are unable to process this transaction since it is below the minimum transaction limit.</string>
    <string name="alertTitleTransactionsLimit">Transactions Limit Exceeded</string>
    <string name="alertMessageTransactionsLimit">We are unable to process this transaction as you have exceeded the number of transactions limit.</string>
    <string name="alertTitleSenderTransactionsLimit">Sender Transactions Limit Exceeded</string>
    <string name="alertMessageSenderTransactionsLimit">We are unable to process this transaction since it exceeds your transactions limit.</string>
    <string name="alertTitleReceiverTransactionsLimit">Receiver Transactions Limit Exceeded</string>
    <string name="alertMessageReceiverTransactionsLimit">We are unable to process this transaction since it exceeds the receiver\'s transactions limit.</string>
    <string name="alertTitleTransactionAmountLimit">Daily Transaction Amount Limit Exceeded</string>
    <string name="alertMessageTransactionAmountLimit">We are unable to process this transaction since it exceeds the daily transaction amount limit.</string>
    <string name="alertTitleReceiverTransactionAmountLimit">Receiver\'s Daily Transaction Amount Limit Exceeded</string>
    <string name="alertMessageReceiverTransactionAmountLimit">We are unable to process this transaction since it exceeds the daily transaction amount limit of the receiver.</string>
    <string name="alertTitleSenderTransactionAmountLimit">Daily Transaction Amount Limit Exceeded</string>
    <string name="alertMessageSenderTransactionAmountLimit">We are unable to process this transaction since it exceeds your daily transaction amount limit.</string>
    <string name="alertTitleTransactionReceiverCurrencyNotAllowed">Currency Not Allowed</string>
    <string name="alertMessageTransactionReceiverCurrencyNotAllowed">%1$s does not accept transfers in %2$s.</string>
    <string name="alertTitleTransactionSenderRecipientSame">Invalid Recipient</string>
    <string name="alertMessageTransactionSenderRecipientSame">You cannot transfer money to yourself.</string>
    <string name="alertTitleTransactionInsufficientBalance">Insufficient Balance</string>
    <string name="alertMessageTransactionInsufficientBalance">We are unable to process this transaction due to insufficient balance in your wallet.</string>
    <string name="alertMessageTransactionInsufficientBalanceWithFee">We are unable to process this transaction due to insufficient balance in your wallet. Please note that %1$s will be added as transaction fee.</string>
    <string name="alertTitleTransactionRecipientAmountLimit">Recipient Wallet Cannot Accept Amount</string>
    <string name="alertMessageTransactionRecipientAmountLimit">Cannot transfer money because of limits on the recipient\'s wallet.</string>
    <string name="alertTitleTransactionSenderIsAgent">Transaction Failed</string>
    <string name="alertMessageTransactionSenderIsAgent">We are unable to process this transaction as the agent cannot send money to other Yafika Mobile users through Yafika Mobile.</string>
    <string name="alertTitleTransactionRecipientIsAgent">Transaction Failed</string>
    <string name="alertMessageTransactionRecipientIsAgent">We are unable to process this transaction as the agent cannot receive money from other Yafika Mobile users through Yafika Mobile.</string>
    <string name="alertTitleTransactionFeeExpired">Fee Expired</string>
    <string name="alertMessageTransactionFeeExpired">We are unable to process this transaction as transaction fees timed out.</string>
    <string name="alertTitleTransactionInactiveRecipient">Inactive Recipient</string>
    <string name="alertMessageTransactionInactiveRecipient">%1$s\'s wallet is inactive.</string>
    <string name="alertMessageTransactionInactiveRecipientAlternate">Recipient wallet is inactive.</string>
    <string name="alertTitleSendMoneyToCounterpartyFailed">Transfer Failed</string>
    <string name="alertMessageSendMoneyToCounterpartyFailed">Recipient\'s merchant declined the transaction.</string>
    <string name="alertTitleCurrencyMismatch">Currency Mismatch</string>
    <string name="alertMessageCurrencyMismatch">Wallet and recipient\'s wallet currency does not match.</string>
    <string name="alertTitleReceivingAccountDeactivated">Receiving Wallet Inactive</string>
    <string name="alertMessageReceivingAccountDeactivated">We are unable to process this transaction because your wallet \'%1$s\' is deactivated.</string>
    <string name="alertTitleSendingAccountDeactivated">Sending Wallet Inactive</string>
    <string name="alertMessageSendingAccountDeactivated">We are unable to process this transaction because your wallet \'%1$s\' is deactivated.</string>
    <string name="alertTitleReceiverAccountNotSelected">Receiver Wallet Not Selected</string>
    <string name="alertMessageReceiverAccountNotSelected">Please select a receiver wallet to continue</string>
    <string name="alertTitleSenderAccountNotSelected">Sender Wallet Not Selected</string>
    <string name="alertMessageSenderAccountNotSelected">Please select a sender wallet to continue</string>
    <string name="alertTitleUnableToPerformTransaction">Transaction Failed</string>
    <string name="alertMessageUnableToPerformTransaction">We are unable to perform this transaction. Please try again later.</string>
    <!--  MARK: LoadWallet -->
    <string name="alertTitleLoadWalletBalanceLimitExceed">Balance Limit Exceeded</string>
    <string name="alertMessageLoadWalletBalanceLimitExceed">We are unable to process this transaction since it exceeds the maximum balance limit of the wallet.</string>
    <string name="alertTitleLoadWalletAccountDeactivated">Wallet Deactivated</string>
    <string name="alertMessageLoadWalletAccountDeactivated">We are unable to process this transaction because your wallet %1$s is deactivated.</string>
    <string name="alertTitleLoadWalletMPGSUnreachable">Mastercard Server Unreachable</string>
    <string name="alertMessageLoadWalletMPGSUnreachable">Mastercard\'s servers are not reachable. Please try again later.</string>
    <string name="alertTitleLoadWalletTimerConfirmation">Please Note</string>
    <string name="alertMessageLoadWalletTimerConfirmation">Please complete the transaction within 3 minutes. Do not close the app until the transaction processes.</string>
    <string name="alertTitleLoadWalletPaymentGatewayTimeout">Loading Wallet Failed</string>
    <string name="alertMessageLoadWalletPaymentGatewayTimeout">Loading money to the wallet failed. Please try again.</string>
    <!--  MARK: NotificationsScreen -->
    <string name="alertTitleFailedToLoadNotifications">Failed to Load Notifications</string>
    <string name="alertMessageFailedToLoadNotifications">Failed to fetch notifications due to connection time out.</string>
    <string name="alertTitleFailedToFetchTransactionsInfo">Failed to Fetch Transactions</string>
    <string name="alertMessageFailedToFetchTransactionsInfo">Failed to fetch transactions information. Please try again later.</string>
    <!--  MARK: SelectCounterpartyUser -->
    <string name="alertTitleRecipientNotFound">Cannot Identify</string>
    <string name="alertMessageRecipientNotFound">%1$s cannot identify \"%2$s\"</string>
    <string name="alertTitleUnableToReachCounterparty">Server Unreachable</string>
    <string name="alertMessageUnableToReachCounterparty">Our payment processor\'s servers are not reachable. Please try again later.</string>
    <string name="alertTitleInvalidRecipientName">Invalid Recipient Name</string>
    <string name="alertMessageInvalidRecipientName">Please enter a valid recipient name.</string>
    <string name="alertTitleMissingRecipientName">Missing Recipient Name</string>
    <string name="alertMessageMissingRecipientName">Recipient Name cannot be empty. Please enter a valid recipient name.</string>
    <string name="alertTitleCounterpartyMissingDetails">Missing Recipient Details</string>
    <string name="alertMessageMissingRecipientId">Recipient account ID cannot be empty. Please enter a valid recipient account ID.</string>
    <string name="alertMessageMissingRecipientNumber">Recipient mobile number cannot be empty. Please enter a valid recipient mobile number.</string>
    <string name="alertMessageMissingConfirmRecipientId">Confirm recipient account ID cannot be empty. Please confirm the recipient\'s account ID.</string>
    <string name="alertMessageMissingConfirmRecipientNumber">Confirm recipient mobile number cannot be empty. Please confirm the recipient\'s mobile number.</string>
    <string name="alertTitleCounterpartyNumbersDoNotMatch">Mobile Numbers Do Not Match</string>
    <string name="alertMessageCounterpartyNumbersDoNotMatch">The mobile numbers you entered do not match. Please try again.</string>
    <string name="alertTitleCounterpartyIdsDoNotMatch">Account IDs Do Not Match</string>
    <string name="alertMessageCounterpartyIdsDoNotMatch">The account IDs you entered do not match. Please try again.</string>
    <!--  MARK: ShowQRCode -->
    <string name="alertTitleFailedToLoadQRCode">QR Code Unavailable</string>
    <string name="alertMessageFailedToLoadQRCode">Failed to load QR code. Please try again later.</string>
    <!--  MARK: Transactions -->
    <string name="alertTitleTransactionsAccountDeactivated">Wallet Deactivated</string>
    <string name="alertMessageTransactionsAccountDeactivated">Your wallet is deactivated. Please select an active wallet.</string>
    <!--  MARK: TransactionStatus -->
    <string name="alertTitleCannotAddTransactionRemark">Cannot Add Narration</string>
    <string name="alertMessageCannotAddTransactionRemarkFailed">This transaction has failed, thus remark cannot be added.</string>
    <string name="alertMessageCannotAddTransactionRemarkExpired">This transaction has expired, thus remark cannot be added.</string>
    <string name="alertMessageCannotAddTransactionRemarkCancelled">This transaction was cancelled, thus remark cannot be added.</string>
    <!--  MARK: AddPrivateRemark -->
    <string name="alertTitleCannotAddPrivateRemark">Cannot Add Narration</string>
    <string name="alertMessageCannotAddNarrationRemarkPresent">Remark already present.</string>
    <string name="alertMessageCannotAddNarrationPrivateRemarkUnsupported">Private Remark not supported.</string>
    <string name="alertMessageCannotAddNarrationValidationFailed">Maximum 200 characters allowed for a remark.</string>
    <!--  MARK: ChangeLanguage -->
    <string name="alertTitleLanguageChangeFailure">Unable to Switch Language</string>
    <string name="alertMessageLanguageChangeFailure">There was a problem while trying to switch the language. Please try again.</string>
    <!--  MARK: ChangePassword -->
    <string name="alertTitleIncorrectCurrentPassword">Incorrect Password</string>
    <string name="alertMessageIncorrectCurrentPassword">The current password you entered is incorrect. Please try again.</string>
    <string name="alertTitlePasswordChanged">Password Changed</string>
    <string name="alertMessagePasswordChanged">Your password has been changed successfully.</string>
    <string name="alertTitleMissingPasswordField">Password Missing</string>
    <string name="alertMessageMissingCurrentPasswordField">Current password field cannot be empty. Please try again.</string>
    <string name="alertMessageMissingNewPasswordField">New password field cannot be empty. Please try again.</string>
    <string name="alertMessageMissingConfirmPasswordField">Confirm password field cannot be empty. Please try again.</string>
    <string name="alertTitleInsufficientPasswordLength">Insufficient Password Length</string>
    <string name="alertMessageInsufficientCurrentPasswordLength">Entered current password does not meet our length requirements. Please try again.</string>
    <string name="alertMessageInsufficientNewPasswordLength">Entered new password does not meet our length requirements. Please try again.</string>
    <string name="alertMessageInsufficientConfirmPasswordLength">Entered confirm password does not meet our length requirements. Please try again.</string>
    <!--  MARK: ManageAccounts -->
    <string name="alertTitleCreateAccountDataMissing">Wallet Details Missing</string>
    <string name="alertMessageCreateAccountDataMissing">Please enter an wallet name and select a currency to proceed.</string>
    <string name="alertTitleCreateAccountCurrencyMissing">Currency Missing</string>
    <string name="alertMessageCreateAccountCurrencyMissing">Please select the currency of the new wallet.</string>
    <string name="alertTitleMaximumAccountLimitReached">Maximum Limit Reached</string>
    <string name="alertMessageMaximumAccountLimitReached">Maximum limit of creating wallets in this currency has reached.</string>
    <string name="alertMessageAccountLimitReachedWithoutCurrency">You have reached the limit of number of wallets that can be created.</string>
    <string name="alertTitleAccountNameExists">Wallet Name Exists</string>
    <string name="alertMessageAccountNameExists">A wallet with the name already exists. Please try again.</string>
    <string name="alertTitleAccountNameTooLong">Invalid Wallet Name</string>
    <string name="alertMessageAccountNameTooLong">The wallet name is too long. Please try again.</string>
    <string name="alertTitleActivateAccount">Mark Wallet as Active?</string>
    <string name="alertMessageActivateAccount">Are you sure you want to mark this wallet as active?</string>
    <string name="alertTitleSetDefaultAccount">Set As Default Wallet?</string>
    <string name="alertMessageSetDefaultAccount">Are you sure you want to set this wallet as default wallet?</string>
    <string name="alertTitleMarkAccountInactive">Mark Wallet As Inactive?</string>
    <string name="alertMessageMarkAccountInactive">Are you sure you want to mark this wallet as inactive?</string>
    <string name="alertTitleManageAccountInactive">Inactive Wallet</string>
    <string name="alertMessageManageAccountInactive">We are unable to process the request as the wallet is inactive.</string>
    <string name="alertTitleAccountAlreadyDisabled">Wallet Already Deactivated</string>
    <string name="alertMessageAccountAlreadyDisabled">This wallet is already deactivated. Please contact customer support.</string>
    <string name="alertTitleEditAccountName">Edit Wallet Name</string>
    <string name="alertMessageEditAccountName">Make changes in the text field below to change the wallet name.</string>
    <string name="alertPlaceholderEditAccountName">Edit Wallet Name</string>
    <string name="alertTitleEmptyAccountName">Wallet Name Empty</string>
    <string name="alertMessageEmptyAccountName">Please enter an wallet name to proceed.</string>
    <string name="alertTitleAgentAccountCannotBeMadeInactive">Account Belongs To Agent</string>
    <string name="alertMessageAgentAccountCannotBeMadeInactive">Commission accounts of an agent cannot be made inactive.</string>
    <!--  MARK: CloseMyAccount -->
    <string name="alertTitleCloseAccount">Close Account?</string>
    <string name="alertMessageCloseAccount">Are you sure you want to close your account? If you do, all your Yafika Mobile account data will be removed. You may sign up if you wish to join again.</string>
    <string name="alertTitleAccountClosed">Account Closed</string>
    <string name="alertMessageAccountClosed">Your account has been closed. Please contact the customer support for further assistance.</string>
    <string name="alertTitleAccountAlreadyClosed">Account Already Closed</string>
    <string name="alertMessageAccountAlreadyClosed">This account is already closed. Please contact customer support.</string>
    <string name="alertTitleNonZeroAccount">Wallet Balance Should be Zero</string>
    <string name="alertMessageNonZeroAccount">Your wallet balance must be zero to close your Yafika Mobile account.</string>
    <string name="alertTitlePendingTransactions">Pending Transactions</string>
    <string name="alertMessagePendingTransactions">There are still transactions that are pending. Please wait till the transactions are complete, and try again.</string>
    <string name="alertTitleUserIsAgent">User Is Agent</string>
    <string name="alertMessageUserIsAgent">Agent accounts cannot be closed. Please contact customer support.</string>
    <string name="alertMessageAgentCannotTransfer">Agents are not allowed to make transactions with other people.</string>
    <!--  MARK: PaymentsScreen -->
    <string name="alertTitlePeriodicTransactionLimit">Periodic Transaction Limit Exceeded</string>
    <string name="alertMessagePeriodicTransactionLimit">We are unable to process this transaction as you have exceeded the number of transactions limit</string>
    <string name="alertTitleMonetaryTransactionLimit">Monetary Transaction Limit Exceeded</string>
    <string name="alertMessageMonetaryTransactionLimit">We are unable to process this transaction since it exceeds the daily transaction limit.</string>
    <string name="alertTitleInsufficientBalance">Insufficient Balance</string>
    <string name="alertMessageInsufficientBalance">We are unable to process this transaction due to an insufficient balance in your wallet.</string>
    <string name="alertTitleTransactionFeeConfirmationTimeOut">Confirmation Timed Out</string>
    <string name="alertMessageTransactionFeeConfirmationTimeOut">We are unable to process this transaction as transaction fees timed out.</string>
    <string name="alertTitleUnableToReachProvider">Unable To Reach Provider</string>
    <string name="alertMessageUnableToReachProvider">We are unable to process this transaction as we are unable to reach our payment processor.</string>
    <string name="alertTitlePaymentFailed">Payment Failed</string>
    <string name="alertMessagePaymentFailed">We are unable to process this transaction as our payment processor rejected it.</string>
    <string name="alertTitlePaymentsMissingFields">Details Missing</string>
    <string name="alertMessagePaymentsMissingFields">Please make sure you have filled all the required fields.</string>
    <!--  MARK: TrustedContactsScreen -->
    <string name="alertTitleRemoveTrustedContact">Remove Trusted Contact?</string>
    <string name="alertMessageRemoveTrustedContact">Are you sure you want to remove %1$s from trusted contacts?</string>
    <string name="alertTitleChooseTrustedContact">Are You Sure?</string>
    <string name="alertMessageChooseTrustedContactScreen">Are you sure you want to send the OTP to %1$s, %2$s?</string>
    <string name="alertTitleAddTrustedContactConfirmation">Are You Sure?</string>
    <string name="alertMessageAddTrustedContactConfirmation">Are you sure you want to add %1$s as your trusted contact?</string>
    <string name="alertTitleUserIsTrustedContact">Cannot Add Self</string>
    <string name="alertMessageUserIsTrustedContact">You cannot add yourself as a trusted contact. Please try again.</string>
    <string name="alertTitleTooManyRequests">Too Many Requests</string>
    <string name="alertMessageTooManyRequests">You have reached the limit of number of requests. Please try again after some time.</string>
    <string name="alertTitleTrustedContactAlreadyAdded">Trusted Contact Already Present</string>
    <string name="alertMessageTrustedContactAlreadyAdded">The contact is already a trusted contact.</string>
    <string name="alertTitleTrustedContactLimitReached">Trusted Contact Limit Reached</string>
    <string name="alertMessageTrustedContactLimitReached">You have reached the maximum amount of trusted contacts that can be added.</string>
    <string name="alertTitleMinNumberOfTrustedContactsNotAdded">Insufficient Trusted Contacts</string>
    <string name="alertMessageMinNumberOfTrustedContactsNotAdded">Number of trusted contacts added is insufficient. Please add the minimum number of trusted contacts required.</string>
    <string name="alertTitleTrustedContactsMissingDetails">Details Missing</string>
    <string name="alertMessageTrustedContactMissingCountry">Please select the country of your trusted contact.</string>
    <string name="alertMessageTrustedContactMissingMobileNumber">Please enter the mobile number of your trusted contact.</string>
    <string name="alertMessageTrustedContactMissingFirstName">Please enter the first name of your trusted contact.</string>
    <!--  MARK: EmailVerification -->
    <string name="alertTitleEmailIdSameAsExisting">Same As Existing</string>
    <string name="alertMessageEmailIdSameAsExisting">Cannot update email ID with the existing email ID. Please try again.</string>
    <string name="alertTitleEmailIdAlreadyVerified">Already Verified</string>
    <string name="alertMessageEmailIdAlreadyVerified">The email ID is already verified.</string>
    <string name="alertTitleEmailNotAdded">Email ID Not Added</string>
    <string name="alertMessageEmailNotAdded">Please add an email ID and try again.</string>
    <string name="alertTitleEmailSent">Email Sent</string>
    <string name="alertMessageEmailSent">An email with verification link has been sent successfully to %1$s.</string>
    <string name="retryButtonTitle">Retry</string>
    <!--  MARK: ResetPassword Deeplink -->
    <string name="alertTitleResetPasswordDeeplink">Reset Password</string>
    <string name="alertMessageResetPasswordDeeplinkLoggedIn">You will be signed out. To reset your password you will have to attempt to sign in and choose ‘Forgot Password’. Do you want to proceed?</string>
    <string name="alertMessageResetPasswordDeeplinkLoggedOut">To reset your password you will have to attempt to sign in and choose ‘Forgot Password’.</string>
    <!--  MARK: - Onboarding -->
    <!--  MARK: PrivacyProtection -->
    <string name="privacyProtectionLabel">Content hidden to protect your privacy</string>
    <string name="noInternetBannerText">Could not connect to the internet!</string>
    <string name="backOnlineBannerText">Back Online!</string>
    <!--  MARK: LanguageSelector -->
    <string name="languageSelectorTitleLabel">Select Your Language</string>
    <string name="languageSelectorOptionEnglish">English</string>
    <string name="languageSelectorOptionNyanja">Nyanja</string>
    <string name="languageSelectorContinue">Continue</string>
    <string name="languageSelectorChangeLanguage">Change Language</string>
    <!--  MARK: OnboardingSlides -->
    <string name="onboardingGlobalTransfer">Send Money</string>
    <string name="onboardingGlobalTransferSubtitle">Transfer money to other Yafika users and bank accounts easily.</string>
    <string name="onboardingMultiCurrency">Pay Bills</string>
    <string name="onboardingMultiCurrencySubtitle">Recharges and bill payments made simple and easy.</string>
    <string name="onboardingDataSecurity">Data security</string>
    <string name="onboardingDataSecuritySubtitle">Set up a Session PIN to lock and protect your data.</string>
    <!--  MARK: - RequiredDocuments -->
    <string name="requiredDocumentsTitle">Requirements to Sign Up</string>
    <string name="requiredDocumentsSubtitile">To complete your sign up process smoothly, make sure that you are ready with the below mentioned requirements.</string>
    <string name="mandatoryDocumentsTitle">Mandatory Documents</string>
    <string name="mandatoryDocumentsSubtitle">• National ID \n• Signature \n• Proof of residence</string>
    <string name="requiredDocumentsTrustedContactsTitle">2 Trusted Contacts</string>
    <!--  "so you can enter it in the app" seems a bit casual. -->
    <string name="requiredDocumentsTrustedContactsSubtitle">You will need 2 trusted contacts to be available. They will receive an OTP via SMS that they will need to communicate to you, so you can enter it in the app.</string>
    <string name="requiredDocumentsSignUpLaterTitle">Sign Up Later</string>
    <string name="requiredDocumentsSignUpLaterSubtitle">If you don’t have these ready, sign up later.</string>
    <string name="requiredDocumentsContinueButtonTitle">Continue</string>
    <!--  MARK: - SignUpIn -->
    <string name="signUpInSignIn">Sign In</string>
    <string name="signUpInSignUp">Sign Up</string>
    <string name="signUpInAlreadyHaveAccountLabel">Already have an account?</string>
    <string name="signUpInDontHaveAccountLabel">Don’t have a Yafika Mobile account?</string>
    <string name="signUpInCountryLabel">Country</string>
    <string name="signUpInCountryPlaceHolder">Select Country</string>
    <string name="signUpInMobileNumberLabel">Mobile Number</string>
    <string name="signUpInMobileNumberPlaceHolder">Mobile Number</string>
    <string name="signUpInMobileNumberSignUpTitle">Sign up to Yafika Mobile</string>
    <string name="signUpInMobileNumberSignUpSubtitle">Use your mobile number to sign up</string>
    <string name="signUpInMobileNumberSignInTitle">Sign in to Yafika Mobile</string>
    <string name="signUpInMobileNumberSignInSubtitle">Use your mobile number to sign in</string>
    <string name="signUpInAgeAgreement">I agree that I am 18 years of age or above</string>
    <string name="countryCodePickerTitle">Select Your Country</string>
    <string name="signUpInCountryCodeFlagLabel">%1$s %2$s</string>
    <string name="countryCodeCellLabel">%1$s (%2$s)</string>
    <!--  MARK: OTPEntry -->
    <string name="otpEntryTitle">Enter OTP</string>
    <string name="otpEntrySubtitle">OTP sent to %1$s</string>
    <string name="otpEntryPlaceHolder">Enter OTP</string>
    <string name="otpEntryLabel">OTP</string>
    <string name="otpEntryResendOTPLabelTimer">Resend (%1$s)</string>
    <string name="otpEntryResendOTPLabel">Resend</string>
    <string name="otpEntryOTPValidTill">• OTP valid till \"%1$s\"</string>
    <string name="otpEntryVerifyButton">Verify OTP</string>
    <!--  MARK: SetSecurityQuestion -->
    <string name="setSecurityQuestionNavigationTitle">Set your security questions</string>
    <string name="setSecurityQuestionTitle">Set your security questions</string>
    <string name="setSecurityQuestionSubTitle">These answers will be saved and can be used to recover a forgotten password</string>
    <string name="setSecurityQuestionStartOver">Start Over</string>
    <string name="setSecurityQuestionChooseQuestion">Choose Question - %1$d</string>
    <string name="setSecurityQuestionAnswer">Answer</string>
    <string name="setSecurityQuestionSubmitButton">Submit</string>
    <string name="setSecurityQuestionDetailViewTitle">Select Your Question</string>
    <!--  MARK: SetPassword -->
    <string name="setPasswordTitle">Set Password</string>
    <!--  Subtitle seems redundant as the placeholders convey the required message. -->
    <string name="setPasswordSubtitle">Enter and confirm password to sign up</string>
    <string name="setPasswordEnterPassword">Enter New Password</string>
    <string name="setPasswordConfirmPassword">Confirm New Password</string>
    <string name="setPasswordInstructionsTitle">Your password must:</string>
    <string name="setPasswordInstructionsPoint1">• Be at least 8 characters long.</string>
    <string name="setPasswordInstructionsPoint2">• Contain at least 1 upper case character, 1 lower case character, 1 special character and 1 digit.</string>
    <string name="setPasswordInstructionsPoint3">• Not contain common dictionary words.</string>
    <string name="setPasswordInstructionsPoint4">• Not start or end with white spaces.</string>
    <string name="setPasswordButtonTitle">Set Password</string>
    <!--  MARK: EnterPassword -->
    <string name="enterPasswordTitle">Enter password</string>
    <string name="enterPasswordSubtitle">Please enter your password to sign in</string>
    <string name="enterPasswordTextFieldPlaceholder">Enter Password</string>
    <string name="enterPasswordSignInButtonTitle">Sign In</string>
    <string name="enterPasswordForgotPassword">Forgot Password?</string>
    <!--  MARK: ForgotPassword -->
    <string name="forgotPasswordAnswerSecurityTitle">Security Questions</string>
    <string name="forgotPasswordAnswerSecuritySubtitle">Answer the following questions correctly to reset your password.</string>
    <string name="forgotPasswordChooseTrustedContactMessage">Can’t answer the questions? Ask your trusted contacts for help.</string>
    <string name="forgotPasswordChooseTrustedContactTitle">Choose Trusted Contacts</string>
    <string name="forgotPasswordAnswerSecuritySubmitButton">Submit</string>
    <string name="forgotPasswordResetPasswordTitle">Reset Password</string>
    <!--  Subtitle seems redundant as the placeholders convey the required message. -->
    <string name="forgotPasswordResetPasswordSubtitle">Enter and confirm password to continue</string>
    <string name="forgotPasswordResetPasswordButton">Reset Password</string>
    <!--  MARK: EnterKYCDetails -->
    <string name="kycDetailsTitle">Enter KYC Details</string>
    <string name="kycDetailsSubtitle">Fill your details to continue.</string>
    <string name="kycBasicDetailsLabel">Basic Details</string>
    <string name="kycFirstNameTitle">First Name</string>
    <string name="kycFirstNamePlaceholder">First Name</string>
    <string name="kycLastNameTitle">Last Name</string>
    <string name="kycLastNamePlaceholder">Last Name (Optional)</string>
    <string name="kycOtherNameTitle">Other Name</string>
    <string name="kycOtherNamePlaceholder">Other Name (Optional)</string>
    <string name="kycDateOfBirthTitle">Date of Birth</string>
    <string name="kycDateOfBirthPlaceholder">Select Date</string>
    <string name="kycGenderTitle">Gender</string>
    <string name="kycGenderPlaceholder">Select Gender</string>
    <string name="kycNationalIdNoTitle">National ID No</string>
    <string name="kycNationalIdNoPlaceholder">ID Number</string>
    <string name="kycEmailIdTitle">Email ID</string>
    <string name="kycEmailIdPlaceholder">Email ID (Optional)</string>
    <string name="kycOccupationTitle">Occupation</string>
    <string name="kycOccupationPlaceholder">Occupation (Optional)</string>
    <string name="kycOccupationResidentAddress">Resident Address</string>
    <string name="kycOccupationPostalAddress">Postal Address</string>
    <string name="kycNextButtonTitle">Next</string>
    <string name="kycDatePickerConfirm">Confirm</string>
    <string name="kycDatePickerCancel">Cancel</string>
    <string name="kycDateOfBirthPickerTitle">Select your date of birth</string>
    <string name="kycGenderPickerTitle">Select your gender</string>
    <string name="kycNationalIdIssuePickerTitle">Select your National ID issue date</string>
    <string name="kycNationalIdExpiryPickerTitle">Select your National ID expiry date</string>
    <string name="kycAlertAddProfilePhoto">Add your profile photo</string>
    <string name="kycAlertChooseCamera">Capture Using Camera</string>
    <string name="kycAlertChooseGallery">Choose From Photo Library</string>
    <string name="kycAlertChooseFiles">Choose From Files</string>
    <string name="kycAlertCancel">Cancel</string>
    <string name="kycScanQRCodeTitle">Scan QR Code</string>
    <string name="kycScanQRCodeSubTitle">Your KYC details will be filled out automatically by scanning the QR code using the camera.</string>
    <string name="kycEnterDetailsManuallyTitle">Enter Manually</string>
    <string name="kycEnterDetailsManuallySubtitle">Enter the KYC details manually.</string>
    <string name="kycScanQRCodeVerifyNationalIdTitle">Verify National ID</string>
    <!--  "or you can fill in the details manually" seems unnecessary here. The user is given an option to enter manually in the previous screen, and a message will be shown if QR code scan fails. -->
    <string name="kycScanQRCodeVerifyNationalIdSubtitle">Please scan the QR code behind your National ID card, or you can fill in the details manually.</string>
    <string name="kycScanQRCodeFailedToVerifyNationalIdSubtitle">National ID not recognized, please try again. You can try entering the KYC details manually as well.</string>
    <string name="kycNationalIdIssuedDateTitle">National ID Issued Date</string>
    <string name="kycNationalIdExpiryDateTitle">National ID Expiry Date</string>
    <string name="kycSelectDatePlaceholder">Select Date</string>
    <string name="kycPlaceOfBirthTitle">Place of Birth</string>
    <string name="kycPlaceOfBirthPlaceholderText">District</string>
    <string name="kycNationalIdFrontTitle">Photo of your National ID - Front</string>
    <string name="kycNationalIdBackTitle">Photo of your National ID - Back</string>
    <string name="kycCameraPermissionDeniedTitle">Camera Permission Denied</string>
    <string name="kycCameraPermissionDeniedSubtitle">Please give camera access in the Settings app.</string>
    <!--  MARK: UploadPhotos -->
    <string name="kycUploadPhotosTitle">Upload Documents</string>
    <!--  We accept documents (pdfs) as well, so "photos" can be changed to something more appropriate. -->
    <string name="kycUploadPhotosSubtitle">Upload the following photos to continue</string>
    <!--  Can remove "Photo of your" in all the fields. The title and subtitle already conveys it. -->
    <string name="kycUploadPhotoOfSignature">Photo of your signature</string>
    <string name="kycUploadPhotoOfPassportFront">Photo of your passport - Front (optional)</string>
    <string name="kycUploadPhotoOfPassportBack">Photo of your passport - Back (optional)</string>
    <string name="kycUploadProofOfResidence">Proof of residence</string>
    <string name="kycUploadPhotoSubmit">Submit</string>
    <string name="kycUploadPhotoPickerSignature">Add photo of your signature</string>
    <string name="kycUploadPhotoPickerNationalIdFront">Add photo of your National ID - Front</string>
    <string name="kycUploadPhotoPickerNationalIdBack">Add photo of your National ID - Back</string>
    <string name="kycUploadPhotoPickerPassportFront">Add photo of your Passport - Front</string>
    <string name="kycUploadPhotoPickerPassportBack">Add photo of your Passport - Back</string>
    <string name="kycUploadPhotoPickerProofOfResidence">Add photo of your proof of residence</string>
    <!--  MARK: SetupSessionPin -->
    <string name="setupSessionPinOnboardingTitle">Setup Session PIN</string>
    <!--  "so only you can use this app" seems casual. Can be changed to something more appropriate. -->
    <string name="setupSessionPinOnboardingSubtitle">Set up a Session PIN so only you can use this app. Session PINs are never synced to our servers.</string>
    <string name="setupSessionPinOnboardingContinue">Continue</string>
    <string name="setupSessionPinTitle">Setup Session PIN</string>
    <string name="setupSessionPinScreenTitle">Setup a Session PIN for Yafika Mobile</string>
    <string name="setupSessionPinEnterNewSessionPinTitle">Enter new Session PIN</string>
    <string name="setupSessionPinBypassUsingBiometrics">Bypass Session PIN entry using %1$s</string>
    <string name="confirmSessionPinTitle">Confirm Session PIN</string>
    <string name="confirmNewPinTitle">Confirm new Session PIN</string>
    <string name="enterSessionPinTitle">Enter Session PIN</string>
    <string name="enterSessionPinBiometricAlternateTitle">Unlock with Session PIN</string>
    <string name="enterSessionPinBiometricTitle">Unlock Yafika Mobile</string>
    <string name="enterSessionPinForgotPin">Forgot Session PIN?</string>
    <string name="enterCurrentAppPinTitle">Enter current Session PIN</string>
    <string name="pinDoesntMatchLabel">The pin you entered doesn’t match. Please try again</string>
    <string name="biometricsNoPermissionLabel">%1$s access permission denied</string>
    <string name="biometricsLockoutLabel">%1$s is locked out due to excessive attempts</string>
    <string name="biometricsNotEnrolledLabel">Device not enrolled to use %1$s</string>
    <string name="passcodeNotSetLabel">%1$s is unavailable as passcode is not set on the device</string>
    <string name="lastSessionPinAttemptLabel">You have 1 Session PIN attempt left</string>
    <!--  MARK: - HomeScreen -->
    <string name="homeScreenMoneyTitle">Money</string>
    <string name="homeScreenTransactionsTitle">Transactions</string>
    <string name="homeScreenProfileTitle">Profile</string>
    <string name="homeScreenPaymentsTitle">Payments</string>
    <!--  MARK: - MoneyScreen -->
    <string name="moneyScreenNavigationLeftTitleDefault">-</string>
    <string name="moneyScreenAvailableBalance">Available Balance</string>
    <string name="moneyScreenAvailableBalancePlaceholder">-</string>
    <string name="moneyScreenSendOrLoad">Send or Load</string>
    <string name="moneyScreenMoneyTransfer">Money Transfer</string>
    <string name="moneyScreenLoadWallet">Load Wallet</string>
    <string name="availableBalanceValueFormat">%1$s %2$s</string>
    <!--  MARK: AccountPicker -->
    <string name="moneyScreenAccountPickerViewTitle">Select Wallet</string>
    <string name="moneyScreenAccountPickerBalance">Balance %1$s</string>
    <!--  MARK: - TransactionsScreen -->
    <string name="transactionsScreenNoRecentTransactionsMessage">No recent transactions. To make a transfer, choose a recipient from the contact list or scan their QR code.</string>
    <string name="transactionScreenMakeTransactionButtonTitle">Make A Transaction</string>
    <string name="transactionsYesterdayTitle">Yesterday</string>
    <string name="transactionsTodayTitle">Today</string>
    <!--  MARK: - TransactionStatusScreen -->
    <string name="transactionStatusScreenDetailsLabel">Transaction Details</string>
    <string name="transactionStatusScreenIdLabel">Transaction ID:</string>
    <string name="transactionStatusScreenTransactionAmount">\U2022 You were charged %1$s for this transaction.</string>
    <string name="transactionStatusScreenExchangeRateInfo">\U2022 The current exchange rate is %1$s.</string>
    <string name="transactionStatusSuccessLabel">Transaction Successful</string>
    <string name="transactionStatusFailedLabel">Transaction Failed</string>
    <string name="transactionStatusPendingLabel">Transaction Pending</string>
    <string name="transactionStatusCancelledLabel">Transaction Cancelled</string>
    <string name="transactionStatusExpiredLabel">Transaction Expired</string>
    <string name="transactionStatusReversedLabel">Transaction Reversed</string>
    <string name="transactionStatusNeedHelpLabel">Need Help?</string>
    <string name="transactionStatusContactSupportButtonTitle">Contact Support</string>
    <string name="transactionStatusContactSupportLabel">Contact support in case of any issues with the transaction.</string>
    <string name="transactionStatusAccountDetailsLabel">Wallet Details</string>
    <string name="transactionStatusDebitedFromLabel">Debited from:</string>
    <string name="transactionStatusCreditedToLabel">Credited to:</string>
    <string name="transactionStatusRemarksLabel">Transaction Remarks</string>
    <string name="transactionStatusNarrationToSelfLabel">Narration to self:</string>
    <string name="transactionStatusNarrationForReceiverLabel">Narration for receiver:</string>
    <string name="transactionStatusAddNarrationButtonTitle">Add Narration</string>
    <string name="transactionStatusPaymentToLabel">Payment to</string>
    <string name="transactionStatusPaymentFromLabel">Payment from</string>
    <string name="transactionStatusDoneButtonTitle">Done</string>
    <string name="transactionStatusZeroTransactionFee">0</string>
    <string name="transactionStatusNilReceiver">Unknown Wallet</string>
    <string name="transactionStatusNilSender">Unknown Wallet</string>
    <string name="transactionStatusGenericFailureReason">Transaction failed due to an unknown error.</string>
    <!--  MARK: Contact Support -->
    <string name="contactSupportCallUsLabel">Call Us</string>
    <string name="contactSupportSendAnEmailLabel">Send an Email</string>
    <!--  MARK: AddPrivateRemarkScreen -->
    <string name="addPrivateRemarkPlaceholder">Narration to Self</string>
    <string name="addPrivateRemarkLabel">Narration to self will only be visible to you.</string>
    <string name="addPrivateRemarkScreenTitle">Add Remark</string>
    <string name="addPrivateRemarkAddButtonTitle">Add</string>
    <string name="addPrivateRemarkEmptyTextAlertTitle">Error</string>
    <string name="addPrivateRemarkEmptyTextAlertMessage">Remark cannot be empty. Please try again.</string>
    <!--  MARK: ProfileScreen -->
    <string name="profileScreenSignOutTitle">Sign Out</string>
    <string name="profileUserDisplayName">%1$s %2$s</string>
    <string name="profileScreenCloseAccountTitle">Close My Account</string>
    <string name="profileScreenOptionChangePassword">Change Password</string>
    <string name="profileScreenOptionFindAgent">Find Agent</string>
    <string name="profileScreenOptionChangeSessionPin">Change Session PIN</string>
    <string name="profileScreenOptionChangeLanguage">Change Language</string>
    <string name="profileScreenOptionShowQRCode">Show QR Code</string>
    <string name="profileScreenOptionManageAccounts">Manage Wallets</string>
    <string name="profileScreenOptionStatements">Statements</string>
    <string name="profileScreenOptionThirdPartySoftware">Third Party Software</string>
    <string name="profileScreenOptionPrivacyPolicy">Privacy Policy</string>
    <string name="profileScreenOptionUpdateKYCDetails">Update KYC Details</string>
    <string name="profileScreenOptionManageTrustedContacts">Manage Trusted Contacts</string>
    <string name="profileScreenOptionShareApp">Share App</string>
    <string name="profileScreenOptionContactSupport">Contact Support</string>
    <string name="profileScreenAddEmail">Add Email ID</string>
    <string name="profileScreenEditEmail">Edit</string>
    <string name="profileScreenAppInfo">App Info</string>
    <string name="profileScreenShareAppDescription">Hi, I just invited you to use the Yafika Mobile app! This app allows you to make instant money transfers, bill payments, and more. Download this fast and secure payment app now: %1$s</string>
    <!--  MARK: AppInfo -->
    <string name="appInfoAppVersion">Version %1$s (%2$s)</string>
    <string name="appInfoSendReports">Send anonymous usage reports</string>
    <string name="appInfoShareLogs">Share Logs</string>
    <!--  MARK: EmailVerification -->
    <string name="emailVerificationAddEmailTitle">Add Email ID</string>
    <string name="emailVerificationAddEmailMessage">By adding and verifying your email ID, you will receive alerts for wallet activity to your email ID directly.</string>
    <string name="emailVerificationUpdateEmailTitle">Update Email ID</string>
    <string name="emailVerificationUpdateEmailMessage">By updating and verifying your email ID, you will receive alerts for wallet activity to your email ID directly.</string>
    <string name="emailVerificationEmailTextfieldLabel">Email ID</string>
    <string name="emailVerificationEmailTextfieldPlaceholder">Enter Email ID</string>
    <string name="emailVerificationVerifyEmailTitle">Verify your Email</string>
    <string name="emailVerificationVerifyEmailMessage">Verify your email ID to receive alerts for wallet activity</string>
    <string name="emailVerificationSendVerificationLink">Send Verification Link</string>
    <!--  MARK: QRCodeScanner -->
    <string name="qrCodeScannerViewTitle">Scan &amp; Pay</string>
    <string name="qrCodeScannerUpdateKYCViewTitle">Scan QR Code</string>
    <string name="qrCodeScannerOverlayTitle">Transfer money by scanning a QR Code or entering a mobile number</string>
    <string name="qrCodeInvalidRecipient">This QR code doesn’t represent a recipient</string>
    <string name="qrCodeScanValidQRCodeToPay">Scan a valid QR to pay</string>
    <string name="qrCodeScannerEnterNameMobileNo">Enter Name/Mobile No.</string>
    <string name="qrCodeScannerRecents">Recents</string>
    <string name="qrCodeScannerUploadFromGallery">Upload from Gallery</string>
    <!--  MARK: ContactPicker -->
    <string name="contactsPickerViewTitle">Pick a Contact</string>
    <string name="contactsPickerSearchBarPlaceholder">Enter Name/Mobile No.</string>
    <string name="contactsPickerNewNumber">New Number</string>
    <string name="contactsPermissionLabel">Access to your contacts is required to select a contact from your address book.</string>
    <string name="contactsPermissionButton">Allow Contacts Permission</string>
    <string name="contactsPickerCountryCodePickerTitle">Select Recipient Country</string>
    <string name="contactPickerCountryCodePickerCancel">Cancel</string>
    <!--  MARK: SendMoney -->
    <string name="sendMoneyViewTitle">Send Money</string>
    <string name="sendMoneyEnterAmount">Enter Amount</string>
    <string name="sendMoneyNarrationToSelfPlaceholder">Narration to Self (Optional)</string>
    <string name="sendMoneyNarrationToSelf">Narration to self will only be visible to you.</string>
    <string name="sendMoneyNarrationToReceiverPlaceholder">Narration for Receiver (Optional)</string>
    <string name="sendMoneyNarrationToReceiver">Narration for receiver will be visible for everyone.</string>
    <string name="sendMoneySelectAccount">Wallet</string>
    <string name="sendMoneyAttemptPayment">Attempt Payment</string>
    <string name="sendMoneyPayFromWallet">PAY FROM WALLET</string>
    <!--  MARK: SelectAccount -->
    <string name="selectAccountViewTitle">Select Wallet</string>
    <string name="selectAccountAccountID">Wallet ID ****%1$s</string>
    <string name="selectAccountAcceptTransfers">%1$s can only accept transfers in %2$s.</string>
    <!--  MARK: NotificationsScreen -->
    <string name="notificationScreenTitle">Notifications</string>
    <string name="notificationScreenCloseButtonTitle">Close</string>
    <string name="notificationScreenNoNotificationsMessage">No New Notifications</string>
    <!--  MARK: LoadWallet -->
    <string name="loadWalletViewTitle">Load Wallet</string>
    <string name="loadWalletEnterAmount">Enter Amount</string>
    <string name="loadWalletNarrationToSelfPlaceholder">Narration to Self (Optional)</string>
    <string name="loadWalletNarrationToSelf">Narration to self will only be visible to you.</string>
    <string name="loadMoneyLoadFromWallet">LOAD TO WALLET</string>
    <!--  MARK: SelectDestination -->
    <string name="selectDestinationViewTitle">Select Destination</string>
    <string name="selectDestinationBCNDestination">Yafika Mobile</string>
    <string name="selectDestinationOtherProviders">Other Providers</string>
    <string name="selectDestinationServerNotReachable">Server is not reachable, try again later.</string>
    <string name="selectDestinationTryAgain">Try Again</string>
    <string name="selectDestinationNoProviders">No Providers Found</string>
    <string name="selectDestinationUserIsAgentLabel">Money cannot be transferred to another Yafika Mobile user from your wallet since you are an agent.</string>
    <!--  MARK: MoneyTransfer -->
    <string name="moneyTransferViewTitle">Money Transfer</string>
    <string name="moneyTransferToAnotherPerson">To Another Person</string>
    <string name="moneyTransferToSelf">To Own Wallet</string>
    <!--  MARK: SelectCounterpartyUser -->
    <string name="selectCounterpartyUserViewTitle">Select User</string>
    <string name="selectCounterpartyUserAccountId">Enter Recipient Account ID</string>
    <string name="confirmCounterpartyUserAccountId">Confirm Recipient Account ID</string>
    <string name="selectCounterpartyUserPhoneNumber">Enter Recipient Mobile Number</string>
    <string name="confirmCounterpartyUserPhoneNumber">Confirm Recipient Mobile Number</string>
    <string name="selectCounterpartyUserName">Enter Recipient Name</string>
    <string name="selectCounterpartyUserNext">Next</string>
    <!--  MARK: ShowQRCode -->
    <string name="showQRCodeSelectorMyCodeTitle">My Code</string>
    <string name="showQRCodeSelectorMyAccountsTitle">My Wallets</string>
    <string name="showQRCodeScreenTitle">Show QR Code</string>
    <string name="showQRCodeUserMessageText">Use this QR code to receive payment in your default wallet. For other wallets, go to Wallets.</string>
    <string name="showQRCodeAccountMessageText">The QR codes are unique to each wallet. Please look through the list for other added wallet QR codes.</string>
    <string name="showQRCodeGetLinkLabelText">Get Link</string>
    <string name="showQRCodeShareCodeLabelText">Share</string>
    <string name="showQRCodeSelectAccountTitle">Select Wallet</string>
    <string name="showQRCodeLinkValue">%1$s has shared the payment link with you. To make a payment, please open this link on the browser - %2$s</string>
    <string name="shareQRCodeMessageText">%1$s has shared a QR Code with you. You can scan this code from your Yafika Mobile app to transfer money.</string>
    <!--  MARK: AccountToAccount -->
    <string name="accountToAccountViewTitle">Wallet to Wallet</string>
    <string name="accountToAccountFromAccount">From</string>
    <string name="accountToAccountFromAccountDescription">Select the wallet to transfer from</string>
    <string name="accountToAccountToAccount">To</string>
    <string name="accountToAccountToAccountDescription">Select the wallet to transfer to</string>
    <string name="accountToAccountNarrationToSelfPlaceholder">Narration to Self (Optional)</string>
    <string name="accountToAccountNarrationToSelf">Narration to self will only be visible to you.</string>
    <string name="accountToAccountEnterAmount">Enter Amount</string>
    <string name="accountToAccountPayNow">Pay Now</string>
    <!--  MARK: ChangePassword -->
    <string name="changePasswordTitle">Change Password</string>
    <string name="changePasswordEnterCurrentPasswordTitle">Enter Current Password</string>
    <string name="changePasswordEnterNewPasswordTitle">Enter New Password</string>
    <string name="changePasswordConfirmNewPasswordTitle">Confirm New Password</string>
    <!--  MARK: ManageAccounts -->
    <string name="manageAccountsViewTitle">Manage Wallets</string>
    <string name="manageAccountsActiveAccounts">Active Wallets</string>
    <string name="manageAccountsInactiveAccounts">Inactive Wallets</string>
    <string name="manageAccountsActivateButton">Activate</string>
    <string name="manageAccountsDefaultAccount">Default Wallet</string>
    <string name="manageAccountsNoInactiveAccount">No inactive wallets were found.</string>
    <!--  MARK: CreateNewAccount -->
    <string name="createNewAccountViewTitle">Create Wallet</string>
    <string name="createNewAccountEnterAccountName">Enter Wallet Name</string>
    <string name="createNewAccountSelectCurrency">Select Currency</string>
    <string name="createNewAccountButtonTitle">Create New Wallet</string>
    <!--  MARK: SelectCurrency -->
    <string name="selectCurrencyViewTitle">Select Currency</string>
    <!--  MARK: AccountDetails -->
    <string name="accountDetailsViewTitle">Wallet Details</string>
    <string name="accountDetailsSetDefaultAccount">Set as Default Wallet</string>
    <string name="accountDetailsEditAccountName">Edit Wallet Name</string>
    <string name="accountDetailsMarkInactive">Mark Inactive</string>
    <!--  MARK: CloseMyAccount -->
    <string name="closeMyAccountPasswordViewTitle">Close My Account</string>
    <string name="closeMyAccountPasswordTitle">Enter Password</string>
    <string name="closeMyAccountPasswordSubtitle">Please confirm your password to close your Yafika Mobile account</string>
    <string name="closeMyAccountPasswordButtonTitle">Close Account</string>
    <!--  MARK: FindAgent -->
    <string name="findAgentViewTitle">Find Agent</string>
    <string name="findAgentSectionAll">All</string>
    <string name="findAgentSectionOpen">Open</string>
    <string name="findAgentCallNow">Call Now</string>
    <string name="findAgentGetDirections">Get Directions</string>
    <string name="findAgentLocationPermissionNotPresent">Access to your Location is needed to search for agents near you.</string>
    <string name="findAgentLocationPermissionNotPresentButton">Allow Location Access</string>
    <string name="findAgentShopStatusOpen">Open</string>
    <string name="findAgentShopStatusClosed">Closed</string>
    <!--  MARK: Payments -->
    <string name="paymentDetailsTitle">Payment Details</string>
    <string name="paymentsOptionsTitle">Select Plan</string>
    <!--  MARK: Statements -->
    <string name="statementsTitle">Statements</string>
    <string name="statementTitle">%1$s Statement</string>
    <string name="statementCaption">%1$s to %2$s</string>
    <string name="statementsPlaceholderMessage">There are no statements yet</string>
    <!--  MARK: Trusted Contacts -->
    <string name="trustedContactsOnboardingTitle">Setup Trusted Contacts</string>
    <string name="trustedContactsOnboardingMessage">Trusted contacts are people who can securely help you if you’re having trouble accessing your account.</string>
    <string name="trustedContactsOnboardingPointOne">Make sure that the trusted contacts are available during the setup.</string>
    <string name="trustedContactsOnboardingPointTwo">Your trusted contact should make sure it’s you before giving you the OTP.</string>
    <string name="trustedContactsOnboardingPointThree">Add 2 contacts who can help, if you’re having trouble accessing your account.</string>
    <string name="trustedContactsOnboardingPointFour">Make sure the setup is complete within%1$s, or you will have to start over again.</string>
    <string name="showTrustedContactsMessage">Add 2 contacts who can help, if you’re having trouble with accessing your account.</string>
    <string name="noTrustedContactsAddedMessage">You have no trusted contacts added. Tap the plus icon on top right to start adding.</string>
    <string name="finishButtonTitle">Finish</string>
    <string name="addTrustedContactTitle">Add Trusted Contact</string>
    <string name="addTrustedContactMessage">Enter the details below to add the trusted contact to your list.</string>
    <string name="addTrustedContactsMobileNumber">Mobile Number</string>
    <string name="addTrustedContactCountry">Country</string>
    <string name="addTrustedContactSelectCountry">Select Country</string>
    <string name="addTrustedContactPickContact">Pick a Contact</string>
    <string name="addTrustedContactOtherDetails">Other Details</string>
    <string name="addTrustedContactsFirstName">First Name</string>
    <string name="addTrustedContactsFirstNamePlaceholder">First Name</string>
    <string name="addTrustedContactsLastName">Last Name</string>
    <string name="addTrustedContactsLastNamePlaceholder">Last Name (Optional)</string>
    <string name="addTrustedContactsEmail">Email ID</string>
    <string name="addTrustedContactsEmailPlaceholder">Email ID (Optional)</string>
    <string name="addTrustedContactCountryCodeFlagLabel">%1$s %2$s</string>
    <string name="addTrustedContactCountryCodeFormat">%1$s (%2$s)</string>
    <string name="addTrustedContactInvalidDetailsTitle">Invalid details</string>
    <string name="addTrustedContactInvalidDetailsMessage">Please enter valid first name and last name (maximum 200 characters).</string>
    <string name="chooseTrustedContactsTitle">Choose Trusted Contact</string>
    <string name="chooseTrustedContactsMessage">Choose one of the trusted contacts from the list. An OTP will be sent to them. Using this OTP, you will be able to access your account.</string>
    <string name="manageTrustedContactsTitle">Manage Trusted Contacts</string>
    <string name="manageTrustedContactsMessage">Add 2 to 5 contacts who can help, if you’re having trouble with accessing your account.</string>
    <!--  MARK: Android Strings -->
    <string name="alertActionOk">OK</string>
    <string name="alertTransactionTermsAndConditionsTitle">Terms and Conditions</string>
    <string name="alertTransactionReceiveAmount">Receive Amount</string>
    <string name="alertLoading">Loading…</string>
    <string name="alertTitleNotificationNotAvailable">Notification Access Denied</string>
    <string name="alertMessageNotificationNotAvailable">Allow Yafika Mobile to send notifications in the Settings app.</string>
    <string name="alertTitleLocationAccessDenied">Location Access Denied</string>
    <string name="alertMessageLocationAccessDenied">Turn on Location access in the Settings app to allow us to locate agents near you.</string>
    <string name="alertTitleContactAccessDenied">Contacts Access Denied</string>
    <string name="alertMessageContactAccessDenied">Please give contacts access in the Settings app.</string>
    <string name="requestCameraAccess">Allow Camera Access</string>
    <string name="alertMessageConfirmMobileNumberAndroid">Is this your mobile number %1$s?</string>
    <string name="alertMessageMissingEmailID">Please enter your Email ID.</string>
    <string name="toastMessageImageDimensions">Please select a photo of resolution 1000x1000</string>
    <string name="alertTitleSessionPinTouchIDAuthRequiredTitle">Authentication Required</string>
    <string name="alertTitleSessionPinTouchIDAuthRequiredMessage">Please authenticate to be able to make this transaction.</string>
    <string name="alertBiometricAuthenticationExhausted">You\'ve exhausted your attempts at a biometric sign in.\nEnter your Session PIN to continue.</string>
    <string name="alertBiometricAuthenticationRequired">Biometric authentication is required to access the app.</string>
    <string name="alertMessageAuthenticationFailed">Unable to process this transaction as you failed session PIN authentication</string>
    <string name="alertMessageDeviceNotConfiguredForBiometric">Your device is not configured for biometric authentication.</string>
    <string name="alertMessageBiometricAttemptsExhaust">You\'ve exhausted your attempts at a biometric sign in.\nEnter your Session PIN to continue.</string>
    <string name="alertMessageDeviceNotEnrolledForBiometric">Your device is not configured for biometrics authentication.</string>
    <string name="alertMessageBiometricAuthenticationFailure">Your biometric authentication has failed. Biometric authentication is disabled.</string>
    <string name="toastMessageNoAppToShareImage">No application was found to share image.</string>
    <string name="alertMessageUnableToLookUpRPC">Unable to look up %1$s. %2$s</string>
    <string name="alertTitleLoadWalletNoBrowser">No Browser</string>
    <string name="alertMessageLoadWalletNoBrowser">Loading money to the wallet failed. Your device is missing a browser.</string>
    <string name="alertTitleNoLoadWalletSelected">Wallet Not Selected</string>
    <string name="alertMessageNoLoadWalletSelected">Please select a wallet to load money</string>
    <string name="alertTitleLoadWalletCancelled">Load Wallet Cancelled</string>
    <string name="alertMessageLoadWalletCancelled">We are unable to load wallet as it was cancelled.</string>
    <string name="transactionStatusScreenshotFileName">/transaction-details-%1$s</string>
    <string name="alertMessageDefaultWalletChanged">The default wallet changed successfully.</string>
    <string name="alertMessageUpdatingDefaultWallet">Updating Default Wallet…</string>
    <string name="alertMessageUpdatingWalletName">Updating Wallet Name…</string>
    <string name="alertMessageMarkingWalletActive">Marking Wallet Active…</string>
    <string name="alertMessageMarkingWalletInactive">Marking Wallet Inactive…</string>
    <string name="alertTitleNoWalletSelected">Wallet Not Selected</string>
    <string name="alertMessageNoWalletSelected">Please select a wallet to continue.</string>
    <string name="signUpInWelcomeGreeting">Welcome to Yafika Mobile, please wait while we are initializing the app.</string>
    <string name="otpTrustedContact">OTP sent to %1$s, %2$s</string>
    <string name="otpLabelInTime">in %1$s</string>
    <string name="otpLabelAutoReadOTP">Auto Reading OTP</string>
    <string name="setSecurityQuestionSelectButton">Select Question</string>
    <string name="questionNumber">Question %1$d</string>
    <string name="kycProfilePhoto">Profile Photo</string>
    <string name="kycAlertAddPhoto">Add Photo</string>
    <string name="kycAlertAddDocument">Add Document</string>
    <string name="kycAlertChooseFromGallery">Choose From Gallery</string>
    <string name="kycAlertRemoveDocument">Remove Document</string>
    <string name="moneyScreenViewAll">View All</string>
    <string name="moneyScreenLabelNationalIdExpiredTitle">National ID Expired.</string>
    <string name="moneyScreenLabelNationalIdExpiredMessage">It seems like your National ID card has expired, please renew it.</string>
    <string name="transactionStatusMessageCopiedClipboard">%1$s is copied to your clipboard.</string>
    <string name="contactSupportNoEmailApp">No email app is available on your device to perform this action.</string>
    <string name="profileAlertAddProfilePhoto">Add Your Profile Photo</string>
    <string name="thirdPartyApacheDescription">Licensed under the Apache License, Version 2.0 (the License); you may not use this file except in compliance with the License. You may obtain a copy of the License at</string>
    <string name="contactPickerLookingUpDetails">Looking up Details for \"%1$s\"</string>
    <string name="sendMoneyPayFromWalletLabel">Pay From Wallet</string>
    <string name="loadMoneyLoadToWallet">Load To Wallet</string>
    <string name="toastMessageOneMinuteLeft">You have 1 minute to go before this transaction times out.</string>
    <string name="manageAccountsCreateAccount">Create</string>
    <string name="alertTitleGPSDisabled">GPS Disabled</string>
    <string name="alertMessageGPSDisabled">Your GPS seems to be disabled, please enable it to continue.</string>
    <string name="alertTitleNoActiveAgent">No Active Agent</string>
    <string name="alertMessageNoActiveAgent">No active agents found, please try again later.</string>
    <string name="nextButtonTitle">Next</string>
    <string name="addButtonTitle">Add</string>
    <string name="setupSessionPinBypassBiometrics">Bypass Session PIN entry using biometrics</string>
    <!--  MARK: Gender Strings -->
    <string name="genderMale">Male</string>
    <string name="genderFemale">Female</string>
    <!--  MARK: Android strings without translations -->
    <string name="passwordPolicyDot">•</string>
    <string name="thirdPartyCoilHeading">Coil</string>
    <string name="gson_heading">Gson</string>
    <string name="okhttp_heading">OkHttp</string>
    <string name="uCrop_heading">uCrop</string>
    <string name="vision_common_heading">Vision-Common</string>
    <string name="thirdPartyGoogleCopyright2008">Copyright 2008 Google Inc</string>
    <string name="selectAccountAcceptsMultiCurrencies">%1$s,</string>
    <string name="dropdownLabel">%1$s:</string>
    <string name="pdfPageNumber">%1$s/%2$s</string>
    <string name="trustedContactFullName">%1$s %2$s</string>
    <string name="fragmentNavigationLabelEnterAmount" translatable="false">"Enter Amount Fragment"</string>
    <string name="emptyString" translatable="false" />
    <string name="dateTimeSeparator" translatable="false">\u0020on\u0020</string>
    <string name="dateTimeFormat" translatable="false">%1$s %2$s</string>
    <string name="appName" translatable="false">Yafika Mobile</string>
    <string name="formattedAmountValue" translatable="false">%.2f</string>
    <string name="formattedAmountWithoutDecimalValue" translatable="false">%.0f</string>
    <string name="transactionStatusItem" translatable="false">transaction_transition_item %1$s</string>
    <string name="transactionStatusTransitionName" translatable="false">transaction_transition_name</string>
    <!--  MARK: Android plural strings -->
    <plurals name="wrongSessionPinEnteredLabelPlural">
        <item quantity="one">Wrong Session PIN entered, %s attempt left</item>
        <item quantity="other">Wrong Session PIN entered, %s attempts left</item>
    </plurals>
    <plurals name="trustedContactsOnboardingLabelHours">
        <item quantity="one">\u0020%d hour</item>
        <item quantity="other">\u0020%d hours</item>
    </plurals>
    <plurals name="trustedContactsOnboardingLabelMinutes">
        <item quantity="one">\u0020%d minute</item>
        <item quantity="other">\u0020%d minutes</item>
    </plurals>
    <plurals name="trustedContactsOnboardingLabelSeconds">
        <item quantity="one">\u0020%d second</item>
        <item quantity="other">\u0020%d seconds</item>
    </plurals>
    <plurals name="otpEntryOTPLeft">
        <item quantity="one">• You have 1 OTP resend attempt left</item>
        <item quantity="other">• You have %d OTP resend attempts left.</item>
    </plurals>
    <!--  MARK: Android strings array -->
    <string-array name="months_array">
        <item>January</item>
        <item>February</item>
        <item>March</item>
        <item>April</item>
        <item>May</item>
        <item>June</item>
        <item>July</item>
        <item>August</item>
        <item>September</item>
        <item>October</item>
        <item>November</item>
        <item>December</item>
    </string-array>
    <!--  MARK: UIAccessibility Strings -->
    <!--  MARK: SignUpInPhoneNumberEntryVC -->
    <string name="axBackButtonLabel">Back.</string>
    <string name="axAlertLabel">Alert. %1$s.</string>
    <string name="axSignUpInLanguageSelectionLabel">Language Selection.</string>
    <string name="axSignUpInLanguageSelectionValue">Selected: %1$s.</string>
    <string name="axSupportedLanguageEnglish">English.</string>
    <string name="axSupportedLanguageNyanja">Nyanja.</string>
    <string name="axSignUpInLanguageSelectionHint">Double tap to change language.</string>
    <string name="axCountryCodeAccessibilityLabel">Select Country.</string>
    <string name="axCountryCodeAccessibilityHint">Double tap to select country.</string>
    <string name="axCountryCodeAccessibilityValue">Selected %1$s.</string>
    <string name="axCountryCodePickerLabel">Country: %1$s. Code: %2$s.</string>
    <string name="axCountryCodePickerHint">Double tap to pick country.</string>
    <string name="axMobileNumberAccessibilityLabel">Mobile Number.</string>
    <string name="axMobileNumberAccessibilityHint">Double tap to enter mobile number.</string>
    <string name="axMobileNumberAccessibiltyEnteredLabel">Mobile Number. Entered: %1$s.</string>
    <string name="axMobileNumberEnteredAccessibilityHint">Double tap to edit mobile number.</string>
    <string name="axAgeAgreementEnabledAccessibilityHint">Double tap to disagree.</string>
    <string name="axAgeAgreementDisabledAccessibilityHint">Double tap to agree.</string>
    <string name="axAgeAgreementEnabledValue">Checked.</string>
    <string name="axAgeAgreementDisabledValue">Unchecked.</string>
    <string name="axSignUpButtonLabel">Sign Up.</string>
    <string name="axSignInButtonLabel">Sign In.</string>
    <string name="axSignUpButtonDisabledCountry">Select Country.</string>
    <string name="axSignUpButtonDisabledMobileNumber">Enter Mobile Number.</string>
    <string name="axSignUpButtonDisabledAgeAgreement">Enable age agreement check to sign up.</string>
    <string name="axSignUpRedirectButtonHint">Double tap to redirect to sign up page.</string>
    <string name="axSignInRedirectButtonHint">Double tap to redirect to sign in page.</string>
    <string name="axErrorAlertHint">Swipe to go through available actions.</string>
    <string name="axAlertTextfieldHint">Double tap to edit.</string>
    <string name="axErrorSignUpInMobileNumberAlert">Are you sure? Is this your mobile number: %1$s.</string>
    <string name="axOtpEntryTextfieldHint">Double tap to enter OTP.</string>
    <string name="axOtpEntryTextfieldEditHint">Double tap to edit OTP.</string>
    <string name="axResendOTPButtonDisabled">Resend OTP button: Disabled for %1$s minutes %2$s seconds.</string>
    <string name="axResendOTPButtonEnabled">Resend OTP button. Double tap to resend OTP.</string>
    <string name="axOtpValidityLabel">OTP valid till: %1$s.</string>
    <string name="axEnterPasswordTextfieldLabel">Enter Password.</string>
    <string name="axEnterPasswordHint">Double tap to enter password.</string>
    <string name="axEnterPasswordEditHint">Double tap to edit password.</string>
    <string name="axEnterPasswordSignInDisabledHint">Enter password to sign in.</string>
    <string name="axForgotPasswordButtonLabel">Forgot Password?.</string>
    <string name="axForgotPasswordButtonHint">Double tap to view methods to reset password.</string>
    <string name="axForgotSessionPinHint">Double tap to reset session pin.</string>
    <string name="axScanQRCodeButtonLabel">Scan Q R Code.</string>
    <string name="axScanQRCodeButtonHint">Double tap to scan Q R code.</string>
    <string name="axNotificationButtonLabel">Notifications.</string>
    <string name="axNotificationButtonHint">Double tap to view notifications.</string>
    <string name="axAccountSelectionButtonLabel">Wallet Selection.</string>
    <string name="axAccountSelectionButtonValue">Selected %1$s wallet.</string>
    <string name="axAccountSelectionButtonHint">Double tap to select a different wallet.</string>
    <string name="axAvailableBalanceLabel">Available Balance in selected wallet: %1$s.</string>
    <string name="axMoneyScreenMoneyTransferHint">Double tap to Send money.</string>
    <string name="axMoneyScreenLoadWalletHint">Double tap to load money into the selected wallet.</string>
    <string name="axAccountPickerLabel">Wallet Name: %1$s. %2$s.</string>
    <string name="axAccountPickerHint">Double tap to select wallet.</string>
    <string name="axAccountPickerSelectedHint">Wallet selected.</string>
    <string name="axTransactionsCredited">Credited.</string>
    <string name="axTransactionsDebited">Debited.</string>
    <string name="axTransactionsLabel">%1$s. Amount: %2$s %3$s. Description: %4$s.</string>
    <string name="axTransactionsDefaultDescription">Description for transaction unavailable.</string>
    <string name="axTransactionsHint">Double tap to view transaction.</string>
    <string name="axTransactionsDate">Transactions on %1$s.</string>
    <string name="axProfileDetailsLabel">Username: %1$s. Mobile Number: %2$s.</string>
    <string name="axPrivateAddEmailLabel">Add Email I D.</string>
    <string name="axProfileAddEmailHint">Double tap to add email I D.</string>
    <string name="axProfileEditEmailHint">Double tap to edit email I D.</string>
    <string name="axProfilePictureLabel">Profile Picture.</string>
    <string name="axProfilePictureHint">Double tap to change profile picture.</string>
    <string name="axVerifyEmailButtonLabel">Double tap to send verification email.</string>
    <string name="axScanQrContactsLabel">Enter name or mobile number.</string>
    <string name="axScanQrContactsHint">Double tap to search using name or mobile number.</string>
    <string name="axScanQrCodeUploadFromGalleryHint">Double tap to upload Q R code from gallery.</string>
    <string name="axScanQrCodeFlashOffLabel">Flash is switched off.</string>
    <string name="axScanQrCodeFlashOffHint">Double tap to switch on.</string>
    <string name="axScanQrCodeFlashOnLabel">Flash is switched on.</string>
    <string name="axScanQrCodeFlashOnHint">Double tap to switch off.</string>
    <string name="axRecentsHint">Swipe to go through recent transactions, select an user to make a transaction.</string>
    <string name="axSendMoneyNameLabel">Transferring to : %1$s.</string>
    <string name="axSendMoneyNameAndNumberLabel">Transferring to %1$s. Mobile Number: %2$s.</string>
    <string name="axSendMoneyNameAndAccountLabel">Transferring to %1$s. Recipient I D: %2$s.</string>
    <string name="axSendMoneyAmountTextfieldLabel">Enter amount. Currency : %1$s.</string>
    <string name="axSendMoneyAmountTextfieldHint">Double tap to enter amount.</string>
    <string name="axSendMoneyAmountTextfieldEnteredHint">Double tap to edit amount.</string>
    <string name="axSendMoneyNarrationReceiverLabel">Enter narration for receiver.</string>
    <string name="axSendMoneyNarrationSelfLabel">Enter narration to self.</string>
    <string name="axSendMoneyNarrationHint">Double tap to start entering narration.</string>
    <string name="axSendMoneySelectWalletLabel">Select Wallet.</string>
    <string name="axSendMoneySelectWalletValue">Selected %1$s wallet.</string>
    <string name="axSendMoneySelectWalletHint">Double tap to select a different wallet.</string>
    <string name="axSendMoneyAccountPickerLabel">Wallet Name: %1$s, Last four characters of the Wallet I D : %2$s.</string>
    <string name="axSendMoneyAccountPickerSelectedHint">Selected.</string>
    <string name="axSendMoneyAccountPickerHint">Double tap to select wallet.</string>
    <string name="axSendMoneyAccountPickerDisabledHint">Cannot select wallet.</string>
    <string name="axSendMoneyAccountPickerCurrencyLabel">Wallets with %1$s currency.</string>
    <string name="axSendMoneyAccountPickerEnabledValue">Wallet active.</string>
    <string name="axSendMoneyAccountPickerDisabledValue">Wallet inactive.</string>
    <string name="axSendMoneyAttemptPaymentHint">Double tap to attempt payment.</string>
    <string name="axSendMoneyAttemptPaymentDisabledHint">Enter amount to attempt payment.</string>
    <string name="axSelectDestinationOtherProvidersHint">Swipe to go through the list of providers.</string>
    <string name="axSelectDestinationTextfieldHint">Double tap to start entering.</string>
    <string name="axSelectDestinationTryAgainHint">Double tap to try fetching counter parties again.</string>
    <string name="axCounterPartyLookupNextHint">Double tap to look up counter party.</string>
    <string name="axCounterPartyNotSupportingLookupHint">Double tap to proceed.</string>
    <string name="axCounterPartyLookupNextDisabled">Enter required details to proceed.</string>
    <string name="axCounterPartyNonLookupDisabledId">Enter recipient I D to proceed.</string>
    <string name="axCounterPartyNonLookupDisabledConfirmId">Confirm recipient I D to proceed.</string>
    <string name="axCounterPartyNonMatchingId">Recipient I D entered does not match.</string>
    <string name="axCounterPartyNonLookupDisabledName">Enter recipient name to proceed.</string>
    <string name="axContactPickerSearchBarLabel">Search using name or mobile number.</string>
    <string name="axContactPickerSearchBarHint">Double tap to start searching.</string>
    <string name="axContactPickerContactLabel">Name: %1$s. Number: %2$s.</string>
    <string name="axContactPickerContactHint">Double tap to pick contact.</string>
    <string name="axAccountToAccountFromLabel">Select wallet to transfer from.</string>
    <string name="axAccountToAccountToLabel">Select wallet to transfer to.</string>
    <string name="axAccountToAccountValue">Selected: %1$s.</string>
    <string name="axAccountToAccountHint">Double tap to select a different wallet.</string>
    <string name="axAccountToAccountDisabledButtonHint">Enter amount to attempt payment.</string>
    <string name="axAccountToAccountAttemptPaymentHint">Double tap to attempt payment.</string>
    <string name="axPaymentsAttemptPaymentDisabledHint">Enter all the required details to proceed.</string>
    <string name="axPaymentsEntryHint">Double tap to edit.</string>
    <string name="axPaymentsEntryLabel">Enter %1$s.</string>
    <string name="axPaymentsSelectWalletLabel">Select wallet to make payment from.</string>
    <string name="axPaymentsAttemptPaymentHint">Double tap to attempt payment.</string>
    <string name="axPaymentsOptionsHint">Double tap to view list of options available for this field.</string>
    <string name="axPaymentsOptionsValue">Name : %1$s :  Cost : %2$s.</string>
    <string name="axPaymentsOptionsNotSelectedValue">Not selected yet.</string>
    <string name="axPaymentsOptionsSelectedValue">Selected %1$s.</string>
    <string name="axPayementsOptionsSelectionHint">Double tap to select.</string>
    <string name="axPaymentsOptionsSelectedHint">Selected.</string>
    <string name="axTransactionDetailsEntryLabel">%1$s : %2$s.</string>
    <string name="axTransactionDetailsLoadWalletNoteLabel">Please note: %1$s.</string>
    <string name="axTransactionDetailsTermsAndConditionsHint">Double tap to view the terms and conditions.</string>
    <string name="axLoadWalletSelectWalletLabel">Select wallet to load money into.</string>
    <string name="axLoadWalletSelectedWalletValue">Selected %1$s.</string>
    <string name="axLoadWalletSelectWalletHint">Double tap to select a different wallet.</string>
    <string name="axLoadWalletAttemptPaymentDisabled">Enter amount to proceed.</string>
    <string name="axLoadWalletAttemptPaymentEnabled">Double tap to attempt payment.</string>
    <string name="axLoadWalletCloseButtonHint">Double tap to close transaction window.</string>
    <string name="axShowQrCodeGetLinkLabel">Get Link for this Q R code.</string>
    <string name="axShowQrCodeGetLinkHint">Double tap to share link.</string>
    <string name="axShowQrCodeShareQrCodeLabel">Share Q R Code.</string>
    <string name="axShowQrCodeShareQrCodeHint">Double tap to share Q R code.</string>
    <string name="axShowQrCodeWalletSelectLabel">Select Wallet.</string>
    <string name="axShowQrCodeWalletSelectHint">Double tap to select a different wallet.</string>
    <string name="axShowQrCodeWalletSelectValue">Selected %1$s wallet.</string>
    <string name="axManageWalletsCurrencyLabel">Wallets with %1$s currency.</string>
    <string name="axManageWalletsSelectWalletLabel">Wallet Name: %1$s, Last four characters of the Wallet I D : %2$s.</string>
    <string name="axManageWalletsSelectDefaultWalletLabel">Wallet Name: %1$s, Last four characters of the Wallet I D : %2$s. This wallet is the default wallet.</string>
    <string name="axManageWalletsSelectWalletHint">Double tap to select wallet.</string>
    <string name="axManageWalletsEditWalletHint">Double tap to edit the name of the wallet.</string>
    <string name="axManageWalletsSetDefaultHint">Double tap to set wallet as the default wallet.</string>
    <string name="axManageWalletsMarkInactiveHint">Double tap to mark the wallet as inactive.</string>
    <string name="axManageWalletsInactiveWalletHint">Double tap to activate wallet.</string>
    <string name="axManageWalletsAddWalletLabel">Add a new wallet.</string>
    <string name="axManageWalletsAddWalletHint">Double tap to add a new wallet.</string>
    <string name="axAddNewWalletTextfieldHint">Double tap to edit wallet name.</string>
    <string name="axAddNewWalletSelectCurrencyLabel">Select Currency of the new wallet.</string>
    <string name="axAddNewWalletSelectCurrencyValue">Selected: %1$s.</string>
    <string name="axAddNewWalletSelectCurrencyHint">Double tap to select currency.</string>
    <string name="axAddNewWalletSelectedCurrencyHint">Double tap to select a different currency.</string>
    <string name="axAddNewWalletEnabledButtonHint">Double tap to create new wallet.</string>
    <string name="axAddNewWalletNameDisabledHint">Enter new wallet\'s name to create a new wallet.</string>
    <string name="axAddNewWalletCurrencyDisabledHint">Select the new wallet\'s currency.</string>
    <string name="axSelectCurrencyLabel">Currency Code: %1$s.</string>
    <string name="axSelectCurrencySelectedHint">Selected.</string>
    <string name="axSelectCurrencyNotSelectedHint">Double tap to select currency.</string>
    <string name="axWalletDetailsLabel">Wallet Name: %1$s, Last four characters of the Wallet I D : %2$s.</string>
    <string name="axChangeSessionPinCloseButtonHint">Double tap to terminate session pin change.</string>
    <string name="axSetupSessionPinSwitchEnabledValue">Enabled.</string>
    <string name="axSetupSessionPinSwitchDisabledValue">Disabled.</string>
    <string name="axSetupSessionPinSwitchLabel">Bypass pin entry using %1$s.</string>
    <string name="axSetupSessionPinSwitchEnabledHint">Double tap to disable.</string>
    <string name="axSetupSessionPinSwitchDisabledHint">Double tap to enable.</string>
    <string name="axChangePasswordCurrentPasswordLabel">Enter Current password.</string>
    <string name="axChangePasswordNewPasswordLabel">Enter New Password.</string>
    <string name="axChangePasswordConfirmPasswordLabel">Confirm New Password.</string>
    <string name="axChangePasswordTextfieldHint">Double tap to edit.</string>
    <string name="axChangePasswordInstructionsLabel">Your password must be at least “8” characters long, contain at least 1 upper case character, 1 lower case character, 1 special character and 1 digit, not contain common dictionary words and not start or end with white spaces.</string>
    <string name="axChangePasswordCurrentDisabledHint">Enter current password to change password.</string>
    <string name="axChangePasswordNewDisabledHint">Enter new password to change password.</string>
    <string name="axChangePasswordConfirmDisabledHint">Confirm new password to change password.</string>
    <string name="axChangePasswordMinLengthDisabledHint">Passwords do not match the minimum length requirement.</string>
    <string name="axChangePasswordEnabledHint">Double tap to change password.</string>
    <string name="axSetSecurityQuestionNotSetLabel">Choose question.</string>
    <string name="axSetSecurityQuestionNotSetHint">Double tap to select a question.</string>
    <string name="axSetSecurityQuestionSetLabel">Question: %1$s.</string>
    <string name="axSetSecurityQuestionSetHint">Double tap to select a different question.</string>
    <string name="axSetSecurityQuestionAnswerDisabledHint">Select a question.</string>
    <string name="axSetSecurityQuestionAnswerLabel">Enter answer.</string>
    <string name="axSetSecurityQuestionAnswerValue">Entered: %1$s.</string>
    <string name="axSetSecurityQuestionAnswerHint">Double tap to edit.</string>
    <string name="axSetSecurityQuestionButtonDisabledHint">Set the minimum number of security questions to proceed.</string>
    <string name="axAnswerSecurityQuestionButtonDisabledHint">Answer all the questions to proceed.</string>
    <string name="axSetSecurityQuestionButtonEnabledHint">Double tap to proceed.</string>
    <string name="axChooseTrustedContactButtonHint">Double tap to proceed.</string>
    <string name="axSelectSecurityQuestionSelectedValue">Selected.</string>
    <string name="axSelectSecurityQuestionNotSelectedValue">Not Selected.</string>
    <string name="axSelectSecurityQuestionHint">Double tap to select question.</string>
    <string name="axKycOptionsScanQrCodeHint">Double tap to scan Q R code.</string>
    <string name="axKycOptionsEnterManuallyHint">Double tap to enter the details manually.</string>
    <string name="axKycDetailsLabel">Enter K Y C details. Fill your details to continue.</string>
    <string name="axKycDetailsProfileImageLabel">Profile Image.</string>
    <string name="axKycDetailsProfileImageAddedValue">Added.</string>
    <string name="axKycDetailsProfileImageNotAddedValue">Not added.</string>
    <string name="axKycDetailsProfileImageHint">Double tap to add profile image.</string>
    <string name="axKycDetailsProfileImageAddedHint">Double tap to edit profile image.</string>
    <string name="axKycDetailsEnteredValue">Entered: %1$s.</string>
    <string name="axKycDetailsNotEnteredValue">Field empty.</string>
    <string name="axKycDetailsTextfieldHint">Double tap to edit.</string>
    <string name="axKycDetailsFirstNameLabel">Enter first name. Required.</string>
    <string name="axKycDetailsLastNameLabel">Enter Last name. Optional.</string>
    <string name="axKycDetailsOtherNameLabel">Enter Other name. Optional.</string>
    <string name="axKycDetailsDateOfBirthLabel">Select date of birth. Required.</string>
    <string name="axKycDetailsPlaceOfBirthLabel">Enter place of birth. Required.</string>
    <string name="axKycDetailsGenderLabel">Select gender. Required.</string>
    <string name="axKycDetailsEmailIdLabel">Enter email I D. Optional.</string>
    <string name="axKycDetailsNationalIDLabel">Enter national I D number. Required.</string>
    <string name="axKycDetailsNationalIDIssueDateLabel">Select national I D issue date. Required.</string>
    <string name="axKycDetailsNationalIDExpiryDateLabel">Select national I D expiry date. Required.</string>
    <string name="axKycDetailsResidentialAddressLabel">Enter residential address. Required.</string>
    <string name="axKycDetailsPostalAddressLabel">Enter postal address. Required.</string>
    <string name="axKycNextButtonDisabledHint">Enter all the required details to continue.</string>
    <string name="axKycNextButtonHint">Double tap to proceed.</string>
    <string name="axUploadDocumentsUploadedValue">Uploaded.</string>
    <string name="axUploadDocumentsNotUploadedValue">Not Uploaded.</string>
    <string name="axUploadDocumentsHint">Double tap to upload document.</string>
    <string name="axUploadDocumentsButtonDisabledHint">Upload all required documents to proceed.</string>
    <string name="axUploadDocumentsButtonEnabledHint">Double tap to proceed.</string>
    <string name="axTrustedContactLabel">Contact Name: %1$s. Mobile Number: %2$s.</string>
    <string name="axTrustedContactWithEmailLabel">Contact Name: %1$s. Mobile Number: %2$s. Email I D: %3$s.</string>
    <string name="axTrustedContactHint">Double tap to remove trusted contact.</string>
    <string name="axTrustedContactAddLabel">Add trusted contact.</string>
    <string name="axTrustedContactAddHint">Double tap to add a trusted contact.</string>
    <string name="axTrustedContactAddDisabledHint">You have reached the limit of maximum number of trusted contacts that can be added.</string>
    <string name="axAddTrustedContactButtonHint">Double tap to add trusted contact.</string>
    <string name="axAddTrustedContactCountryDisabledHint">Select country of the trusted contact.</string>
    <string name="axAddTrustedContactMobileNumberDisabledHint">Enter mobile number of the trusted contact.</string>
    <string name="axAddTrustedContacFirstNameDisabledHint">Enter the first name of the trusted contact.</string>
    <string name="axAddTrustedContactCountryLabel">Select country.</string>
    <string name="axAddTrustedContactCountryValue">Selected %1$s.</string>
    <string name="axAddTrustedContactCountryHint">Double tap to select country.</string>
    <string name="axAddTrustedContactCountrySelectedHint">Double tap to select a different country.</string>
    <string name="axAddTrustedContactMobileNumberLabel">Mobile Number.</string>
    <string name="axAddTrustedContactMobileNumberEnteredLabel">Mobile Number. Entered: %1$s.</string>
    <string name="axAddTrustedContactMobileNumberHint">Double tap to edit mobile number field.</string>
    <string name="axAddTrustedContactFirstNameLabel">Enter first name.</string>
    <string name="axAddTrustedContactEnteredFirstNameLabel">Enter first name. Entered: %1$s.</string>
    <string name="axAddTrustedContactFirstNameHint">Double tap to edit first name.</string>
    <string name="axAddTrustedContactLastNameLabel">Enter last name (optional).</string>
    <string name="axAddTrustedContactEnteredLastNameLabel">Enter last name (optional). Entered: %1$s.</string>
    <string name="axAddTrustedContactLastNameHint">Double tap to edit last name.</string>
    <string name="axAddTrustedContactEmailLabel">Enter email I D (optional).</string>
    <string name="axAddTrustedContactEnteredEmailLabel">Enter email I D (optional). Entered: %1$s.</string>
    <string name="axAddTrustedContactEmailHint">Double tap to edit email I D.</string>
    <string name="axAddTrustedContactConfirmationAlert">Are you sure you want to add %1$s as your trusted contact?.</string>
    <string name="axSetupTrustedContactFinishButtonHint">Double tap to proceed.</string>
    <string name="axSetupTrustedContactFinishButtonDisabledHint">Add two trusted contacts to proceed.</string>
    <string name="axChooseTrustedContactHint">Double tap to choose trusted contact.</string>
    <string name="axFindAgentShopCardLabel">Agent Name: %1$s. Shop Name: %2$s.</string>
    <string name="axFindAgentShopCardOpenValue">Status: Open.</string>
    <string name="axFindAgentShopCardClosedValue">Status: Closed.</string>
    <string name="axFindAgentShopCardHint">Swipe to go through available actions.</string>
    <string name="axFindAgentCallNowHint">Double tap to call the agent.</string>
    <string name="axFindAgentGetDirectionsHint">Double tap to get directions to reach this agent.</string>
    <string name="axFindAgentPinHint">Double tap to view agent.</string>
    <string name="axFindAgentRecenterMapLabel">Recenter Map.</string>
    <string name="axFindAgentRecenterMapHint">Double tap to recenter map.</string>
    <string name="axLanguageSelectionCellLabel">Language: %1$s.</string>
    <string name="axLanguageSelectionCellHint">Double tap to select language.</string>
    <string name="axLanguageSelectionNotSelectedValue">Not Selected.</string>
    <string name="axLanguageSelectionButtonDisabledHint">Select a language to proceed.</string>
    <string name="axLanguageSelectionButtonHint">Double tap to change language.</string>
    <string name="axProfileLanguageSelectionValue">Current language: %1$s.</string>
    <string name="axTransactionStatusHeaderLabel">%1$s. Performed at %2$s.</string>
    <string name="axTransactionStatusHeaderHint">Double tap to share transaction.</string>
    <string name="axTransactionStatusDetailsWithDescriptionLabel">Transaction Details. %1$s. %2$s. %3$s.</string>
    <string name="axTransactionStatusDetailsWithoutDescriptionLabel">Transaction Details. %1$s. %2$s.</string>
    <string name="axTransactiontatusAmountLabel">Amount: %1$s.</string>
    <string name="axTransactionStatusIDHint">Transaction I D: %1$s.</string>
    <string name="axTransactionStatusDetailsDescription">Transaction Description : %1$s.</string>
    <string name="axTransactionStatusItemLabel">%1$s : %2$s.</string>
    <string name="axTransactionStatusCopyableTextHint">Double tap to copy text.</string>
    <string name="axTransactionStatusContactSupportAccessibilityLabel">Need Help? Contact support in case of any issues with the transaction.</string>
    <string name="axTransactionStatusContactSupportHint">Double tap to contact support.</string>
    <string name="axTransactionStatusAddNarrationLabel">Add Narration to the transaction.</string>
    <string name="axTransactionStatusAddNarrationHint">Double tap to Add Narration.</string>
    <string name="axTransactionStatusDoneButtonHint">Double tap to close transaction status.</string>
    <string name="axTransactionStatusWalletIdLabel">Last four digits of the wallet I D: %1$s.</string>
    <string name="axAddPrivateRemarkTextviewLabel">Narration to self will only be visible to you.</string>
    <string name="axAddPrivateRemarkTextviewHint">Double tap to add narration.</string>
    <string name="axAddPrivateRemarkAddButtonHint">Double tap to submit narration.</string>
    <string name="axRequiredDocumentsTitleLabel">Requirements to Sign Up. To complete your sign up process smoothly, make sure that you are ready with the below mentioned requirements.</string>
    <string name="axRequiredDocumentsMandatoryDocumentsLabel">Mandatory Documents: National I D, Signature and Proof of residence.</string>
    <string name="axRequiredDocumentsSignUpLaterLabel">Sign Up Later. If you don’t have these ready, Double tap here to sign up later.</string>
    <string name="axRequiredDocumentsSignUpLaterHint">Double tap to sign up later.</string>
    <string name="axRequiredDocumentsContinueHint">Double tap to proceed.</string>
    <string name="axOtpLeftLabel">You have %1$s O T P\'s left.</string>
    <string name="axKycScanQRCodeVerifyNationalIdLabel">Verify National I D.</string>
    <string name="axKycScanQRCodeVerifyNationalIdSubtitleLabel">Please scan the Q R code behind your National I D card, or you can fill in the details manually.</string>
    <string name="axKycScanQRCodeFailedToVerifyNationalIdSubtitleLabel">National I D not recognized, please try again. You can try entering the K Y C details manually as well.</string>
    <string name="axAddEmailIdTitleLabel">Add Email I D. By adding and verifying your email I D you will be able to receive alerts for account activity to your email address directly.</string>
    <string name="axUpdateEmailTitleLabel">Update Email I D. By updating and verifying your email I D you will be able to receive alerts for account activity to your email address directly.</string>
    <string name="axEmailIdTextfieldLabel">Email I D.</string>
    <string name="axEmailIdTextfieldValue">Entered: %1$s.</string>
    <string name="axEmailIdTextfieldHint">Double tap to edit.</string>
    <string name="axIdInvalidLabel">ID</string>
    <string name="axIdValidLabel">I D</string>
    <string name="axKycInvalidLabel">KYC</string>
    <string name="axKycValidLabel">K Y C</string>
    <string name="axBulletInvalidLabel">•</string>
    <string name="axBulletValidLabel">,</string>
    <string name="axStatementsLabel">Statement for: %1$s.</string>
    <string name="axStatementsHint">Double tap to view statement.</string>
    <string name="axStatementsShareLabel">Share statement.</string>
    <string name="axStatementsShareHint">Double tap to share statement.</string>
    <string name="axAmountEntryWithoutDecimalValue">Entered: %1$s.</string>
    <string name="axAmountEntryWithDecimalValue">Entered: %1$s point %2$s.</string>
    <string name="axNotificationTimeLabel">Title: %1$s. Description: %2$s. Notification received at: %3$s.</string>
    <string name="axNotificationDateLabel">Title: %1$s. Description: %2$s. Notification received on: %3$s.</string>
    <string name="axNotificationHint">Double tap to view notification.</string>
    <string name="axSelectCounterpartyEnteredValue">%1$s. Entered: %2$s.</string>
    <!--  MARK: Android AX Strings -->
    <string name="axSignInTrustedContactLabel">Name starts with %1$s and Mobile number ends with %2$s</string>
    <string name="axProfileDetailsUsernameLabel">Username: %1$s</string>
    <string name="axProfileDetailsMobileNumberLabel">Mobile Number: %1$s</string>
    <string name="axProfileDetailsEmailIDLabel">Email I D: %1$s</string>
    <string name="axTransactionStatusTime">Time: %1$s</string>
    <string name="axTransactionAlertPayableAmount">Payable: amount %1$s</string>
    <string name="axTransactionAlertReceiveAmount">Receive: amount %1$s</string>
    <string name="axTransactionAlertWalletName">Wallet: %1$s</string>
    <string name="axTransactionAlertFee">Transaction Fee: %1$s</string>
    <string name="axTransactionAlertRecipientReceives">Recipient Receives: %1$s</string>
    <string name="axQRCodeCameraFlash">Torch Button</string>
    <string name="axToolbarMenu">Double tap to show menu</string>
</resources>
