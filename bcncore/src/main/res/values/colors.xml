<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="dialogBg">#FFFFFF</color>
    <color name="buttonLabelColor">#7B7B7B</color>
    <color name="spinnerItemTextColor">#ffffff</color>
    <color name="spinnerBgColor">#FFFFFF</color>
    <color name="selectedPageIndicatorColor">#7C4DFF</color>
    <color name="unselectedPageIndicatorColor">#888888</color>
    <color name="listHeaderBgColor">#F2F2F5</color>
    <color name="listSubHeaderBgColor">#BDBDBD</color>
    <color name="listSubHeaderTextColor">#ffffff</color>
    <color name="unavailableAccountTextColor">#7B7B7B</color>
    <color name="listDividerColor">#E3E4EB</color>
    <color name="descriptionTextColor">#555770</color>
    <color name="descriptionTextDarkColor">#555770</color>
    <color name="dialogSubtitleTextColor">#555770</color>
    <color name="dialogBgColor">#FFFFFF</color>
    <color name="titleTextColor">#1C1C28</color>
    <color name="titleDescriptionTextColor">#8E90A6</color>
    <color name="boldTitleTextColor">#1C1C28</color>
    <color name="unavailableDocumentTextColor">#7B7B7B</color>
    <color name="surfaceColor">#F2F2F5</color>
    <color name="sectionHeadingColor">#000000</color>
    <color name="successful_transaction_theme_color">#06C270</color>
    <color name="positiveIndicatorColor">#1C9559</color>
    <color name="failed_transaction_theme_color">#FF3B3B</color>
    <color name="expire_transaction_theme_color">#FF8800</color>
    <color name="pullToRefreshBgColor">#ffffff</color>
    <color name="destructiveActionColor">#FF3B3B</color>
    <color name="dialogAlertActionColor">#B66802</color>
    <color name="buttonPressedRippleColor">#D5D5D5</color>
    <color name="windowBackground">#ffffff</color>
    <color name="windowDividerBackground">#F2F2F5</color>
    <color name="transparent">#00000000</color>
    <color name="dropdownDividerBackground">#f1f1f1</color>
    <color name="spinnerDropDownBackground">#818181</color>
    <color name="selectedPageIndicatorColorGreen">#21CE99</color>
    <color name="subtitleTextColor">#28293D</color>
    <color name="agentClosed">#C60000</color>
    <color name="editTextDivider">#8E90A6</color>
    <color name="bg_outline_button">#C7C8D9</color>
    <color name="highLightedTextColor">#17906B</color>
    <color name="dividerColor">#E3E4EB</color>
    <color name="buttonDisabledColor">#E3E4EB</color>
    <color name="buttonDisabledTextColor">#8E90A6</color>
    <color name="subtitleTextColorSecondary">#8E90A6</color>
    <color name="passwordToggleColor">#555770</color>
    <color name="cardBorderColor">#E3E4EB</color>
    <color name="editTextHintColor">#8E90A6</color>
    <color name="negativeInfoTextColor">#B66802</color>
    <color name="errorTextColor">#C60000</color>
    <color name="inactiveTabColor">#B49CF8</color>
    <!--    Placeholder colors:-->
    <!--    The app will have to set these values: so these will be overridden during resource merge
    that will happen when merging resources at compile time. -->
    <color name="colorPrimary">#6A39F1</color>
    <color name="colorPrimaryDark">#6A39F1</color>
    <color name="colorPrimaryLight">@color/colorPrimary</color>
    <color name="colorAccent">#ffb3cc</color>
    <color name="colorAccentLight">#C7C8D9</color>
    <!--End placeholder colors-->
</resources>
