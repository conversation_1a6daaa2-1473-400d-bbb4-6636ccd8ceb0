<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="CardTheme">
        <item name="colorControlActivated">#7C4DFF</item>
        <item name="colorControlNormal">#7C4DFF</item>
    </style>

    <style name="ListHeaderStyle">
        <item name="android:textColor">@color/descriptionTextColor</item>
        <item name="android:background">@color/listHeaderBgColor</item>
    </style>

    <style name="AlertDialogStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/bg_4dp</item>
    </style>

    <style name="AlertButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="ProfileItemRow">
        <item name="android:drawablePadding">@dimen/dimen_32dp</item>
        <item name="android:foreground">?android:selectableItemBackground</item>
        <item name="android:padding">@dimen/dimen_16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:textColor">@color/subtitleTextColor</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:gravity">center|start</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="ContactSupportItemRow">
        <item name="android:drawablePadding">@dimen/dimen_16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:textColor">@color/subtitleTextColor</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:gravity">center|start</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="ProfileItemHeader">
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">@color/descriptionTextColor</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">28dp</item>
        <item name="android:background">@color/surfaceColor</item>
        <item name="android:paddingStart">@dimen/dimen_10dp</item>
    </style>

    <style name="ToolbarStyle">
        <item name="textStartPadding">@dimen/dimen_32dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">@dimen/text_18sp</item>
    </style>

    <style name="TabLayout">
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="RootLayout" parent="Theme.MaterialComponents">
        <item name="android:background">@color/windowBackground</item>
    </style>

    <style name="ListItemSeparator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/windowDividerBackground</item>
    </style>

    <style name="ThirdPartyActionBar" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
    </style>

    <style name="CustomBottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/CustomBottomSheetStyle</item>
    </style>

    <style name="CustomBottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="roundedImageViewRounded">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="roundedFlagImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">6dp</item>
    </style>

    <style name="PrimaryButtonOutline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:height">@dimen/button_height</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/colorPrimaryLight</item>
        <item name="strokeColor">@color/bg_outline_button</item>
        <item name="strokeWidth">@dimen/dimen_1dp</item>
    </style>

    <style name="SwitchTheme">
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="TextDropDownStyle">
        <item name="fontFamily">@font/pt_sans_regular</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="popupMenuStyle" parent="Widget.AppCompat.PopupMenu">
        <item name="android:popupBackground">@drawable/bg_popup_menu</item>
        <item name="android:overlapAnchor">true</item>
    </style>

    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="android:stateListAnimator">@animator/material_button_animator</item>
        <item name="elevation">@dimen/dimen_4dp</item>
        <item name="android:height">@dimen/button_height</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@drawable/text_color_primary_button_selector</item>
        <item name="backgroundTint">@drawable/bg_button_primary_4dp_color_selector</item>
        <item name="android:background">@drawable/bg_button_primary_4dp_selector</item>
    </style>

    <style name="AlertDialogButton" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:minWidth">@dimen/dimen_36dp</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">@color/colorPrimaryLight</item>
        <item name="android:textSize">@dimen/text_14sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="boxStrokeColor">@color/text_input_activation_color</item>
        <item name="boxCollapsedPaddingTop">2dp</item>
        <item name="android:textColorHint">@color/subtitleTextColorSecondary</item>
        <item name="boxBackgroundColor">@color/windowBackground</item>
        <item name="hintTextColor">@color/text_input_edit_text_hint_color_selector</item>
    </style>

    <style name="TextInputEditTextCursorTheme">
        <item name="colorControlNormal">@color/editTextDivider</item>
        <item name="colorControlActivated">@color/colorPrimaryLight</item>
    </style>

    <style name="BillersTextInputEditTextCursorTheme">
        <item name="colorControlNormal">@color/editTextDivider</item>
        <item name="colorControlActivated">@color/colorPrimaryLight</item>
    </style>

    <style name="BottomNavigationText">
        <item name="android:textSize">@dimen/text_12sp</item>
    </style>
</resources>
