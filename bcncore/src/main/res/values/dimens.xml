<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="qr_recent_section_padding_top">450dp</dimen>
    <dimen name="app_pin_change_dialog_title_text_size">18sp</dimen>
    <dimen name="app_pin_change_dialog_subtitle_text_size">16sp</dimen>
    <dimen name="change_password_item_top_margin">44dp</dimen>
    <dimen name="change_password_button_top_margin">64dp</dimen>
    <dimen name="otp_digit_width">24dp</dimen>
    <dimen name="button_corner_radius">4dp</dimen>
    <dimen name="button_height">40dp</dimen>
    <dimen name="outline_button_height">52dp</dimen>
    <dimen name="margin_40dp">40dp</dimen>
    <dimen name="margin_24dp">24dp</dimen>
    <dimen name="dimen_16dp_small_screen">16dp</dimen>
    <dimen name="widget_image_48dp">48dp</dimen>
    <dimen name="max_text_input_len">200</dimen>
    <dimen name="editTextBottomPadding">16dp</dimen>
    <dimen name="sessionPinEditTextDistance">16dp</dimen>

    <dimen name="dimen_0dp">0dp</dimen>
    <dimen name="dimen_1dp">1dp</dimen>
    <dimen name="dimen_4dp">4dp</dimen>
    <dimen name="dimen_8dp">8dp</dimen>
    <dimen name="dimen_12dp">12dp</dimen>
    <dimen name="dimen_16dp">16dp</dimen>
    <dimen name="dimen_20dp">20dp</dimen>
    <dimen name="dimen_24dp">24dp</dimen>
    <dimen name="dimen_28dp">28dp</dimen>
    <dimen name="dimen_32dp">32dp</dimen>
    <dimen name="dimen_36dp">36dp</dimen>
    <dimen name="dimen_40dp">40dp</dimen>
    <dimen name="dimen_48dp">48dp</dimen>
    <dimen name="dimen_52dp">52dp</dimen>
    <dimen name="dimen_60dp">60dp</dimen>
    <dimen name="dimen_64dp">64dp</dimen>
    <dimen name="dimen_68dp">68dp</dimen>
    <dimen name="dimen_72dp">72dp</dimen>
    <dimen name="dimen_120dp">120dp</dimen>

    <!--    Text Sizes-->
    <dimen name="text_2sp">2sp</dimen>
    <dimen name="text_10sp">10sp</dimen>
    <dimen name="text_12sp">12sp</dimen>
    <dimen name="text_14sp">14sp</dimen>
    <dimen name="text_16sp">16sp</dimen>
    <dimen name="text_18sp">18sp</dimen>
    <dimen name="text_20sp">20sp</dimen>
    <dimen name="text_28sp">28sp</dimen>
</resources>
