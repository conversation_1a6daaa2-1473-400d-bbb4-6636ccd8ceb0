<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context="com.resoluttech.core.auth.signup.SignUpFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/logo"
            android:layout_width="@dimen/dimen_64dp"
            android:layout_height="@dimen/dimen_64dp"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:src="@drawable/ic_logo_bcn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/select_language_ll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/language_flag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_flag_uk"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/language_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="@dimen/dimen_4dp"
                android:text="@string/languageSelectorOptionEnglish"
                android:textSize="@dimen/text_14sp"
                android:textColor="@color/subtitleTextColor" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_chevron_right_primary"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/enter_phonenumber_contaier"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dimen_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/logo">

            <TextView
                android:id="@+id/create_account_label_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/signUpInMobileNumberSignUpTitle"
                android:textColor="@color/boldTitleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/description_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:text="@string/signUpInMobileNumberSignUpSubtitle"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <LinearLayout
                android:id="@+id/enter_number_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:orientation="horizontal"
                tools:context=".CountryPickerFragment">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:background="@color/transparent"
                    android:contentDescription="@string/emptyString"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_phone_number" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:gravity="bottom"
                    android:orientation="vertical">

                    <com.hbb20.CountryCodePicker
                        android:id="@+id/country_code_picker"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/axCountryCodeAccessibilityHint"
                        app:ccpDialog_allowSearch="false"
                        app:ccpDialog_background="@drawable/bg_4dp"
                        app:ccpDialog_cornerRadius="@dimen/dimen_4dp"
                        app:ccpDialog_textColor="@color/subtitleTextColor"
                        app:ccp_arrowColor="@color/subtitleTextColor"
                        app:ccp_contentColor="@color/subtitleTextColor"
                        app:ccp_showNameCode="false"
                        app:ccp_textSize="@dimen/text_16sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_gravity="bottom"
                        android:layout_marginTop="@dimen/dimen_8dp"
                        android:background="@color/editTextDivider" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/phone_number_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="@dimen/dimen_8dp"
                    android:layout_weight="6"
                    android:autofillHints="phonenumber"
                    android:background="@color/transparent"
                    android:hint="@string/signUpInMobileNumberPlaceHolder"
                    android:inputType="number"
                    android:maxLength="15"
                    android:textSize="@dimen/text_16sp"
                    android:theme="@style/TextInputEditTextCursorTheme">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/phone_number_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:autofillHints="phonenumber"
                        android:importantForAutofill="no"
                        android:inputType="phone"
                        android:maxLength="15"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <CheckBox
                android:id="@+id/age_cb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:button="@drawable/bg_checkbox"
                android:ellipsize="end"
                android:gravity="top"
                android:maxLines="2"
                android:paddingStart="@dimen/dimen_16dp"
                android:text="@string/signUpInAgeAgreement"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/verify_bt"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:contentDescription="@string/axSignUpButtonDisabledMobileNumber"
                android:text="@string/signUpInSignUp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/sign_in_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <View
                android:id="@+id/separator_1"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/dividerColor"
                app:layout_constraintBottom_toTopOf="@id/sign_in_label_tv"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/verify_bt"
                app:layout_constraintVertical_bias="1.0" />

            <TextView
                android:id="@+id/sign_in_label_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dimen_16dp"
                android:foreground="?attr/selectableItemBackground"
                android:gravity="center"
                android:textSize="@dimen/text_14sp"
                android:text="@string/signUpInAlreadyHaveAccountLabel"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/loading_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/dimen_16dp"
                android:indeterminateTint="@color/colorPrimaryDark" />

            <TextView
                android:id="@+id/welcome_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:gravity="center"
                android:text="@string/signUpInWelcomeGreeting"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/error_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/error_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:gravity="center"
                android:text="@string/alertMessageServerError"
                android:textColor="@color/titleTextColor"
                android:textSize="@dimen/text_18sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/retry_button"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:layout_marginBottom="@dimen/dimen_16dp"
                android:text="@string/alertActionTryAgain" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
