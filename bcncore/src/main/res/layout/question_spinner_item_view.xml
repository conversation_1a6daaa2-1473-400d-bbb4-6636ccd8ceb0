<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/dropdown_item_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:lineSpacingExtra="6sp"
            android:paddingStart="0dp"
            android:paddingTop="6dp"
            android:paddingEnd="@dimen/dimen_10dp"
            android:paddingBottom="6dp"
            android:textSize="18sp"
            android:textStyle="bold"
            tools:text="Delivered" />

        <ImageButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:background="@android:color/transparent"
            android:clickable="false"
            android:layout_gravity="center"
            android:src="@drawable/ic_arrow_drop_down"
            tools:ignore="ContentDescription" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/divider" />

</LinearLayout>
