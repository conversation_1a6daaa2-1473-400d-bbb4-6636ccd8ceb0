<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="@dimen/dimen_8dp"
    android:fillViewport="true"
    android:theme="@style/RootLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/coil_heading_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:text="@string/thirdPartyCoilHeading"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/coil_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/coil_heading_tv"
            tools:text="http://schemas.android.com/tools" />

        <TextView
            android:id="@+id/coil_license_info_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyApacheDescription"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/coil_link_tv" />

        <TextView
            android:id="@+id/coil_license_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/coil_license_info_tv"
            tools:text="https://www.apache.org/licenses/LICENSE-2.0" />

        <View
            android:id="@+id/separator_0"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/listDividerColor"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/coil_license_link_tv" />

        <TextView
            android:id="@+id/gson_heading_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/gson_heading"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/separator_0" />

        <TextView
            android:id="@+id/gson_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gson_heading_tv"
            tools:text="https://github.com/google/gson" />

        <TextView
            android:id="@+id/gson_copyright_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyGoogleCopyright2008"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gson_link_tv" />

        <TextView
            android:id="@+id/gson_license_info_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyApacheDescription"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gson_copyright_tv" />

        <TextView
            android:id="@+id/gson_license_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gson_license_info_tv"
            tools:text="https://www.apache.org/licenses/LICENSE-2.0" />

        <View
            android:id="@+id/separator_1"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/listDividerColor"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gson_license_link_tv" />


        <TextView
            android:id="@+id/okHttp_heading_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/okhttp_heading"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/separator_1" />

        <TextView
            android:id="@+id/okHttp_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/okHttp_heading_tv"
            tools:text="https://github.com/square/okhttp" />

        <TextView
            android:id="@+id/okHttp_license_info_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyApacheDescription"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/okHttp_link_tv" />

        <TextView
            android:id="@+id/okHttp_license_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/okHttp_license_info_tv"
            tools:text="https://www.apache.org/licenses/LICENSE-2.0" />

        <View
            android:id="@+id/separator_2"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/listDividerColor"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/okHttp_license_link_tv" />

        <TextView
            android:id="@+id/uCrop_heading_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/uCrop_heading"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/separator_2" />

        <TextView
            android:id="@+id/uCrop_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/uCrop_heading_tv"
            tools:text="https://github.com/Yalantis/uCrop" />

        <TextView
            android:id="@+id/uCrop_license_info_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyApacheDescription"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/uCrop_link_tv" />

        <TextView
            android:id="@+id/uCrop_license_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/uCrop_license_info_tv"
            tools:text="https://www.apache.org/licenses/LICENSE-2.0" />

        <View
            android:id="@+id/separator_3"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/listDividerColor"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/uCrop_license_link_tv" />

        <TextView
            android:id="@+id/vision_common_heading_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/vision_common_heading"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/separator_3" />

        <TextView
            android:id="@+id/vision_common_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vision_common_heading_tv"
            tools:text="https://developers.google.com/ml-kit/terms" />

        <TextView
            android:id="@+id/barcode_scanning_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vision_common_link_tv"
            tools:text="https://developers.google.com/ml-kit/vision/barcode-scanning" />

        <TextView
            android:id="@+id/vision_common_license_info_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/thirdPartyApacheDescription"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barcode_scanning_link_tv" />

        <TextView
            android:id="@+id/vision_common_license_link_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vision_common_license_info_tv"
            tools:text="https://www.apache.org/licenses/LICENSE-2.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
