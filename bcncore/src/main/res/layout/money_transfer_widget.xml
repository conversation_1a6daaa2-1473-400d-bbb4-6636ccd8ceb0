<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_4dp"
    android:orientation="vertical"
    android:padding="@dimen/dimen_16dp_small_screen">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:gravity="center"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <!-- We are not using compound drawables here as widgets does not support it-->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_logo_bcn"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/appwidget_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:gravity="center"
            android:text="@string/appName"
            android:textColor="@color/sectionHeadingColor"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp_small_screen"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/scan_and_pay"
                android:layout_width="@dimen/widget_image_48dp"
                android:layout_height="@dimen/widget_image_48dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_widget_scan_and_pay"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:text="@string/appShortCutScanAndPay"
                android:textColor="@color/sectionHeadingColor"
                android:textSize="11sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/send_money"
                android:layout_width="@dimen/widget_image_48dp"
                android:layout_height="@dimen/widget_image_48dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_widget_send_money"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:text="@string/appShortCutSendMoney"
                android:textColor="@color/sectionHeadingColor"
                android:textSize="11sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/load_Wallet"
                android:layout_width="@dimen/widget_image_48dp"
                android:layout_height="@dimen/widget_image_48dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_widget_load_money"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:text="@string/appShortCutLoadWallet"
                android:textColor="@color/sectionHeadingColor"
                android:textSize="11sp" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/pay_bill"
                android:layout_width="@dimen/widget_image_48dp"
                android:layout_height="@dimen/widget_image_48dp"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_widget_pay_bills"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:text="@string/appShortCutPayBills"
                android:textColor="@color/sectionHeadingColor"
                android:textSize="11sp" />
        </LinearLayout>


    </LinearLayout>
</LinearLayout>
