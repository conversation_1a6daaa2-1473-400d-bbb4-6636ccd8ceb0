<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_16dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/description_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:importantForAccessibility="no"
        android:text="@string/alertTransactionDetailAmount"
        android:textColor="@color/descriptionTextColor" />

    <TextView
        android:id="@+id/quantity_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/subtitleTextColor"
        android:textStyle="bold"
        tools:text="10000.00" />

</LinearLayout>
