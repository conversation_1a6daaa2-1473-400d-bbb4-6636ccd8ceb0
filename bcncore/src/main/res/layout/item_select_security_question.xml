<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingTop="@dimen/dimen_40dp">

    <LinearLayout
        android:id="@+id/security_question_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:minHeight="@dimen/dimen_48dp"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dimen_4dp"
        tools:ignore="UseCompoundDrawables">

        <TextView
            android:id="@+id/question_hint_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLength="100"
            android:paddingStart="@dimen/dimen_4dp"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_12sp"
            android:visibility="gone"
            tools:text="Question 1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="@dimen/dimen_4dp"
            tools:ignore="UseCompoundDrawables">

            <TextView
                android:id="@+id/question_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_4dp"
                android:layout_weight="1"
                android:maxLength="100"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                tools:text="What is your middle name" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_chevron_right_grey"
                tools:ignore="ContentDescription" />

        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/separator"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:background="@color/editTextDivider" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/answer_til"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/TextInputEditTextCursorTheme">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/answer_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/setSecurityQuestionAnswer"
            android:paddingHorizontal="@dimen/dimen_4dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp" />
    </com.google.android.material.textfield.TextInputLayout>
</LinearLayout>
