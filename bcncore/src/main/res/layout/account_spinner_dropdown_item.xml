<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/account_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center">

        <TextView
            android:id="@+id/account_holder_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/spinnerItemTextColor"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="<PERSON>" />

        <TextView
            android:id="@+id/account_number"
            android:layout_width="wrap_content"
            android:gravity="end"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dimen_8dp"
            android:textColor="@color/spinnerItemTextColor"
            android:textSize="12sp"
            tools:text="**************" />

    </LinearLayout>

    <TextView
        android:id="@+id/create_account_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/spinnerItemTextColor"
        android:textSize="16sp"
        android:textStyle="bold"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:text="@string/createNewAccountViewTitle"
        android:drawablePadding="@dimen/dimen_8dp"
        app:drawableStartCompat="@drawable/ic_add_with_circular_border"
        android:visibility="gone"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/listDividerColor"
        android:alpha="0.5"/>
</LinearLayout>
