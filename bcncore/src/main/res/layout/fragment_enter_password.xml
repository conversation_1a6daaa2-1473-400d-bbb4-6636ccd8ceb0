<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context="com.resoluttech.core.auth.signin.EnterPasswordFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/please_enter_password_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/enterPasswordSubtitle"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/lock_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:background="@color/transparent"
            android:src="@drawable/ic_password_lock"
            app:layout_constraintBottom_toBottomOf="@id/enter_password_til"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/enter_password_til"
            tools:ignore="ContentDescription" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/enter_password_til"
            style="@style/TextInputLayoutStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:theme="@style/TextInputEditTextCursorTheme"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/lock_iv"
            app:layout_constraintTop_toBottomOf="@id/please_enter_password_tv"
            app:passwordToggleDrawable="@drawable/show_password_selector"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@color/passwordToggleColor">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/enter_password_tiet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:autofillHints="password"
                android:hint="@string/enterPasswordTextFieldPlaceholder"
                android:inputType="textPassword"
                android:paddingHorizontal="@dimen/dimen_4dp"
                android:textSize="@dimen/text_16sp" />
        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/forget_password_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_28dp"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center"
            android:text="@string/enterPasswordForgotPassword"
            android:textAllCaps="true"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/sign_in_bt"
            app:layout_constraintEnd_toEndOf="@+id/sign_in_bt"
            app:layout_constraintStart_toStartOf="@+id/sign_in_bt" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/sign_in_bt"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_margin="@dimen/dimen_16dp"
            android:text="@string/enterPasswordSignInButtonTitle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
