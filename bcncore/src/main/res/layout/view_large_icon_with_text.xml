<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/section_label_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toTopOf="@id/icon_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Processed By" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/icon_view"
            android:layout_width="@dimen/dimen_48dp"
            android:layout_height="@dimen/dimen_48dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:minWidth="@dimen/dimen_48dp"
            android:minHeight="@dimen/dimen_48dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/section_label_tv"
            app:shapeAppearanceOverlay="@style/roundedImageViewRounded" />

        <TextView
            android:id="@+id/section_title_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_12dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/section_description_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/icon_view"
            app:layout_constraintTop_toTopOf="@+id/icon_view"
            tools:text="Rosie Downey" />

        <TextView
            android:id="@+id/section_description_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_12dp"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toBottomOf="@+id/icon_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/icon_view"
            app:layout_constraintTop_toBottomOf="@+id/section_title_tv"
            tools:text="<EMAIL>" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
