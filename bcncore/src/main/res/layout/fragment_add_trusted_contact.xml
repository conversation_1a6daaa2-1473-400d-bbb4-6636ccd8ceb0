<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.auth.trustedcontacts.SetupTrustedContactsFragment">

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/finish_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:gravity="center"
                android:text="@string/addTrustedContactMessage"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/heading_tv"
                style="@style/ListHeaderStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_8dp"
                android:text="@string/addTrustedContactsMobileNumber"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_12sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@id/mobile_number_ll"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/description" />

            <LinearLayout
                android:id="@+id/mobile_number_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_16dp"
                app:layout_constraintBottom_toTopOf="@id/heading_2_tv"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:src="@drawable/ic_phone_number"
                    tools:ignore="contentDescription" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:orientation="vertical">

                    <com.hbb20.CountryCodePicker
                        android:id="@+id/country_code_picker"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:ccpDialog_allowSearch="false"
                        app:ccpDialog_background="@drawable/bg_4dp"
                        app:ccpDialog_cornerRadius="@dimen/dimen_4dp"
                        app:ccpDialog_textColor="@color/subtitleTextColor"
                        app:ccp_arrowColor="@color/subtitleTextColor"
                        app:ccp_contentColor="@color/subtitleTextColor"
                        app:ccp_showNameCode="false"
                        app:ccp_textSize="@dimen/text_16sp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_gravity="bottom"
                        android:layout_marginTop="@dimen/dimen_8dp"
                        android:background="@color/editTextDivider" />

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/phone_number_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginEnd="@dimen/dimen_8dp"
                        android:hint="@string/addTrustedContactsMobileNumber"
                        android:theme="@style/TextInputEditTextCursorTheme"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/contact_picker"
                        app:layout_constraintStart_toStartOf="parent">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/phone_number_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:autofillHints="phonenumber"
                            android:importantForAutofill="no"
                            android:inputType="phone"
                            android:maxLength="15"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textSize="@dimen/text_16sp" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <ImageButton
                        android:id="@+id/contact_picker"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_8dp"
                        android:background="?android:attr/actionBarItemBackground"
                        android:backgroundTint="@color/transparent"
                        android:contentDescription="@string/axContactPickerContactHint"
                        android:src="@drawable/ic_select_contact"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/phone_number_til"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/heading_2_tv"
                style="@style/ListHeaderStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_8dp"
                android:text="@string/addTrustedContactOtherDetails"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_12sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mobile_number_ll" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/first_name_til"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:hint="@string/addTrustedContactsFirstName"
                android:theme="@style/TextInputEditTextCursorTheme"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/heading_2_tv">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/first_name_et"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAutofill="no"
                    android:inputType="textCapWords"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dimen_4dp"
                    android:textColor="@color/titleTextColor"
                    android:textSize="@dimen/text_16sp"
                    tools:hint="@string/addTrustedContactsFirstName"
                    tools:text="Wade" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/last_name_til"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:hint="@string/addTrustedContactsLastNamePlaceholder"
                android:theme="@style/TextInputEditTextCursorTheme"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/first_name_til">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/last_name_et"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAutofill="no"
                    android:inputType="textCapWords"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dimen_4dp"
                    android:textColor="@color/titleTextColor"
                    android:textSize="@dimen/text_16sp"
                    tools:hint="@string/addTrustedContactsLastNamePlaceholder"
                    tools:text="Warren" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/email_id_til"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:hint="@string/addTrustedContactsEmailPlaceholder"
                android:theme="@style/TextInputEditTextCursorTheme"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/last_name_til">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/email_id_et"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAutofill="no"
                    android:inputType="textEmailAddress"
                    android:maxLength="200"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dimen_4dp"
                    android:textColor="@color/titleTextColor"
                    android:textSize="@dimen/text_16sp"
                    tools:hint="@string/addTrustedContactsEmailPlaceholder"
                    tools:text="<EMAIL>" />

            </com.google.android.material.textfield.TextInputLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/finish_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:contentDescription="@string/axAddTrustedContactButtonHint"
        android:text="@string/addTrustedContactTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scroll_view"
        app:layout_constraintVertical_bias="1" />

</androidx.constraintlayout.widget.ConstraintLayout>
