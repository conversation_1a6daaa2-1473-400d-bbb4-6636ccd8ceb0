<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dimen_24dp">

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dimen_16dp"
        android:text="@string/emailVerificationAddEmailMessage"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_40dp"
        android:paddingHorizontal="@dimen/dimen_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/description">

        <ImageButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:background="@color/transparent"
            android:importantForAutofill="no"
            android:src="@drawable/ic_mail"
            tools:ignore="ContentDescription" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/email_til"
            style="@style/TextInputLayoutStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/TextInputEditTextCursorTheme">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/email_et"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/emailVerificationEmailTextfieldPlaceholder"
                android:inputType="text"
                android:maxLength="100"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dimen_4dp"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                tools:hint="@string/emailVerificationEmailTextfieldPlaceholder" />

        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/send_verification_button"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:text="@string/emailVerificationSendVerificationLink"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
