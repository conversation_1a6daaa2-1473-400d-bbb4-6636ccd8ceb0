<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.enteramount.EnterAmountFragment">

    <FrameLayout
        android:id="@+id/amount_text_field"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="40dp"
        android:gravity="bottom"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/accountToAccountEnterAmount">

            <com.resoluttech.core.views.AmountEditText
                android:id="@+id/amount_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:imeOptions="actionDone"
                android:paddingStart="5dp"
                android:paddingEnd="45dp" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:id="@+id/currency_suffix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:paddingStart="@dimen/dimen_10dp"
            android:paddingEnd="@dimen/dimen_10dp"
            android:paddingBottom="14dp" />
    </FrameLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/public_remark_til"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:hint="@string/accountToAccountNarrationToSelf"
        app:counterEnabled="true"
        app:layout_constraintEnd_toEndOf="@+id/amount_text_field"
        app:layout_constraintStart_toStartOf="@+id/amount_text_field"
        app:layout_constraintTop_toBottomOf="@id/amount_text_field">

        <com.resoluttech.core.views.LimitedCharacterEditText
            android:id="@+id/public_remark_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inputType="none"
            android:maxLines="5"
            android:paddingHorizontal="@dimen/dimen_4dp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/next_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_marginStart="40dp"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="40dp"
        android:background="@drawable/bg_button_4dp_primary_enabled"
        android:text="@string/nextButtonTitle"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@+id/public_remark_til" />

</androidx.constraintlayout.widget.ConstraintLayout>
