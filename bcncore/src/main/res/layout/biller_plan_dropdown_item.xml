<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/plan_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_8dp"
        android:paddingEnd="@dimen/dimen_8dp"
        android:paddingBottom="@dimen/dimen_8dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        tools:text="DSTV Plan" />

    <TextView
        android:id="@+id/plan_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:paddingEnd="@dimen/dimen_16dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        tools:text="10000 MWK" />

</LinearLayout>
