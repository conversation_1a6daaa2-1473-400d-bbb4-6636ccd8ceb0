<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:id="@+id/account_holder_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingTop="4dp"
        android:textColor="@color/sectionHeadingColor"
        android:textSize="18sp"
        tools:text="Personal"/>

    <TextView
        android:id="@+id/account_number"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="4dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="12sp"
        tools:text="Account No. ****5647"/>

    <View
        android:id="@+id/account_dropdown_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="4dp"
        android:background="@color/name_label_color"
        android:alpha="0.5"/>
</LinearLayout>
