<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/app_icon_iv"
        android:layout_width="@dimen/dimen_64dp"
        android:layout_height="@dimen/dimen_64dp"
        android:contentDescription="@string/selectDestinationBCNDestination"
        android:src="@drawable/ic_logo_bcn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.400" />

    <TextView
        android:id="@+id/app_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/appName"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_icon_iv" />

    <TextView
        android:id="@+id/app_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:textColor="@color/titleDescriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_name_tv"
        tools:text="Version 1.0" />

    <TextView
        android:id="@+id/share_logs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_horizontal"
        android:paddingVertical="@dimen/dimen_12dp"
        android:text="@string/appInfoShareLogs"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimaryLight"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_version" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/share_usage_reports_switch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:text="@string/appInfoSendReports"
        android:textColor="@color/descriptionTextColor"
        android:theme="@style/SwitchTheme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
