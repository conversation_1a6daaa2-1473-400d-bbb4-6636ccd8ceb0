<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.home.HomeFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/home_layout_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">

        <TextView
            android:id="@+id/retry_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:gravity="center"
            android:text="@string/alertMessageServerError"
            android:textSize="25sp"
            app:layout_constraintBottom_toTopOf="@+id/retry_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/retry_button"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/button_height"
            android:background="@drawable/bg_button_4dp_primary_enabled"
            android:text="@string/alertActionTryAgain"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/retry_message"
            app:layout_constraintVertical_chainStyle="packed" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/home_layout_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateTint="@color/colorPrimaryDark"
            android:indeterminateTintMode="src_atop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/home_layout_data"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/home_layout_swipe_to_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/primary_balance_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="19dp"
                        android:paddingTop="15dp"
                        android:paddingBottom="15dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.8">

                        <TextView
                            android:id="@+id/balance_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/moneyScreenAvailableBalance"
                            android:textColor="@color/white"
                            android:textSize="12sp"
                            app:layout_constraintBottom_toTopOf="@+id/secondary_balance_layout"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_chainStyle="packed" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/secondary_balance_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/balance_label">

                            <TextView
                                android:id="@+id/currency"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="5dp"
                                android:textColor="@color/white"
                                android:textSize="25sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toStartOf="@+id/balance"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/balance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/white"
                                android:textSize="25sp"
                                android:textStyle="bold"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHorizontal_chainStyle="packed"
                                app:layout_constraintStart_toEndOf="@+id/currency"
                                app:layout_constraintTop_toTopOf="parent" />
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/balance_visibility_button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@color/transparent"
                                android:clickable="false"
                                android:gravity="center"
                                tools:text = "Show"
                                android:textAllCaps="false"
                                android:textColor="@color/white"
                                android:textSize="25sp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintVertical_bias="0.0" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/national_id_expired_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/dialogBgColor"
                        android:padding="@dimen/dimen_16dp"
                        app:layout_constraintBottom_toTopOf="@id/body_block_container"
                        app:layout_constraintTop_toBottomOf="@id/primary_balance_layout">

                        <ImageView
                            android:id="@+id/error_info"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="2dp"
                            android:src="@drawable/ic_error_info"
                            app:layout_constraintEnd_toStartOf="@id/information_title"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/information_title"
                            tools:ignore="ContentDescription" />

                        <TextView
                            android:id="@+id/information_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/moneyScreenLabelNationalIdExpiredTitle"
                            android:textColor="@color/destructiveActionColor"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            app:layout_constraintStart_toEndOf="@+id/error_info"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/information_description"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/moneyScreenLabelNationalIdExpiredMessage"
                            android:textColor="@color/destructiveActionColor"
                            android:textSize="12sp"
                            app:layout_constraintStart_toStartOf="@id/information_title"
                            app:layout_constraintTop_toBottomOf="@id/information_title" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/body_block_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_16dp"
                        app:layout_constraintTop_toBottomOf="@+id/national_id_expired_layout" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
