<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:paddingBottom="@dimen/dimen_12dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Alert title" />

    <TextView
        android:id="@+id/errorMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:paddingBottom="@dimen/dimen_24dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_title"
        tools:text="Are you sure you would like to cancel this Cash In request?" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/dismiss"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginVertical="@dimen/dimen_8dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:minWidth="@dimen/dimen_68dp"
        android:text="@string/alertActionDismiss"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimaryLight"
        android:textStyle="bold"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/errorMessage" />

</androidx.constraintlayout.widget.ConstraintLayout>
