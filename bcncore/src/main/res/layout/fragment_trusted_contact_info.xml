<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.auth.trustedcontacts.TrustedContactInfoFragment">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dimen_16dp"
            app:layout_constraintBottom_toTopOf="@id/setup_trusted_contact_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/illustration_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_8dp"
                android:src="@drawable/ic_setup_trusted_contact_illustration"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:gravity="center"
                android:text="@string/trustedContactsOnboardingTitle"
                android:textColor="@color/titleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/subtitle_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:gravity="center"
                android:text="@string/trustedContactsOnboardingMessage"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <TextView
                android:id="@+id/description_1_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:drawablePadding="@dimen/dimen_8dp"
                android:text="@string/trustedContactsOnboardingPointOne"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_desc_trusted_contact_pt_1" />

            <TextView
                android:id="@+id/description_2_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:drawablePadding="@dimen/dimen_8dp"
                android:text="@string/trustedContactsOnboardingPointTwo"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_desc_trusted_contact_pt_2" />

            <TextView
                android:id="@+id/description_3_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:drawablePadding="@dimen/dimen_8dp"
                android:text="@string/trustedContactsOnboardingPointThree"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_desc_trusted_contact_pt_3" />

            <TextView
                android:id="@+id/description_4_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:drawablePadding="@dimen/dimen_8dp"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:drawableStartCompat="@drawable/ic_desc_trusted_contact_pt_4"
                tools:text="@string/trustedContactsOnboardingPointFour" />

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/setup_trusted_contact_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:text="@string/trustedContactsOnboardingTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
