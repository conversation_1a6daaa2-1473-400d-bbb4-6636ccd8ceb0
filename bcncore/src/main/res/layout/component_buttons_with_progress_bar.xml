<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/button_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="end"
    android:orientation="horizontal"
    android:layout_marginStart="20dp"
    android:layout_marginEnd="20dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/negative_button"
        style="@style/Widget.AppCompat.Button.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:text="@string/alertActionCancel"
        android:textColor="@color/failed_transaction_theme_color"
        android:textStyle="bold" />

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_gravity="center"
        android:padding="@dimen/dimen_8dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/positive_button"
        style="@style/Widget.AppCompat.Button.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:text="@string/transactionStatusDoneButtonTitle"
        android:paddingHorizontal="@dimen/dimen_10dp"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold" />

</LinearLayout>
