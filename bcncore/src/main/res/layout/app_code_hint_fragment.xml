<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.auth.app.AppCodeHintScreen">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/continue_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="@dimen/dimen_16dp">

            <ImageView
                android:id="@+id/bcn_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:src="@drawable/ic_logo_bcn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription" />

            <ImageView
                android:id="@+id/logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_60dp"
                android:background="@drawable/ic_data_security"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bcn_logo"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:gravity="center"
                android:text="@string/setupSessionPinOnboardingTitle"
                android:textColor="@color/titleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/logo" />

            <TextView
                android:id="@+id/hint_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_8dp"
                android:gravity="center"
                android:text="@string/setupSessionPinOnboardingSubtitle"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintTop_toBottomOf="@id/title_tv" />

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/continue_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:gravity="center"
        android:text="@string/continue_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
