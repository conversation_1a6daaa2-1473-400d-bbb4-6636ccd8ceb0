<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="@dimen/dimen_10dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textStyle="bold"
        android:textSize="18sp"
        android:paddingTop="@dimen/dimen_16dp"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_8dp"
        android:textColor="@color/sectionHeadingColor"
        tools:text="Select Language"/>

    <RadioGroup
        android:id="@+id/radio_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dimen_8dp"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_16dp"
        android:orientation="vertical"/>


</LinearLayout>
