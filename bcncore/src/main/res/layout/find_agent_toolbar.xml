<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageButton
        android:id="@+id/arrow_back_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?android:attr/actionBarItemBackground"
        android:contentDescription="@string/axBackButtonLabel"
        android:padding="@dimen/dimen_16dp"
        android:src="@drawable/ic_arrow_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp"
        android:text="@string/findAgentViewTitle"
        android:textColor="@color/white"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/arrow_back_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <com.suryadigital.leo.libui.textdropdown.TextDropdown
        android:id="@+id/status_dropdown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:dropDownWidth="96dp"
        android:gravity="end"
        android:padding="@dimen/dimen_16dp"
        android:popupBackground="@drawable/bg_popup_menu"
        android:visibility="gone"
        android:theme="@style/TextDropDownStyle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@id/title"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
