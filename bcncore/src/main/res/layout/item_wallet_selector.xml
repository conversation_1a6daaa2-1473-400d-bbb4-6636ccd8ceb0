<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_wallet_info"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:padding="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/account_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/is_selected_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Malawi Office 1" />

    <TextView
        android:id="@+id/balance_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="@dimen/text_14sp"
        android:textColor="@color/descriptionTextColor"
        app:layout_constraintEnd_toStartOf="@id/is_selected_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_name_tv"
        tools:text="Balance: MWK 234,555" />

    <ImageView
        android:id="@+id/is_selected_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_check_green"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/account_name_tv"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
