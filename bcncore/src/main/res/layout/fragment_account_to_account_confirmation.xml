<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/alertTitleTransactionDetails"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailAmount"
            android:textColor="@color/dialogSubtitleTextColor" />

        <TextView
            android:id="@+id/send_amount_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            tools:text="10000.00" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/exchange_rate_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/alertTransactionDetailExchangeRate"
            android:textColor="@color/dialogSubtitleTextColor" />

        <TextView
            android:id="@+id/exchange_from_rate_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="@dimen/text_14sp"
            app:autoSizeMinTextSize="@dimen/text_10sp"
            app:autoSizeStepGranularity="@dimen/text_2sp"
            app:autoSizeTextType="uniform"
            tools:text="10000.00" />

        <ImageButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dimen_10dp"
            android:layout_marginEnd="@dimen/dimen_10dp"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_exchange_rate"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/exchange_to_rate_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="@dimen/text_14sp"
            app:autoSizeMinTextSize="@dimen/text_10sp"
            app:autoSizeStepGranularity="@dimen/text_2sp"
            app:autoSizeTextType="uniform"
            tools:text="10000.00" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/receiving_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/alertTransactionReceiveAmount"
            android:textColor="@color/dialogSubtitleTextColor" />

        <TextView
            android:id="@+id/receive_amount_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="@dimen/text_14sp"
            app:autoSizeMinTextSize="@dimen/text_10sp"
            app:autoSizeStepGranularity="@dimen/text_2sp"
            app:autoSizeTextType="uniform"
            tools:text="10000.00" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/alertTransactionDetailTransactionFee"
            android:textColor="@color/dialogSubtitleTextColor" />

        <TextView
            android:id="@+id/transaction_fee_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="@dimen/text_14sp"
            app:autoSizeMinTextSize="@dimen/text_10sp"
            app:autoSizeStepGranularity="@dimen/text_2sp"
            app:autoSizeTextType="uniform"
            tools:text="2.00" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginVertical="@dimen/text_14sp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp">

        <TextView
            android:id="@+id/payment_amount_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailPayableAmount"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/payable_amount_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/payable_amount_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:autoSizeMaxTextSize="@dimen/text_14sp"
            app:autoSizeMinTextSize="@dimen/text_10sp"
            app:autoSizeStepGranularity="2sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="10,002.00" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/decline_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionDecline" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/proceed_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/alertActionProceed" />

    </LinearLayout>
</LinearLayout>
