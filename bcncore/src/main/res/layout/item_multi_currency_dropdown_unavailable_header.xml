<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/ListHeaderStyle">
    <TextView
        android:id="@+id/unavailable_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="35dp"
        android:paddingStart="21dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_10dp"
        android:paddingBottom="@dimen/dimen_10dp"
        android:textColor="@color/buttonLabelColor"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Unavailable"/>

    <ImageView
        android:id="@+id/info_IV"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_info"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_10dp"
        android:paddingBottom="@dimen/dimen_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/unavailable_message_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="30dp"
        android:background="@color/listSubHeaderBgColor"
        android:textColor="@color/listSubHeaderTextColor"
        tools:text="Martha can only accept transfers in MWK."
        android:textSize="12sp"
        android:paddingStart="21dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_10dp"
        android:paddingBottom="@dimen/dimen_10dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/unavailable_tv"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
