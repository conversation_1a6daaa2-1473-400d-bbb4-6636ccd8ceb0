<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/dialogBg"
    android:orientation="vertical"
    android:paddingTop="@dimen/dimen_24dp"
    android:paddingBottom="@dimen/dimen_8dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:text="@string/alertTitleEditAccountName"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginVertical="@dimen/dimen_12dp"
        android:text="@string/alertMessageEditAccountName"
        android:textColor="@color/descriptionTextDarkColor"
        android:textSize="@dimen/text_14sp" />


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/account_name_til"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:theme="@style/TextInputEditTextCursorTheme">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/account_name_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/dialogBgColor"
            android:hint="@string/alertPlaceholderEditAccountName"
            android:inputType="textPersonName"
            android:paddingHorizontal="@dimen/dimen_4dp"
            android:textSize="@dimen/text_16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:gravity="end|center_vertical"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionCancel" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/ok_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/alertActionSave" />

    </LinearLayout>

</LinearLayout>
