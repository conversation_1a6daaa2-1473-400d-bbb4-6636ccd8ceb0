<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dimen_32dp"
    tools:context="com.resoluttech.core.transfers.peertopeer.SendMoneyFragment">

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/pay_now_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/user_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxWidth="96dp"
                    android:maxHeight="96dp"
                    android:minWidth="48dp"
                    android:minHeight="48dp"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/profile_image_view"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:importantForAccessibility="no"
                        android:maxHeight="48dp"
                        android:minHeight="48dp"
                        android:src="@drawable/ic_person_black_24dp" />
                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/person_name_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:gravity="center_vertical"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toTopOf="@+id/person_phone_number_tv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cardView"
                    app:layout_constraintTop_toTopOf="@+id/cardView"
                    tools:text="Rosie Downey" />

                <TextView
                    android:id="@+id/person_phone_number_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:gravity="center_vertical"
                    android:textColor="@color/descriptionTextDarkColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toBottomOf="@+id/cardView"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/cardView"
                    app:layout_constraintTop_toBottomOf="@+id/person_name_tv"
                    tools:text="+91 99887 76643" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dimen_40dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/user_info">

                <LinearLayout
                    android:id="@+id/send_money_layout_error"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:visibility="gone"
                    tools:visibility="gone">

                    <TextView
                        android:id="@+id/retry_message"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="18dp"
                        android:gravity="center"
                        android:text="@string/alertMessageServerError"
                        android:textColor="@color/descriptionTextColor"
                        android:textSize="@dimen/text_14sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/retry_button"
                        style="@style/PrimaryButton"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/button_height"
                        android:text="@string/alertActionTryAgain" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/send_money_layout_enter_amount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <FrameLayout
                        android:id="@+id/amount_text_field"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dimen_16dp">

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/TextInputLayoutStyle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/sendMoneyEnterAmount"
                            android:minHeight="@dimen/dimen_48dp"
                            android:theme="@style/TextInputEditTextCursorTheme"
                            app:hintTextColor="@color/subtitleTextColorSecondary">

                            <com.resoluttech.core.views.AmountEditText
                                android:id="@+id/amount_edit_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:imeOptions="actionDone"
                                android:minHeight="@dimen/dimen_48dp"
                                android:textSize="@dimen/text_16sp"
                                android:paddingHorizontal="@dimen/dimen_4dp"
                                android:textColor="@color/subtitleTextColor" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:id="@+id/currency_suffix"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="end|bottom"
                            android:layout_marginEnd="@dimen/dimen_8dp"
                            android:paddingBottom="@dimen/editTextBottomPadding"
                            android:textColor="@color/subtitleTextColorSecondary"
                            android:textSize="@dimen/text_14sp"
                            tools:text="MWK" />
                    </FrameLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/private_remark_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_16dp"
                        android:hint="@string/sendMoneyNarrationToSelfPlaceholder"
                        android:minHeight="@dimen/dimen_48dp"
                        android:layout_marginHorizontal="@dimen/dimen_16dp"
                        android:theme="@style/TextInputEditTextCursorTheme"
                        app:hintTextColor="@color/subtitleTextColorSecondary"
                        app:layout_constraintEnd_toEndOf="@+id/amount_text_field"
                        app:layout_constraintStart_toStartOf="@+id/amount_text_field"
                        app:layout_constraintTop_toBottomOf="@id/amount_text_field">

                        <com.resoluttech.core.views.LimitedCharacterEditText
                            android:id="@+id/private_remark_et"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:maxLines="5"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textSize="@dimen/text_16sp"
                            android:textColor="@color/subtitleTextColor" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/public_remark_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_16dp"
                        android:hint="@string/sendMoneyNarrationToReceiverPlaceholder"
                        android:minHeight="@dimen/dimen_48dp"
                        android:theme="@style/TextInputEditTextCursorTheme"
                        android:layout_marginHorizontal="@dimen/dimen_16dp"
                        app:hintTextColor="@color/subtitleTextColorSecondary"
                        app:layout_constraintEnd_toEndOf="@+id/amount_text_field"
                        app:layout_constraintStart_toStartOf="@+id/amount_text_field"
                        app:layout_constraintTop_toBottomOf="@id/private_remark_til">

                        <com.resoluttech.core.views.LimitedCharacterEditText
                            android:id="@+id/public_remark_et"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:maxLines="5"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textSize="@dimen/text_16sp"
                            android:textColor="@color/subtitleTextColor" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <include
                        layout="@layout/component_pay_from_wallet"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/public_remark_til" />

                </LinearLayout>

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/pay_now_button"
        style="@style/PrimaryButton"
        android:layout_margin="@dimen/dimen_16dp"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:contentDescription="@string/axSendMoneyAttemptPaymentDisabledHint"
        android:text="@string/sendMoneyAttemptPayment"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scroll_view" />

</androidx.constraintlayout.widget.ConstraintLayout>
