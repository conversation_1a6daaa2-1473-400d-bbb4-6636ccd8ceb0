<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingVertical="@dimen/dimen_12dp">

    <TextView
        android:id="@+id/statement_month_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/statement_date_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/subtitleTextColorSecondary"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statement_month_tv" />

</androidx.constraintlayout.widget.ConstraintLayout>
