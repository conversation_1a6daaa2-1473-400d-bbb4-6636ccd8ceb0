<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="56dp"
    android:paddingStart="0dp"
    android:paddingEnd="@dimen/dimen_16dp"
    android:paddingTop="@dimen/dimen_10dp"
    android:paddingBottom="@dimen/dimen_10dp">

    <TextView
        android:id="@+id/unavailable_account_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/descriptionTextColor"
        android:textSize="15sp"
        android:textStyle="bold"
        android:layout_marginStart="46dp"
        tools:text="Office"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/unavailable_account_currency_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/descriptionTextColor"
        android:textSize="15sp"
        android:textStyle="bold"
        tools:text="ZMW"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
