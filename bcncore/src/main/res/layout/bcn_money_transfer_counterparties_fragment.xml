<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <TextView
        android:id="@+id/to_another_person_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:minHeight="70dp"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_16dp"
        android:textColor="@color/titleTextColor"
        android:textSize="18sp"
        android:textStyle="bold"
        app:drawableEndCompat="@drawable/ic_next"
        android:text="@string/moneyTransferToAnotherPerson" />

    <TextView
        android:id="@+id/no_access_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/unavailableAccountTextColor"
        android:padding="@dimen/dimen_8dp"
        android:textColor="@color/spinnerItemTextColor"
        android:textSize="12sp"
        android:visibility="gone"
        tools:visibility="visible"
        android:text="@string/selectDestinationUserIsAgentLabel" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/listDividerColor" />

    <TextView
        android:id="@+id/to_own_account_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:minHeight="70dp"
        android:paddingStart="@dimen/dimen_16dp"
        android:paddingTop="@dimen/dimen_16dp"
        android:paddingEnd="@dimen/dimen_16dp"
        android:paddingBottom="@dimen/dimen_16dp"
        android:textColor="@color/titleTextColor"
        android:textSize="18sp"
        android:textStyle="bold"
        app:drawableEndCompat="@drawable/ic_next"
        android:text="@string/moneyTransferToSelf" />

</LinearLayout>
