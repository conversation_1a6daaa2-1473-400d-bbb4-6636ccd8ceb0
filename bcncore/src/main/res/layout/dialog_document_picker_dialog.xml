<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/dialogBg">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/dimen_12dp"
        android:text="@string/kycAlertAddPhoto"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/take_a_picture"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:layout_marginEnd="@dimen/dimen_28dp"
        android:clickable="true"
        android:drawablePadding="@dimen/dimen_16dp"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dimen_12dp"
        android:text="@string/kycAlertChooseCamera"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_camera"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_tv" />

    <TextView
        android:id="@+id/select_from_gallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_28dp"
        android:clickable="true"
        android:drawablePadding="@dimen/dimen_16dp"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dimen_12dp"
        android:text="@string/kycAlertChooseFromGallery"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_gallery"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/take_a_picture" />

    <TextView
        android:id="@+id/select_document"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_28dp"
        android:clickable="true"
        android:drawablePadding="@dimen/dimen_16dp"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dimen_12dp"
        android:text="@string/kycAlertChooseFiles"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_pick_document"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/select_from_gallery" />

    <TextView
        android:id="@+id/remove_document"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_28dp"
        android:clickable="true"
        android:drawablePadding="@dimen/dimen_16dp"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dimen_12dp"
        android:text="@string/kycAlertRemoveDocument"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_delete_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/select_document"
        app:layout_constraintVertical_bias="0" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/cancel_button"
        style="@style/AlertDialogButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:text="@string/alertActionCancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/remove_document" />

</androidx.constraintlayout.widget.ConstraintLayout>
