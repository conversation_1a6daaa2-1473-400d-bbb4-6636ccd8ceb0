<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:gravity="center|start"
    android:orientation="horizontal"
    android:padding="@dimen/dimen_16dp"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/question_selector_iv"
        android:layout_width="@dimen/dimen_20dp"
        android:layout_height="@dimen/dimen_20dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_radiobox_blank"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/security_question_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_32dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        tools:text="@string/setSecurityQuestionSubTitle" />

</LinearLayout>
