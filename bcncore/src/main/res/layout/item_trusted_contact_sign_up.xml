<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/dimen_12dp"
    android:paddingStart="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/numbers_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_circle"
        android:gravity="center"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dimen_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/remove_iv"
        app:layout_constraintStart_toEndOf="@id/numbers_tv"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            tools:text="Wade Waren" />

        <TextView
            android:id="@+id/phone_number_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            tools:text="+265 135654560" />

        <TextView
            android:id="@+id/email_id_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center|start"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            tools:text="<EMAIL>" />

    </LinearLayout>

    <ImageView
        android:id="@+id/remove_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp"
        android:src="@drawable/ic_delete_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
