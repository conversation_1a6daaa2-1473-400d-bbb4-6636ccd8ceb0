<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.resoluttech.core.sendmoney.RecipientIdentifierFragment">


    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/enter_payee_identifier_text"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_40dp"
        android:theme="@style/TextInputEditTextCursorTheme">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/identifier_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberPassword"
            android:maxLength="200"
            android:paddingHorizontal="@dimen/dimen_4dp" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/confirm_payee_identifier_til"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:theme="@style/TextInputEditTextCursorTheme"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/confirm_payee_identifier_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLength="200"
            android:paddingHorizontal="@dimen/dimen_4dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/payee_name_til"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_weight="1"
        android:hint="@string/selectCounterpartyUserName"
        android:theme="@style/TextInputEditTextCursorTheme"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/payee_name_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLength="200"
            android:paddingHorizontal="@dimen/dimen_4dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/identifier_next_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:foreground="?selectableItemBackground"
        android:text="@string/selectCounterpartyUserNext" />

</LinearLayout>
