<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/dialogBg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dimen_24dp">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_12dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            tools:text="Alert title" />

        <TextView
            android:id="@+id/dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            tools:text="Alert message description" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dimen_8dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            tools:text="@string/alertActionCancel" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/ok_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            tools:text="@string/alertActionDismiss" />

    </LinearLayout>


</LinearLayout>
