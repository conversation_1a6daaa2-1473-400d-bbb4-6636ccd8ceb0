<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="Overdraw">

    <ImageView
        android:id="@+id/app_icon_iv"
        android:layout_width="@dimen/dimen_64dp"
        android:layout_height="@dimen/dimen_64dp"
        android:layout_marginTop="@dimen/margin_24dp"
        android:contentDescription="@string/emptyString"
        android:src="@drawable/ic_logo_bcn"
        app:layout_constraintBottom_toTopOf="@+id/landing_screen_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.suryadigital.leo.libui.landingscreen.LandingScreen
        android:id="@+id/landing_screen_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:buttonTextColor="@color/white"
        app:finishButtonText="@string/signUpInSignUp"
        app:finishButtonTextColor="@color/spinnerItemTextColor"
        app:landingScreenBackgroundColor="@color/windowBackground"
        app:layout_constraintBottom_toTopOf="@+id/sign_in_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_icon_iv"
        app:nextButtonColor="@color/colorPrimary"
        app:selectedPageColor="@color/selectedPageIndicatorColorGreen"
        app:shouldShowOnlyFinishButton="true"
        app:skipButtonColor="@color/colorPrimary"
        app:subTitleColor="@color/descriptionTextColor"
        app:subTitleTextSize="@dimen/text_14sp"
        app:titleColor="@color/titleTextColor"
        app:titleTextSize="@dimen/text_18sp" />

    <LinearLayout
        android:id="@+id/sign_in_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/sign_up_container"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_1dp"
            android:background="@color/listDividerColor"
            app:layout_constraintBottom_toTopOf="@id/sign_in_label_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/landing_screen_view"
            app:layout_constraintVertical_bias="1.0" />

        <TextView
            android:id="@+id/sign_in_label_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/dimen_16dp"
            android:text="@string/signUpInAlreadyHaveAccountLabel"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
