<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.transactions.TransactionsFragment">

    <LinearLayout
        android:id="@+id/data"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:orientation="vertical">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/tranasction_swipe_to_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.suryadigital.leo.libui.listview.ListRecyclerView
                android:id="@+id/transaction_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:transitionGroup="true" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminateTint="@color/colorPrimaryDark"
        android:indeterminateTintMode="src_atop" />

    <LinearLayout
        android:id="@+id/error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:src="@drawable/ic_no_internet"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/fullScreenErrorTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/fullScreenErrorMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/alertMessageNoInternet"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/retry_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dimen_16dp"
            android:text="@string/alertActionTryAgain" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/no_data_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/no_data_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="@dimen/dimen_16dp"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:text="@string/transactionsScreenNoRecentTransactionsMessage"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:drawableTopCompat="@drawable/ic_no_transaction" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/transfer_now_bt"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_margin="@dimen/dimen_16dp"
            android:background="@drawable/bg_button_4dp_primary_enabled"
            android:text="@string/transactionScreenMakeTransactionButtonTitle" />

    </LinearLayout>

</FrameLayout>
