<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/kyc_document_sv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/confirm_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingTop="@dimen/dimen_24dp"
            android:paddingBottom="@dimen/dimen_16dp">

            <TextView
                android:id="@+id/add_signature"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycUploadPhotoOfSignature"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/signature_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/signature_iv" />

            <ImageView
                android:id="@+id/upload_signature"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/signature_iv"
                app:layout_constraintEnd_toEndOf="@id/signature_iv"
                app:layout_constraintStart_toStartOf="@id/signature_iv"
                app:layout_constraintTop_toTopOf="@id/signature_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/signature_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ProgressBar
                android:id="@+id/signature_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/signature_iv"
                app:layout_constraintEnd_toEndOf="@id/signature_iv"
                app:layout_constraintStart_toStartOf="@id/signature_iv"
                app:layout_constraintTop_toTopOf="@id/signature_iv" />

            <ImageButton
                android:id="@+id/signature_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/signature_iv"
                app:layout_constraintEnd_toEndOf="@+id/signature_iv"
                app:layout_constraintTop_toTopOf="@+id/signature_iv"
                app:layout_constraintVertical_bias="0.94" />

            <TextView
                android:id="@+id/add_national_id_front"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycNationalIdFrontTitle"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/national_id_front_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/national_id_front_iv" />

            <ImageView
                android:id="@+id/upload_national_id_front"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/national_id_front_iv"
                app:layout_constraintEnd_toEndOf="@id/national_id_front_iv"
                app:layout_constraintStart_toStartOf="@id/national_id_front_iv"
                app:layout_constraintTop_toTopOf="@id/national_id_front_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/national_id_front_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/signature_iv" />

            <ProgressBar
                android:id="@+id/national_id_front_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/national_id_front_iv"
                app:layout_constraintEnd_toEndOf="@id/national_id_front_iv"
                app:layout_constraintStart_toStartOf="@id/national_id_front_iv"
                app:layout_constraintTop_toTopOf="@id/national_id_front_iv" />

            <ImageButton
                android:id="@+id/national_id_front_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/national_id_front_iv"
                app:layout_constraintEnd_toEndOf="@+id/national_id_front_iv"
                app:layout_constraintTop_toTopOf="@+id/national_id_front_iv"
                app:layout_constraintVertical_bias="0.94" />


            <TextView
                android:id="@+id/add_national_id_back"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycNationalIdBackTitle"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/national_id_back_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/national_id_back_iv" />

            <ImageView
                android:id="@+id/upload_national_id_back"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/national_id_back_iv"
                app:layout_constraintEnd_toEndOf="@id/national_id_back_iv"
                app:layout_constraintStart_toStartOf="@id/national_id_back_iv"
                app:layout_constraintTop_toTopOf="@id/national_id_back_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/national_id_back_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/national_id_front_iv" />

            <ProgressBar
                android:id="@+id/national_id_back_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/national_id_back_iv"
                app:layout_constraintEnd_toEndOf="@id/national_id_back_iv"
                app:layout_constraintStart_toStartOf="@id/national_id_back_iv"
                app:layout_constraintTop_toTopOf="@id/national_id_back_iv" />

            <ImageButton
                android:id="@+id/national_id_back_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/national_id_back_iv"
                app:layout_constraintEnd_toEndOf="@+id/national_id_back_iv"
                app:layout_constraintTop_toTopOf="@+id/national_id_back_iv"
                app:layout_constraintVertical_bias="0.94" />

            <TextView
                android:id="@+id/add_passport_front"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycUploadPhotoOfPassportFront"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/passport_front_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/passport_front_iv" />

            <ImageView
                android:id="@+id/upload_passport_front"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/passport_front_iv"
                app:layout_constraintEnd_toEndOf="@id/passport_front_iv"
                app:layout_constraintStart_toStartOf="@id/passport_front_iv"
                app:layout_constraintTop_toTopOf="@id/passport_front_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/passport_front_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/residence_iv" />

            <ProgressBar
                android:id="@+id/passport_front_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/passport_front_iv"
                app:layout_constraintEnd_toEndOf="@id/passport_front_iv"
                app:layout_constraintStart_toStartOf="@id/passport_front_iv"
                app:layout_constraintTop_toTopOf="@id/passport_front_iv" />

            <ImageButton
                android:id="@+id/passport_front_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/passport_front_iv"
                app:layout_constraintEnd_toEndOf="@+id/passport_front_iv"
                app:layout_constraintTop_toTopOf="@+id/passport_front_iv"
                app:layout_constraintVertical_bias="0.94" />

            <TextView
                android:id="@+id/add_passport_back"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycUploadPhotoOfPassportBack"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/passport_back_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/passport_back_iv" />

            <ImageView
                android:id="@+id/upload_passport_back"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/passport_back_iv"
                app:layout_constraintEnd_toEndOf="@id/passport_back_iv"
                app:layout_constraintStart_toStartOf="@id/passport_back_iv"
                app:layout_constraintTop_toTopOf="@id/passport_back_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/passport_back_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/passport_front_iv" />

            <ProgressBar
                android:id="@+id/passport_back_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/passport_back_iv"
                app:layout_constraintEnd_toEndOf="@id/passport_back_iv"
                app:layout_constraintStart_toStartOf="@id/passport_back_iv"
                app:layout_constraintTop_toTopOf="@id/passport_back_iv" />

            <ImageButton
                android:id="@+id/passport_back_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/passport_back_iv"
                app:layout_constraintEnd_toEndOf="@+id/passport_back_iv"
                app:layout_constraintTop_toTopOf="@+id/passport_back_iv"
                app:layout_constraintVertical_bias="0.94" />

            <TextView
                android:id="@+id/add_residence"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:text="@string/kycUploadProofOfResidence"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_16sp"
                app:layout_constraintBottom_toBottomOf="@id/residence_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/residence_iv" />

            <ImageView
                android:id="@+id/upload_residence"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:contentDescription="@string/axUploadDocumentsNotUploadedValue"
                android:src="@drawable/ic_upload_doc"
                app:layout_constraintBottom_toBottomOf="@id/residence_iv"
                app:layout_constraintEnd_toEndOf="@id/residence_iv"
                app:layout_constraintStart_toStartOf="@id/residence_iv"
                app:layout_constraintTop_toTopOf="@id/residence_iv"
                tools:ignore="ContentDescription" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/residence_iv"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:contentDescription="@string/axUploadDocumentsUploadedValue"
                android:visibility="invisible"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/national_id_back_iv" />

            <ProgressBar
                android:id="@+id/residence_loading"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/residence_iv"
                app:layout_constraintEnd_toEndOf="@id/residence_iv"
                app:layout_constraintStart_toStartOf="@id/residence_iv"
                app:layout_constraintTop_toTopOf="@id/residence_iv" />

            <ImageButton
                android:id="@+id/residence_camera_iv"
                android:layout_width="@dimen/dimen_20dp"
                android:layout_height="@dimen/dimen_20dp"
                android:background="@drawable/ic_upload"
                android:contentDescription="@string/axUploadDocumentsHint"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/residence_iv"
                app:layout_constraintEnd_toEndOf="@+id/residence_iv"
                app:layout_constraintTop_toTopOf="@+id/residence_iv"
                app:layout_constraintVertical_bias="0.94" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.80" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/confirm_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:text="@string/kycUploadPhotoSubmit"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/kyc_document_sv" />
</androidx.constraintlayout.widget.ConstraintLayout>
