<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:animateLayoutChanges="true"
        android:gravity="center_horizontal"
        android:padding="@dimen/dimen_16dp">

        <ImageView
            android:id="@+id/signin_logo_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:src="@drawable/ic_logo_bcn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/setupSessionPinTitle"
            android:textColor="@color/titleTextColor"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/signin_logo_iv" />

        <TextView
            android:id="@+id/error_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:textColor="@color/negativeInfoTextColor"
            android:textSize="@dimen/text_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />


        <com.poovam.pinedittextfield.CirclePinField
            android:id="@+id/password_et"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:cursorVisible="false"
            android:importantForAutofill="no"
            android:inputType="textPassword"
            android:textSelectHandle="@drawable/text_handle"
            app:circleRadius="@dimen/dimen_12dp"
            app:distanceInBetween="@dimen/sessionPinEditTextDistance"
            app:fieldBgColor="@color/colorAccentLight"
            app:fieldColor="@color/colorAccentLight"
            app:fillerColor="@color/colorPrimary"
            app:fillerRadius="13dp"
            app:highlightColor="@color/colorAccentLight"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/error_tv"
            app:noOfFields="8"
            tools:ignore="LabelFor" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/biometric_switch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/setupSessionPinBypassBiometrics"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            android:theme="@style/SwitchTheme"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_et"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
