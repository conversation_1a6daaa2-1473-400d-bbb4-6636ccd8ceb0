<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingVertical="@dimen/dimen_20dp">

    <TextView
        android:id="@+id/account_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="65dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Malawi Office 1" />

    <TextView
        android:id="@+id/account_id_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="65dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_name_tv"
        tools:text="Account ID ****2874" />

    <TextView
        android:id="@+id/default_account_tag_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="65dp"
        android:text="@string/manageAccountsDefaultAccount"
        android:textColor="@color/positiveIndicatorColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/account_id_tv" />

    <ImageView
        android:id="@+id/menu_iv"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="0dp"
        android:src="@drawable/ic_menu_dots"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/account_name_tv"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/activate_tv"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="@string/manageAccountsActivateButton"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
