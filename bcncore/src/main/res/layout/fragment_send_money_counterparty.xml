<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.sendmoney.SendMoneyCounterpartyFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/current_installation_provider_tv"
            style="@style/ListHeaderStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/axSelectDestinationOtherProvidersHint"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_8dp"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_12sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@+id/current_installation_provider_section"
            tools:text="Yafika Mobile" />

        <TextView
            android:id="@+id/to_another_person_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dimen_48dp"
            android:padding="@dimen/dimen_16dp"
            android:text="@string/moneyTransferToAnotherPerson"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            app:drawableEndCompat="@drawable/ic_right_arrow" />

        <TextView
            android:id="@+id/error_agent_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dimen_48dp"
            android:padding="@dimen/dimen_16dp"
            android:text="@string/selectDestinationUserIsAgentLabel"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/to_own_wallet_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_8dp"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center_vertical"
            android:minHeight="@dimen/dimen_48dp"
            android:padding="@dimen/dimen_16dp"
            android:text="@string/moneyTransferToSelf"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            app:drawableEndCompat="@drawable/ic_right_arrow" />

        <TextView
            android:id="@+id/counterparty_section_title"
            style="@style/ListHeaderStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/axSelectDestinationOtherProvidersHint"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_8dp"
            android:text="@string/selectDestinationOtherProviders"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_12sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@+id/current_installation_provider_section" />


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/select_counterparty_error"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="50dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/error_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:gravity="center"
                    android:textSize="20sp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/retry_button"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/button_height"
                    android:background="@drawable/bg_button_4dp_primary_enabled"
                    android:contentDescription="@string/axSelectDestinationTryAgainHint"
                    android:text="@string/alertActionTryAgain"
                    android:textColor="@color/white"
                    android:textStyle="bold" />

            </LinearLayout>

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/counterparty_section" />

            <LinearLayout
                android:id="@+id/no_provider_found_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/counterparty_section_title"
                tools:ignore="UseCompoundDrawables">

                <ImageView
                    android:id="@+id/no_provider_found_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="24dp"
                    android:contentDescription="@string/selectDestinationNoProviders"
                    android:src="@drawable/ic_no_provider_found" />

                <TextView
                    android:id="@+id/no_provider_found_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="12dp"
                    android:text="@string/selectDestinationNoProviders"
                    android:textColor="@color/unavailableAccountTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/counterparty_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/dimen_8dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/counterparty_section_title" />

        </FrameLayout>

    </LinearLayout>

</ScrollView>
