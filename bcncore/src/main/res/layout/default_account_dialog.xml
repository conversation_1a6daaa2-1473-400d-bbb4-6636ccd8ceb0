<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">

    <LinearLayout
        android:id="@+id/default_account_dropdown_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/default_account_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_8dp"
            android:text="@string/manageAccountsDefaultAccount"
            android:textColor="@color/sectionHeadingColor"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.suryadigital.leo.libui.textdropdown.TextDropdown
            android:id="@+id/default_account_dropdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:background="@drawable/bg_spinner_border"
            android:gravity="center_horizontal"
            android:minHeight="35dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/success_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp"
        android:textColor="@color/titleTextColor"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone" />


    <com.resoluttech.core.uicomponents.ButtonWithProgressBar
        android:id="@+id/buttons_progress_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>
