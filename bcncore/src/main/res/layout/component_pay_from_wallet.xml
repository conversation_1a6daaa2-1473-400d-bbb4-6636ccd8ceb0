<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_select_wallet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_40dp"
    android:foreground="?attr/selectableItemBackground"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingVertical="@dimen/dimen_8dp">

    <TextView
        android:id="@+id/pay_from_account_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sendMoneyPayFromWalletLabel"
        android:textColor="@color/descriptionTextDarkColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/account_name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/right_chevron_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pay_from_account_label"
        tools:text="Malawi Personal" />

    <ImageView
        android:id="@+id/right_chevron_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:importantForAccessibility="no"
        android:src="@drawable/ic_right_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
