<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="56dp"
    android:orientation="vertical"
    android:gravity="center_vertical"
    android:paddingStart="0dp"
    android:paddingEnd="@dimen/dimen_16dp"
    android:paddingTop="@dimen/dimen_10dp"
    android:paddingBottom="@dimen/dimen_10dp">

    <TextView
        android:id="@+id/account_name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/titleTextColor"
        android:textSize="15sp"
        android:textStyle="bold"
        android:layout_marginStart="46dp"
        tools:text="Home" />

    <TextView
        android:id="@+id/account_id_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@color/titleTextColor"
        android:textSize="14sp"
        android:layout_marginStart="46dp"
        android:visibility="gone"
        tools:text="Account id : 1235737gsufi12785" />
</LinearLayout>
