<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:foreground="?attr/selectableItemBackground"
    android:gravity="center_horizontal|top"
    android:orientation="vertical"
    android:padding="@dimen/dimen_16dp"
    tools:ignore="UseCompoundDrawables">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/biller_icon_iv"
        android:layout_width="@dimen/dimen_48dp"
        android:layout_height="@dimen/dimen_48dp"
        android:importantForAccessibility="no"
        android:minWidth="@dimen/dimen_48dp"
        android:minHeight="@dimen/dimen_48dp"
        app:shapeAppearanceOverlay="@style/roundedImageViewRounded"
        tools:src="@drawable/ic_person_black_24dp" />

    <TextView
        android:id="@+id/biller_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        tools:text="Airtel Topup" />

</LinearLayout>
