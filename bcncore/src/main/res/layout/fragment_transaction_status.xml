<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/root_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:context="com.resoluttech.core.views.TransactionStatusFragment">

                <LinearLayout
                    android:id="@+id/top_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:padding="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:background="@color/successful_transaction_theme_color">

                    <ImageView
                        android:id="@+id/transaction_state_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/transactionStatusSuccessLabel"
                        android:importantForAccessibility="no"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:background="@drawable/ic_success_tick" />

                    <TextView
                        android:id="@+id/title_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_12dp"
                        android:gravity="center"
                        android:maxLines="2"
                        android:text="@string/transactionStatusSuccessLabel"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/text_18sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/transaction_state_iv" />

                    <TextView
                        android:id="@+id/subtitle_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_4dp"
                        android:gravity="center"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/text_14sp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/title_tv"
                        tools:text="Sorry we are unable to process your request due to insufficient amount. "
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/date_time_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_4dp"
                        android:gravity="center"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/text_14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/subtitle_tv"
                        tools:text="12:20 PM on Jan 07 2020" />

                </LinearLayout>

                <TextView
                    android:id="@+id/label_transaction_details"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:text="@string/transactionStatusScreenDetailsLabel"
                    android:textColor="@color/descriptionTextColor"
                    android:textSize="@dimen/text_12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/top_container" />

                <TextView
                    android:id="@+id/amount_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lines="1"
                    android:padding="@dimen/dimen_16dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_28sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/label_transaction_details"
                    tools:text="MWK 10,000" />

                <TextView
                    android:id="@+id/tranaaction_id_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:importantForAccessibility="no"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:text="@string/transactionStatusScreenIdLabel"
                    android:textColor="@color/descriptionTextDarkColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/amount_tv" />

                <TextView
                    android:id="@+id/transaction_id_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingBottom="@dimen/dimen_16dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tranaaction_id_label"
                    app:layout_constraintTop_toBottomOf="@+id/tranaaction_id_label"
                    tools:text="b16bedc5-addd-48d6-8b4f-224a6f05fcc4" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transaction_id_tv">

                    <TextView
                        android:id="@+id/transaction_description_tv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dimen_8dp"
                        android:paddingHorizontal="@dimen/dimen_8dp"
                        android:paddingBottom="@dimen/dimen_16dp"
                        android:textColor="@color/descriptionTextDarkColor"
                        android:textSize="@dimen/text_14sp"
                        android:visibility="gone"
                        tools:text="You were charged MWK 2 for this transaction."
                        tools:visibility="visible" />

                    <LinearLayout
                        android:id="@+id/details_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical" />

                    <TextView
                        android:id="@+id/label_need_help"
                        style="@style/ListHeaderStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dimen_16dp"
                        android:paddingVertical="@dimen/dimen_8dp"
                        android:text="@string/transactionStatusNeedHelpLabel"
                        android:textColor="@color/descriptionTextColor"
                        android:textSize="@dimen/text_12sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/details_container" />

                    <TextView
                        android:id="@+id/support_button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="@dimen/dimen_8dp"
                        android:foreground="?attr/selectableItemBackground"
                        android:gravity="center_vertical"
                        android:padding="@dimen/dimen_16dp"
                        android:text="@string/transactionStatusContactSupportButtonTitle"
                        android:textColor="@color/colorPrimaryLight"
                        android:textSize="@dimen/text_14sp"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:drawableStartCompat="@drawable/ic_circle_question_mark"
                        app:icon="@drawable/ic_help_circle"
                        app:iconTint="@color/titleTextColor"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/desc_support"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dimen_16dp"
                        android:textSize="@dimen/text_12sp"
                        android:text="@string/transactionStatusContactSupportLabel"
                        android:textColor="@color/descriptionTextDarkColor" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible" />


    </FrameLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/done_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_gravity="bottom"
        android:layout_margin="@dimen/dimen_16dp"
        android:background="@drawable/bg_button_4dp_primary_enabled"
        android:contentDescription="@string/axTransactionStatusDoneButtonHint"
        android:text="@string/transactionStatusDoneButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/content_section" />
</LinearLayout>
