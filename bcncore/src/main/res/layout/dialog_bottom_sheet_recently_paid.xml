<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recently_paid_bottom_sheet"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_background"
    android:orientation="vertical"
    app:behavior_hideable="false"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <EditText
        android:id="@+id/enter_manually"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="@dimen/dimen_12dp"
        android:backgroundTint="@color/subtitleTextColorSecondary"
        android:drawableEnd="@drawable/ic_select_contact"
        android:focusable="false"
        android:hint="@string/qrCodeScannerEnterNameMobileNo"
        android:importantForAutofill="no"
        android:inputType="text"
        android:paddingHorizontal="@dimen/dimen_8dp"
        android:paddingBottom="@dimen/editTextBottomPadding"
        android:textColorHint="@color/subtitleTextColorSecondary"
        android:textSize="@dimen/text_16sp" />

    <TextView
        android:id="@+id/recent_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:contentDescription="@string/axRecentsHint"
        android:text="@string/qrCodeScannerRecents"
        android:textColor="@color/titleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        android:visibility="gone"
        tools:visibility="visible" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dimen_16dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/recent_user_grid_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:visibility="gone"
            tools:visibility="visible">

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/recent_transfer1_contact_icon"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginStart="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:cardBackgroundColor="@color/descriptionTextColor"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="@dimen/zero_dimen"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/recent_transfer1_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:ems="4"
                android:gravity="center"
                android:maxWidth="100dp"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_14sp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@+id/recent_transfer1_contact_icon"
                app:layout_constraintStart_toStartOf="@+id/recent_transfer1_contact_icon"
                app:layout_constraintTop_toBottomOf="@id/recent_transfer1_contact_icon"
                tools:text="Rachel Zen"
                tools:visibility="visible" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/recent_transfer2_contact_icon"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:cardBackgroundColor="@color/descriptionTextColor"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="@dimen/zero_dimen"
                app:layout_constraintEnd_toStartOf="@+id/recent_transfer3_contact_icon"
                app:layout_constraintStart_toEndOf="@id/recent_transfer1_contact_icon"
                app:layout_constraintTop_toTopOf="@id/recent_transfer1_contact_icon"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/recent_transfer2_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:ems="4"
                android:gravity="center"
                android:maxWidth="100dp"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_14sp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@+id/recent_transfer2_contact_icon"
                app:layout_constraintStart_toStartOf="@+id/recent_transfer2_contact_icon"
                app:layout_constraintTop_toBottomOf="@id/recent_transfer2_contact_icon"
                tools:text="Rachel Zen"
                tools:visibility="visible" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/recent_transfer3_contact_icon"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:cardBackgroundColor="@color/descriptionTextColor"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="@dimen/zero_dimen"
                app:layout_constraintEnd_toStartOf="@+id/recent_transfer4_contact_icon"
                app:layout_constraintStart_toEndOf="@id/recent_transfer2_contact_icon"
                app:layout_constraintTop_toTopOf="@id/recent_transfer2_contact_icon"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/recent_transfer3_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:ems="4"
                android:gravity="center"
                android:maxWidth="100dp"
                android:paddingStart="@dimen/dimen_10dp"
                android:paddingEnd="@dimen/dimen_10dp"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_14sp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@+id/recent_transfer3_contact_icon"
                app:layout_constraintStart_toStartOf="@+id/recent_transfer3_contact_icon"
                app:layout_constraintTop_toBottomOf="@id/recent_transfer3_contact_icon"
                tools:text="Rachel Zen"
                tools:visibility="visible" />

            <com.suryadigital.leo.libui.contactview.ContactIconView
                android:id="@+id/recent_transfer4_contact_icon"
                android:layout_width="@dimen/dimen_48dp"
                android:layout_height="@dimen/dimen_48dp"
                android:layout_marginEnd="@dimen/dimen_16dp"
                android:importantForAccessibility="no"
                android:visibility="invisible"
                app:cardBackgroundColor="@color/descriptionTextColor"
                app:cardCornerRadius="@dimen/dimen_24dp"
                app:cardElevation="@dimen/zero_dimen"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/recent_transfer3_contact_icon"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/recent_transfer4_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:ems="4"
                android:gravity="center"
                android:maxWidth="100dp"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_14sp"
                android:visibility="invisible"
                app:layout_constraintEnd_toEndOf="@+id/recent_transfer4_contact_icon"
                app:layout_constraintStart_toStartOf="@+id/recent_transfer4_contact_icon"
                app:layout_constraintTop_toBottomOf="@id/recent_transfer4_contact_icon"
                tools:text="Rachel Zen"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ProgressBar
            android:id="@+id/recentUserProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminateTint="@color/colorPrimary" />
    </FrameLayout>

</LinearLayout>
