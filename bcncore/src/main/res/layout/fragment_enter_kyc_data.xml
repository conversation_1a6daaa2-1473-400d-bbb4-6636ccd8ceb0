<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:id="@+id/enter_kyc_sv"
        style="style/RootLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/next_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/content_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/dimen_16dp"
                android:visibility="invisible"
                tools:visibility="visible">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/add_profile_cl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingTop="@dimen/dimen_8dp"
                    android:paddingBottom="@dimen/dimen_24dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/add_profile"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/kycProfilePhoto"
                        android:textColor="@color/subtitleTextColor"
                        android:textSize="@dimen/text_16sp"
                        app:layout_constraintBottom_toBottomOf="@id/profile_iv"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/profile_iv" />

                    <ImageView
                        android:id="@+id/upload_profile_image"
                        android:layout_width="@dimen/dimen_48dp"
                        android:layout_height="@dimen/dimen_48dp"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_upload_doc"
                        app:layout_constraintBottom_toBottomOf="@id/profile_iv"
                        app:layout_constraintEnd_toEndOf="@+id/profile_iv"
                        app:layout_constraintStart_toStartOf="@id/profile_iv"
                        app:layout_constraintTop_toTopOf="@id/profile_iv" />

                    <com.suryadigital.leo.libui.contactview.ContactIconView
                        android:id="@+id/profile_iv"
                        android:layout_width="@dimen/dimen_48dp"
                        android:layout_height="@dimen/dimen_48dp"
                        android:contentDescription="@string/emptyString"
                        android:visibility="invisible"
                        app:cardCornerRadius="@dimen/dimen_24dp"
                        app:cardElevation="0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageButton
                        android:id="@+id/profile_camera_iv"
                        android:layout_width="@dimen/dimen_20dp"
                        android:layout_height="@dimen/dimen_20dp"
                        android:background="@drawable/ic_upload"
                        android:contentDescription="@string/profileAlertAddProfilePhoto"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="@+id/profile_iv"
                        app:layout_constraintEnd_toEndOf="@+id/profile_iv"
                        app:layout_constraintTop_toTopOf="@+id/profile_iv"
                        app:layout_constraintVertical_bias="0.94" />

                    <ProgressBar
                        android:id="@+id/profile_loading"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/profile_iv"
                        app:layout_constraintEnd_toEndOf="@+id/profile_iv"
                        app:layout_constraintStart_toStartOf="@id/profile_iv"
                        app:layout_constraintTop_toTopOf="@id/profile_iv" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/basic_details_heading"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:text="@string/kycBasicDetailsLabel"
                    android:textColor="@color/descriptionTextColor"
                    android:textSize="@dimen/text_12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/add_profile_cl" />

                <LinearLayout
                    android:id="@+id/first_name_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/basic_details_heading">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_profile" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/first_name_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycFirstNamePlaceholder"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/first_name_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycFirstNamePlaceholder"
                            tools:text="Katrina" />

                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/last_name_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/first_name_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_profile" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/last_name_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycLastNamePlaceholder"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/last_name_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycLastNamePlaceholder"
                            tools:text="Venus" />

                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/date_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/other_names_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_calendar" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/date_of_birth_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycDateOfBirthTitle"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <EditText
                            android:id="@+id/date_of_birth_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:importantForAutofill="no"
                            android:inputType="date"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycDateOfBirthPlaceholder"
                            tools:ignore="LabelFor"
                            tools:text="20-Dec-1996" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/gender_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/date_container">

                    <ImageView
                        android:id="@+id/gender_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:paddingTop="@dimen/dimen_8dp"
                        android:src="@drawable/ic_gender" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/gender_dropdown_hint_tv"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dimen_4dp"
                            android:text="@string/kycGenderPlaceholder"
                            android:textAppearance="?attr/textAppearanceCaption"
                            android:textColor="@color/editTextHintColor"
                            android:textSize="@dimen/text_12sp"
                            android:visibility="invisible" />

                        <com.suryadigital.leo.libui.textdropdown.TextDropdown
                            android:id="@+id/gender_dropdown"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_spinner_grey_border"
                            android:dropDownWidth="match_parent"
                            android:minHeight="@dimen/dimen_40dp"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:paddingBottom="@dimen/dimen_12dp"
                            android:popupBackground="@drawable/bg_popup_menu"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            android:theme="@style/TextDropDownStyle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/gender_label" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/nrb_number_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/gender_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_id_card" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/nrb_number_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycNationalIdNoTitle"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/nrb_number_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycNationalIdNoPlaceholder"
                            tools:text="1728492746829281" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/other_names_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/last_name_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_profile" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/other_name_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycOtherNamePlaceholder"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/other_name_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycOtherNamePlaceholder"
                            tools:text="Katrina Robert" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/email_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/nrb_number_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_mail" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/email_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycEmailIdPlaceholder"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/email_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress"
                            android:maxLength="200"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycEmailIdPlaceholder" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/place_of_birth_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/email_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_map_marker" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/place_of_birth_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycPlaceOfBirthTitle"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/place_of_birth_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycPlaceOfBirthPlaceholderText" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/national_id_issue_date_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/place_of_birth_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_calendar" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/national_id_issue_date_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycNationalIdIssuedDateTitle"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/national_id_issue_date_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:importantForAutofill="no"
                            android:inputType="date"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycNationalIdIssuedDateTitle"
                            tools:ignore="LabelFor"
                            tools:text="20-Dec-1996" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/national_id_expiry_date_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/national_id_issue_date_container">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_calendar" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/national_id_expiry_date_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycNationalIdExpiryDateTitle"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/national_id_expiry_date_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:importantForAutofill="no"
                            android:inputType="date"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycNationalIdExpiryDateTitle"
                            tools:ignore="LabelFor"
                            tools:text="20-Dec-1996" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/resident_address_heading"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:text="@string/kycOccupationResidentAddress"
                    android:textColor="@color/descriptionTextColor"
                    android:textSize="@dimen/text_12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/national_id_expiry_date_container" />

                <LinearLayout
                    android:id="@+id/resident_address_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/resident_address_heading">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_map_marker" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/resident_address_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycOccupationResidentAddress"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/resident_address_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycOccupationResidentAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/postal_address_heading"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dimen_16dp"
                    android:paddingVertical="@dimen/dimen_8dp"
                    android:text="@string/kycOccupationPostalAddress"
                    android:textColor="@color/descriptionTextColor"
                    android:textSize="@dimen/text_12sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/resident_address_container" />

                <LinearLayout
                    android:id="@+id/postal_address_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dimen_16dp"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/dimen_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/postal_address_heading">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dimen_16dp"
                        android:background="@color/transparent"
                        android:contentDescription="@string/emptyString"
                        android:src="@drawable/ic_map_marker" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/postal_address_til"
                        style="@style/TextInputLayoutStyle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/kycOccupationPostalAddress"
                        android:theme="@style/TextInputEditTextCursorTheme">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/postal_address_et"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textCapWords"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dimen_4dp"
                            android:textColor="@color/subtitleTextColor"
                            android:textSize="@dimen/text_16sp"
                            tools:hint="@string/kycOccupationPostalAddress" />

                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ProgressBar
                android:id="@+id/loading_progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:indeterminateTint="@color/colorPrimaryDark"
                android:visibility="gone" />
        </FrameLayout>
    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/next_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:contentDescription="@string/axKycNextButtonDisabledHint"
        android:text="@string/kycNextButtonTitle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/enter_kyc_sv"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
