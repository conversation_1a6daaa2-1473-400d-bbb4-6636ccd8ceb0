<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dimen_16dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/reset_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_8dp"
                android:gravity="center"
                android:text="@string/setPasswordSubtitle"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <LinearLayout
                android:id="@+id/enter_password_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:src="@drawable/ic_password_lock"
                    tools:ignore="ContentDescription" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/enter_password_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/passwordToggleColor">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/enter_password_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/setPasswordEnterPassword"
                        android:inputType="textPassword"
                        android:minHeight="@dimen/dimen_48dp"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textSize="@dimen/text_16sp" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/confirm_password_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:src="@drawable/ic_password_lock"
                    tools:ignore="ContentDescription" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/confirm_password_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/passwordToggleColor">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/confirm_password_tiet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/setPasswordConfirmPassword"
                        android:inputType="textPassword"
                        android:minHeight="@dimen/dimen_48dp"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textSize="@dimen/text_16sp" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/privacy_policy_heading_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_40dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/password_policy_rv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/reset_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:text="@string/setPasswordButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
