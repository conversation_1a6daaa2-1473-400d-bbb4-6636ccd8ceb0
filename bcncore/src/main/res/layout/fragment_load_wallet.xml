<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <WebView
        android:id="@+id/payment_page_web_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:layout_marginTop="-6dp"
        android:layout_marginBottom="-8dp"
        android:indeterminate="true"
        android:visibility="gone"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/data_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/dimen_24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toTopOf="@id/proceed_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/amount_text_field"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/amount_hint_text"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/loadWalletEnterAmount"
                    android:theme="@style/TextInputEditTextCursorTheme">

                    <com.resoluttech.core.views.AmountEditText
                        android:id="@+id/amount_edit_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionDone"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textColor="@color/subtitleTextColor"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/currency_suffix"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|bottom"
                    android:layout_marginEnd="@dimen/dimen_8dp"
                    android:paddingBottom="@dimen/editTextBottomPadding"
                    android:textColor="@color/subtitleTextColorSecondary"
                    android:textSize="@dimen/text_14sp"
                    tools:text="MWK" />
            </FrameLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/private_remark_til"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:hint="@string/loadWalletNarrationToSelfPlaceholder"
                android:minHeight="@dimen/dimen_48dp"
                android:theme="@style/TextInputEditTextCursorTheme"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/amount_text_field">

                <com.resoluttech.core.views.LimitedCharacterEditText
                    android:id="@+id/private_remark_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:maxLines="5"
                    android:paddingHorizontal="@dimen/dimen_4dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <include
                layout="@layout/component_pay_from_wallet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:contentDescription="@string/axLoadWalletSelectWalletLabel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/private_remark_til" />

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/proceed_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="bottom"
            android:layout_margin="@dimen/dimen_16dp"
            android:text="@string/sendMoneyAttemptPayment"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
