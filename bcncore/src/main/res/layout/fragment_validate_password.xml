<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/desc_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:gravity="center"
        android:text="@string/closeMyAccountPasswordSubtitle"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/enter_password_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_40dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/desc_tv">

        <ImageButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dimen_20dp"
            android:background="@color/transparent"
            android:src="@drawable/ic_password_lock"
            tools:ignore="ContentDescription" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/enter_password_til"
            style="@style/TextInputLayoutStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/TextInputEditTextCursorTheme"
            app:passwordToggleDrawable="@drawable/show_password_selector"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="@color/passwordToggleColor">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/enter_password_tiet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/enterPasswordTextFieldPlaceholder"
                android:inputType="textPassword"
                android:paddingHorizontal="@dimen/dimen_4dp"
                android:textSize="@dimen/text_16sp" />
        </com.google.android.material.textfield.TextInputLayout>
    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/close_account_pb"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:text="@string/closeMyAccountPasswordButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
