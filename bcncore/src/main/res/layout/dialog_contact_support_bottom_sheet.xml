<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_background"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginVertical="@dimen/dimen_20dp"
        android:text="@string/transactionStatusNeedHelpLabel"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/call_us"
        style="@style/ContactSupportItemRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp"
        android:drawablePadding="@dimen/dimen_16dp"
        android:foreground="?attr/selectableItemBackground"
        android:text="@string/contactSupportCallUsLabel"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_phone_number" />

    <TextView
        android:id="@+id/send_email"
        style="@style/ContactSupportItemRow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:drawablePadding="@dimen/dimen_16dp"
        android:foreground="?attr/selectableItemBackground"
        android:padding="@dimen/dimen_16dp"
        android:text="@string/contactSupportSendAnEmailLabel"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        app:drawableStartCompat="@drawable/ic_mail_dark" />

</LinearLayout>
