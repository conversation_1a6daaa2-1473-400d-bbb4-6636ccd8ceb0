<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipe_to_refresh"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/accounts_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:clipToPadding="false"
            android:paddingBottom="60dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:progressBackgroundTint="@color/colorPrimary" />

        <TextView
            android:id="@+id/no_inactive_account_found"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="@dimen/dimen_16dp"
            android:padding="@dimen/dimen_16dp"
            android:text="@string/manageAccountsNoInactiveAccount"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp"
            app:drawableTopCompat="@drawable/ic_empty_state" />

    </FrameLayout>


</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
