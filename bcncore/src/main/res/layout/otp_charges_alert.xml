<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/alertTitleTransactionDetails"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginVertical="@dimen/dimen_12dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/entered_amount_label_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailAmount"
            android:textColor="@color/dialogSubtitleTextColor"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/entered_amount_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="10000.00" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/transaction_charges_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailTransactionFee"
            android:textColor="@color/dialogSubtitleTextColor"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/otpChangesAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="2.00" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/receiving_amount_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailReceiveAmount"
            android:textColor="@color/dialogSubtitleTextColor"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/receivingAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="288.00" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailAccount"
            android:textColor="@color/dialogSubtitleTextColor"
            android:textSize="@dimen/text_14sp" />

        <TextView
            android:id="@+id/pay_from_account"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:textSize="@dimen/text_14sp"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textStyle="bold"
            tools:text="Personal" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginVertical="@dimen/dimen_12dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/payment_amount_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:importantForAccessibility="no"
            android:text="@string/alertTransactionDetailPayableAmount"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/total_amount_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="10,002.00" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:id="@+id/information_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="2dp"
            android:src="@drawable/ic_information"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/information_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_4dp"
            android:textColor="@color/dialogAlertActionColor"
            android:textStyle="bold"
            android:textSize="@dimen/text_14sp"
            tools:text="@string/alertTransactionFeeChargesInfo" />
    </LinearLayout>

    <TextView
        android:id="@+id/terms_and_condition_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:contentDescription="@string/axTransactionDetailsTermsAndConditionsHint"
        android:focusable="false"
        android:text="@string/alertTransactionTermsAndConditions"
        android:textColor="@color/colorPrimaryLight"
        android:textSize="@dimen/text_14sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/decline"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionDecline"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/proceed"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/alertActionProceed"/>

    </LinearLayout>

</LinearLayout>
