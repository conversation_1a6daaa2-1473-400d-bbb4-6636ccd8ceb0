<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <ImageButton
        android:id="@+id/arrow_back_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:contentDescription="@string/axBackButtonLabel"
        android:padding="@dimen/dimen_16dp"
        android:src="@drawable/ic_arrow_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:text="@string/qrCodeScannerViewTitle"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/arrow_back_icon"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/pick_qr_code_from_gallery"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_10dp"
        android:background="@android:color/transparent"
        android:contentDescription="@string/qrCodeScannerUploadFromGallery"
        android:padding="@dimen/dimen_8dp"
        android:src="@drawable/ic_pick_from_gallery"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/flash_icon"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@id/title"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageButton
        android:id="@+id/flash_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_10dp"
        android:background="@android:color/transparent"
        android:contentDescription="@string/axQRCodeCameraFlash"
        android:padding="@dimen/dimen_8dp"
        android:src="@drawable/ic_flashlight_off"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@id/title"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
