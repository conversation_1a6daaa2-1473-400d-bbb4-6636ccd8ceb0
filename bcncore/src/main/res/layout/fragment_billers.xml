<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.payments.BillersFragment">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_to_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/biller_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dimen_24dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:padding="@dimen/dimen_8dp"
        android:visibility="visible" />

    <LinearLayout
        android:id="@+id/error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/error_icon"
            android:layout_width="@dimen/dimen_120dp"
            android:layout_height="@dimen/dimen_120dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:src="@drawable/ic_no_internet"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/fullScreenErrorTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/fullScreenErrorMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/alertMessageNoInternet"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/retry_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dimen_16dp"
            android:text="@string/alertActionTryAgain" />

    </LinearLayout>

</FrameLayout>
