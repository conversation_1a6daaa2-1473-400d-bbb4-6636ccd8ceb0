<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="35dp"
    android:paddingStart="21dp"
    android:paddingEnd="@dimen/dimen_16dp"
    android:paddingTop="@dimen/dimen_10dp"
    android:paddingBottom="@dimen/dimen_10dp"
    style="@style/ListHeaderStyle"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/flag_iv"
        android:layout_width="24dp"
        android:layout_height="20dp"
        tools:src="@drawable/flag_malawi"
        tools:ignore="ContentDescription"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/heading_tv"
        app:layout_constraintBottom_toBottomOf="@id/heading_tv"/>

    <TextView
        android:id="@+id/heading_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center"
        android:textColor="@color/buttonLabelColor"
        android:textSize="13sp"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/dimen_10dp"
        tools:text="Malawi"
        app:layout_constraintStart_toEndOf="@id/flag_iv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
