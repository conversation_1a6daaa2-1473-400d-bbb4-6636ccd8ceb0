<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.auth.trustedcontacts.SetupTrustedContactsFragment">

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:gravity="center"
        android:text="@string/showTrustedContactsMessage"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/data_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        app:layout_constraintBottom_toTopOf="@id/add_contact_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/description">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:progressTint="@color/colorPrimary" />

        <TextView
            android:id="@+id/no_trusted_contact_found_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="12dp"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:text="@string/noTrustedContactsAddedMessage"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp"
            app:drawableTopCompat="@drawable/ic_no_trusted_contact" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/trusted_contacts_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </FrameLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/add_contact_button"
        style="@style/PrimaryButtonOutline"
        android:layout_width="0dp"
        android:layout_height="@dimen/outline_button_height"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_10dp"
        android:layout_weight="1"
        android:text="@string/addButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/next_button"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/next_button"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:layout_weight="1"
        android:text="@string/nextButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/add_contact_button" />

</androidx.constraintlayout.widget.ConstraintLayout>
