<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/breakdown_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dimen_16dp"
        android:paddingVertical="@dimen/dimen_24dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/decline_button"
            style="@style/PrimaryButtonOutline"
            android:layout_width="0dp"
            android:layout_height="@dimen/outline_button_height"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:layout_weight="1"
            android:text="@string/alertActionDecline" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/proceed_button"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:layout_weight="1"
            android:text="@string/alertActionProceed" />

    </LinearLayout>

</LinearLayout>
