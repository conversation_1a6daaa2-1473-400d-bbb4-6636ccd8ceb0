<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:padding="@dimen/dimen_16dp">

    <ImageView
        android:id="@+id/transaction_type_iv"
        android:layout_width="@dimen/dimen_40dp"
        android:layout_height="@dimen/dimen_40dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription"
        tools:srcCompat="@drawable/ic_notification" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_4dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/timestamp_tv"
        app:layout_constraintStart_toEndOf="@+id/transaction_type_iv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="OTP Notification" />

    <TextView
        android:id="@+id/message_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/next_arrow_iv"
        app:layout_constraintStart_toEndOf="@+id/transaction_type_iv"
        app:layout_constraintTop_toBottomOf="@+id/title_tv"
        tools:text="The OTP is 3654 for CASH_IN request of Amount: MWK 300. The OTP is valid for 90 seconds." />

    <TextView
        android:id="@+id/timestamp_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_2dp"
        android:textColor="@color/subtitleTextColorSecondary"
        android:textSize="@dimen/text_12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/title_tv"
        app:layout_constraintTop_toTopOf="@id/title_tv"
        tools:text="3/9/21" />

    <ImageView
        android:id="@+id/next_arrow_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="1dp"
        app:layout_constraintBottom_toBottomOf="@id/message_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/message_tv"
        app:srcCompat="@drawable/ic_right_arrow"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
