<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dropdown_item_text_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:ellipsize="end"
    android:lineSpacingExtra="6sp"
    android:lines="1"
    android:paddingStart="@dimen/dimen_8dp"
    android:paddingTop="@dimen/dimen_12dp"
    android:paddingBottom="@dimen/dimen_12dp"
    android:textColor="@color/descriptionTextColor"
    android:textSize="@dimen/text_16sp"
    android:textStyle="normal"
    tools:text="Plan" />
