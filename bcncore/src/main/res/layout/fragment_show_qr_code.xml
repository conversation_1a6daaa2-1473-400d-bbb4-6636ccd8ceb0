<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.resoluttech.core.profile.ShowQRCodeFragment">

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/windowBackground"
        android:elevation="@dimen/dimen_16dp"
        app:tabBackground="@color/colorPrimary"
        app:tabIndicatorColor="@color/white"
        app:tabIndicatorHeight="@dimen/dimen_2dp"
        app:tabSelectedTextColor="@color/white"
        app:tabTextAppearance="@style/TabLayout"
        app:tabTextColor="@color/inactiveTabColor" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>
