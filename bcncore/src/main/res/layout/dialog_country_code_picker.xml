<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/dialogBg"
    android:orientation="vertical"
    android:paddingTop="@dimen/dimen_24dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:text="@string/contactsPickerCountryCodePickerTitle"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp" />

    <com.hbb20.CountryCodePicker
        android:id="@+id/country_code_picker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_10dp"
        app:ccpDialog_allowSearch="false"
        app:ccpDialog_background="@drawable/bg_4dp"
        app:ccpDialog_showTitle="false"
        app:ccpDialog_textColor="@color/subtitleTextColor"
        app:ccp_arrowColor="@color/subtitleTextColor"
        app:ccp_contentColor="@color/subtitleTextColor"
        app:ccp_textSize="@dimen/text_16sp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/ok_button"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:minWidth="@dimen/dimen_36dp"
        android:text="@string/alertActionConfirm"
        android:textColor="@color/colorPrimaryLight"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold" />

</LinearLayout>
