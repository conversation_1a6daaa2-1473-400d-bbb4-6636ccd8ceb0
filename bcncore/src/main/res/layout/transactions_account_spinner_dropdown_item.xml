<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/account_holder_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/spinnerItemTextColor"
        android:textSize="16sp"
        android:textStyle="bold"
        android:padding="@dimen/dimen_8dp"
        tools:text="<PERSON>" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/spinnerItemTextColor"
        android:alpha="0.5"/>
</LinearLayout>
