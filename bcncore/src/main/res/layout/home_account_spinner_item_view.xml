<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/dropdown_item_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_4dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:lineSpacingExtra="6sp"
        android:lines="1"
        android:textColor="@color/spinnerItemTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        tools:text="Delivered" />

    <ImageButton
        android:id="@+id/dropdown_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:contentDescription="@string/emptyString"
        android:src="@drawable/ic_arrow_home_account_drop_down" />

</LinearLayout>
