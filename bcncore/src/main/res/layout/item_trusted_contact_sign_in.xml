<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/item_trusted_contact"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?android:attr/selectableItemBackground"
    android:paddingHorizontal="@dimen/dimen_16dp"
    android:paddingVertical="@dimen/dimen_16dp">

    <TextView
        android:id="@+id/serial_number_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_circle"
        android:gravity="center"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1" />

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:gravity="center"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/phone_number_tv"
        app:layout_constraintStart_toEndOf="@id/serial_number_tv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Wade Waren" />

    <TextView
        android:id="@+id/phone_number_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:gravity="center"
        android:textColor="@color/subtitleTextColorSecondary"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/serial_number_tv"
        app:layout_constraintTop_toBottomOf="@id/name_tv"
        tools:text="+265 135654560" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_chevron_right_grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

</androidx.constraintlayout.widget.ConstraintLayout>
