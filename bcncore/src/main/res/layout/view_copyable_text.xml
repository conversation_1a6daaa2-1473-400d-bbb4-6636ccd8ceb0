<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/section_label_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/accountToAccountFromAccount"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toTopOf="@id/section_title_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/copy"
            android:layout_width="@dimen/dimen_16dp"
            android:layout_height="@dimen/dimen_16dp"
            android:layout_marginStart="@dimen/dimen_10dp"
            android:src="@drawable/ic_content_copy"
            app:layout_constraintBottom_toBottomOf="@+id/section_title_tv"
            app:layout_constraintStart_toEndOf="@+id/section_title_tv"
            app:layout_constraintTop_toTopOf="@+id/section_title_tv"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/section_title_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:layout_toEndOf="@id/copy"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/section_label_tv"
            tools:text="Personal" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
