<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp">

        <ImageView
            android:id="@+id/app_icon_iv"
            android:layout_width="@dimen/dimen_64dp"
            android:layout_height="@dimen/dimen_64dp"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:src="@drawable/ic_logo_bcn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/sign_up_requirements"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/requiredDocumentsTitle"
            android:textColor="@color/titleTextColor"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_icon_iv" />

        <TextView
            android:id="@id/description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:text="@string/requiredDocumentsSubtitile"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/sign_up_requirements" />

        <TextView
            android:id="@+id/mandatory_documents"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/mandatoryDocumentsTitle"
            android:textColor="@color/titleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/description" />

        <TextView
            android:id="@+id/mandatory_documents_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:paddingStart="@dimen/dimen_8dp"
            android:text="@string/mandatoryDocumentsSubtitle"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mandatory_documents" />

        <TextView
            android:id="@id/trusted_contacts_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/requiredDocumentsTrustedContactsTitle"
            android:textColor="@color/titleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mandatory_documents_desc" />

        <TextView
            android:id="@+id/trusted_contacts_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:text="@string/requiredDocumentsTrustedContactsSubtitle"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/trusted_contacts_tv" />

        <TextView
            android:id="@+id/sign_up_later_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginVertical="@dimen/dimen_16dp"
            android:text="@string/requiredDocumentsSignUpLaterSubtitle"
            android:textColor="@color/titleDescriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toTopOf="@id/sign_up_later_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/trusted_contacts_desc"
            app:layout_constraintVertical_bias="1.0" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/sign_up_later_button"
            style="@style/PrimaryButtonOutline"
            android:layout_width="0dp"
            android:layout_height="@dimen/outline_button_height"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_10dp"
            android:text="@string/requiredDocumentsSignUpLaterTitle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/done_button"
            app:layout_constraintStart_toStartOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/done_button"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:text="@string/continue_btn"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sign_up_later_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
