<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.resoluttech.core.auth.signin.SignUpSecurityQuestionsFragment">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/margin_24dp"
        android:gravity="center"
        android:text="@string/forgotPasswordAnswerSecuritySubtitle"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/security_question_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_weight="1"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_security_question_answer" />

    <TextView
        android:id="@+id/trusted_contacts_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:foreground="?attr/selectableItemBackground"
        android:textColor="@color/buttonLabelColor"
        android:textSize="@dimen/text_14sp"
        tools:text="Can’t answer the questions? Ask your trusted contacts for help. Choose trusted contact" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/nextButton"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:text="@string/forgotPasswordAnswerSecuritySubmitButton" />

</LinearLayout>
