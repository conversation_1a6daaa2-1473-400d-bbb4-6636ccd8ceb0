<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dimen_24dp"
        android:paddingBottom="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/guideline_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="QR Code For:" />

        <include
            layout="@layout/component_pay_from_wallet"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@+id/guideline_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="60dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@id/qr_code_image"
            app:layout_constraintEnd_toEndOf="@id/qr_code_image"
            app:layout_constraintStart_toStartOf="@id/qr_code_image"
            app:layout_constraintTop_toTopOf="@id/qr_code_image" />

        <ImageView
            android:id="@+id/qr_code_image"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:scaleType="centerCrop"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/guideline_top"
            tools:src="@drawable/ic_person_black_24dp"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:gravity="center"
            android:text="@string/showQRCodeUserMessageText"
            android:textColor="@color/descriptionTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/qr_code_image" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/get_link_button"
            style="@style/PrimaryButtonOutline"
            android:layout_width="0dp"
            android:layout_height="@dimen/outline_button_height"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="18dp"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:contentDescription="@string/axShowQrCodeGetLinkHint"
            android:enabled="false"
            android:text="@string/showQRCodeGetLinkLabelText"
            app:layout_constraintEnd_toStartOf="@id/share_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/message" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/share_button"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:contentDescription="@string/axShowQrCodeShareQrCodeHint"
            android:enabled="false"
            android:text="@string/showQRCodeShareCodeLabelText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/get_link_button"
            app:layout_constraintTop_toBottomOf="@id/message" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
