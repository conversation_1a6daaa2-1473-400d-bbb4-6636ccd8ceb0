<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/section_label_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            tools:text="Narration to self" />

        <TextView
            android:id="@+id/section_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            tools:text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non cursus mi. Cras sit amet feugiat risus, vitae semper lectus. Vestibulum finibus efficitur tempor." />

    </LinearLayout>
</merge>
