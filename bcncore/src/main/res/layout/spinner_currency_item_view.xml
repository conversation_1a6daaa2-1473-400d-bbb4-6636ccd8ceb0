<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="@dimen/dimen_8dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/currency_country_flag_iv"
        android:layout_width="24dp"
        android:layout_height="20dp"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/dropdown_item_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="6sp"
        android:lines="1"
        android:layout_weight="1"
        android:textStyle="bold"
        android:layout_marginStart="4dp"
        android:textColor="@color/spinnerItemTextColor"
        android:textSize="16sp"
        tools:text="Delivered" />

    <ImageButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:id="@+id/dropdown_icon"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:contentDescription="@string/axAddNewWalletSelectCurrencyLabel"
        android:src="@drawable/ic_arrow_home_account_drop_down" />

</LinearLayout>
