<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/dropdown_item_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:lineSpacingExtra="6sp"
        android:lines="1"
        android:paddingTop="@dimen/dimen_4dp"
        android:paddingEnd="@dimen/dimen_10dp"
        android:paddingBottom="@dimen/dimen_4dp"
        android:textSize="@dimen/text_16sp"
        tools:text="Delivered" />

    <ImageButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:contentDescription="@string/emptyString"
        android:src="@drawable/ic_arrow_drop_down" />

</LinearLayout>
