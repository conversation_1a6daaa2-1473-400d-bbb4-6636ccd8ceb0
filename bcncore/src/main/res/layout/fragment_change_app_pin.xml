<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/data_layout"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:gravity="center_horizontal"
    android:paddingHorizontal="@dimen/dimen_16dp">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="@dimen/dimen_64dp"
        android:layout_height="@dimen/dimen_64dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:src="@drawable/ic_logo_bcn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/enterCurrentAppPinTitle"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logo" />

    <TextView
        android:id="@+id/error_biometric"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:gravity="center"
        android:text="@string/alertBiometricAuthenticationExhausted"
        android:textColor="@color/negativeInfoTextColor"
        android:textSize="@dimen/text_14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/error_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_tv" />
    <TextView
        android:id="@+id/error_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:gravity="center"
        android:textColor="@color/errorTextColor"
        android:textSize="@dimen/text_14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/error_biometric"
        tools:text="@string/alertBiometricAuthenticationExhausted" />

    <com.poovam.pinedittextfield.CirclePinField
        android:id="@+id/password_et"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_60dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:cursorVisible="false"
        android:importantForAutofill="no"
        android:inputType="textPassword"
        android:paddingBottom="@dimen/dimen_24dp"
        android:textSelectHandle="@drawable/text_handle"
        app:circleRadius="@dimen/dimen_12dp"
        app:distanceInBetween="@dimen/sessionPinEditTextDistance"
        app:fieldBgColor="@color/colorAccentLight"
        app:fieldColor="@color/colorAccentLight"
        app:fillerColor="@color/colorPrimary"
        app:fillerRadius="13dp"
        app:highlightColor="@color/colorAccentLight"
        app:layout_constraintBottom_toTopOf="@id/biometric_switch"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/error_tv"
        app:layout_constraintVertical_bias="1.0"
        app:noOfFields="8"
        tools:ignore="LabelFor" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/biometric_switch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:text="@string/setupSessionPinBypassBiometrics"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        android:theme="@style/SwitchTheme"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/password_et"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
