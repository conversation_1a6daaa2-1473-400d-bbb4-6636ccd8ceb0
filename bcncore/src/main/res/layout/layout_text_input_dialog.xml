<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/input_til"
        style="@style/TextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_24dp"
        android:theme="@style/TextInputEditTextCursorTheme">

        <com.resoluttech.core.views.LimitedCharacterEditText
            android:id="@+id/input_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:maxLines="5"
            android:paddingHorizontal="@dimen/dimen_4dp" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginVertical="@dimen/dimen_8dp"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancel_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionCancel"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/save_button"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/alertActionSave" />

    </LinearLayout>
</LinearLayout>
