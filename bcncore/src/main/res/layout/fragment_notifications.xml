<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_to_refresh"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dimen_8dp">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:progressBackgroundTint="@color/colorPrimary" />

        <TextView
            android:id="@+id/no_notification_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawablePadding="@dimen/dimen_16dp"
            android:text="@string/notificationScreenNoNotificationsMessage"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp"
            android:visibility="gone"
            app:drawableTopCompat="@drawable/ic_no_notification_bell"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/notifications_rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </FrameLayout>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
