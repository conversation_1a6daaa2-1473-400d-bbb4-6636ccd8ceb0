<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:background="@drawable/bg_4dp">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="OTP has been sent to {Wade Warren, +265 135654560}"
        tools:visibility="visible" />

    <com.suryadigital.leo.libui.otptextfield.OTPTextField
        android:id="@+id/otp_et"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_20dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:contentDescription="@string/axOtpEntryTextfieldHint"
        android:importantForAutofill="no"
        android:textColor="@color/subtitleTextColor"
        app:bar_active_color="@color/subtitleTextColor"
        app:bar_enabled="true"
        app:bar_height="2dp"
        app:bar_inactive_color="@color/subtitleTextColor"
        app:flow_horizontalBias="0"
        app:height="36dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_tv"
        app:length="6"
        app:otp_text_size="@dimen/text_16sp"
        app:width="@dimen/otp_digit_width" />

    <FrameLayout
        android:id="@+id/resend_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:layout_marginEnd="@dimen/dimen_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otp_reading_container">

        <TextView
            android:id="@+id/resend_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:foreground="?attr/selectableItemBackground"
            android:paddingVertical="@dimen/dimen_8dp"
            android:text="@string/otpEntryResendOTPLabel"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold" />

        <ProgressBar
            android:id="@+id/resend_progress_bar"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:indeterminateTint="@color/colorPrimaryLight"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/otp_reading_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otp_et">

        <ProgressBar
            android:layout_width="@dimen/dimen_16dp"
            android:layout_height="@dimen/dimen_20dp"
            android:indeterminateTint="@color/colorPrimaryLight" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_4dp"
            android:text="@string/otpLabelAutoReadOTP"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/otp_valid_till_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/resend_container"
        tools:text="@string/otpEntryOTPValidTill" />

    <TextView
        android:id="@+id/otp_left_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otp_valid_till_tv"
        tools:text="• You have %d OTPs left" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/cancel_button"
        style="@style/AlertDialogButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_32dp"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:text="@string/alertActionCancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otp_left_tv" />

</androidx.constraintlayout.widget.ConstraintLayout>
