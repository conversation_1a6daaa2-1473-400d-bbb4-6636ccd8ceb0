<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/actionBarSize"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    tools:background="@color/colorPrimary">

    <TextView
        android:id="@+id/selected_wallet_name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_12dp"
        android:paddingStart="@dimen/dimen_4dp"
        android:drawablePadding="@dimen/dimen_4dp"
        android:ellipsize="end"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:maxWidth="110dp"
        android:maxLines="1"
        android:textColor="@android:color/white"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:drawableEndCompat="@drawable/ic_chevron_right_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Malawi Work" />

</LinearLayout>
