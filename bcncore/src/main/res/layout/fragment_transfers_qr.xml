<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/barcode_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:tag="barcode_fragment"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/barcode_camera_mask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/bg_scanner_background"
            app:layout_constraintBottom_toTopOf="@+id/footer_layout"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1.0"
            tools:ignore="ContentDescription" />

        <LinearLayout
            android:id="@+id/footer_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_transparent_mask"
            android:gravity="top"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barcode_camera_mask"
            tools:layout_editor_absoluteX="-16dp">

            <TextView
                android:id="@+id/barcode_error_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_20dp"
                android:gravity="center"
                android:text="@string/qrCodeScannerOverlayTitle"
                android:textColor="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/camera_permission_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:gravity="center"
                android:text="@string/kycCameraPermissionDeniedTitle"
                android:textColor="@color/titleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:padding="5dp"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:textSize="@dimen/text_14sp"
                android:text="@string/kycCameraPermissionDeniedSubtitle"
                android:textColor="@color/titleDescriptionTextColor" />

            <TextView
                android:id="@+id/gallery_barcode_error_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:padding="5dp"
                android:textColor="@color/destructiveActionColor"
                android:visibility="gone"
                tools:text="@string/qrCodeInvalidRecipient" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/request_camera_permission"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dimen_16dp"
                android:background="@drawable/bg_button_4dp_primary_enabled"
                android:text="@string/requestCameraAccess" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include layout="@layout/dialog_bottom_sheet_recently_paid" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
