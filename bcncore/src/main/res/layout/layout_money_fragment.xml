<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_to_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/network_info"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/destructiveActionColor"
                        android:padding="@dimen/dimen_16dp"
                        android:text="@string/noInternetBannerText"
                        android:textColor="@color/subtitleTextColorSecondary"
                        android:textSize="@dimen/text_14sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/data_layout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:paddingBottom="@dimen/dimen_16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/colorPrimary"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/dimen_16dp"
                            android:paddingVertical="@dimen/dimen_24dp">

                            <TextView
                                android:id="@+id/balance_label"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:contentDescription="@string/moneyScreenAvailableBalance"
                                android:gravity="center"
                                android:importantForAccessibility="no"
                                android:text="@string/moneyScreenAvailableBalance"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_14sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/balance_tv"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dimen_8dp"
                                android:gravity="center"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_20sp"
                                android:textStyle="bold"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/balance_label"
                                tools:text="MWK 10,569" />
                        </LinearLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/national_id_expired_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/surfaceColor"
                            android:padding="@dimen/dimen_16dp"
                            android:visibility="gone"
                            tools:visibility="visible">

                            <ImageView
                                android:id="@+id/error_info"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="2dp"
                                android:src="@drawable/ic_error_info"
                                app:layout_constraintEnd_toStartOf="@id/information_title"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/information_title"
                                tools:ignore="ContentDescription" />

                            <TextView
                                android:id="@+id/information_title"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:text="@string/moneyScreenLabelNationalIdExpiredTitle"
                                android:textColor="@color/destructiveActionColor"
                                android:textSize="@dimen/text_18sp"
                                android:textStyle="bold"
                                app:layout_constraintStart_toEndOf="@+id/error_info"
                                app:layout_constraintTop_toTopOf="parent" />

                            <TextView
                                android:id="@+id/information_description"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/moneyScreenLabelNationalIdExpiredMessage"
                                android:textColor="@color/destructiveActionColor"
                                android:textSize="@dimen/text_14sp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="@id/information_title"
                                app:layout_constraintTop_toBottomOf="@id/information_title" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <TextView
                            android:id="@+id/money_transfer_card"
                            android:layout_width="match_parent"
                            android:layout_height="160dp"
                            android:layout_marginHorizontal="@dimen/dimen_16dp"
                            android:layout_marginTop="@dimen/dimen_16dp"
                            android:background="@drawable/bg_outline_8dp"
                            android:contentDescription="@string/axMoneyScreenMoneyTransferHint"
                            android:drawablePadding="@dimen/dimen_16dp"
                            android:gravity="center"
                            android:padding="@dimen/dimen_16dp"
                            android:text="@string/moneyScreenMoneyTransfer"
                            android:textColor="@color/titleTextColor"
                            android:textSize="@dimen/text_14sp"
                            android:textStyle="bold"
                            android:foreground="@drawable/bg_ripple_8dp"
                            app:drawableTopCompat="@drawable/ic_send_money" />

                        <TextView
                            android:id="@+id/load_wallet_card"
                            android:layout_width="match_parent"
                            android:layout_height="160dp"
                            android:layout_marginHorizontal="@dimen/dimen_16dp"
                            android:layout_marginTop="@dimen/dimen_16dp"
                            android:background="@drawable/bg_outline_8dp"
                            android:contentDescription="@string/axMoneyScreenLoadWalletHint"
                            android:drawablePadding="@dimen/dimen_16dp"
                            android:gravity="center"
                            android:padding="@dimen/dimen_16dp"
                            android:text="@string/moneyScreenLoadWallet"
                            android:textColor="@color/titleTextColor"
                            android:textSize="@dimen/text_14sp"
                            android:textStyle="bold"
                            android:foreground="@drawable/bg_ripple_8dp"
                            app:drawableTopCompat="@drawable/ic_load_money" />

                    </LinearLayout>


                </LinearLayout>

            </FrameLayout>

        </ScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_no_internet"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/fullScreenErrorTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="10dp"
            android:gravity="center"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/fullScreenErrorMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/alertMessageNoInternet"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/retry_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dimen_16dp"
            android:text="@string/alertActionTryAgain" />

    </LinearLayout>
</FrameLayout>
