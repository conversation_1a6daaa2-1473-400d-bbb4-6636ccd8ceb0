<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/ListHeaderStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/flag_iv"
        android:layout_width="@dimen/dimen_16dp"
        android:layout_height="@dimen/dimen_16dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundedFlagImageView"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/header_text_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/dimen_8dp"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/flag_iv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Zambia" />
</androidx.constraintlayout.widget.ConstraintLayout>
