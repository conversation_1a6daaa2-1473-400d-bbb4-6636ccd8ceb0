<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.resoluttech.core.profile.ProfileFragment">

    <ScrollView
        android:id="@+id/data_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/root_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/listHeaderBgColor"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/top_section_background_drawable"
                android:padding="@dimen/dimen_16dp">

                <com.suryadigital.leo.libui.contactview.ContactIconView
                    android:id="@+id/profile_iv"
                    android:layout_width="@dimen/dimen_64dp"
                    android:layout_height="@dimen/dimen_64dp"
                    app:cardCornerRadius="@dimen/dimen_32dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/camera_iv"
                    android:layout_width="@dimen/dimen_24dp"
                    android:layout_height="@dimen/dimen_24dp"
                    android:background="@drawable/ic_upload"
                    android:contentDescription="@string/axProfilePictureHint"
                    app:layout_constraintBottom_toBottomOf="@+id/profile_iv"
                    app:layout_constraintEnd_toEndOf="@+id/profile_iv"
                    app:layout_constraintTop_toTopOf="@+id/profile_iv"
                    app:layout_constraintVertical_bias="0.94" />

                <TextView
                    android:id="@+id/user_name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:gravity="top"
                    android:textColor="@color/boldTitleTextColor"
                    android:textSize="@dimen/text_16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@id/phonenumber_tv"
                    app:layout_constraintStart_toEndOf="@+id/profile_iv"
                    app:layout_constraintTop_toTopOf="@+id/profile_iv"
                    tools:text="Kartina Venus" />

                <TextView
                    android:id="@+id/phonenumber_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/descriptionTextColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toTopOf="@id/user_id_tv"
                    app:layout_constraintStart_toStartOf="@+id/user_name_tv"
                    app:layout_constraintTop_toBottomOf="@+id/user_name_tv"
                    tools:text="Blantyre, Malawi" />

                <TextView
                    android:id="@+id/user_id_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@color/titleTextColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toBottomOf="@id/profile_iv"
                    app:layout_constraintEnd_toStartOf="@id/edit_email_iv"
                    app:layout_constraintStart_toStartOf="@+id/phonenumber_tv"
                    app:layout_constraintTop_toBottomOf="@+id/phonenumber_tv"
                    tools:text="<EMAIL>"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/edit_email_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/dimen_8dp"
                    android:contentDescription="@string/axProfileEditEmailHint"
                    android:src="@drawable/ic_edit"
                    app:layout_constraintBottom_toBottomOf="@id/user_id_tv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_id_tv"
                    app:layout_constraintTop_toTopOf="@id/user_id_tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/verifyEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:background="@drawable/middle_section_background_drawable"
                android:padding="@dimen/dimen_16dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/info_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/profileScreenAppInfo"
                    android:src="@drawable/ic_info_yellow"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/verify_email_tv" />

                <TextView
                    android:id="@+id/verify_email_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_32dp"
                    android:text="@string/emailVerificationVerifyEmailTitle"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@id/verify_email_info_tv"
                    app:layout_constraintStart_toEndOf="@id/info_iv"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/verify_email_info_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:text="@string/emailVerificationVerifyEmailMessage"
                    android:textColor="@color/titleDescriptionTextColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toTopOf="@+id/verification_link"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/verify_email_tv"
                    app:layout_constraintTop_toBottomOf="@id/verify_email_tv" />

                <TextView
                    android:id="@+id/verification_link"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_4dp"
                    android:foreground="?attr/selectableItemBackground"
                    android:text="@string/emailVerificationSendVerificationLink"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimaryLight"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/verify_email_info_tv"
                    app:layout_constraintTop_toBottomOf="@+id/verify_email_info_tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/profile_options_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:background="@drawable/middle_section_background_drawable"
                android:paddingVertical="@dimen/dimen_8dp">

                <TextView
                    android:id="@+id/qr_code_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionShowQRCode"
                    android:textSize="@dimen/text_14sp"
                    app:drawableStartCompat="@drawable/ic_qr_code"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/transfer_header"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dimen_10dp"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/qr_code_tv" />

                <TextView
                    android:id="@+id/reccuring_transfers_tv"
                    style="@style/ProfileItemRow"
                    android:drawablePadding="28dp"
                    android:visibility="gone"
                    app:drawableStartCompat="@drawable/ic_recurring_transfer"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transfer_header" />

                <TextView
                    android:id="@+id/app_settings_header"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dimen_10dp"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/reccuring_transfers_tv" />

                <LinearLayout
                    android:id="@+id/change_language_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:foreground="?android:selectableItemBackground"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/app_settings_header">

                    <TextView
                        style="@style/ProfileItemRow"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:drawablePadding="@dimen/dimen_24dp"
                        android:gravity="center|start"
                        android:text="@string/profileScreenOptionChangeLanguage"
                        app:drawableStartCompat="@drawable/ic_language" />

                </LinearLayout>

                <TextView
                    android:id="@+id/find_agent_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionFindAgent"
                    app:drawableStartCompat="@drawable/ic_agent_icon"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/change_language_ll" />

                <TextView
                    android:id="@+id/manage_accounts_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionManageAccounts"
                    app:drawableStartCompat="@drawable/ic_manage_wallets"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/find_agent_tv" />

                <TextView
                    android:id="@+id/app_pin_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionChangeSessionPin"
                    app:drawableStartCompat="@drawable/ic_lock"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/manage_accounts_tv" />

                <TextView
                    android:id="@+id/change_password_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionChangePassword"
                    app:drawableStartCompat="@drawable/ic_password"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/app_pin_tv" />

                <LinearLayout
                    android:id="@+id/biometric_switch_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/change_password_tv">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dimen_16dp"
                        android:src="@drawable/ic_fingerprint"
                        tools:ignore="ContentDescription" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/biometric_switch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/titleTextColor"
                        android:textSize="@dimen/text_14sp"
                        app:thumbTint="@drawable/color_thumb"
                        app:trackTint="@drawable/color_track" />

                </LinearLayout>


                <TextView
                    android:id="@+id/account_settings_header"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dimen_10dp"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/biometric_switch_layout" />

                <TextView
                    android:id="@+id/trusted_contacts_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionManageTrustedContacts"
                    app:drawableStartCompat="@drawable/ic_trusted_contact"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/account_settings_header" />

                <TextView
                    android:id="@+id/update_kyc_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionUpdateKYCDetails"
                    app:drawableStartCompat="@drawable/ic_badge"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/trusted_contacts_tv" />

                <TextView
                    android:id="@+id/statements_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/statementsTitle"
                    app:drawableStartCompat="@drawable/ic_statements"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/update_kyc_tv" />

                <TextView
                    android:id="@+id/share_app_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionShareApp"
                    app:drawableStartCompat="@drawable/ic_share_app"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/statements_tv" />

                <TextView
                    android:id="@+id/legal_header"
                    style="@style/ListHeaderStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dimen_10dp"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/app_info_tv" />

                <TextView
                    android:id="@+id/contact_support_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionContactSupport"
                    app:drawableStartCompat="@drawable/ic_contact_support"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/share_app_tv" />

                <TextView
                    android:id="@+id/third_party_software_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionThirdPartySoftware"
                    app:drawableStartCompat="@drawable/ic_tps"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/contact_support_tv" />

                <TextView
                    android:id="@+id/privacy_policy_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenOptionPrivacyPolicy"
                    app:drawableStartCompat="@drawable/ic_privacy_policy"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/third_party_software_tv" />


                <TextView
                    android:id="@+id/app_info_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenAppInfo"
                    app:drawableStartCompat="@drawable/ic_info_blue"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/privacy_policy_tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/sign_out_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:background="@drawable/middle_section_background_drawable"
                android:paddingVertical="@dimen/dimen_8dp">

                <TextView
                    android:id="@+id/sign_out_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenSignOutTitle"
                    app:drawableStartCompat="@drawable/ic_logout"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/close_account_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:background="@drawable/bottom_section_background_drawable"
                android:paddingVertical="@dimen/dimen_8dp">

                <TextView
                    android:id="@+id/close_account_tv"
                    style="@style/ProfileItemRow"
                    android:text="@string/profileScreenCloseAccountTitle"
                    android:textColor="@color/destructiveActionColor"
                    app:drawableStartCompat="@drawable/ic_close_account"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </ScrollView>

    <ProgressBar
        android:id="@+id/loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminateTint="@color/colorPrimaryDark"
        android:indeterminateTintMode="src_atop" />
</FrameLayout>
