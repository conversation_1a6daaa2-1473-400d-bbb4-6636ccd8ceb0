<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="?android:attr/actionBarSize"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent">

    <ImageView
        android:id="@+id/ic_back_arrow"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:contentDescription="@string/axBackButtonLabel"
        android:padding="@dimen/dimen_16dp"
        android:src="@drawable/ic_arrow_back"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ImageView
        android:id="@+id/overflow_icon"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:contentDescription="@string/axToolbarMenu"
        android:padding="@dimen/dimen_16dp"
        android:src="@drawable/ic_overflow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
