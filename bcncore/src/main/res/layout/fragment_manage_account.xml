<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context="com.resoluttech.core.profile.ShowQRCodeFragment">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/windowBackground"
            android:elevation="@dimen/dimen_16dp"
            app:tabBackground="@color/colorPrimary"
            app:tabIndicatorColor="@color/spinnerItemTextColor"
            app:tabIndicatorHeight="@dimen/dimen_2dp"
            app:tabSelectedTextColor="@color/spinnerItemTextColor"
            app:tabTextAppearance="@style/TabLayout"
            app:tabTextColor="@color/inactiveTabColor" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/create_account_fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:backgroundTint="@color/colorPrimary"
        android:padding="@dimen/dimen_8dp"
        android:text="@string/manageAccountsCreateAccount"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:fabCustomSize="56dp"
        app:icon="@drawable/ic_add"
        app:iconTint="@color/white" />

</FrameLayout>
