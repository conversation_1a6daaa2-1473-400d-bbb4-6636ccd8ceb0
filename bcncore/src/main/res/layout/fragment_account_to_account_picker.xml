<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:layout_marginTop="@dimen/dimen_8dp"
            tools:context="com.resoluttech.core.accounttoaccount.AccountToAccountPickerFragment">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/item_from_wallet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:foreground="?attr/selectableItemBackground"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/pay_from_wallet_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/accountToAccountFromAccount"
                    android:textColor="@color/descriptionTextDarkColor"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/from_wallet_name_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    android:ellipsize="end"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/right_chevron_from_wallet_iv"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/pay_from_wallet_label"
                    tools:text="Malawi Personal" />

                <ImageView
                    android:id="@+id/right_chevron_from_wallet_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_right_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/item_to_wallet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:foreground="?attr/selectableItemBackground"
                android:paddingHorizontal="@dimen/dimen_16dp"
                android:paddingVertical="@dimen/dimen_8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/item_from_wallet">

                <TextView
                    android:id="@+id/pay_to_account_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/accountToAccountToAccount"
                    android:textColor="@color/descriptionTextDarkColor"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/to_wallet_name_tv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/right_chevron_to_wallet_iv"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/pay_to_account_label"
                    tools:text="Malawi Personal" />

                <ImageView
                    android:id="@+id/right_chevron_to_wallet_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:importantForAccessibility="no"
                    android:src="@drawable/ic_right_arrow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <FrameLayout
                android:id="@+id/amount_text_field"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_40dp"
                android:gravity="bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/item_to_wallet">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/accountToAccountEnterAmount"
                    android:minHeight="@dimen/dimen_48dp"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:hintTextColor="@color/subtitleTextColorSecondary">

                    <com.resoluttech.core.views.AmountEditText
                        android:id="@+id/amount_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:imeOptions="actionDone"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textColor="@color/subtitleTextColor"
                        android:textSize="@dimen/text_16sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/currency_suffix"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|bottom"
                    android:layout_marginEnd="@dimen/dimen_4dp"
                    android:paddingBottom="@dimen/editTextBottomPadding"
                    android:textColor="@color/subtitleTextColorSecondary"
                    android:textSize="@dimen/text_14sp"
                    tools:text="MWK" />
            </FrameLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/private_remark_til"
                style="@style/TextInputLayoutStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:hint="@string/accountToAccountNarrationToSelfPlaceholder"
                android:minHeight="@dimen/dimen_48dp"
                android:theme="@style/TextInputEditTextCursorTheme"
                app:hintTextColor="@color/subtitleTextColorSecondary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/amount_text_field">

                <com.resoluttech.core.views.LimitedCharacterEditText
                    android:id="@+id/private_remark_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:maxLines="5"
                    android:paddingHorizontal="@dimen/dimen_4dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_16sp" />

            </com.google.android.material.textfield.TextInputLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/pay_now_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:text="@string/accountToAccountPayNow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</LinearLayout>
