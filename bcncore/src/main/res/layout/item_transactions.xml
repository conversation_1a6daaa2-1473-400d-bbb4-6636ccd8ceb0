<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/shared_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="?attr/selectableItemBackground"
    android:padding="@dimen/dimen_16dp"
    tools:context="com.resoluttech.core.transactions.TransactionsAdapter">

    <com.suryadigital.leo.libui.contactview.ContactIconView
        android:id="@+id/contact_icon_view"
        android:layout_width="@dimen/dimen_40dp"
        android:layout_height="@dimen/dimen_40dp"
        app:cardCornerRadius="@dimen/dimen_20dp"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/subtitle_tv"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toEndOf="@+id/contact_icon_view"
        app:layout_constraintTop_toTopOf="@id/contact_icon_view"
        tools:text="Marcus Aurelius Tiberius Caesar" />

    <TextView
        android:id="@+id/subtitle_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:textColor="@color/descriptionTextDarkColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toTopOf="@id/error_message_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/contact_icon_view"
        app:layout_constraintTop_toBottomOf="@+id/title_tv"
        tools:text="Rent Money" />

    <TextView
        android:id="@+id/error_message_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:textColor="@color/descriptionTextDarkColor"
        android:textSize="@dimen/text_14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/contact_icon_view"
        app:layout_constraintTop_toBottomOf="@+id/subtitle_tv"
        tools:text="Failed due to insufficient balance" />

    <ImageButton
        android:id="@+id/transaction_typ_icon"
        android:layout_width="@dimen/dimen_16dp"
        android:layout_height="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_4dp"
        android:background="@color/transparent"
        android:importantForAccessibility="no"
        app:layout_constraintBottom_toBottomOf="@+id/amount_tv"
        app:layout_constraintEnd_toStartOf="@+id/amount_tv"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="@+id/guideline"
        app:layout_constraintTop_toTopOf="@+id/amount_tv"
        tools:ignore="ContentDescription"
        tools:srcCompat="@drawable/ic_credited_to" />


    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.6" />

    <TextView
        android:id="@+id/amount_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@+id/title_tv"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="MK 500" />

</androidx.constraintlayout.widget.ConstraintLayout>
