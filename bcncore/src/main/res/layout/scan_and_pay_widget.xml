<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/scan_and_pay"
        android:layout_width="@dimen/widget_image_48dp"
        android:layout_height="@dimen/widget_image_48dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/dimen_8dp"
        android:layout_marginBottom="4dp"
        android:src="@drawable/ic_widget_scan_and_pay"
        tools:ignore="ContentDescription" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/appShortCutScanAndPay"
        android:textColor="@color/sectionHeadingColor"
        android:textSize="11sp" />
</LinearLayout>
