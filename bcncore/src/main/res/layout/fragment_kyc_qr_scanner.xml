<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/barcode_fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="barcode_fragment"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/heading_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_transparent_mask"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_32dp"
            android:gravity="center_horizontal"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_24dp"
            android:text="@string/kycScanQRCodeVerifyNationalIdTitle"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold" />

    </LinearLayout>

    <ImageView
        android:id="@+id/barcode_camera_mask"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:contentDescription="@string/axScanQRCodeButtonLabel"
        android:src="@drawable/bg_scanner_background"
        app:layout_constraintBottom_toTopOf="@+id/footer_layout"
        app:layout_constraintTop_toBottomOf="@id/heading_layout"
        app:layout_constraintVertical_bias="1.0" />

    <LinearLayout
        android:id="@+id/footer_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_transparent_mask"
        android:gravity="top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/barcode_camera_mask">

        <TextView
            android:id="@+id/message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top|center"
            android:padding="@dimen/dimen_20dp"
            android:text="@string/kycScanQRCodeVerifyNationalIdSubtitle"
            android:textColor="@color/white"
            android:textSize="@dimen/text_14sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/camera_permission_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:contentDescription="@string/emptyString"
            android:gravity="center"
            android:src="@drawable/ic_camera_permission_denied" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_margin="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/kycCameraPermissionDeniedSubtitle"
            android:textColor="@color/subtitleTextColorSecondary"
            android:textSize="@dimen/text_14sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/request_camera_permission"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:background="@drawable/bg_button_4dp_primary_enabled"
            android:text="@string/requestCameraAccess" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
