<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/account_holder_name"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/sectionHeadingColor"
        android:paddingStart="@dimen/dimen_8dp"
        android:paddingEnd="@dimen/dimen_8dp"
        android:gravity="center_vertical"
        tools:text="Personal" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="4dp"
        android:background="@color/name_label_color"
        android:alpha="0.5"/>
</LinearLayout>
