<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_16dp">

        <TextView
            android:id="@+id/section_label_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/transactionStatusDebitedFromLabel"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.suryadigital.leo.libui.contactview.ContactIconView
            android:id="@+id/icon_view"
            android:layout_width="@dimen/dimen_24dp"
            android:layout_height="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:backgroundTint="@color/transparent"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="@id/section_description_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/section_label_tv" />

        <TextView
            android:id="@+id/section_title_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_12dp"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toTopOf="@id/section_description_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/icon_view"
            app:layout_constraintTop_toTopOf="@+id/icon_view"
            tools:text="Personal" />

        <TextView
            android:id="@+id/section_description_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_12dp"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:textColor="@color/descriptionTextDarkColor"
            android:textSize="@dimen/text_14sp"
            app:layout_constraintBottom_toBottomOf="@+id/icon_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/icon_view"
            app:layout_constraintTop_toBottomOf="@+id/section_title_tv"
            tools:text="****1234" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
