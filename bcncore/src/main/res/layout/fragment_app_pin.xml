<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/data_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/app_pin_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="@dimen/dimen_16dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/bcn_logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:src="@drawable/ic_logo_bcn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:text="@string/enterSessionPinTitle"
            android:textColor="@color/titleTextColor"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/bcn_logo" />

        <TextView
            android:id="@+id/error_biometric"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/alertBiometricAuthenticationExhausted"
            android:textColor="@color/negativeInfoTextColor"
            android:textSize="@dimen/text_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/description" />
        <TextView
            android:id="@+id/error_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:gravity="center"
            android:textColor="@color/errorTextColor"
            android:textSize="@dimen/text_14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/error_biometric"
            tools:text="@string/alertMessageBiometricAttemptsExhaust" />

        <com.poovam.pinedittextfield.CirclePinField
            android:id="@+id/password_et"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_60dp"
            android:layout_marginTop="@dimen/dimen_40dp"
            android:contentDescription="@string/enterSessionPinTitle"
            android:cursorVisible="false"
            android:importantForAutofill="no"
            android:inputType="textPassword"
            android:textSelectHandle="@drawable/text_handle"
            app:circleRadius="@dimen/dimen_12dp"
            app:distanceInBetween="@dimen/sessionPinEditTextDistance"
            app:fieldBgColor="@color/colorAccentLight"
            app:fieldColor="@color/colorAccentLight"
            app:fillerColor="@color/colorPrimary"
            app:fillerRadius="13dp"
            app:highlightColor="@color/colorAccentLight"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/error_tv"
            app:noOfFields="8"
            tools:ignore="LabelFor" />

        <TextView
            android:id="@+id/forgot_session_pin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:foreground="?attr/selectableItemBackground"
            android:gravity="center_horizontal"
            android:padding="@dimen/dimen_8dp"
            android:text="@string/enterSessionPinForgotPin"
            android:textAllCaps="true"
            android:textColor="@color/colorPrimaryLight"
            android:textSize="@dimen/text_14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password_et" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
