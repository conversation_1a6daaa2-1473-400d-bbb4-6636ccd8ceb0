<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/langauge_nyanja"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center|start"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/flag_iv"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="@dimen/dimen_16dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_32dp"
        android:scaleType="fitXY"
        android:src="@drawable/flag_malawi"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/language_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/languageSelectorOptionNyanja"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp" />

    <ImageView
        android:id="@+id/selector_iv"
        android:layout_width="@dimen/dimen_20dp"
        android:layout_height="@dimen/dimen_20dp"
        android:layout_margin="@dimen/dimen_16dp"
        android:src="@drawable/ic_radiobox_blank"
        tools:ignore="ContentDescription" />

</LinearLayout>
