<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="@dimen/dimen_40dp">

    <FrameLayout
        android:id="@+id/amount_text_field"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:gravity="bottom"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/account_name_til"
            style="@style/TextInputLayoutStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/TextInputEditTextCursorTheme"
            app:suffixTextColor="@color/subtitleTextColorSecondary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/account_name_et"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/createNewAccountEnterAccountName"
                android:inputType="textPersonName"
                android:paddingHorizontal="@dimen/dimen_4dp"
                android:textSize="@dimen/text_16sp"
                app:suffixText="MWK" />
        </com.google.android.material.textfield.TextInputLayout>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/dropdown_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginTop="40dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/amount_text_field">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/createNewAccountSelectCurrency"
            android:textColor="@color/descriptionTextColor" />

        <com.suryadigital.leo.libui.textdropdown.TextDropdown
            android:id="@+id/currency_dropdown"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:background="@null"
            android:minHeight="24dp"
            android:paddingTop="@dimen/dimen_8dp"
            android:paddingBottom="6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/pay_from_account_label"
            app:layout_constraintTop_toBottomOf="@+id/pay_from_account_label" />

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/create_account_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:contentDescription="@string/axAddNewWalletNameDisabledHint"
        android:foreground="?selectableItemBackground"
        android:text="@string/createNewAccountButtonTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
