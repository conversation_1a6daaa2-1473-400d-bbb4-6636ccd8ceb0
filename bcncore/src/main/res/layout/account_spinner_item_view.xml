<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/dropdown_item_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="6sp"
        android:lines="1"
        android:paddingStart="@dimen/dimen_8dp"
        android:paddingEnd="@dimen/dimen_8dp"
        android:textStyle="bold"
        android:layout_weight="1"
        android:textSize="14sp"
        android:textColor="@color/sectionHeadingColor"
        tools:text="Personal" />

    <ImageButton
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:contentDescription="@string/axAccountSelectionButtonLabel"
        android:src="@drawable/ic_arrow_account_drop_down" />

</LinearLayout>
