<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/root_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:layout_marginTop="@dimen/dimen_40dp"
                app:layout_constraintBottom_toTopOf="@+id/submit_layout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:context="com.resoluttech.core.payments.BillersInputFragment" />

            <LinearLayout
                android:id="@+id/submit_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dimen_2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:context="com.resoluttech.core.payments.BillersInputFragment">

                <include layout="@layout/component_pay_from_wallet" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/ok_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:contentDescription="@string/axPaymentsAttemptPaymentDisabledHint"
        android:text="@string/sendMoneyAttemptPayment"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="1.0" />

</LinearLayout>
