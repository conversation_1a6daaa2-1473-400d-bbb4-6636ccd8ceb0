<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/confirm_with_otp_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginVertical="@dimen/dimen_16dp"
            android:orientation="vertical"
            tools:context="com.resoluttech.core.auth.ChangePasswordFragment">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:contentDescription="@string/emptyString"
                    android:src="@drawable/ic_password_lock" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/current_password_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/passwordToggleColor">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/current_password_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/changePasswordEnterCurrentPasswordTitle"
                        android:inputType="textPassword"
                        android:paddingHorizontal="@dimen/dimen_4dp"
                        android:textSize="@dimen/text_16sp" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:contentDescription="@string/emptyString"
                    android:src="@drawable/ic_password_lock" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/new_password_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/passwordToggleColor">


                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/new_password_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/changePasswordEnterNewPasswordTitle"
                        android:inputType="textPassword"
                        android:paddingHorizontal="@dimen/dimen_8dp"
                        android:textSize="@dimen/text_16sp" />
                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:contentDescription="@string/emptyString"
                    android:src="@drawable/ic_password_lock" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/confirm_password_til"
                    style="@style/TextInputLayoutStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:theme="@style/TextInputEditTextCursorTheme"
                    app:passwordToggleDrawable="@drawable/show_password_selector"
                    app:passwordToggleEnabled="true"
                    app:passwordToggleTint="@color/passwordToggleColor">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/confirm_password_et"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/changePasswordConfirmNewPasswordTitle"
                        android:inputType="textPassword"
                        android:paddingHorizontal="4dp"
                        android:textSize="@dimen/text_16sp" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/privacy_policy_heading_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dimen_40dp"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    tools:text="Your password must:" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/password_policy_rv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginTop="@dimen/dimen_8dp"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/confirm_with_otp_button"
        style="@style/PrimaryButton"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_margin="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/change_password_item_top_margin"
        android:contentDescription="@string/changePasswordTitle"
        android:text="@string/changePasswordTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
