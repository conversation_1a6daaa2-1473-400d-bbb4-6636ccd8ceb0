<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/dimen_16dp"
            android:paddingVertical="@dimen/dimen_24dp">

            <ImageView
                android:id="@+id/scan_icon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:contentDescription="@string/emptyString"
                android:src="@drawable/ic_scan"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/scan_qr_code_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:gravity="center"
                android:text="@string/kycScanQRCodeTitle"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scan_icon" />

            <TextView
                android:id="@+id/scan_qr_code_description_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:gravity="center"
                android:text="@string/kycScanQRCodeSubTitle"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scan_qr_code_tv" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/scan_qr_button"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:text="@string/kycScanQRCodeTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scan_qr_code_description_tv"
                app:layout_constraintVertical_bias="1.0"
                tools:layout_editor_absoluteX="41dp" />

            <View
                android:id="@+id/separator"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_1dp"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:background="@color/listDividerColor"
                app:layout_constraintTop_toBottomOf="@id/scan_qr_button" />

            <ImageView
                android:id="@+id/enter_manually_icon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_24dp"
                android:background="@android:color/transparent"
                android:contentDescription="@string/emptyString"
                android:src="@drawable/ic_enter_manually"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/separator" />

            <TextView
                android:id="@+id/enter_manually_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:gravity="center"
                android:text="@string/kycEnterDetailsManuallyTitle"
                android:textColor="@color/subtitleTextColor"
                android:textSize="@dimen/text_18sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/enter_manually_icon" />

            <TextView
                android:id="@+id/enter_manually_description_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_4dp"
                android:gravity="center"
                android:text="@string/kycEnterDetailsManuallySubtitle"
                android:textColor="@color/descriptionTextColor"
                android:textSize="@dimen/text_14sp"
                app:layout_constraintTop_toBottomOf="@+id/enter_manually_tv" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/enter_manually_button"
                style="@style/PrimaryButtonOutline"
                android:layout_width="match_parent"
                android:layout_height="@dimen/outline_button_height"
                android:layout_marginTop="@dimen/dimen_10dp"
                android:text="@string/kycEnterDetailsManuallyTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/enter_manually_description_tv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
