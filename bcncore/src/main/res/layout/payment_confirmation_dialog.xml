<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/AlertDialogStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_24dp"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:text="@string/paymentDetailsTitle"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginVertical="@dimen/dimen_12dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <com.resoluttech.core.uicomponents.FixedHeightRecyclerView
        android:id="@+id/breakdown_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_24dp" />

    <LinearLayout
        android:id="@+id/payable_amount"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="3dp"
            android:layout_marginVertical="@dimen/dimen_12dp"
            android:background="@drawable/ic_dialog_line_separator"
            android:layerType="software" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_12dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/alertTransactionDetailPayableAmount"
                android:textColor="@color/subtitleTextColor"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/payable_amount_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/subtitleTextColor"
                android:textStyle="bold"
                app:autoSizeMaxTextSize="16sp"
                app:autoSizeMinTextSize="10sp"
                app:autoSizeStepGranularity="2sp"
                app:autoSizeTextType="uniform"
                tools:text="10000.00" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:background="@drawable/ic_dialog_line_separator"
        android:layerType="software" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/decline"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionDecline" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/proceed"
            style="@style/AlertDialogButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:text="@string/alertActionProceed" />

    </LinearLayout>

</LinearLayout>
