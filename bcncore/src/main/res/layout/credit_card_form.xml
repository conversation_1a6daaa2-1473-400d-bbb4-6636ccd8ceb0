<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:orientation="vertical"
    android:paddingStart="@dimen/dimen_10dp"
    android:paddingEnd="@dimen/dimen_10dp"
    android:theme="@style/CardTheme"
    tools:context="com.resoluttech.core.uicomponents.CreditCardView">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/expanded_view"
        android:paddingBottom="@dimen/dimen_10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <com.braintreepayments.cardform.view.CardForm
            android:id="@+id/cardForm"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textfield.TextInputLayout
            style="@style/Widget.Design.TextInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="49dp"
            android:paddingTop="@dimen/dimen_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardForm">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/nickname_edit_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:height="60dp"
                android:maxEms="200"
                android:paddingBottom="24dp"
                android:inputType="textPersonName"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
