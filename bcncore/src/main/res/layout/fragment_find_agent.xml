<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/location_error_block"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/search_icon"
                android:layout_width="125dp"
                android:layout_height="125dp"
                android:layout_gravity="center"
                android:contentDescription="@string/emptyString"
                android:src="@drawable/ic_location" />

            <TextView
                android:id="@+id/location_permission_error"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dimen_16dp"
                android:text="@string/findAgentLocationPermissionNotPresent"
                android:textColor="@color/titleDescriptionTextColor"
                android:textSize="@dimen/text_14sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/request_location_permission"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:text="@string/findAgentLocationPermissionNotPresentButton" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/map_block"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/findAgent"
            android:name="com.suryadigital.leo.libui.location.MapFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/recenter_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|right"
            android:layout_margin="16dp"
            android:src="@drawable/ic_recenter"
            app:backgroundTint="@color/dialogBgColor"
            app:elevation="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:ignore="ContentDescription" />

        <androidx.cardview.widget.CardView
            android:id="@+id/findAgentCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dimen_16dp"
            android:visibility="gone"
            app:cardCornerRadius="@dimen/button_corner_radius"
            app:cardElevation="@dimen/dimen_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/dimen_16dp">

                <com.suryadigital.leo.libui.contactview.ContactIconView
                    android:id="@+id/agentProfileImage"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_marginStart="@dimen/dimen_8dp"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    app:cardCornerRadius="28dp"
                    app:cardElevation="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/agentName"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/agentName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/subtitleTextColor"
                    android:textSize="@dimen/text_16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/shopNameTV"
                    app:layout_constraintEnd_toStartOf="@+id/agentProfileImage"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Zambia Mitchell" />

                <TextView
                    android:id="@+id/shopNameTV"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/titleDescriptionTextColor"
                    android:textSize="@dimen/text_14sp"
                    app:layout_constraintBottom_toTopOf="@+id/shopStatusTV"
                    app:layout_constraintEnd_toStartOf="@+id/agentProfileImage"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/agentName"
                    tools:text="Agent" />

                <TextView
                    android:id="@+id/shopStatusTV"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/findAgentShopStatusClosed"
                    android:textColor="@color/agentClosed"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toStartOf="@id/agentProfileImage"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/shopNameTV" />

                <View
                    android:id="@+id/separator"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:background="@color/listDividerColor"
                    android:layerType="software"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/shopStatusTV" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/navigateToDialerScreen"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:layout_marginTop="@dimen/dimen_16dp"
                    android:layout_marginEnd="@dimen/dimen_8dp"
                    android:background="@drawable/bg_button_4dp_primary_enabled"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/findAgentCallNow"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/navigateToMapButton"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/separator" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/navigateToMapButton"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginEnd="@dimen/dimen_16dp"
                    android:background="@drawable/bg_button_4dp_primary_enabled"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@string/findAgentGetDirections"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_14sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/navigateToDialerScreen"
                    app:layout_constraintTop_toTopOf="@id/navigateToDialerScreen" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center"
        android:progressTint="@color/colorPrimary"
        android:visibility="gone"
        tools:visibility="visible" />

</FrameLayout>
