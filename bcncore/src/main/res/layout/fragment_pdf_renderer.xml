<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/RootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="?android:attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/back_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dimen_16dp"
            android:src="@drawable/ic_arrow_back"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/toolbar_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dimen_20dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/white"
            android:textSize="@dimen/text_18sp"
            android:textStyle="bold"
            tools:text="May Statement" />

        <ImageView
            android:id="@+id/share_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dimen_16dp"
            android:src="@drawable/ic_share"
            tools:ignore="ContentDescription" />

    </LinearLayout>

    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:contentDescription="@null"
        android:scaleType="fitCenter" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/listSubHeaderBgColor" />

    <LinearLayout
        style="?android:attr/buttonBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:measureWithLargestChild="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/previous_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dimen_16dp"
            android:src="@drawable/ic_chevron_left_enabled"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/page_count_tv"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/subtitleTextColor"
            android:textSize="@dimen/text_14sp"
            tools:text="1/6" />

        <ImageView
            android:id="@+id/next_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dimen_16dp"
            android:src="@drawable/ic_chevron_right_enabled"
            tools:ignore="ContentDescription" />

    </LinearLayout>

</LinearLayout>
