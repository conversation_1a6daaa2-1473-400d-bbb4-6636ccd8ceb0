<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/margin_40dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/question_number_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/descriptionTextColor"
        android:textSize="@dimen/text_14sp"
        tools:text="Question 1" />

    <TextView
        android:id="@+id/question_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:textColor="@color/subtitleTextColor"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        tools:text="Which is your favourite sports?" />

    <com.google.android.material.textfield.TextInputLayout
        style="@style/TextInputLayoutStyle"
        android:id="@+id/answer_til"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_24dp"
        android:textColorHint="@color/editTextHintColor"
        android:theme="@style/TextInputEditTextCursorTheme">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/answer_et"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:hint="@string/setSecurityQuestionAnswer"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dimen_4dp"
            android:textSize="@dimen/text_16sp" />
    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>
