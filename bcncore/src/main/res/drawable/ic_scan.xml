<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="52dp"
    android:height="52dp"
    android:viewportWidth="52"
    android:viewportHeight="52"
    tools:ignore="VectorPath">
  <path
      android:pathData="M26,2L26,2A24,24 0,0 1,50 26L50,26A24,24 0,0 1,26 50L26,50A24,24 0,0 1,2 26L2,26A24,24 0,0 1,26 2z"
      android:fillColor="#6A39F1"/>
  <group>
    <clip-path
        android:pathData="M16,16h20v20h-20z"/>
    <path
        android:pathData="M22.667,33.5H19.333C19.112,33.5 18.9,33.412 18.744,33.256C18.588,33.1 18.5,32.888 18.5,32.667V29.333C18.5,29.112 18.412,28.9 18.256,28.744C18.1,28.588 17.888,28.5 17.667,28.5C17.446,28.5 17.234,28.588 17.077,28.744C16.921,28.9 16.833,29.112 16.833,29.333V32.667C16.833,33.33 17.097,33.966 17.566,34.435C18.034,34.903 18.67,35.167 19.333,35.167H22.667C22.888,35.167 23.1,35.079 23.256,34.923C23.412,34.766 23.5,34.555 23.5,34.333C23.5,34.112 23.412,33.901 23.256,33.744C23.1,33.588 22.888,33.5 22.667,33.5ZM34.333,28.5C34.112,28.5 33.9,28.588 33.744,28.744C33.588,28.9 33.5,29.112 33.5,29.333V32.667C33.5,32.888 33.412,33.1 33.256,33.256C33.1,33.412 32.888,33.5 32.667,33.5H29.333C29.112,33.5 28.9,33.588 28.744,33.744C28.588,33.901 28.5,34.112 28.5,34.333C28.5,34.555 28.588,34.766 28.744,34.923C28.9,35.079 29.112,35.167 29.333,35.167H32.667C33.33,35.167 33.966,34.903 34.434,34.435C34.903,33.966 35.167,33.33 35.167,32.667V29.333C35.167,29.112 35.079,28.9 34.923,28.744C34.766,28.588 34.554,28.5 34.333,28.5ZM32.667,16.833H29.333C29.112,16.833 28.9,16.921 28.744,17.078C28.588,17.234 28.5,17.446 28.5,17.667C28.5,17.888 28.588,18.1 28.744,18.256C28.9,18.412 29.112,18.5 29.333,18.5H32.667C32.888,18.5 33.1,18.588 33.256,18.744C33.412,18.9 33.5,19.112 33.5,19.333V22.667C33.5,22.888 33.588,23.1 33.744,23.256C33.9,23.412 34.112,23.5 34.333,23.5C34.554,23.5 34.766,23.412 34.923,23.256C35.079,23.1 35.167,22.888 35.167,22.667V19.333C35.167,18.67 34.903,18.035 34.434,17.566C33.966,17.097 33.33,16.833 32.667,16.833ZM17.667,23.5C17.888,23.5 18.1,23.412 18.256,23.256C18.412,23.1 18.5,22.888 18.5,22.667V19.333C18.5,19.112 18.588,18.9 18.744,18.744C18.9,18.588 19.112,18.5 19.333,18.5H22.667C22.888,18.5 23.1,18.412 23.256,18.256C23.412,18.1 23.5,17.888 23.5,17.667C23.5,17.446 23.412,17.234 23.256,17.078C23.1,16.921 22.888,16.833 22.667,16.833H19.333C18.67,16.833 18.034,17.097 17.566,17.566C17.097,18.035 16.833,18.67 16.833,19.333V22.667C16.833,22.888 16.921,23.1 17.077,23.256C17.234,23.412 17.446,23.5 17.667,23.5ZM24.333,20.167H21C20.779,20.167 20.567,20.255 20.411,20.411C20.254,20.567 20.167,20.779 20.167,21V24.333C20.167,24.555 20.254,24.767 20.411,24.923C20.567,25.079 20.779,25.167 21,25.167H24.333C24.554,25.167 24.766,25.079 24.923,24.923C25.079,24.767 25.167,24.555 25.167,24.333V21C25.167,20.779 25.079,20.567 24.923,20.411C24.766,20.255 24.554,20.167 24.333,20.167ZM23.5,23.5H21.833V21.833H23.5V23.5ZM27.667,25.167H31C31.221,25.167 31.433,25.079 31.589,24.923C31.746,24.767 31.833,24.555 31.833,24.333V21C31.833,20.779 31.746,20.567 31.589,20.411C31.433,20.255 31.221,20.167 31,20.167H27.667C27.446,20.167 27.234,20.255 27.077,20.411C26.921,20.567 26.833,20.779 26.833,21V24.333C26.833,24.555 26.921,24.767 27.077,24.923C27.234,25.079 27.446,25.167 27.667,25.167ZM28.5,21.833H30.167V23.5H28.5V21.833ZM24.333,26.833H21C20.779,26.833 20.567,26.921 20.411,27.078C20.254,27.234 20.167,27.446 20.167,27.667V31C20.167,31.221 20.254,31.433 20.411,31.589C20.567,31.746 20.779,31.833 21,31.833H24.333C24.554,31.833 24.766,31.746 24.923,31.589C25.079,31.433 25.167,31.221 25.167,31V27.667C25.167,27.446 25.079,27.234 24.923,27.078C24.766,26.921 24.554,26.833 24.333,26.833ZM23.5,30.167H21.833V28.5H23.5V30.167ZM27.667,29.333C27.888,29.333 28.1,29.246 28.256,29.089C28.412,28.933 28.5,28.721 28.5,28.5C28.721,28.5 28.933,28.412 29.089,28.256C29.246,28.1 29.333,27.888 29.333,27.667C29.333,27.446 29.246,27.234 29.089,27.078C28.933,26.921 28.721,26.833 28.5,26.833H27.667C27.446,26.833 27.234,26.921 27.077,27.078C26.921,27.234 26.833,27.446 26.833,27.667V28.5C26.833,28.721 26.921,28.933 27.077,29.089C27.234,29.246 27.446,29.333 27.667,29.333ZM31,26.833C30.779,26.833 30.567,26.921 30.411,27.078C30.254,27.234 30.167,27.446 30.167,27.667V30.167C29.946,30.167 29.734,30.255 29.577,30.411C29.421,30.567 29.333,30.779 29.333,31C29.333,31.221 29.421,31.433 29.577,31.589C29.734,31.746 29.946,31.833 30.167,31.833H31C31.221,31.833 31.433,31.746 31.589,31.589C31.746,31.433 31.833,31.221 31.833,31V27.667C31.833,27.446 31.746,27.234 31.589,27.078C31.433,26.921 31.221,26.833 31,26.833ZM27.667,30.167C27.502,30.167 27.341,30.216 27.204,30.307C27.067,30.399 26.96,30.529 26.897,30.681C26.834,30.833 26.817,31.001 26.849,31.163C26.882,31.324 26.961,31.473 27.077,31.589C27.194,31.706 27.342,31.785 27.504,31.817C27.666,31.85 27.833,31.833 27.986,31.77C28.138,31.707 28.268,31.6 28.36,31.463C28.451,31.326 28.5,31.165 28.5,31C28.5,30.779 28.412,30.567 28.256,30.411C28.1,30.255 27.888,30.167 27.667,30.167Z"
        android:fillColor="#ffffff"/>
  </group>
  <path
      android:pathData="M26,2L26,2A24,24 0,0 1,50 26L50,26A24,24 0,0 1,26 50L26,50A24,24 0,0 1,2 26L2,26A24,24 0,0 1,26 2z"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:strokeColor="#F0EBFE"/>
</vector>
