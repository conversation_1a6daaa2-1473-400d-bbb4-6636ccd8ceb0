<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sign_in_nav"
    app:startDestination="@id/signInFragment">

    <fragment
        android:id="@+id/signInFragment"
        android:name="com.resoluttech.core.auth.signin.SignInFragment"
        android:label="fragment_sign_in"
        tools:layout="@layout/fragment_sign_in">
        <action
            android:id="@+id/action_signInFragment_to_enterPasswordFragment"
            app:destination="@id/enterPasswordFragment" />
        <action
            android:id="@+id/action_signInFragment_to_sign_up_nav"
            app:destination="@id/sign_up_nav" />
        <action
            android:id="@+id/action_signInFragment_to_selectLanguage"
            app:destination="@id/changeLanguageFragment" />
    </fragment>
    <fragment
        android:id="@+id/enterPasswordFragment"
        android:name="com.resoluttech.core.auth.signin.EnterPasswordFragment"
        android:label="EnterPasswordFragment"
        tools:layout="@layout/fragment_enter_password">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="string" />
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <action
            android:id="@+id/action_enterPasswordFragment_to_sign_up_nav"
            app:destination="@id/sign_up_nav" />
        <action
            android:id="@+id/action_enterPasswordFragment_to_securityQuestionsFragment"
            app:destination="@id/securityQuestionsFragment" />
    </fragment>
    <fragment
        android:id="@+id/securityQuestionsFragment"
        android:name="com.resoluttech.core.auth.signin.SignInSecurityQuestionsFragment"
        android:label="fragment_security_questions"
        tools:layout="@layout/fragment_security_questions">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="string" />
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <action
            android:id="@+id/action_securityQuestionsFragment_to_resetPasswordFragment"
            app:destination="@id/resetPasswordFragment" />
        <action
            android:id="@+id/action_securityQuestionsFragment_to_signInTrustedContactFragment"
            app:destination="@id/signInTrustedContactFragment" />
    </fragment>
    <fragment
        android:id="@+id/resetPasswordFragment"
        android:name="com.resoluttech.core.auth.signin.ResetPasswordFragment"
        android:label="ResetPasswordFragment"
        tools:layout="@layout/fragment_set_password">
        <action
            android:id="@+id/action_resetPasswordFragment_to_enter_password"
            app:destination="@id/enterPasswordFragment"
            app:popUpTo="@id/enterPasswordFragment"
            app:popUpToInclusive="true" />
    </fragment>
    <include app:graph="@navigation/sign_up_nav" />
    <include app:graph="@navigation/kyc_nav" />
    <fragment
        android:id="@+id/signInTrustedContactFragment"
        android:name="com.resoluttech.core.auth.signin.SignInTrustedContactFragment"
        android:label="SignInTrustedContactFragment"
        tools:layout="@layout/fragment_sign_in_trusted_contact">
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="java.util.UUID" />
        <action
            android:id="@+id/action_signInTrustedContactFragment_to_resetPasswordFragment"
            app:destination="@id/resetPasswordFragment" />
    </fragment>
    <fragment
        android:id="@+id/changeLanguageFragment"
        android:name="com.resoluttech.core.landingscreen.SelectLanguageFragment"
        android:label="fragment_select_language"
        tools:layout="@layout/fragment_select_language" />
</navigation>
