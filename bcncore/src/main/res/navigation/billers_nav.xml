<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/billers_nav"
    app:startDestination="@id/billersInputFragment">

    <fragment
        android:id="@+id/billersFragment"
        android:name="com.resoluttech.core.payments.BillersFragment"
        android:label="fragment_billers"
        tools:layout="@layout/fragment_billers">
        <action
            android:id="@+id/action_billerFragment_to_billerInputFragment"
            app:destination="@id/billers_nav" />
    </fragment>

    <fragment
        android:id="@+id/billersInputFragment"
        android:name="com.resoluttech.core.payments.BillersInputFragment"
        android:label="fragment_billers_input"
        tools:layout="@layout/fragment_billers_input">
        <deepLink app:uri="resoluttechbcn://billers-input" />
        <action
            android:id="@+id/action_billersInputFragment_to_paymentDetailConfirmationFragment"
            app:destination="@id/paymentDetailConfirmationFragment" />
        <action
            android:id="@+id/action_billersInputFragment_to_auth_provider_nav"
            app:destination="@id/auth_provider_nav" />
    </fragment>
    <fragment
        android:id="@+id/paymentDetailConfirmationFragment"
        android:name="com.resoluttech.core.payments.PaymentDetailConfirmationFragment"
        android:label="fragment_payment_detail_confirmation"
        tools:layout="@layout/fragment_payment_detail_confirmation" />

    <include app:graph="@navigation/auth_provider_nav" />
</navigation>
