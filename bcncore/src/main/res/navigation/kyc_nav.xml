<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/kyc_nav"
    app:startDestination="@id/KYCInputSelectionFragment">

    <fragment
        android:id="@+id/KYCQRScanner"
        android:name="com.resoluttech.core.kyc.KYCQRScannerFragment"
        android:label="kyc_qr_scanner_fragment"
        tools:layout="@layout/fragment_kyc_qr_scanner">
        <action
            android:id="@+id/action_KYCQRScanner_to_enterKYCDataFragment"
            app:destination="@id/enterKYCDataFragment" />
    </fragment>

    <fragment
        android:id="@+id/enterKYCDataFragment"
        android:name="com.resoluttech.core.kyc.EnterKYCDataFragment"
        android:label="fragment_enter_kyc_data"
        tools:layout="@layout/fragment_enter_kyc_data">
        <argument
            android:name="kycData"
            app:argType="com.resoluttech.core.kyc.KYCDataUtils"
            app:nullable="true"/>
        <action
            android:id="@+id/action_enterKYCDataFragment_to_KYCDocumentsFragment"
            app:destination="@id/KYCDocumentsFragment" />
    </fragment>
    <fragment
        android:id="@+id/KYCDocumentsFragment"
        android:name="com.resoluttech.core.kyc.KYCDocumentsFragment"
        android:label="kyc_documents_fragment"
        tools:layout="@layout/fragment_kyc_documents" >
        <argument
            android:name="signatureImageId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
        <argument
            android:name="nationalIdImageFrontId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
        <argument
            android:name="nationalIdImageBackId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
        <argument
            android:name="proofOfResidenceImageId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
        <argument
            android:name="passportFrontImageId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
        <argument
            android:name="passportBackImageId"
            app:argType="java.util.UUID"
            app:nullable="true"/>
    </fragment>
    <fragment
        android:id="@+id/KYCInputSelectionFragment"
        android:name="com.resoluttech.core.kyc.KYCInputSelectionFragment"
        android:label="fragment_kyc_input_selection"
        tools:layout="@layout/fragment_kyc_input_selection" >
        <action
            android:id="@+id/action_KYCInputSelectionFragment_to_KYCQRScanner"
            app:destination="@id/KYCQRScanner" />
        <action
            android:id="@+id/action_KYCInputSelectionFragment_to_enterKYCDataFragment"
            app:destination="@id/enterKYCDataFragment" />
    </fragment>
</navigation>
