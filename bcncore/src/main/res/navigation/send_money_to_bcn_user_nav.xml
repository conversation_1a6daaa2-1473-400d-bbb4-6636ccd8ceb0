<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/peer_to_peer_transfer_nav"
    app:startDestination="@id/qrCode"
    tools:ignore="UnusedNavigation">

    <fragment
        android:id="@+id/qrCode"
        android:name="com.resoluttech.core.transfers.peertopeer.TransfersQRFragment"
        tools:layout="@layout/fragment_transfers_qr">
        <action
            android:id="@+id/action_start_contact_picker"
            app:destination="@id/contact_picker" />
        <action
            android:id="@+id/action_qr_code_to_sendMoneyToUser"
            app:destination="@id/sendMoneyToUser" />
        <deepLink app:uri="resoluttechbcn://scan_and_pay" />
    </fragment>
    <fragment
        android:id="@+id/contact_picker"
        android:name="com.resoluttech.core.utils.ContactPickerFragment"
        tools:layout="@layout/fragment_transfers_contact_picker">
        <action
            android:id="@+id/action_contact_picker_to_sendMoneyToUser"
            app:destination="@id/sendMoneyToUser"
            app:popUpTo="@id/contact_picker"
            app:popUpToInclusive="true" />
    </fragment>
    <fragment
        android:id="@+id/sendMoneyToUser"
        android:name="com.resoluttech.core.transfers.peertopeer.SendMoneyFragment"
        android:label="fragment_send_money_to_user"
        tools:layout="@layout/fragment_send_money">
        <deepLink app:uri="resoluttechbcn://?username={username}&amp;userId={userId}&amp;accountId={accountId}&amp;shouldNavigate={shouldNavigate}" />
        <argument
            android:name="username"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="userId"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="shouldNavigate"
            app:argType="boolean"
            app:nullable="false" />
        <argument
            android:name="accountId"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/action_sendMoneyToUser_To_AuthenticationProvider"
            app:destination="@id/auth_provider_nav" />
        <action
            android:id="@+id/action_sendMoneyFragment_to_PDFRendererFragment"
            app:destination="@+id/PDFRendererFragment" />
    </fragment>
    <fragment
        android:id="@+id/PDFRendererFragment"
        android:name="com.resoluttech.core.profile.statements.PDFRendererFragment"
        android:label="PDFRendererFragment">
        <argument
            android:name="title"
            app:argType="string" />
        <argument
            android:name="previousPath"
            app:argType="string" />
    </fragment>
    <include app:graph="@navigation/transaction_status_nav" />
    <include app:graph="@navigation/auth_provider_nav" />
</navigation>
