<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/landing_screen_nav"
    app:startDestination="@id/selectLanguageFragment">
    <fragment
        android:id="@+id/selectLanguageFragment"
        android:name="com.resoluttech.core.landingscreen.SelectLanguageFragment"
        android:label="fragment_select_language"
        tools:layout="@layout/fragment_select_language" >
        <action
            android:id="@+id/action_selectLanguageFragment_to_landingScreenFragment"
            app:destination="@id/landingScreenFragment" />
    </fragment>

    <fragment
        android:id="@+id/landingScreenFragment"
        android:name="com.resoluttech.core.landingscreen.LandingScreenFragment"
        android:label="fragment_landing_screen"
        tools:layout="@layout/fragment_landing_screen">
        <action
            android:id="@+id/action_landingScreenFragment_to_signupRequirementsFragment"
            app:destination="@id/sign_up_nav"
            app:popUpTo="@id/landingScreenFragment"
            app:popUpToInclusive="true"/>
        <action
            android:id="@+id/action_landingScreenFragment_to_signInFragment"
            app:destination="@+id/sign_in_nav" />
    </fragment>
    <include app:graph="@navigation/sign_up_nav" />
    <include app:graph="@navigation/sign_in_nav" />
</navigation>
