<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_nav"
    app:startDestination="@id/appPinAuthFragment">

    <fragment
        android:id="@+id/appPinAuthFragment"
        android:name="com.resoluttech.core.auth.app.AppPinAuthFragment"
        android:label="AppPinFragment"
        tools:layout="@layout/fragment_app_pin">
        <action
            android:id="@+id/action_appPinAuthFragment_to_moneyScreenFragment"
            app:destination="@id/moneyScreenFragment"
            app:popUpTo="@id/appPinAuthFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_appPinAuthFragment_to_appCodeHintScreen"
            app:destination="@id/appCodeHintScreen" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.resoluttech.core.home.HomeFragment"
        android:label="HomeFragment">
        <action
            android:id="@+id/action_homeFragment_to_inAppBrowserFragment"
            app:destination="@id/inAppBrowserFragment" />
    </fragment>
    <fragment
        android:id="@+id/transactionFragment"
        android:name="com.resoluttech.core.transactions.TransactionsFragment"
        android:label="TransactionFragment"
        tools:layout="@layout/fragment_transactions">

        <deepLink app:uri="resoluttechbcn://transactions" />
    </fragment>
    <fragment
        android:id="@+id/profileFragment"
        android:name="com.resoluttech.core.profile.ProfileFragment"
        android:label="fragment_test_profile"
        tools:layout="@layout/fragment_profile">
        <action
            android:id="@+id/action_profileFragment_to_addEmailAddressFragment"
            app:destination="@+id/addEmailAddressFragment" />
        <action
            android:id="@+id/action_profileFragment_to_appInfoFragment"
            app:destination="@+id/appInfoFragment" />
        <action
            android:id="@+id/action_profileFragment_to_landing_screen"
            app:destination="@id/landing_screen_nav" />
        <action
            android:id="@+id/action_profileFragment_to_changePasswordFragment"
            app:destination="@id/change_password_nav" />
        <action
            android:id="@+id/action_profileFragment_to_findAgentFragment"
            app:destination="@id/findAgentFragment" />
        <action
            android:id="@+id/action_profileFragment_to_manageAccountsFragment"
            app:destination="@id/manage_accounts_nav" />
        <action
            android:id="@+id/action_profileFragment_to_validatePasswordFragment"
            app:destination="@id/validatePasswordFragment" />
        <action
            android:id="@+id/action_profileFragment_to_inAppBrowserFragment"
            app:destination="@id/inAppBrowserFragment" />
        <action
            android:id="@+id/action_profileFragment_to_PDFRendererFragment"
            app:destination="@+id/PDFRendererFragment" />
        <action
            android:id="@+id/action_profileFragment_to_showQRCodeFragment"
            app:destination="@id/showQRCodeFragment" />
        <action
            android:id="@+id/action_profileFragment_to_changeAppPinFragment"
            app:destination="@id/changeAppPinFragment" />
        <action
            android:id="@+id/action_profileFragment_to_listOfStatementMonthsFragment"
            app:destination="@id/listOfStatementMonthsFragment" />
        <action
            android:id="@+id/action_profileFragment_to_manageTrustedContactsFragment"
            app:destination="@id/manageTrustedContactsFragment" />
        <action
            android:id="@+id/action_profileFragment_to_kycNav"
            app:destination="@id/kyc_nav" />
        <action
            android:id="@+id/action_profileFragment_to_thirdPartySoftwareFragments"
            app:destination="@id/thirdPartySoftwareFragments" />
        <action
            android:id="@+id/action_profileFragment_to_changeLanguageFragment"
            app:destination="@id/selectLanguageFragment" />
    </fragment>

    <fragment
        android:id="@+id/selectLanguageFragment"
        android:name="com.resoluttech.core.landingscreen.SelectLanguageFragment"
        android:label="fragment_select_language"
        tools:layout="@layout/fragment_select_language" />

    <fragment
        android:id="@+id/thirdPartySoftwareFragments"
        android:name="com.resoluttech.core.profile.ThirdPartySoftwareFragment"
        android:label="ThirdPartySoftwareFragments"
        tools:layout="@layout/fragment_third_party">
        <action
            android:id="@+id/action_thirdPartySoftwareFragments_to_InAppBrowserFragment"
            app:destination="@id/inAppBrowserFragment" />
    </fragment>

    <fragment
        android:id="@+id/transactionsFragment"
        android:name="com.resoluttech.core.transactions.TransactionsFragment"
        android:label="fragment_test_transactions"
        tools:layout="@layout/fragment_transactions" />
    <fragment
        android:id="@+id/inAppBrowserFragment"
        android:name="com.resoluttech.core.views.InAppBrowserFragment"
        android:label="InAppBrowserFragment">
        <argument
            android:name="destination_url"
            app:argType="string" />
        <argument
            android:name="shouldShowBackButton"
            app:argType="boolean" />
        <argument
            android:name="toolbarTitle"
            app:argType="string" />
    </fragment>

    <include app:graph="@navigation/send_money_to_bcn_user_nav" />
    <include app:graph="@navigation/account_to_account_nav" />
    <include app:graph="@navigation/transaction_status_nav" />
    <include app:graph="@navigation/landing_screen_nav" />
    <include app:graph="@navigation/change_password_nav" />
    <include app:graph="@navigation/send_money_to_counter_party_nav" />
    <include app:graph="@navigation/load_wallet_nav" />
    <include app:graph="@navigation/billers_nav" />
    <include app:graph="@navigation/manage_accounts_nav" />
    <include app:graph="@navigation/auth_provider_nav" />
    <include app:graph="@navigation/kyc_nav" />
    <include app:graph="@navigation/wallet_selector_nav" />

    <fragment
        android:id="@+id/addEmailAddressFragment"
        android:name="com.resoluttech.core.profile.AddEmailAddressFragment"
        android:label="fragment_add_email_address"
        tools:layout="@layout/fragment_add_email" />
    <fragment
        android:id="@+id/appInfoFragment"
        android:name="com.resoluttech.core.profile.AppInfoFragment"
        android:label="fragment_app_info"
        tools:layout="@layout/fragment_app_info" />

    <fragment
        android:id="@+id/changePasswordFragment"
        android:name="com.resoluttech.core.auth.ChangePasswordFragment"
        android:label="fragment_change_password"
        tools:layout="@layout/fragment_change_password" />
    <include app:graph="@navigation/sign_in_nav" />
    <fragment
        android:id="@+id/moneyScreenFragment"
        android:name="com.resoluttech.core.home.MoneyScreenFragment"
        android:label="MoneyScreenFragment">
        <action
            android:id="@+id/action_moneyScreenFragment_to_notificationsFragment"
            app:destination="@id/notificationsFragment" />
        <action
            android:id="@+id/action_moneyScreenFragment_to_send_money_nav"
            app:destination="@+id/send_money_nav" />
        <action
            android:id="@+id/action_moneyScreenFragment_to_load_wallet_nav"
            app:destination="@+id/load_wallet_nav" />
    </fragment>
    <fragment
        android:id="@+id/appPinSetupFragment"
        android:name="com.resoluttech.core.uicomponents.AppPinSetupFragment"
        android:label="AppPinFragment" />
    <fragment
        android:id="@+id/appCodeHintScreen"
        android:name="com.resoluttech.core.auth.app.AppCodeHintScreen"
        android:label="app_code_hint_fragment"
        tools:layout="@layout/app_code_hint_fragment">
        <action
            android:id="@+id/action_appCodeHintScreen_to_appPinSetupFragment"
            app:destination="@id/appPinSetupFragment" />
        <action
            android:id="@+id/action_appCodeHintScreen_to_moneyScreenFragment"
            app:destination="@id/moneyScreenFragment" />
    </fragment>
    <fragment
        android:id="@+id/deepLinkedTransactionStatusFragment"
        android:name="com.resoluttech.core.deeplinks.DeepLinkedTransactionStatusFragment"
        android:label="DeepLinkedTransactionStatusFragment">
        <deepLink app:uri="resoluttech://transaction_status/?status={status}&amp;date_time={date_time}&amp;amount={amount}&amp;currency={currency}&amp;public_remark={public_remark}&amp;private_remark={private_remark}&amp;transaction_id={transaction_id}&amp;failure_reason={failure_reason}&amp;locale={locale}" />
    </fragment>
    <fragment
        android:id="@+id/notificationsFragment"
        android:name="com.resoluttech.core.home.NotificationsFragment"
        android:label="NotificationsFragment" />
    <fragment
        android:id="@+id/findAgentFragment"
        android:name="com.resoluttech.core.profile.FindAgentFragment"
        android:label="fragment_find_agent"
        tools:layout="@layout/fragment_find_agent" />
    <fragment
        android:id="@+id/billersFragment"
        android:name="com.resoluttech.core.payments.BillersFragment"
        android:label="fragment_billers"
        tools:layout="@layout/fragment_billers">
        <action
            android:id="@+id/action_billerFragment_to_billerInputFragment"
            app:destination="@id/billers_nav" />

        <deepLink app:uri="resoluttechbcn://pay_bill" />
    </fragment>
    <fragment
        android:id="@+id/validatePasswordFragment"
        android:name="com.resoluttech.core.profile.ValidatePasswordFragment"
        android:label="ValidatePasswordFragment"
        tools:layout="@layout/fragment_validate_password" />
    <fragment
        android:id="@+id/showQRCodeFragment"
        android:name="com.resoluttech.core.profile.ShowQRCodeFragment"
        android:label="fragment_show_qr_code"
        tools:layout="@layout/fragment_show_qr_code" />
    <fragment
        android:id="@+id/showQRCodeContent"
        android:name="com.resoluttech.core.profile.ShowQRCodeContent"
        android:label="ShowQRCodeContent" />
    <fragment
        android:id="@+id/changeAppPinFragment"
        android:name="com.resoluttech.core.profile.changesessionpin.ChangeAppPinFragment"
        android:label="ChangeAppPinFragment"
        tools:layout="@layout/fragment_change_app_pin" />
    <fragment
        android:id="@+id/createNewAccountFragment2"
        android:name="com.resoluttech.core.profile.manageaccount.CreateNewAccountFragment"
        android:label="CreateNewAccountFragment" />
    <fragment
        android:id="@+id/listOfStatementMonthsFragment"
        android:name="com.resoluttech.core.profile.statements.ListOfStatementMonthsFragment"
        android:label="ListOfStatementMonthsFragment"
        tools:layout="@layout/fragment_list_of_statement_months">
        <action
            android:id="@+id/action_listOfStatementMonthsFragment_to_PDFRendererFragment"
            app:destination="@id/PDFRendererFragment" />
    </fragment>
    <fragment
        android:id="@+id/PDFRendererFragment"
        android:name="com.resoluttech.core.profile.statements.PDFRendererFragment"
        android:label="PDFRendererFragment">
        <argument
            android:name="title"
            app:argType="string" />
        <argument
            android:name="previousPath"
            app:argType="string" />
    </fragment>
    <fragment
        android:id="@+id/manageTrustedContactsFragment"
        android:name="com.resoluttech.core.profile.trustedcontacts.ManageTrustedContactsFragment"
        android:label="ManageTrustedContactsFragment"
        tools:layout="@layout/fragment_manage_trusted_contacts">
        <action
            android:id="@+id/action_manageTrustedContactsFragment_to_addTrustedContactFragmentProfile"
            app:destination="@id/addTrustedContactFragmentProfile" />
    </fragment>
    <fragment
        android:id="@+id/addTrustedContactFragmentProfile"
        android:name="com.resoluttech.core.profile.trustedcontacts.AddTrustedContactFragment"
        android:label="AddTrustedContactFragment"
        tools:layout="@layout/fragment_add_trusted_contact">
        <action
            android:id="@+id/action_start_contact_picker"
            app:destination="@id/contact_picker" />
    </fragment>
    <fragment
        android:id="@+id/contact_picker"
        android:name="com.resoluttech.core.utils.ContactPickerFragment"
        tools:layout="@layout/fragment_transfers_contact_picker" />
</navigation>
