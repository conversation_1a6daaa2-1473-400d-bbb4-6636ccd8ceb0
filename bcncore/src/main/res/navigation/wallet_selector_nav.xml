<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/wallet_selector_nav"
    app:startDestination="@id/walletSelectorFragment">

    <fragment
        android:id="@+id/walletSelectorFragment"
        android:name="com.resoluttech.core.views.walletselector.WalletSelectorFragment"
        android:label="WalletSelectorFragment"
        tools:layout="@layout/fragment_wallet_selector">
        <deepLink app:uri="resoluttech://wallet_selector/?previousPath={previousPath}&amp;selectedAccountId={selectedAccountId}" />
    </fragment>
</navigation>
