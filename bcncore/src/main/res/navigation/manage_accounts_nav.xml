<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/manage_accounts_nav"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/manageAccountFragment">

    <fragment
        android:id="@+id/manageAccountFragment"
        android:name="com.resoluttech.core.profile.manageaccount.ManageAccountFragment"
        android:label="ManageAccountFragment"
        tools:layout="@layout/fragment_manage_account">
        <action
            android:id="@+id/action_manageAccountFragment_to_createNewAccountFragment"
            app:destination="@id/createNewAccountFragment" />
        <deepLink app:uri="resoluttech://manage_accounts/?previousPath={previousPath}" />
    </fragment>
    <fragment
        android:id="@+id/createNewAccountFragment"
        android:name="com.resoluttech.core.profile.manageaccount.CreateNewAccountFragment"
        android:label="CreateNewAccountFragment">
        <argument
            android:name="previousPath"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="null"/>
    </fragment>


</navigation>
