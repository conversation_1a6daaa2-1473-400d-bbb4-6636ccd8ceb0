<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/send_money_nav"
    app:startDestination="@id/sendMoneyFragment">
    <include app:graph="@navigation/send_money_to_bcn_user_nav" />
    <include app:graph="@navigation/account_to_account_nav"/>

    <fragment
        android:id="@+id/sendMoneyFragment"
        android:name="com.resoluttech.core.sendmoney.SendMoneyCounterpartyFragment"
        android:label="@string/selectDestinationViewTitle"
        tools:layout="@layout/fragment_send_money_counterparty">
        <deepLink app:uri="resoluttechbcn://money-transfer" />
        <action
            android:id="@+id/action_sendMoneyFragment_to_recipientIdentifierFragment"
            app:destination="@id/recipientIdentifierFragment" />
        <action
            android:id="@+id/action_sendMoneyFragment_to_BCNMoneyTransferCounterpartiesFragment2"
            app:destination="@id/BCNMoneyTransferCounterpartiesFragment2" />
    </fragment>
    <fragment
        android:id="@+id/recipientIdentifierFragment"
        android:name="com.resoluttech.core.sendmoney.RecipientIdentifierFragment"
        android:label="@string/selectCounterpartyUserViewTitle"
        tools:layout="@layout/fragment_recipient_identifier_screen">
        <action
            android:id="@+id/action_recipientIdentifierFragment_to_confirmationFragment"
            app:destination="@id/sendMoneyConfirmationFragment" />
    </fragment>
    <fragment
        android:id="@+id/sendMoneyConfirmationFragment"
        android:name="com.resoluttech.core.sendmoney.SendMoneyToExternalUserConfirmationFragment"
        android:label="@string/selectCounterpartyUserViewTitle"
        tools:layout="@layout/fragment_send_money">
        <action
            android:id="@+id/action_sendMoneyConfirmationFragment_To_AuthenticationProvider"
            app:destination="@id/auth_provider_nav" />
    </fragment>
    <fragment
        android:id="@+id/BCNMoneyTransferCounterpartiesFragment2"
        android:name="com.resoluttech.core.sendmoney.BCNMoneyTransferCounterpartiesFragment"
        android:label="BCNMoneyTransferCounterpartiesFragment" >
        <action
            android:id="@+id/action_bcn_counterparty_to_qr_code"
            app:destination="@id/peer_to_peer_transfer_nav" />
        <action
            android:id="@+id/action_bcn_counterparty_to_self_transfer"
            app:destination="@id/account_to_account_nav" />
    </fragment>
    <include app:graph="@navigation/transaction_status_nav" />
    <include app:graph="@navigation/auth_provider_nav" />
</navigation>
