<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/account_to_account_nav"
    app:startDestination="@id/accountToAccountFragment">
    <include app:graph="@navigation/transaction_status_nav" />

    <fragment
        android:id="@+id/accountToAccountFragment"
        android:name="com.resoluttech.core.accounttoaccount.AccountToAccountPickerFragment"
        android:label="fragment_account_to_account"
        tools:layout="@layout/fragment_account_to_account_picker">
        <action
            android:id="@+id/action_accountToAccountFragment_to_accountToAccountConfirmationFragment"
            app:destination="@id/accountToAccountConfirmationFragment" />
        <deepLink app:uri="resoluttechbcn://account-to-account" />
        <action
            android:id="@+id/action_accountToAccountFragment_to_transaction_status_nav2"
            app:destination="@id/transaction_status_nav" />
        <action
            android:id="@+id/action_accountToAccountFragment_to_auth_provider_nav"
            app:destination="@id/auth_provider_nav" />
    </fragment>

    <dialog
        android:id="@+id/accountToAccountConfirmationFragment"
        android:name="com.resoluttech.core.accounttoaccount.AccountToAccountConfirmationDialogFragment"
        android:label="fragment_account_to_account_confirmation"
        tools:layout="@layout/fragment_account_to_account_confirmation">
        <argument
            android:name="confirmationData"
            app:argType="com.resoluttech.core.accounttoaccount.AccountConfirmationData" />

    </dialog>
    <include app:graph="@navigation/auth_provider_nav" />


</navigation>
