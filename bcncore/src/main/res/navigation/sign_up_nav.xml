<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/sign_up_nav"
    app:startDestination="@id/signUpRequirementsFragment">
    <fragment
        android:id="@+id/signUpRequirementsFragment"
        android:name="com.resoluttech.core.auth.signup.SignUpRequirementsFragment"
        android:label="fragment_required_documents">
        <action
            android:id="@+id/action_signUpRequirementsFragment_to_signupFragment"
            app:destination="@id/signUpFragment" />
    </fragment>
    <fragment
        android:id="@+id/signUpFragment"
        android:name="com.resoluttech.core.auth.signup.SignUpFragment"
        android:label="SignUpFragment"
        tools:layout="@layout/fragment_sign_up">
        <action
            android:id="@+id/action_signUpFragment_to_trustedContactInfoFragment"
            app:destination="@id/trustedContactInfoFragment" />
        <action
            android:id="@+id/action_signUpFragment_to_selectLanguage"
            app:destination="@id/changeLanguageFragment" />
    </fragment>
    <fragment
        android:id="@+id/setPasswordFragment"
        android:name="com.resoluttech.core.auth.signup.SetupPasswordFragment"
        android:label="SetupPasswordFragment"
        tools:layout="@layout/fragment_set_password">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="string" />
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <action
            android:id="@+id/action_setPasswordFragment_to_signUpFragment"
            app:destination="@id/signUpFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/signUpFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_setPasswordFragment_to_kyc_nav"
            app:destination="@id/kyc_nav" />
    </fragment>
    <fragment
        android:id="@+id/securityQuestions"
        android:name="com.resoluttech.core.auth.signin.SignUpSecurityQuestionsFragment"
        android:label="fragment_security_questions"
        tools:layout="@layout/fragment_security_questions">
        <action
            android:id="@+id/action_securityQuestionsFragment_to_setPasswordFragment"
            app:destination="@id/setPasswordFragment" />
        <action
            android:id="@+id/action_securityQuestionFragment_to_selectQuestionFragment"
            app:destination="@id/selectSecurityQuestion" />
    </fragment>
    <fragment
        android:id="@+id/selectSecurityQuestion"
        android:name="com.resoluttech.core.auth.signin.SelectSecurityQuestionFragment"
        android:label="fragment_select_security_question"
        tools:layout="@layout/fragment_select_security_question" />
    <include app:graph="@navigation/kyc_nav" />
    <fragment
        android:id="@+id/trustedContactInfoFragment"
        android:name="com.resoluttech.core.auth.trustedcontacts.TrustedContactInfoFragment"
        android:label="fragment_trusted_contact_info"
        tools:layout="@layout/fragment_trusted_contact_info">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="java.util.UUID" />
        <argument
            android:name="phoneNumberValidatedTokenValidityTimeInSeconds"
            app:argType="integer" />
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <action
            android:id="@+id/action_trustedContactInfoFragment_to_setupTrustedContactsFragment"
            app:destination="@id/setupTrustedContactsFragment" />
    </fragment>
    <fragment
        android:id="@+id/setupTrustedContactsFragment"
        android:name="com.resoluttech.core.auth.trustedcontacts.SetupTrustedContactsFragment"
        android:label="fragment_setup_trusted_contacts"
        tools:layout="@layout/fragment_setup_trusted_contacts">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="java.util.UUID" />
        <argument
            android:name="phoneNumber"
            app:argType="string" />
        <action
            android:id="@+id/action_setupTrustedContactsFragment_to_addTrustedContactFragment"
            app:destination="@id/addTrustedContactFragment" />
        <action
            android:id="@+id/action_setupTrustedContactsFragment_to_securityQuestions"
            app:destination="@id/securityQuestions" />
    </fragment>
    <fragment
        android:id="@+id/addTrustedContactFragment"
        android:name="com.resoluttech.core.auth.trustedcontacts.AddTrustedContactFragment"
        android:label="fragment_add_trusted_contact"
        tools:layout="@layout/fragment_add_trusted_contact">
        <argument
            android:name="phoneNumberValidatedToken"
            app:argType="java.util.UUID" />
        <action
            android:id="@+id/action_start_contact_picker"
            app:destination="@id/contact_picker" />
    </fragment>
    <fragment
        android:id="@+id/contact_picker"
        android:name="com.resoluttech.core.utils.ContactPickerFragment"
        tools:layout="@layout/fragment_transfers_contact_picker">
        <action
            android:id="@+id/action_contact_picker_to_sendMoneyToUser"
            app:destination="@id/sendMoneyToUser" />
    </fragment>
    <fragment
        android:id="@+id/changeLanguageFragment"
        android:name="com.resoluttech.core.landingscreen.SelectLanguageFragment"
        android:label="fragment_select_language"
        tools:layout="@layout/fragment_select_language" />
</navigation>
