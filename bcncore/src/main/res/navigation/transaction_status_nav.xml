<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/transaction_status_nav"
    app:startDestination="@id/transactionStatusFragment">

    <fragment
        android:id="@+id/transactionStatusFragment"
        android:name="com.resoluttech.core.views.TransactionStatusFragment"
        android:label="fragment_successful_transaction"
        tools:layout="@layout/fragment_transaction_status" >
    </fragment>

</navigation>
