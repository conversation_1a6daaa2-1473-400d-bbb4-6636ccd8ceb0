<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/auth_provider_nav"
    app:startDestination="@id/AuthenticationProvider">

    <fragment
        android:id="@+id/AuthenticationProvider"
        android:name="com.resoluttech.core.utils.apppin.BCNAppAppAuthenticationProvider"
        android:label="AuthenticationProvider"
        tools:layout="@layout/fragment_app_pin" >
        <action
            android:id="@+id/action_AuthenticationProvider_to_AppPinAuthFragment"
            app:destination="@id/appPinAuthFragment"
            app:popUpToInclusive="true"/>
    </fragment>
    <fragment
        android:id="@+id/appPinAuthFragment"
        android:name="com.resoluttech.core.auth.app.AppPinAuthFragment"
        android:label="AppPinFragment"
        tools:layout="@layout/fragment_app_pin">
    </fragment>
</navigation>
