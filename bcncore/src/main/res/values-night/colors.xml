<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="dialogBg">#28293D</color>
    <color name="buttonLabelColor">#DFDEDE</color>
    <color name="spinnerItemTextColor">#ffffff</color>
    <color name="spinnerBgColor">#28293D</color>
    <color name="selectedPageIndicatorColor">#7C4DFF</color>
    <color name="unselectedPageIndicatorColor">#888888</color>
    <color name="listHeaderBgColor">#28293D</color>
    <color name="listSubHeaderBgColor">#44ffffff</color>
    <color name="listSubHeaderTextColor">#fafafa</color>
    <color name="unavailableAccountTextColor">#888888</color>
    <color name="listDividerColor">#28293D</color>
    <color name="dialogBgColor">#28293D</color>
    <color name="titleTextColor">#FAFAFC</color>
    <color name="descriptionTextColor">#F2F2F5</color>
    <color name="highLightedTextColor">#21CE99</color>
    <color name="descriptionTextDarkColor">#8E90A6</color>
    <color name="dialogSubtitleTextColor">#C7C8D9</color>
    <color name="titleDescriptionTextColor">#8E90A6</color>
    <color name="boldTitleTextColor">#ffffff</color>
    <color name="sectionHeadingColor">#ffffff</color>
    <color name="surfaceColor">#555770</color>
    <color name="pullToRefreshBgColor">#404040</color>
    <color name="destructiveActionColor">#FF3B3B</color>
    <color name="dialogAlertActionColor">#FDAC42</color>
    <color name="successful_transaction_theme_color">#06C270</color>
    <color name="positiveIndicatorColor">#06C270</color>
    <color name="buttonPressedRippleColor">#404040</color>
    <color name="windowBackground">#1C1C1E</color>
    <color name="windowDividerBackground">#808080</color>
    <color name="dropdownDividerBackground">#20ffffff</color>
    <color name="spinnerDropDownBackground">#aaaaaa</color>
    <color name="subtitleTextColor">#FAFAFC</color>
    <color name="dividerColor">#28293D</color>
    <color name="buttonDisabledColor">#28293D</color>
    <color name="editTextDivider">#8E90A6</color>
    <color name="colorPrimaryLight">#B49CF8</color>
    <color name="agentClosed">#FF3B3B</color>
    <color name="colorAccentLight">#8E90A6</color>
    <color name="bg_outline_button">#B49CF8</color>
    <color name="subtitleTextColorSecondary">#8E90A6</color>
    <color name="passwordToggleColor">#F2F2F5</color>
    <color name="cardBorderColor">#555770</color>
    <color name="negativeInfoTextColor">#FDAC42</color>
    <color name="errorTextColor">#FF5C5C</color>
</resources>
