<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/option_remark"
        android:title="@string/transactionStatusAddNarrationButtonTitle"
        android:icon="@drawable/ic_remark_edit"
        android:contentDescription="@string/axTransactionStatusAddNarrationHint"
        app:showAsAction="never" />

    <item
        android:id="@+id/option_share"
        android:icon="@drawable/ic_share_app"
        android:title="@string/showQRCodeShareCodeLabelText"
        android:contentDescription="@string/axTransactionStatusHeaderHint"
        app:showAsAction="never" />

</menu>
