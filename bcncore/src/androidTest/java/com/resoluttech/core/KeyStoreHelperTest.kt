package com.resoluttech.core

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.resoluttech.core.auth.KeyStoreHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import java.util.UUID

@RunWith(AndroidJUnit4::class)
class KeyStoreHelperTest {

    private val context = InstrumentationRegistry.getInstrumentation().context

    @Before
    fun setup() {
        startKoin {
            androidContext(context)
        }
    }

    @Test
    fun savingAndFetchingSLTTest() {
        val slt = UUID.randomUUID().toString()
        GlobalScope.launch(Dispatchers.IO) {
            KeyStoreHelper.storeSLT(slt)
            Assert.assertEquals(slt, KeyStoreHelper.getSLT())
        }
    }

    @Test
    fun savingAndFetchingLLTTest() {
        val llt = UUID.randomUUID().toString()
        GlobalScope.launch(Dispatchers.IO) {
            KeyStoreHelper.storeLLT(llt)
            Assert.assertEquals(llt, KeyStoreHelper.getLLT())
        }
    }

    @Test
    fun clearAllTokensTest() {
        GlobalScope.launch(Dispatchers.IO) {
            KeyStoreHelper.clearAllTokens()
            val slt = KeyStoreHelper.getSLT()
            val llt = KeyStoreHelper.getLLT()
            Assert.assertEquals(slt, null)
            Assert.assertEquals(llt, null)
        }
    }

    @After
    fun stop() {
        stopKoin()
    }
}
