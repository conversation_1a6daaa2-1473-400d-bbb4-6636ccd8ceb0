package com.resoluttech.core

import android.content.Context
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.resoluttech.bcn.assets.BitmapImageType
import com.resoluttech.bcn.assets.MultiResolutionBitmapImage
import com.resoluttech.bcn.assets.RemoteBitmapImage
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import java.net.URL

@RunWith(AndroidJUnit4::class)
class ImageResolutionTest {

    private lateinit var appContext: Context

    @Test
    fun validateAppContext() {
        appContext = InstrumentationRegistry.getInstrumentation().targetContext
    }

    @Test
    fun testForPickingCorrectAndNextUpperResolution() {
        val mdpi = RemoteBitmapImage(
            URL("http://mdpi.png"),
            BitmapImageType.PNG,
            0,
            0,
        )
        val xhdpi = RemoteBitmapImage(
            URL("http://xhdpi.png"),
            BitmapImageType.PNG,
            0,
            0,
        )
        val xxhdpi = RemoteBitmapImage(
            URL("http://xxhdpi.png"),
            BitmapImageType.PNG,
            0,
            0,
        )
        val xxxhdpi = RemoteBitmapImage(
            URL("http://xxxhdpi.png"),
            BitmapImageType.PNG,
            0,
            0,
        )

        val multiResolutionBitmapImage = MultiResolutionBitmapImage(
            mdpi,
            xhdpi,
            xxhdpi,
            xxxhdpi,
        )

        // For 0.5F, should pick 1x (mdpi)
        var url = getSpecificResolutionImageURL(0.5F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://mdpi.png")

        // For 1.5F, should pick 2x (xhdpi)
        url = getSpecificResolutionImageURL(1.5F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://xhdpi.png")

        // For 2.7F, should pick 3x (xxhdpi)
        url = getSpecificResolutionImageURL(2.7F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://xxhdpi.png")

        // For 3.0F, should pick 3x (xxhdpi)
        url = getSpecificResolutionImageURL(3.0F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://xxhdpi.png")

        // For 3.5F, should pick 4x (xxxhdpi)
        url = getSpecificResolutionImageURL(3.5F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://xxxhdpi.png")

        // For 4.0F, should pick 4x (xxxhdpi)
        url = getSpecificResolutionImageURL(4.0F, multiResolutionBitmapImage)
        Assert.assertTrue("$url" == "http://xxxhdpi.png")
    }
}
