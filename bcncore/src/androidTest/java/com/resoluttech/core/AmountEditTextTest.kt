package com.resoluttech.core

import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.resoluttech.core.config.AmountLimitConfig
import com.resoluttech.core.views.AmountEditText
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.core.module.Module
import org.koin.dsl.module

@RunWith(AndroidJUnit4::class)
class AmountEditTextTest {

    private lateinit var amountEditText: AmountEditText
    private val testModule: Module = module {
        single {
            AmountLimitConfig(12, 2)
        }
    }

    @Before
    fun setup() {
        startKoin {
            modules(testModule)
        }
        amountEditText = AmountEditText(InstrumentationRegistry.getInstrumentation().context)
    }

    @Test
    fun getInputTextTest() {
        val initialText = "12345.33"
        amountEditText.setText("12345.33")
        val outputText = amountEditText.getInputText()
        Assert.assertEquals(outputText, initialText)
    }

    @Test
    fun getInputTextWithoutDecimalTest() {
        val initialText = "12345"
        amountEditText.setText("12345")
        val outputText = amountEditText.getInputText()
        Assert.assertEquals(outputText, initialText)
    }

    @Test
    fun getEnteredAmountTest() {
        val initialText = "12345.33"
        val expectedOutput: Long = 123453300 // 12345.33 * 10000
        amountEditText.setText(initialText)
        val output = amountEditText.getFormattedAmount()
        Assert.assertEquals(expectedOutput, output!!.processedValue)
    }

    @Test
    fun getEnteredAmountWithoutDecimalTest() {
        val initialText = "12345"
        val expectedOutput: Long = 123450000 // 12345 * 10000
        amountEditText.setText(initialText)
        val output = amountEditText.getFormattedAmount()
        Assert.assertEquals(expectedOutput, output!!.processedValue)
    }

    @After
    fun destroy() {
        stopKoin()
    }
}
