package com.resoluttech.core

import com.resoluttech.bcn.assets.MultiResolutionBitmapImage
import java.net.URL

/**
 * This method returns image URL based on device density.
 * Default is xxxhdpi as we don't want to the upscaling of lower density images but rather to down
 * sample a higher density image.
 */
internal fun getSpecificResolutionImageURL(deviceDensity: Float, multiResolutionBitmapImage: MultiResolutionBitmapImage): URL {
    return when (deviceDensity) {
        in MDPI -> multiResolutionBitmapImage.mdpi.imageURL
        in XHDPI -> multiResolutionBitmapImage.xhdpi.imageURL
        in XXHDPI -> multiResolutionBitmapImage.xxhdpi.imageURL
        else -> multiResolutionBitmapImage.xxxhdpi.imageURL
    }
}

// Thanks: https://developer.android.com/training/multiscreen/screendensities#TaskProvideAltBmp
private val MDPI = 0.0F..1.0F // for density 0.0x to 1x, will remain 1x
private val XHDPI = 1.1F..2.0F // for density 1.1x to 2x, will remain 2x
private val XXHDPI = 2.1F..3.0F // for density 2.1x to 3x, will remain 3x
