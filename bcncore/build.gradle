import java.nio.file.Paths

plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'androidx.navigation.safeargs.kotlin'
    alias(lkr.plugins.spotless)
    alias(lkr.plugins.kotlinx.serialization)
}

android {
    namespace 'com.resoluttech.bcncore'
    compileSdk compile_sdk_version.toInteger()
    buildToolsVersion = build_tools_version

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_21
    }

    defaultConfig {
        minSdkVersion min_sdk_version.toInteger()
        //noinspection OldTargetApi
        targetSdkVersion target_sdk_version.toInteger()

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // Thanks : https://stackoverflow.com/a/47509465/13268813
    packagingOptions {
        resources.excludes.add("META-INF/*")
    }
    lint {
        abortOnError true
        disable 'GradleDependency',
                'MediaCapabilities',
                'FragmentBackPressedCallback',
                'FragmentLiveDataObserve',
                'FragmentAddMenuProvider',
                'UnusedResources',
                'AppBundleLocaleChanges'
        warningsAsErrors true
    }
    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    // Internal modules
    implementation libs.libui
    // BCN RPCs
    implementation libs.home.screen.android.client
    implementation libs.transfers.android.client
    implementation libs.home.screen.interface
    implementation libs.payments.interface
    implementation libs.profile.interface
    implementation libs.transfers.interface
    implementation libs.auth.interface
    implementation libs.sign.up.in.interface
    implementation libs.transactions.interface
    implementation libs.document.interface
    implementation libs.push.notification.interface
    implementation libs.document.android.client
    // External dependencies
    implementation lkr.androidx.camera.lifecycle
    implementation lkr.androidx.camera.camera2

    implementation lkr.androidx.core
    implementation lkr.androidx.appcompat
    implementation lkr.androidx.legacy.support
    implementation lkr.google.android.material
    implementation lkr.card.form
    implementation lkr.androidx.navigation.fragment
    implementation lkr.androidx.fragment
    implementation lkr.androidx.navigation.ui
    implementation lkr.timber
    implementation lkr.coil
    implementation lkr.androidx.swiperefreshlayout
    implementation lkr.androidx.localbroadcastmanager
    implementation lkr.androidx.constraintlayout
    implementation lkr.koin.android
    implementation lkr.google.play.services.location
    implementation lkr.androidx.security.crypto
    implementation lkr.google.zxing.core
    implementation lkr.google.play.services.vision
    implementation lkr.androidx.biometric
    implementation lkr.pin.edit.text.field
    implementation lkr.ucrop

    implementation lkr.photo.view
    implementation lkr.gson
    coreLibraryDesugaring lkr.desugar
    implementation lkr.google.play.services.auth
    implementation lkr.libphonenumber
    implementation lkr.leo.kedwig.jvm
    implementation lkr.otp.view
    implementation lkr.hbb20.ccp
    implementation lkr.androidx.camera.view
    implementation lkr.google.play.services.barcode.scanning
    implementation lkr.google.play.services.maps
    // Firebase
    implementation platform(lkr.firebase.bom)
    implementation lkr.firebase.crashlytics
    implementation lkr.firebase.messaging

    // Test dependencies
    testImplementation lkr.junit
    androidTestImplementation lkr.androidx.test.core
    androidTestImplementation lkr.androidx.test.ext.junit
    androidTestImplementation lkr.androidx.test.espresso
}

apply from: Paths.get("${project.rootDir}", "spotless.gradle")

// We skip the `testReleaseUnitTest` task because the release variant runs through R8/ProGuard minification and obfuscation.
// Minification can rename or strip classes/methods in ways that break unit tests (which rely on un-obfuscated names).
// which leads to ClassNotFound or other mismatches. To avoid false failures, we disable the release tests and rely on
// debug build unit tests for correctness.
tasks.matching { it.name.startsWith("test") && it.name.endsWith("ReleaseUnitTest") }.configureEach {
    enabled = false
}
